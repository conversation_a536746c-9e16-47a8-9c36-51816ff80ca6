import { <PERSON><PERSON><PERSON>, EditButton, FilterDropdown, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IUser } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { Input } from 'antx'
import type { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const UserList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IUser>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'email', 'from', 'languageId', 'createdAt'],
      join: [
        {
          field: 'language',
          select: ['id', 'name__en'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/users', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="email"
          title="Email"
          className="cursor-pointer"
          align="center"
          defaultFilteredValue={getDefaultFilter('email', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 280 }} placeholder="User email" normalize={(value) => value.trim()} />
            </FilterDropdown>
          )}
          width="7rem"
        />
        <Table.Column
          dataIndex="languageId"
          title="Language"
          className="cursor-pointer"
          render={(_, row: IUser) => row.language?.name__en}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IUser>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
