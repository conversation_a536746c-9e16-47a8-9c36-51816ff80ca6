import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Body, Controller, Get, Post, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { type Express } from 'express'

import { ImageUsedInType } from '~/constants'
import { ImageEntity } from '~/entities'
import { ImageService } from '~admin/services'

import { AdminJwtAuthGuard } from '../guards'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ImageEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  routes: {
    // only support read operations
    only: ['getOneBase', 'getManyBase'],
  },
})
@Controller('admin/images')
export class ImageController implements CrudController<ImageEntity> {
  constructor(public service: ImageService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  uploadImage(
    @UploadedFile() file: Express.Multer.File,
    @Body() { name }: { entityId: string; entityType: ImageUsedInType; name: string }
  ) {
    return this.service.uploadImage({ file, originalFilename: name })
  }
}
