import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { PaymentListProblemItemReason, PaymentListType } from '~/constants'
import { type ISaleItem, SaleItemEntity } from '~/entities'

@Entity('payment_list_problem_item')
export class PaymentListProblemItemEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column()
  dueDate: Date

  @Column({ type: 'varchar' })
  paymentListType: PaymentListType

  @Column({ default: false })
  isResolved: boolean

  @Column({ nullable: true })
  beneficiaryName?: string

  @Column({ nullable: true })
  iban?: string

  @Column({ default: false })
  isDonation: boolean

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  amount: number

  @Column({ type: 'varchar' })
  reason: PaymentListProblemItemReason

  @Column({ nullable: true })
  note?: string

  @ManyToOne(() => SaleItemEntity, (saleItem) => saleItem.paymentListProblemItems)
  saleItem: Relation<SaleItemEntity>

  @Column()
  @RelationId((paymentListProblemItem: PaymentListProblemItemEntity) => paymentListProblemItem.saleItem)
  saleItemId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IPaymentListProblemItem = Omit<PaymentListProblemItemEntity, keyof BaseEntity> & {
  saleItem: ISaleItem
}
