import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { AddressType } from '~/constants'
import { type ILocaleCountry, type IUser, LocaleCountryEntity, UserEntity } from '~/entities'

registerEnumType(AddressType, { name: 'AddressType' })

@ObjectType('Address')
@Entity('address')
export class AddressEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column()
  firstName: string

  @Field()
  @Column()
  lastName: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  phoneAreaCode?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  phoneNumber?: string

  @Field(() => LocaleCountryEntity)
  @ManyToOne(() => LocaleCountryEntity, { nullable: false })
  country: Relation<LocaleCountryEntity>

  @Column()
  @RelationId((address: AddressEntity) => address.country)
  countryId: string

  @Field()
  @Column()
  postalCode: string

  @Field()
  @Column()
  houseNumber: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  addition?: string | null

  @Field()
  @Column()
  street: string

  @Field()
  @Column()
  city: string

  @Field(() => AddressType)
  @Column({ type: 'varchar' })
  type: AddressType

  @ManyToOne(() => UserEntity, (user) => user.addresses, { nullable: false, onDelete: 'CASCADE' })
  user: Relation<UserEntity>

  @Column()
  @RelationId((address: AddressEntity) => address.user)
  userId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IAddress = Omit<AddressEntity, keyof BaseEntity> & {
  country: ILocaleCountry
  user: IUser
}
