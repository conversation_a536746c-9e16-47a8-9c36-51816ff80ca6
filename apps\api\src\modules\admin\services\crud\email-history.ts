import { CrudRequest, GetManyDefaultResponse } from '@dataui/crud'
import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { EmailHistoryEntity } from '~/entities'
import { renderEmail } from '~/utils'

const findAndRemoveRelatedIdsSearch = (obj: any, result: string[] = []): string[] => {
  if (Array.isArray(obj)) {
    obj.forEach((item) => findAndRemoveRelatedIdsSearch(item, result))
  } else if (typeof obj === 'object' && obj !== null) {
    for (const key in obj) {
      if (key === 'relatedIds' && obj[key].$eq && !result.includes(obj[key].$eq)) {
        result.push(obj[key].$eq)
        delete obj[key]
      } else {
        findAndRemoveRelatedIdsSearch(obj[key], result)
      }
    }
  }
  return result
}

export class CrudEmailHistoryService extends TypeOrmCrudService<EmailHistoryEntity> {
  constructor(@InjectRepository(EmailHistoryEntity) repo: Repository<EmailHistoryEntity>) {
    super(repo)
  }

  // FIXME: ugly hack to fix relatedIds filter, because searching for an varchar array is not supported by the library
  public async getMany(req: CrudRequest): Promise<GetManyDefaultResponse<EmailHistoryEntity> | EmailHistoryEntity[]> {
    const { parsed, options } = req

    const relatedIds = findAndRemoveRelatedIdsSearch(parsed.search)
    const builder = await this.createBuilder(parsed, options)
    if (relatedIds.length) {
      builder.andWhere(`${this.getFieldWithAlias('relatedIds')} @> ARRAY[:...relatedIds]::uuid[]`, {
        relatedIds,
      })
    }
    return this.doGetMany(builder, parsed, options)
  }

  async previewEmail(id: string) {
    const emailHistory = await this.repo.findOne({ where: { id } })
    return renderEmail({
      type: emailHistory.type as any,
      lang: emailHistory.lang,
      data: emailHistory.data as any,
      templatePrefix: emailHistory?.templatePrefix,
    })
  }
}
