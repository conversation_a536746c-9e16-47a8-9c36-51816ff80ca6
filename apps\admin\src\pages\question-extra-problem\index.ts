import type { PageType } from '~/interfaces'

import { QuestionExtraProblemCreate } from './create'
import { QuestionExtraProblemEdit } from './edit'
import { QuestionExtraProblemList } from './list'

export const QuestionExtraProblemPage: PageType = {
  path: 'question-extra-problems',
  label: 'Extra Problems',
  index: QuestionExtraProblemList,
  create: QuestionExtraProblemCreate,
  edit: QuestionExtraProblemEdit,
  parent: 'product',
  withLayout: true,
}
