import { ArgsType, Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import { IsEnum, IsIn, IsNotEmpty } from 'class-validator'

import { LOCALE_ENABLED_LANGUAGES, SeoContentType } from '~/constants'

registerEnumType(SeoContentType, { name: 'SeoContentType' })

@ArgsType()
export class GetSeoContentInput {
  @Field(() => SeoContentType)
  @IsEnum(SeoContentType)
  type: SeoContentType

  @Field(() => String, { description: 'Current language in frontend' })
  @IsNotEmpty()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @Field({ description: 'The slug from frontend URL' })
  @IsNotEmpty()
  slug: string
}

@ObjectType()
export class SeoFooterContent {
  @Field()
  title: string

  @Field()
  content: string
}

@ObjectType()
export class GetSeoContentOutput {
  @Field(() => ID)
  id: string

  @Field({ nullable: true })
  header__en?: string | null

  @Field({ nullable: true })
  header__nl?: string | null

  @Field({ nullable: true })
  header__de?: string | null

  @Field({ nullable: true })
  header__pl?: string | null

  @Field(() => [SeoFooterContent], { nullable: true })
  footer__en?: InstanceType<typeof SeoFooterContent>[] | null

  @Field(() => [SeoFooterContent], { nullable: true })
  footer__nl?: InstanceType<typeof SeoFooterContent>[] | null

  @Field(() => [SeoFooterContent], { nullable: true })
  footer__de?: InstanceType<typeof SeoFooterContent>[] | null

  @Field(() => [SeoFooterContent], { nullable: true })
  footer__pl?: InstanceType<typeof SeoFooterContent>[] | null

  @Field()
  name__en: string

  @Field()
  name__nl: string

  @Field()
  name__de: string

  @Field()
  name__pl: string
}
