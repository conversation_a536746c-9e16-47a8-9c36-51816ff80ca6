import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { OrganizationBankAccountEntity } from '~/entities'

export class CrudOrganizationBankAccountService extends TypeOrmCrudService<OrganizationBankAccountEntity> {
  constructor(@InjectRepository(OrganizationBankAccountEntity) repo: Repository<OrganizationBankAccountEntity>) {
    super(repo)
  }
}
