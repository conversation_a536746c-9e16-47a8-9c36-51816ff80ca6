import { capitalize } from 'lodash'

export const formatSelectOptionsFromEnum = (
  enumObject: Record<string, string | number>,
  transform: { capitalize?: boolean; upperCase?: boolean; lowerCase?: boolean } = {
    capitalize: true,
    upperCase: false,
    lowerCase: false,
  }
) =>
  Object.values(enumObject).map((value) => {
    let label = String(value).replace(/_/g, ' ')
    if (transform.upperCase) {
      label = label.toUpperCase()
    } else if (transform.lowerCase) {
      label = label.toLowerCase()
    }
    if (transform.capitalize) {
      label = capitalize(label)
    }
    return { label, value }
  })

export const formatMoney = (amount: number, currencyCode = 'EUR', locale = 'en-NL') =>
  new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount)

export const formatCurrencySymbol = (currencyCode: string = 'EUR', locale = 'en-NL') =>
  new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })
    .format(0)
    .replace(/\d/g, '')
    .trim()

export const formatDate = (date?: string | Date, locale = 'en-NL') => {
  if (!date) return ''
  return new Date(date).toLocaleDateString(locale, {
    month: 'short',
    day: '2-digit',
    year: 'numeric',
  })
}
