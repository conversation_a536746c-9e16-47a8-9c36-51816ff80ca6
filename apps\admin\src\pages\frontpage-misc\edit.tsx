import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps } from '@refinedev/core'
import type { IFrontpageMisc } from '@valyuu/api/entities'
import { Form } from 'antd'
import { InputNumber } from 'antx'
import { type FC } from 'react'

export const FrontpageMiscEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps, formLoading } = useForm<IFrontpageMisc, HttpError, IFrontpageMisc>({
    action: 'edit',
    resource: 'admin/frontpage-misc',
    id: 'ID',
  })

  return (
    <Edit isLoading={formLoading} saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <InputNumber
          label="Total members"
          precision={0}
          min={0}
          name="totalMembers"
          rules={['required']}
          selfClass="w-full"
        />
        <InputNumber
          label="Total saved CO2"
          min={0}
          name="totalSavedCo2"
          rules={['required']}
          suffix="ton"
          selfClass="w-full"
        />
        <InputNumber
          label="Total saved CO2"
          min={0}
          name="totalSavedCo2Km"
          rules={['required']}
          suffix="KM"
          selfClass="w-full"
        />
        <InputNumber
          label="Total saved e-waste"
          min={0}
          name="totalSavedEwaste"
          rules={['required']}
          suffix="ton"
          selfClass="w-full"
        />
        <InputNumber
          label="Total saved e-waste"
          min={0}
          name="totalSavedEwasteElephant"
          rules={['required']}
          suffix="🐘s equivalent"
          selfClass="w-full"
        />
        <InputNumber label="Total donation" min={0} name="totalDonation" rules={['required']} selfClass="w-full" />
      </Form>
    </Edit>
  )
}
