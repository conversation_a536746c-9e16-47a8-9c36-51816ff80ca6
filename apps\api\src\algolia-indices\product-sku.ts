import * as Sentry from '@sentry/nestjs'
import { plainToClass } from 'class-transformer'
import { pick } from 'lodash'
import * as pMap from 'p-map'
import type { ConditionalExcept } from 'type-fest'
import { type EntityManager, IsNull, Not } from 'typeorm'

import { ProductModelIndex } from '~/algolia-indices'
import { envConfig } from '~/configs'
import { ProductSkuColor, ProductSkuGrading, ProductSkuSourceType } from '~/constants'
import { ProductModelAttributeCombinationEntity, ProductSkuEntity } from '~/entities'
import { AlgoliaIndex } from '~/utils'

export type ProductSkuIndexPrice = {
  price: number
  originalPrice: number
  brandNewPrice: number
  isDeal: boolean
}

export type ProductSkuIndexCrudArgs = {
  id: string
  manager?: EntityManager
}

export class ProductSkuIndex extends AlgoliaIndex {
  static indexName = envConfig.ALGOLIA_INDEX_PREFIX + 'product_sku'
  static entity = ProductSkuEntity

  objectID: string

  name__en: string

  name__nl: string

  name__de: string

  slug__en: string

  slug__nl: string

  slug__de: string

  slugNumber__en: string

  slugNumber__nl: string

  slugNumber__de: string

  brandId: string

  brandName__en: string

  brandName__nl: string

  brandName__de: string

  brandSlug__en: string

  brandSlug__nl: string

  brandSlug__de: string

  categoryId: string

  categoryName__en: string

  categoryName__nl: string

  categoryName__de: string

  categorySlug__en: string

  categorySlug__nl: string

  categorySlug__de: string

  seriesId: string

  seriesName__en: string

  seriesName__nl: string

  seriesName__de: string

  seriesSlug__en: string

  seriesSlug__nl: string

  modelId: string

  modelName__en: string

  modelName__nl: string

  modelName__de: string

  modelSlug__en: string

  modelSlug__nl: string

  modelSlug__de: string

  variantId: string

  variantName__en: string

  variantName__nl: string

  variantName__de: string

  variantSlug__en: string

  variantSlug__nl: string

  variantSlug__de: string

  imagePublicId: string

  imageUrl: string

  releaseDate: string

  releaseDateTimestamp: number

  color: ProductSkuColor

  grading: ProductSkuGrading

  attributes: {
    value__en: string
    value__nl: string
    value__de: string
  }[]

  // TODO: listen to price changes
  prices: Record<string, ProductSkuIndexPrice>

  collections: { slug__en: string; slug__nl: string; slug__de: string; id: string }[]

  recentOrdersCount: number = 0

  isBestseller: boolean = false

  isMvp: boolean = false

  isC2c: boolean

  static async getObject(objectID: string): Promise<ProductSkuIndex> {
    return super.getObject(objectID)
  }

  static async getObjects(objectIDs: string[]): Promise<ProductSkuIndex[]> {
    return super.getObjects(objectIDs)
  }

  static async partialUpdateObject(object: Partial<ProductSkuIndex['_model']>) {
    return super.partialUpdateObject(object)
  }

  static async partialUpdateObjects(objects: Partial<ProductSkuIndex['_model']>[]) {
    return super.partialUpdateObjects(objects)
  }

  static async fromEntity({
    id,
    manager = ProductSkuEntity.getRepository().manager,
  }: {
    id: string
    manager?: EntityManager
  }): Promise<Partial<ProductSkuIndex>> {
    if (!id) {
      throw new Error(`ProductSkuIndex.fromEntity: id is required: ${id}`)
    }
    const sku = await manager.findOneOrFail(ProductSkuEntity, {
      where: { id },
      relations: {
        variant: true,
        category: true,
        brand: true,
        model: {
          series: true,
        },
        modelMetrics: true,
        heroImage: true,
        prices: true,
        marketingSkuCollections: true,
      },
      select: {
        id: true,
        name__en: true,
        name__nl: true,
        name__de: true,
        slug__en: true,
        slug__nl: true,
        slug__de: true,
        slugNumber__en: true,
        slugNumber__nl: true,
        slugNumber__de: true,
        modelId: true,
        color: true,
        displayColor__en: true,
        displayColor__nl: true,
        displayColor__de: true,
        grading: true,
        prices: true,
        source: true,
        excludeFromBestseller: true,
        excludeFromMvp: true,
        marketingSkuCollections: {
          id: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
        },
        extraAttributes: true,
        extraTestedItems: true,
        variant: {
          id: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
        },
        variantId: true,
        brand: {
          id: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
        },
        category: {
          id: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
        },
        model: {
          id: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          releaseDate: true,
          series: {
            id: true,
            name__en: true,
            name__nl: true,
            name__de: true,
            slug__en: true,
            slug__nl: true,
            slug__de: true,
          },
        },
        modelMetrics: {
          id: true,
          isMvp: true,
          isBestseller: true,
          recentOrdersCount: true,
        },
        heroImage: {
          publicId: true,
          url: true,
        },
      },
    })

    const attributeCombination = await manager.findOne(ProductModelAttributeCombinationEntity, {
      where: { variantId: sku.variantId },
      relations: {
        choices: {
          option: true,
        },
      },
      select: {
        id: true,
        choices: {
          sortOrder: true,
          option: {
            name__en: true,
            name__nl: true,
            name__de: true,
          },
        },
      },
      order: {
        choices: {
          sortOrder: 'ASC',
        },
      },
    })
    if (!attributeCombination) {
      Sentry.captureException(new Error('Attribute combination not found'), {
        tags: {
          type: 'product-sku:attribute-combination-not-found',
        },
        extra: {
          skuId: sku.id,
        },
      })
    }
    return plainToClass(ProductSkuIndex, {
      ...pick(sku, [
        'name__en',
        'name__nl',
        'name__de',
        'slug__en',
        'slug__nl',
        'slug__de',
        'slugNumber__en',
        'slugNumber__nl',
        'slugNumber__de',
        'displayColor__en',
        'displayColor__nl',
        'displayColor__de',
      ]),
      isC2c: sku.source === ProductSkuSourceType.C2C,
      objectID: sku.id,
      modelId: sku.modelId,
      modelName__en: sku.model.name__en,
      modelName__nl: sku.model.name__nl,
      modelName__de: sku.model.name__de,
      modelSlug__en: sku.model.slug__en,
      modelSlug__nl: sku.model.slug__nl,
      modelSlug__de: sku.model.slug__de,
      variantId: sku.variantId,
      variantName__en: sku.variant.name__en,
      variantName__nl: sku.variant.name__nl,
      variantName__de: sku.variant.name__de,
      variantSlug__en: sku.variant.slug__en,
      variantSlug__nl: sku.variant.slug__nl,
      variantSlug__de: sku.variant.slug__de,
      brandId: sku.brand.id,
      brandName__en: sku.brand.name__en,
      brandName__nl: sku.brand.name__nl,
      brandName__de: sku.brand.name__de,
      brandSlug__en: sku.brand.slug__en,
      brandSlug__nl: sku.brand.slug__nl,
      brandSlug__de: sku.brand.slug__de,
      category_id: sku.category.id,
      categoryName__en: sku.category.name__en,
      categoryName__nl: sku.category.name__nl,
      categoryName__de: sku.category.name__de,
      categorySlug__en: sku.category.slug__en,
      categorySlug__nl: sku.category.slug__nl,
      categorySlug__de: sku.category.slug__de,
      seriesId: sku.model.series.id,
      seriesName__en: sku.model.series.name__en,
      seriesName__nl: sku.model.series.name__nl,
      seriesName__de: sku.model.series.name__de,
      seriesSlug__en: sku.model.series.slug__en,
      seriesSlug__nl: sku.model.series.slug__nl,
      seriesSlug__de: sku.model.series.slug__de,
      isBestseller: sku.excludeFromBestseller ? false : sku.modelMetrics.isBestseller,
      isMvp: sku.excludeFromMvp ? false : sku.modelMetrics.isMvp,
      recentOrdersCount: sku.modelMetrics.recentOrdersCount,
      imagePublicId: sku.heroImage.publicId,
      imageUrl: sku.heroImage.url,
      releaseDate: sku.model.releaseDate,
      releaseDateTimestamp: new Date(sku.model.releaseDate).getTime(),
      color: sku.color,
      grading: sku.grading,
      prices: sku.prices.reduce(
        (acc, price) => {
          acc[price.channelId] = pick(price, ['price', 'originalPrice', 'brandNewPrice', 'isDeal'])
          return acc
        },
        {} as Record<string, ProductSkuIndexPrice>
      ),
      collections: sku.marketingSkuCollections.map(({ id, slug__en, slug__nl, slug__de }) => ({
        id,
        slug__en,
        slug__nl,
        slug__de,
      })),
      attributes: [
        { name__en: sku.displayColor__en, name__nl: sku.displayColor__nl, name__de: sku.displayColor__de },
        ...(sku.extraAttributes ?? []).map((field) => pick(field, ['name__en', 'name__nl', 'name__de'])),
        ...(attributeCombination?.choices ?? []).map((choice) =>
          pick(choice.option, ['name__en', 'name__nl', 'name__de'])
        ),
      ],
    })
  }
  static async addIndex({ id, manager = ProductSkuEntity.getRepository().manager }: ProductSkuIndexCrudArgs) {
    const indexSku = await ProductSkuIndex.fromEntity({ id, manager })
    if (!indexSku) {
      throw new Error(`ProductSkuIndex.addIndex: sku not found: ${id}`)
    }
    await indexSku.save()
    const indexModel = await ProductModelIndex.getObject(indexSku.modelId)
    if (!indexModel) {
      throw new Error(`ProductSkuIndex.addIndex: model not found: ${indexSku.modelId}`)
    }
    const skuCount = await ProductModelIndex.getSkuCount({ modelId: indexSku.modelId, manager })
    const skuPricesFrom = await ProductModelIndex.getSkuPriceFrom({ modelId: indexSku.modelId, manager })
    await ProductModelIndex.partialUpdateObject({ objectID: indexSku.modelId, skuCount, skuPricesFrom })
    return indexSku
  }
  static async updateIndex({ id, manager = ProductSkuEntity.getRepository().manager }: ProductSkuIndexCrudArgs) {
    const indexSku = await ProductSkuIndex.fromEntity({
      id,
      manager,
    })
    await indexSku.save()
  }
  static async deleteIndex({ id, manager = ProductSkuEntity.getRepository().manager }: ProductSkuIndexCrudArgs) {
    const indexSku = await ProductSkuIndex.getObject(id)
    if (!indexSku) {
      return
    }
    await ProductSkuIndex.deleteObject(id)
    const indexModel = await ProductModelIndex.getObject(indexSku.modelId)
    if (!indexModel) {
      throw new Error(`ProductModelIndex.addIndex: model not found: ${indexSku.modelId}`)
    }
    const skuCount = await ProductModelIndex.getSkuCount({ modelId: indexSku.modelId, manager })
    const skuPricesFrom = await ProductModelIndex.getSkuPriceFrom({ modelId: indexSku.modelId, manager })
    await ProductModelIndex.partialUpdateObject({ objectID: indexSku.modelId, skuCount, skuPricesFrom })
  }
  // TODO: update statistic fields
  static async rebuild(manager: EntityManager = ProductSkuEntity.getRepository().manager) {
    await this.init()
    await this.clearObjects()
    const skus = await manager.find(ProductSkuEntity, {
      where: { publishedAt: Not(IsNull()), sold: false },
      select: ['id'],
    })
    await pMap(
      skus,
      async ({ id }) => {
        await this.addIndex({ id, manager })
      },
      { concurrency: 10 }
    )
  }
}

export type IProductSkuIndex = Omit<ConditionalExcept<ProductSkuIndex, Function>, keyof AlgoliaIndex>
