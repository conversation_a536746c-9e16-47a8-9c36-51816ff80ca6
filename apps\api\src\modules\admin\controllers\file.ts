import { <PERSON><PERSON>, <PERSON>rudController } from '@dataui/crud'
import { Body, Controller, Post, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'

import { FileEntity } from '~/entities'
import { FileService } from '~admin/services'

import { AdminJwtAuthGuard } from '../guards'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: FileEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  routes: {
    // only support read operations
    only: ['getOneBase', 'getManyBase'],
  },
})
@Controller('admin/files')
export class FileController implements CrudController<FileEntity> {
  constructor(public service: FileService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(@UploadedFile() file: Express.Multer.File, @Body() { name }: { name: string }) {
    return this.service.uploadFile({ file, originalFilename: name })
  }
}
