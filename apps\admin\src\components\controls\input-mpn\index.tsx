import './index.css'

import { useApiUrl } from '@refinedev/core'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Input, InputProps, Popconfirm } from 'antd'
import { create } from 'antx'
import { useState } from 'react'
import { BsMagic } from 'react-icons/bs'

export type InputMpnProps = InputProps & {
  value?: string
  variantName?: string
  onChange?: (value: string) => void
  disabled?: boolean
}

export const InputMpn = create(({ value, variantName, onChange, disabled }: InputMpnProps) => {
  const [isGenerating, setIsGenerating] = useState(false)

  const apiUrl = useApiUrl()

  const hasVariantName = variantName?.trim()

  const handleGenerateClick = async () => {
    if (disabled) {
      return
    }

    if (hasVariantName) {
      const { data } = await axios.get(`${apiUrl}/admin/agi/generate-mpn`, {
        params: {
          'variant-name': variantName,
        },
      })

      try {
        setIsGenerating(true)
        if (data) {
          onChange?.(data)
        }
      } catch {
      } finally {
        setIsGenerating(false)
      }
    }
  }

  const addon = hasVariantName ? (
    value?.trim() ? (
      <Popconfirm
        key="generate"
        okText="Generate"
        cancelText="Cancel"
        okType="primary"
        title="Override the current content?"
        onConfirm={handleGenerateClick}
        className="addon"
        disabled={disabled || isGenerating}
      >
        <span title="Generate MPN by AI">
          <BsMagic />
        </span>
      </Popconfirm>
    ) : (
      <span title="Generate MPN by AI" onClick={handleGenerateClick} className="addon">
        <BsMagic />
      </span>
    )
  ) : null

  return (
    <Input
      value={value}
      onChange={({ target: { value } }) => onChange?.(value)}
      className="input-mpn-wrapper"
      addonAfter={addon}
      disabled={disabled}
    />
  )
})
