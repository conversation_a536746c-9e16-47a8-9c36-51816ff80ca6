import { FC } from 'react'

export type IPublishStatusProps = {
  value: string | null
}

export const PublishStatus: FC<IPublishStatusProps> = ({ value }) =>
  value ? (
    <span className="flex items-center gap-2">
      <span className="inline-block size-1.5 rounded-full bg-green-600"></span>
      <span>Published</span>
    </span>
  ) : (
    <span className="flex items-center gap-2">
      <span className="inline-block size-1.5 rounded-full bg-gray-300"></span>
      <span>Unpublished</span>
    </span>
  )
