import { Create, useForm, useSelect } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps, useApiUrl } from '@refinedev/core'
import type {
  IProductModel,
  IProductModelAttributeCombination,
  IProductModelAttributeCombinationChoice,
  IProductVariant,
} from '@valyuu/api/entities'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Form } from 'antd'
import { Input, InputNumber, Select, Watch } from 'antx'
import cx from 'clsx'
import { FC, useMemo, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { v4 as uuid } from 'uuid'

import { InputLanguageTab, InputMultiLang, InputPublish, InputSlug, LanguageTabChoices } from '~/components'

export const ProductVariantCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<IProductVariant, HttpError, IProductVariant>()
  const id = useMemo(() => uuid(), [])
  const publishedAt = Form.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })

  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)
  let initialModelId = searchParams.get('model-id')
  let initialAttributeCombinationId = searchParams.get('attribute-combination-id')

  const modelId = Form.useWatch('modelId', form)

  const apiUrl = useApiUrl()

  // TODO: add link from model page to variant create page with modelId and attributeCombinationId
  const handleValuesChange = async (changedValues: Partial<IProductVariant>, allValues: IProductVariant) => {
    const { modelId, attributeCombination } = allValues ?? {}
    if ((changedValues.modelId || changedValues.attributeCombination?.id) && modelId && attributeCombination?.id) {
      const { data: model } = await axios.get<IProductModel>(`admin/product-models/${modelId}`, {
        baseURL: apiUrl,
        params: {
          fields: 'id,name__en,name__nl,name__de',
        },
      })
      const { data: combination } = await axios.get<IProductModelAttributeCombination>(
        `admin/product-model-attribute-combinations/${attributeCombination.id}`,
        {
          baseURL: apiUrl,
          params: {
            join: ['choices', 'choices.option'],
            fields: 'id,choices,choices.option',
          },
        }
      )
      if (model && combination) {
        form.setFieldsValue({
          name__en:
            model.name__en +
            ' ' +
            combination.choices
              .map((choice: IProductModelAttributeCombinationChoice) => choice.option.name__en)
              .join(' '),
          name__nl:
            model.name__nl +
            ' ' +
            combination.choices
              .map((choice: IProductModelAttributeCombinationChoice) => choice.option.name__nl)
              .join(' '),
          name__de:
            model.name__de +
            ' ' +
            combination.choices
              .map((choice: IProductModelAttributeCombinationChoice) => choice.option.name__de)
              .join(' '),
        })
      }
    }
  }

  const { selectProps: productModelSelectProps } = useSelect<IProductModel>({
    resource: 'admin/product-models',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__nl', 'name__de'],
    },
    pagination: {
      mode: 'off',
    },
  })
  if (initialModelId && !productModelSelectProps?.options?.find((option) => option.value === initialModelId)) {
    initialModelId = null
  }
  const { selectProps: attributeCombinationSelectProps } = useSelect<IProductModelAttributeCombination>({
    resource: 'admin/product-model-attribute-combinations',
    optionLabel: ((value: IProductModelAttributeCombination) =>
      value?.choices
        ?.map((choice: IProductModelAttributeCombinationChoice) => choice.option.name__en)
        .join(' | ')) as any,
    filters: [
      { field: 'modelId', operator: 'eq', value: modelId },
      { field: 'allowVariant', operator: 'eq', value: true },
      { field: 'variantId', operator: 'null', value: true },
    ],
    meta: {
      fields: ['id', 'internalName', 'choices'],
      join: [
        {
          field: 'choices',
        },
        {
          field: 'choices.option',
        },
      ],
    },
    pagination: {
      mode: 'off',
    },
  })
  if (
    initialAttributeCombinationId &&
    !attributeCombinationSelectProps?.options?.find((option) => option.value === initialAttributeCombinationId)
  ) {
    initialAttributeCombinationId = null
  }

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
        onValuesChange={handleValuesChange}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Select
          label="Product model"
          name="modelId"
          rules={['required']}
          {...(productModelSelectProps as any)}
          onChange={() => {
            form.setFieldValue(['attributeCombination', 'id'], null)
          }}
          initialValue={initialModelId}
        />
        <Select
          label="Attribute combination"
          name={['attributeCombination', 'id']}
          rules={['required']}
          {...(attributeCombinationSelectProps as any)}
          initialValue={initialAttributeCombinationId}
        />
        <Watch list={['name__en', 'name__nl', 'name__de']}>
          {([name__en, name__nl, name__de]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
                autoFollow
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
                autoFollow
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
                autoFollow
              />
            </>
          )}
        </Watch>
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} initialValue={10} />
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
