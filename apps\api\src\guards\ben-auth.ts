import * as crypto from 'node:crypto'

import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { addDays, subDays } from 'date-fns'
import { Observable } from 'rxjs'

import { envConfig } from '~/configs'
@Injectable()
export class BenAuthG<PERSON> implements CanActivate {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest()

    try {
      const { signature } = request.body
      if (!signature) {
        return false
      }

      // Decode the base64-encoded signature
      const decoded = Buffer.from(signature, 'base64')

      const iv = decoded.subarray(0, 16) // The first 16 bytes are the IV
      const encrypted = decoded.subarray(16) // The rest is the encrypted timestamp

      // Create a decipher
      const decipher = crypto.createDecipheriv('aes-256-cbc', envConfig.BEN_API_SECRET, iv)
      let decrypted = decipher.update(encrypted)
      decrypted = Buffer.concat([decrypted, decipher.final()])
      const date = new Date(Number(decrypted.toString()) * 1000)
      const now = new Date()
      // If date is 3 days different from now, throw error
      if (now > addDays(date, 3) || now < subDays(date, 3)) {
        return false
      }
    } catch (error) {
      return false
    }

    return true
  }
}
