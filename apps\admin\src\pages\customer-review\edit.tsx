import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps } from '@refinedev/core'
import type { ICustomerReview } from '@valyuu/api/entities'
import { Form } from 'antd'
import { InputNumber } from 'antx'
import { type FC } from 'react'

export const CustomerReviewEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps, formLoading } = useForm<ICustomerReview, HttpError, ICustomerReview>({
    action: 'edit',
    resource: 'admin/customer-review',
    id: 'ID',
  })

  return (
    <Edit isLoading={formLoading} saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <InputNumber label="Google score" precision={1} min={0} name="googleScore" rules={['required']} />
        <InputNumber label="Trustpilot score" precision={1} min={0} name="trustpilotScore" rules={['required']} />
        <InputNumber
          label="Trustpilot reviews count"
          precision={0}
          min={0}
          name="trustpilotCount"
          rules={['required']}
        />
      </Form>
    </Edit>
  )
}
