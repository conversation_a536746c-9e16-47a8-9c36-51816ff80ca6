import { createReadStream } from 'node:fs'
import { stat, unlink } from 'node:fs/promises'
import { tmpdir } from 'node:os'
import { join } from 'node:path'

import axios from 'axios'
import { v2 as cloudinary } from 'cloudinary'
import { createWriteStream } from 'fs'
import * as mime from 'mime-types'
import ms from 'ms'
import { v4 as uuid } from 'uuid'

import { FileEntity, ImageEntity } from '~/entities'
import { cloudinaryUploadResrouce, s3GetUrl, s3UploadFile } from '~/utils'

import { cliOptions } from './args-parser'

const dummyCloudinaryPublicId = 'backend/migration/dummy/valyuu.png'

const dummyS3FilePath = 'migration/test-report/dummy.pdf'

const downloadFile = async (url: string, fileName: string = uuid()): Promise<string> => {
  const tempFilePath = join(tmpdir(), fileName)
  const writer = createWriteStream(tempFilePath)

  const response = await axios({
    url,
    method: 'GET',
    responseType: 'stream',
    timeout: ms('3 minutes'),
  })

  response.data.pipe(writer)

  return new Promise((resolve, reject) => {
    writer.on('finish', () => {
      resolve(tempFilePath)
    })

    writer.on('error', async (err) => {
      try {
        await unlink(tempFilePath)
      } catch {
        void 0
      }
      reject(err)
    })
  })
}

export const checkUrl = async (url: string) => {
  try {
    await axios.head(url)
  } catch {
    return false
  }
  return true
}

export const dummyS3File = (): Pick<FileEntity, 'path' | 'url' | 'size' | 'originalFilename' | 'mimeType'> => ({
  path: dummyS3FilePath,
  url: s3GetUrl(dummyS3FilePath),
  size: 69_990,
  originalFilename: 'dummy.pdf',
  mimeType: 'application/pdf',
})

export const uploadS3FileFromUrl = async (
  url: string,
  path: string
): Promise<Pick<FileEntity, 'path' | 'url' | 'size' | 'mimeType'>> => {
  if (cliOptions.dummyFiles) {
    return dummyS3File()
  }
  const localFilePath = await downloadFile(url)
  const { size } = await stat(localFilePath)
  await s3UploadFile(path, createReadStream(localFilePath))
  try {
    await unlink(localFilePath)
  } catch {
    void 0
  }
  return {
    path,
    url: s3GetUrl(path),
    size,
    mimeType: mime.lookup(url) || 'application/octet-stream',
  }
}

export const dummyCloudinaryImage = (): Pick<
  ImageEntity,
  'publicId' | 'url' | 'width' | 'height' | 'originalFilename' | 'format'
> => ({
  publicId: dummyCloudinaryPublicId,
  url: cloudinary.url(dummyCloudinaryPublicId, { secure: true }),
  width: 1200,
  height: 630,
  originalFilename: 'dummy.png',
  format: 'png',
})

export const uploadCloudinaryImageFromUrl = async (
  url: string,
  publicId: string
): Promise<Pick<ImageEntity, 'publicId' | 'url' | 'width' | 'height' | 'format'>> => {
  if (cliOptions.dummyFiles) {
    return dummyCloudinaryImage()
  }
  const res = await cloudinaryUploadResrouce(url, publicId)
  return {
    publicId: res.public_id,
    url: res.secure_url,
    width: res.width,
    height: res.height,
    format: res.format,
  }
}
