import { Create, useForm } from '@refinedev/antd'
import type { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { IQuestionExtraProblem } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, Watch } from 'antx'
import cx from 'clsx'
import { intersection, uniq } from 'lodash'
import { type FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputLanguageTab, InputMultiLang, LanguageTabChoices } from '~/components'

export const QuestionExtraProblemCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps } = useForm<IQuestionExtraProblem, HttpError, IQuestionExtraProblem>()
  const id = useMemo(() => uuid(), [])

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Input label="Internal name" name="internalName" rules={['required']} required />
        <Watch list={['template__en', 'template__nl', 'template__de', 'template__pl']}>
          {([template__en, template__nl, template__de, template__pl]) => {
            const variables__en = uniq<string>(template__en?.match(/\{\{([a-zA-Z0-9_]+)\}\}/g) || [])
            const variables__nl = uniq<string>(template__nl?.match(/\{\{([a-zA-Z0-9_]+)\}\}/g) || [])
            const variables__de = uniq<string>(template__de?.match(/\{\{([a-zA-Z0-9_]+)\}\}/g) || [])
            const variables__pl = uniq<string>(template__pl?.match(/\{\{([a-zA-Z0-9_]+)\}\}/g) || [])
            const allVariables = uniq([...variables__en, ...variables__nl, ...variables__de, ...variables__pl])
            const commonVariables = intersection(variables__en, variables__nl, variables__de, variables__pl)

            return (
              <>
                <InputMultiLang
                  element="textarea"
                  sources={{ en: template__en, nl: template__nl, de: template__de, pl: template__pl }}
                  targetLang="en"
                  label="Template (English)"
                  name="template__en"
                  rules={['required']}
                  className={clsHideEn}
                  fillType="translate"
                  help="Use {{variable}} to insert a variable"
                />
                <InputMultiLang
                  element="textarea"
                  sources={{ en: template__en, nl: template__nl, de: template__de, pl: template__pl }}
                  targetLang="nl"
                  label="Template (Dutch)"
                  name="template__nl"
                  rules={['required']}
                  className={clsHideNl}
                  fillType="translate"
                  help="Use {{variable}} to insert a variable"
                />
                <InputMultiLang
                  element="textarea"
                  sources={{ en: template__en, nl: template__nl, de: template__de, pl: template__pl }}
                  targetLang="de"
                  label="Template (German)"
                  name="template__de"
                  rules={['required']}
                  className={clsHideDe}
                  fillType="translate"
                  help="Use {{variable}} to insert a variable"
                />
                <InputMultiLang
                  element="textarea"
                  sources={{ en: template__en, nl: template__nl, de: template__de, pl: template__pl }}
                  targetLang="pl"
                  label="Template (Polish)"
                  name="template__pl"
                  rules={['required']}
                  className={clsHidePl}
                  fillType="translate"
                  help="Use {{variable}} to insert a variable"
                />
                <Form.Item label="Variables" name="variables">
                  <div className="flex flex-wrap gap-2">
                    {allVariables.map((variable: string) => (
                      <span
                        key={variable}
                        className={`rounded px-2 py-1 ${
                          commonVariables.includes(variable) ? 'bg-lime-200' : 'bg-red-200'
                        }`}
                      >
                        {variable}
                      </span>
                    ))}
                  </div>
                </Form.Item>
              </>
            )
          }}
        </Watch>
      </Form>
    </Create>
  )
}
