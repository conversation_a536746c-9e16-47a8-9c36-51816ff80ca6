import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import { IsEmail, IsNotEmpty } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { UserFrom } from '~/constants'
import {
  AddressEntity,
  BankAccountEntity,
  EmailHistoryEntity,
  type IAddress,
  type IBankAccount,
  type ILocaleLanguage,
  type IOrder,
  LocaleLanguageEntity,
  OrderEntity,
  PartnerEntity,
  SaleEntity,
} from '~/entities'

registerEnumType(UserFrom, { name: 'UserFrom' })

@ObjectType('User')
@Entity('user')
export class UserEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column({ unique: true })
  @IsEmail()
  @IsNotEmpty()
  email: string

  @Column({ nullable: true })
  password?: string

  @Column({ nullable: true })
  resetPasswordToken?: string

  @Column({ nullable: true })
  confirmationToken?: string

  @Field()
  @Column({ default: false })
  confirmed: boolean

  @Field()
  @Column({ default: false })
  blocked: boolean

  @Column({ default: 'VALYUU' })
  from: string

  @ManyToOne(() => PartnerEntity, { nullable: true })
  fromPartner: Relation<PartnerEntity>

  @Column({ nullable: true })
  @RelationId((user: UserEntity) => user.fromPartner)
  fromPartnerId: string

  @OneToMany(() => AddressEntity, (address) => address.user)
  addresses: Relation<AddressEntity>[]

  @OneToMany(() => BankAccountEntity, (bankAccount) => bankAccount.user)
  bankAccounts: Relation<BankAccountEntity>[]

  @Field(() => LocaleLanguageEntity)
  @ManyToOne(() => LocaleLanguageEntity)
  language: Relation<LocaleLanguageEntity>

  @Column()
  @RelationId((user: UserEntity) => user.language)
  languageId: string

  @OneToMany(() => OrderEntity, (order) => order.user)
  orders: Relation<OrderEntity>[]

  @OneToMany(() => SaleEntity, (sale) => sale.user)
  sales: Relation<SaleEntity>[]

  @Column({ nullable: true })
  stripeAccount?: string

  @Column({ nullable: true })
  stripeCustomer?: string

  @Column({ type: 'date', nullable: true })
  dateOfBirth?: Date | null

  @OneToMany(() => EmailHistoryEntity, (emailHistory) => emailHistory.user, { nullable: true })
  emailHistories: Relation<EmailHistoryEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IUser = Omit<UserEntity, keyof BaseEntity> & {
  addresses: IAddress[]
  bankAccounts: IBankAccount[]
  language: ILocaleLanguage
  orders: IOrder[]
}
