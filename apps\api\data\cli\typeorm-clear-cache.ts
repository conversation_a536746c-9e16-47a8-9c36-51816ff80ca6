import { DataSource } from 'typeorm'

import { typeOrmConfig } from '~/configs'

console.info('Clearing TypeORM query result cache...')

new DataSource({
  ...typeOrmConfig,
  dropSchema: false,
  synchronize: false,
  logging: false,
})
  .initialize()
  .then(async (dataSource) => {
    await dataSource.queryResultCache.clear()
    console.info('TypeORM query result cache cleared')
    process.exit(0)
  })
