import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  BrandEntity,
  CategoryEntity,
  type IBrand,
  type ICategory,
  type IProductModel,
  type ITestedItemList,
  ProductModelEntity,
  TestedItemListEntity,
} from '~/entities'

@ObjectType('ProductSeries')
@Entity('product_series')
export class ProductSeriesEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column({ unique: true })
  slug__en: string

  @Field()
  @Column({ unique: true })
  slug__nl: string

  @Field()
  @Column({ unique: true })
  slug__de: string

  @Field()
  @Column({ unique: true })
  slug__pl: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @ManyToOne(() => BrandEntity, (brand) => brand.productSeries, {
    nullable: false,
  })
  brand: Relation<BrandEntity>

  @Column()
  @RelationId((productSeries: ProductSeriesEntity) => productSeries.brand)
  brandId: string

  @ManyToOne(() => CategoryEntity, (category) => category.productSeries, {
    nullable: false,
  })
  category: Relation<CategoryEntity>

  @Column()
  @RelationId((productSeries: ProductSeriesEntity) => productSeries.category)
  categoryId: string

  @OneToMany(() => ProductModelEntity, (productModel) => productModel.series, { nullable: false })
  models: Relation<ProductModelEntity>[]

  // TODO: change it to testedItemList
  @ManyToOne(() => TestedItemListEntity, (testComponentList) => testComponentList.productSeries, { nullable: false })
  testComponentList: Relation<TestedItemListEntity>

  @Column()
  @RelationId((productSeries: ProductSeriesEntity) => productSeries.testComponentList)
  testComponentListId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IProductSeries = Omit<ProductSeriesEntity, keyof BaseEntity> & {
  models: IProductModel[]
  brand: IBrand
  category: ICategory
  testComponentList: ITestedItemList
}
