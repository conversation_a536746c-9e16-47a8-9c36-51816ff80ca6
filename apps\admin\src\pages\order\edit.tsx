import { Edit, EditButton, useDrawerForm, useForm, useSelect, useTable } from '@refinedev/antd'
import {
  type HttpError,
  type IResourceComponentsProps,
  useApiUrl,
  useCustomMutation,
  useInvalidate,
  useList,
  useNavigation,
  useNotification,
} from '@refinedev/core'
import { OrderSkuItemStatus, OrderStatus, UserFrom } from '@valyuu/api/constants'
import type { ILocaleCountry, IOrder, IOrderAccessoryProductItem, IOrderSkuItem, IWarranty } from '@valyuu/api/entities'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Button, Descriptions, Drawer, Form, Modal, Space, Table, Tabs, Typography } from 'antd'
import { Input, InputNumber, RadioGroup, Select, TextArea } from 'antx'
import { capitalize } from 'lodash'
import { type FC, useState } from 'react'
import { AiOutlineMail, AiOutlinePlus } from 'react-icons/ai'

import { BlockEmailHistory, ButtonCopy, FormItemStatic, LabelWithDetails, LinkShipmentDocument } from '~/components'
import {
  formatMoney,
  formatSelectOptionsFromEnum,
  getAddressText,
  formatCurrencySymbol,
  getSendCloudParcelUrl,
  handleTableRowClick,
} from '~/utils'

enum PageTab {
  ORDER = 'order',
  SKU_ITEMS = 'sku-items',
  ACCESSORY_ITEMS = 'accessory-items',
  SHIPMENT = 'shipment',
  BILLING_ADDRESS = 'billing-address',
  EMAIL_HISTORY = 'email-history', // Add new tab enum
}

export const OrderEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, query, formLoading } = useForm<IOrder, HttpError, IOrder>({
    meta: {
      join: [
        {
          field: 'user',
          select: ['email'],
        },
        {
          field: 'shippingAddress',
        },
        {
          field: 'billingAddress',
        },
        {
          field: 'shipment',
        },
        {
          field: 'shipment.shippingMethod',
          select: ['shippingProductName'],
        },
      ],
    },
  })

  const { data: { data: countries } = {} } = useList<ILocaleCountry>({
    resource: 'admin/countries',
    meta: { fields: ['id', 'name__en'] },
    pagination: { mode: 'off' },
  })

  const { selectProps: warrantySelectProps } = useSelect<IWarranty>({
    resource: 'admin/warranties',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const record = query?.data?.data
  const { shippingAddress, billingAddress } = record ?? {}

  const { tableProps: skuItemsTableProps } = useTable<IOrderSkuItem>({
    syncWithLocation: false,
    resource: 'admin/order-sku-items',
    filters: { permanent: [{ field: 'orderId', operator: 'eq', value: id }] },
    meta: {
      fields: ['unitPrice', 'currencyId', 'status', 'warrantyPrice', 'note'],
      join: [
        {
          field: 'productSku',
          select: ['name__en'],
        },
        {
          field: 'warranty',
          select: ['name__en'],
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { tableProps: accessoryProductItemTableProps } = useTable<IOrderAccessoryProductItem>({
    syncWithLocation: false,
    resource: 'admin/order-accessory-product-items',
    filters: { permanent: [{ field: 'orderId', operator: 'eq', value: id }] },
    meta: {
      fields: ['unitPrice', 'quantity', 'currencyId', 'note'],
      join: [
        {
          field: 'accessoryProduct',
          select: ['name__en'],
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl } = useNavigation()
  const apiUrl = useApiUrl()
  const { open } = useNotification()

  const resendOrderEmail = async () => {
    try {
      await axios.post(`${apiUrl}/admin/emails/resend/buyer-order`, { id })
      open?.({
        key: 'resend-email-buyer-order',
        type: 'success',
        message: 'Email sent',
      })
    } catch {
      open?.({
        key: 'resend-email-buyer-order',
        type: 'error',
        message: 'Failed to send email',
      })
    }
  }

  const [activeTab, setActiveTab] = useState<PageTab>(PageTab.ORDER)

  const {
    formProps: skuItemFormProps,
    drawerProps: skuItemDrawerProps,
    show: skuItemShow,
    saveButtonProps: skuItemSaveButtonProps,
    id: skuItemId,
    query: skuItemQueryResult,
    formLoading: skuItemFormLoading,
  } = useDrawerForm<IOrderSkuItem, HttpError, IOrderSkuItem>({
    action: 'edit',
    meta: {
      join: [
        {
          field: 'productSku',
          select: ['name__en'],
        },
      ],
    },
    resource: 'admin/order-sku-items',
  })

  const skuItemRecord = skuItemQueryResult?.data?.data

  const {
    formProps: accessoryItemFormProps,
    drawerProps: accessoryItemDrawerProps,
    show: accessoryItemShow,
    saveButtonProps: accessoryItemSaveButtonProps,
    id: accessoryItemId,
    query: accessoryItemQueryResult,
    formLoading: accessoryItemFormLoading,
  } = useDrawerForm<IOrderAccessoryProductItem, HttpError, IOrderAccessoryProductItem>({
    action: 'edit',
    meta: {
      join: [
        {
          field: 'accessoryProduct',
          select: ['name__en'],
        },
      ],
    },
    resource: 'admin/order-accessory-product-items',
  })

  const accessoryItemRecord = accessoryItemQueryResult?.data?.data

  const { mutate: createShipment, isLoading: isCreatingShipment } = useCustomMutation<{ override?: boolean }>()
  const invalidate = useInvalidate()

  const handleCreateShipment = (override: boolean) => {
    createShipment(
      {
        method: 'post',
        url: `${apiUrl}/admin/orders/${id}/create-shipment`,
        values: { override },
        successNotification: {
          message: `Shipment ${override ? 'recreate' : 'created'} successfully`,
          type: 'success',
        },
        errorNotification: {
          message: `Failed to ${override ? 'recreate' : 'create'} shipment`,
          type: 'error',
        },
      },
      {
        onSuccess: () => {
          invalidate({
            resource: 'admin/orders',
            invalidates: ['detail'],
            id,
          })
          query?.refetch()
        },
      }
    )
  }

  const handleRecreateShipmentClick = () => {
    Modal.confirm({
      title: 'Recreate Shipment',
      content: (
        <div className="space-y-2">
          <p>This action will:</p>
          <ul className="list-disc pl-4">
            <li>Cancel the existing parcel</li>
            <li>Create a new shipping label</li>
            <li>Send a new order confirmation email</li>
          </ul>
          <p className="font-bold text-red-500">Please be extra careful with this action.</p>
        </div>
      ),
      okText: 'Yes, Recreate',
      cancelText: 'Cancel',
      okButtonProps: {
        danger: true,
      },
      onOk: () => handleCreateShipment(true),
    })
  }

  const handleResendEmailClick = () => {
    Modal.confirm({
      title: 'Resend Order Email',
      content: (
        <div className="space-y-2">
          <p>This will send a new copy of the order confirmation email to the customer.</p>
          <p>The customer will receive the same email content again.</p>
        </div>
      ),
      okText: 'Yes, Send Email',
      cancelText: 'Cancel',
      onOk: resendOrderEmail,
    })
  }

  return (
    <>
      <Edit
        isLoading={formLoading}
        saveButtonProps={{ ...saveButtonProps, disabled: activeTab !== PageTab.ORDER }}
        headerProps={{
          subTitle: (
            <>
              ID: <Typography.Text copyable>{id}</Typography.Text>
            </>
          ),
        }}
        headerButtons={({ defaultButtons }) => (
          <Space>
            {defaultButtons}
            <Button
              disabled={!id || formLoading}
              icon={
                <span className="anticon">
                  <AiOutlineMail />
                </span>
              }
              onClick={handleResendEmailClick}
            >
              Resend order email
            </Button>
          </Space>
        )}
      >
        <Tabs
          activeKey={activeTab}
          className="select-none"
          onChange={(key) => setActiveTab(key as PageTab)}
          items={[
            {
              key: PageTab.ORDER,
              label: 'Order',
              children: (
                <Form
                  {...formProps}
                  onFinishFailed={(errorInfo) => {
                    setActiveTab(PageTab.ORDER)
                    formProps.onFinishFailed?.(errorInfo)
                  }}
                  layout="vertical"
                >
                  <Input label="Order number" name="orderNumber" rules={['required']} disabled />
                  <InputNumber
                    label="Total"
                    name="total"
                    prefix={record?.currencyId ? formatCurrencySymbol(record.currencyId) : undefined}
                    precision={2}
                    min={0}
                    controls={false}
                    rules={['required']}
                    required
                    disabled
                  />
                  <Select
                    label="Status"
                    name="status"
                    rules={['required']}
                    options={formatSelectOptionsFromEnum(OrderStatus)}
                  />
                  <Input
                    label={
                      <LabelWithDetails
                        label="Stripe charge"
                        link={
                          record?.stripeCharge
                            ? `https://dashboard.stripe.com/charges/${record.stripeCharge}`
                            : undefined
                        }
                        target="_blank"
                      />
                    }
                    name="stripeCharge"
                    disabled
                  />
                  <Input label="Tracking number" name="trackingNumber" />
                  <Input label="Parcel ID" name="parcelId" />
                  <TextArea label="Note" name="note" className="col-span-2" />
                  <RadioGroup
                    label="To pickup point"
                    name="toPickupPoint"
                    options={[
                      { label: 'Yes', value: true },
                      { label: 'No', value: false },
                    ]}
                    rules={['required']}
                  />
                  <RadioGroup
                    label="Is returning customer"
                    name="isReturningCustomer"
                    options={[
                      { label: 'Yes', value: true },
                      { label: 'No', value: false },
                    ]}
                    rules={['required']}
                    disabled
                  />
                  <Select
                    label="From"
                    name="from"
                    rules={['required']}
                    options={formatSelectOptionsFromEnum(UserFrom)}
                  />
                  <Input
                    label={
                      <LabelWithDetails
                        label="User"
                        link={record?.userId ? editUrl('admin/users', record.userId) : undefined}
                      />
                    }
                    value={record?.user?.email}
                    disabled
                  />
                </Form>
              ),
            },
            {
              key: PageTab.SKU_ITEMS,
              label: 'SKU Items',
              disabled: !skuItemsTableProps?.dataSource?.length,
              children: (
                <Table
                  {...skuItemsTableProps}
                  rowKey="id"
                  onRow={({ id }) => handleTableRowClick(() => skuItemShow(id))}
                >
                  <Table.Column
                    dataIndex="id"
                    title="ID"
                    className="max-w-10 cursor-pointer"
                    render={(value) => <span title={value}>{value}</span>}
                    width="5rem"
                    ellipsis={true}
                  />
                  <Table.Column
                    dataIndex="productSku"
                    title="Product SKU"
                    className="cursor-pointer"
                    render={(value) => value.name__en}
                  />
                  <Table.Column
                    dataIndex="status"
                    title="Status"
                    className="cursor-pointer"
                    render={(value) => capitalize(value)}
                    width="10rem"
                  />
                  <Table.Column
                    dataIndex="unitPrice"
                    title="Unit price"
                    className="cursor-pointer"
                    render={(value, row: IOrderSkuItem) => formatMoney(value, row.currencyId)}
                    width="8rem"
                  />
                  <Table.Column
                    dataIndex="warranty"
                    title="Warranty"
                    className="cursor-pointer"
                    render={(value) => value.name__en}
                    width="10rem"
                  />
                  <Table.Column
                    dataIndex="warrantyPrice"
                    title="Warranty price"
                    className="cursor-pointer"
                    render={(value, row: IOrderSkuItem) => (value ? formatMoney(value, row.currencyId) : 'Free')}
                    width="8rem"
                  />
                  <Table.Column
                    title="Sub total"
                    className="cursor-pointer"
                    width="10rem"
                    render={(_, row: IOrderSkuItem) => formatMoney(row.unitPrice + row.warrantyPrice, row.currencyId)}
                  />
                  <Table.Column dataIndex="note" title="Note" className="cursor-pointer" width="20rem" />
                  <Table.Column<IOrder>
                    title="Actions"
                    dataIndex="actions"
                    render={(_, record) => (
                      <EditButton
                        size="small"
                        resource="admin/order-sku-items"
                        recordItemId={record.id}
                        onClick={() => skuItemShow(record.id)}
                      />
                    )}
                    width="6rem"
                    fixed="right"
                  />
                </Table>
              ),
            },
            {
              key: PageTab.ACCESSORY_ITEMS,
              label: 'Accessory Items',
              disabled: !accessoryProductItemTableProps?.dataSource?.length,
              children: (
                <Table
                  {...accessoryProductItemTableProps}
                  rowKey="id"
                  onRow={({ id }) => handleTableRowClick(() => accessoryItemShow(id))}
                >
                  <Table.Column
                    dataIndex="id"
                    title="ID"
                    className="max-w-10 cursor-pointer"
                    render={(value) => <span title={value}>{value}</span>}
                    width="5rem"
                    ellipsis={true}
                  />
                  <Table.Column
                    dataIndex="accessoryProduct"
                    title="Accessory product"
                    className="cursor-pointer"
                    render={(value) => value.name__en}
                  />
                  <Table.Column
                    dataIndex="unitPrice"
                    title="Unit price"
                    className="cursor-pointer"
                    render={(value, row: IOrderAccessoryProductItem) => formatMoney(value, row.currencyId)}
                    width="8rem"
                  />
                  <Table.Column dataIndex="quantity" title="Quantity" className="cursor-pointer" width="8rem" />
                  <Table.Column
                    title="Sub total"
                    className="cursor-pointer"
                    width="10rem"
                    render={(_, row: IOrderAccessoryProductItem) =>
                      formatMoney(row.unitPrice * row.quantity, row.currencyId)
                    }
                  />
                  <Table.Column dataIndex="note" title="Note" className="cursor-pointer" width="20rem" />
                  <Table.Column<IOrder>
                    title="Actions"
                    dataIndex="actions"
                    render={(_, record) => (
                      <EditButton
                        size="small"
                        resource="admin/order-accessory-product-items"
                        recordItemId={record.id}
                        onClick={() => accessoryItemShow(record.id)}
                      />
                    )}
                    width="6rem"
                    fixed="right"
                  />
                </Table>
              ),
            },
            {
              key: PageTab.SHIPMENT,
              label: 'Shipment',
              children: (
                <>
                  {shippingAddress ? (
                    <Descriptions
                      title="Shipping address"
                      extra={
                        <Space>
                          <ButtonCopy text={getAddressText(shippingAddress, countries)} label="Copy address" />
                          <EditButton resource="admin/addresses" recordItemId={shippingAddress.id} />
                        </Space>
                      }
                      bordered
                    >
                      <Descriptions.Item label="First name">{shippingAddress.firstName}</Descriptions.Item>
                      <Descriptions.Item label="Last name">{shippingAddress.lastName}</Descriptions.Item>
                      <Descriptions.Item label="Phone">
                        {shippingAddress.phoneAreaCode + ' ' + shippingAddress.phoneNumber}
                      </Descriptions.Item>
                      <Descriptions.Item label="Street">{shippingAddress.street}</Descriptions.Item>
                      <Descriptions.Item label="House number">{shippingAddress.houseNumber}</Descriptions.Item>
                      <Descriptions.Item label="Addition">{shippingAddress.addition}</Descriptions.Item>
                      <Descriptions.Item label="Postal code">{shippingAddress.postalCode}</Descriptions.Item>
                      <Descriptions.Item label="City">{shippingAddress.city}</Descriptions.Item>
                      <Descriptions.Item label="Country">
                        {countries
                          ? countries.find(({ id }) => id === shippingAddress.countryId)?.name__en
                          : shippingAddress.countryId}
                      </Descriptions.Item>
                    </Descriptions>
                  ) : null}
                  <Descriptions
                    title="Shipment information"
                    className="mt-4"
                    extra={
                      record?.shipment ? (
                        <Space>
                          <Button danger loading={isCreatingShipment} onClick={handleRecreateShipmentClick}>
                            <span className="anticon">
                              <AiOutlinePlus />
                            </span>
                            Recreate shipment
                          </Button>
                          <EditButton resource="admin/shipments" recordItemId={record.shipment.id} />
                        </Space>
                      ) : (
                        <Button type="primary" onClick={() => handleCreateShipment(false)} loading={isCreatingShipment}>
                          <span className="anticon">
                            <AiOutlinePlus />
                          </span>
                          Create shipment
                        </Button>
                      )
                    }
                    bordered={record?.shipment}
                    labelStyle={{ color: record?.shipment ? undefined : '#ff4d4f' }}
                  >
                    {!record?.shipment ? (
                      <Descriptions.Item label="Error">
                        <Typography.Text type="danger">
                          No shipment record found. This could be due to an invalid shipping address or a system error.
                          Please try to create a new shipment. If the problem persists, please contact the development
                          team.
                        </Typography.Text>
                      </Descriptions.Item>
                    ) : (
                      <>
                        <Descriptions.Item label="Tracking number">
                          <Typography.Text copyable={{ text: record?.shipment?.trackingUrl }}>
                            {record?.shipment?.trackingUrl ? (
                              <a title="View tracking page" href={record.shipment.trackingUrl} target="_blank">
                                {record?.shipment.trackingNumber}
                              </a>
                            ) : (
                              (record?.shipment?.trackingNumber ?? '')
                            )}
                          </Typography.Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="Parcel ID">
                          <Typography.Text copyable={{ text: record?.shipment?.parcelId }}>
                            {record?.shipment?.parcelId ? (
                              <a
                                title="View parcel details on SendCloud"
                                href={getSendCloudParcelUrl(record.shipment.parcelId)}
                                target="_blank"
                              >
                                {record?.shipment?.parcelId}
                              </a>
                            ) : (
                              ''
                            )}
                          </Typography.Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="Status">{record?.shipment?.statusMessage ?? ''}</Descriptions.Item>
                        <Descriptions.Item label="Is return">
                          {record?.shipment?.isReturn ? 'Yes' : 'No'}
                        </Descriptions.Item>
                        <Descriptions.Item label="Is paperless">
                          {record?.shipment?.isPaperless ? 'Yes' : 'No'}
                        </Descriptions.Item>
                        <Descriptions.Item label="Shipping label / Paperless code">
                          {record?.shipment ? (
                            <LinkShipmentDocument
                              shipmentId={record.shipment.id}
                              trackingNumber={record.shipment.trackingNumber}
                            />
                          ) : (
                            ''
                          )}
                        </Descriptions.Item>
                        <Descriptions.Item label="Shipping method">
                          {record?.shipment?.shippingMethod?.shippingProductName}
                        </Descriptions.Item>
                        <Descriptions.Item label="Note">{record?.shipment?.note ?? ''}</Descriptions.Item>
                      </>
                    )}
                  </Descriptions>
                </>
              ),
            },
            {
              key: PageTab.BILLING_ADDRESS,
              label: 'Billing Address',
              children: billingAddress ? (
                <Descriptions
                  extra={
                    <Space>
                      <ButtonCopy text={getAddressText(billingAddress, countries)} label="Copy address" />
                      <EditButton resource="admin/addresses" recordItemId={billingAddress.id} />
                    </Space>
                  }
                  bordered
                >
                  <Descriptions.Item label="First name">{billingAddress.firstName}</Descriptions.Item>
                  <Descriptions.Item label="Last name">{billingAddress.lastName}</Descriptions.Item>
                  <Descriptions.Item label="Phone">
                    {billingAddress.phoneAreaCode + ' ' + billingAddress.phoneNumber}
                  </Descriptions.Item>
                  <Descriptions.Item label="Street">{billingAddress.street}</Descriptions.Item>
                  <Descriptions.Item label="House number">{billingAddress.houseNumber}</Descriptions.Item>
                  <Descriptions.Item label="Addition">{billingAddress.addition}</Descriptions.Item>
                  <Descriptions.Item label="Postal code">{billingAddress.postalCode}</Descriptions.Item>
                  <Descriptions.Item label="City">{billingAddress.city}</Descriptions.Item>
                  <Descriptions.Item label="Country">
                    {countries
                      ? countries.find(({ id }) => id === billingAddress.countryId)?.name__en
                      : billingAddress.countryId}
                  </Descriptions.Item>
                </Descriptions>
              ) : null,
            },
            {
              key: PageTab.EMAIL_HISTORY,
              label: 'Email History',
              children: <BlockEmailHistory userId={record?.userId} relatedIds={id ? [id as string] : undefined} />,
            },
          ]}
        />
      </Edit>
      <Drawer {...skuItemDrawerProps} width={800}>
        <Edit
          isLoading={skuItemFormLoading}
          saveButtonProps={skuItemSaveButtonProps}
          headerProps={{
            title: 'Edit Order SKU Item',
            subTitle: (
              <>
                ID: <Typography.Text copyable>{skuItemId}</Typography.Text>
              </>
            ),
            backIcon: false,
            extra: false,
          }}
          breadcrumb={false}
          resource="admin/order-sku-items"
          recordItemId={skuItemId}
        >
          <Form {...skuItemFormProps} layout="vertical">
            <FormItemStatic
              label={
                <LabelWithDetails
                  label="Product SKU"
                  link={
                    skuItemRecord?.productSku?.id
                      ? editUrl('admin/product-skus', skuItemRecord.productSku.id)
                      : undefined
                  }
                />
              }
              name={['productSku', 'name__en']}
            />
            <InputNumber
              label="Unit price"
              name="unitPrice"
              rules={['required']}
              prefix={skuItemRecord?.currencyId && formatCurrencySymbol(skuItemRecord.currencyId)}
              disabled
            />
            <Select label="Warranty" name="warrantyId" rules={['required']} {...(warrantySelectProps as any)} />
            <InputNumber
              label="Warranty price"
              name="warrantyPrice"
              rules={['required']}
              prefix={skuItemRecord?.currencyId && formatCurrencySymbol(skuItemRecord.currencyId)}
              disabled
            />
            <Select
              label="Status"
              name="status"
              rules={['required']}
              options={formatSelectOptionsFromEnum(OrderSkuItemStatus)}
            />
            <TextArea label="Note" name="note" className="col-span-2" />
          </Form>
        </Edit>
      </Drawer>
      <Drawer {...accessoryItemDrawerProps} width={800}>
        <Edit
          isLoading={accessoryItemFormLoading}
          saveButtonProps={accessoryItemSaveButtonProps}
          headerProps={{
            title: 'Edit Order SKU Item',
            subTitle: (
              <>
                ID: <Typography.Text copyable>{accessoryItemId}</Typography.Text>
              </>
            ),
            backIcon: false,
            extra: false,
          }}
          breadcrumb={false}
          resource="admin/order-accessory-product-items"
          recordItemId={accessoryItemId}
        >
          <Form {...accessoryItemFormProps} layout="vertical">
            <Form.Item label="Accessory product" shouldUpdate>
              {({ getFieldValue }) => (
                <LabelWithDetails
                  label={getFieldValue(['accessoryProduct', 'name__en'])}
                  link={
                    accessoryItemRecord?.accessoryProduct?.id
                      ? editUrl('admin/accessory-products', accessoryItemRecord.accessoryProduct.id)
                      : undefined
                  }
                />
              )}
            </Form.Item>
            <InputNumber
              label="Unit price"
              name="unitPrice"
              rules={['required']}
              prefix={accessoryItemRecord?.currencyId && formatCurrencySymbol(accessoryItemRecord.currencyId)}
              disabled
            />
            <InputNumber label="Quantity" name="quantity" rules={['required']} disabled />
            <TextArea label="Note" name="note" className="col-span-2" />
          </Form>
        </Edit>
      </Drawer>
    </>
  )
}
