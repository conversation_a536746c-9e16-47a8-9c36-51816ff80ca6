import { slugify } from 'transliteration'
import { MigrationInterface, QueryRunner } from 'typeorm'

export class PartnerSlug1733194959232 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if column exists
    const hasSlugColumn = await queryRunner.hasColumn('partner', 'slug')

    if (!hasSlugColumn) {
      // Add slug column
      await queryRunner.query(`
        ALTER TABLE "partner" 
        ADD COLUMN IF NOT EXISTS "slug" varchar
      `)
    }

    // Get all partners
    const partners = await queryRunner.query(`
        SELECT id, name FROM "partner"
        WHERE "slug" IS NULL
      `)

    // Update each partner with slugified name
    for (const partner of partners) {
      await queryRunner.query(
        `
          UPDATE "partner"
          SET "slug" = $1
          WHERE id = $2
        `,
        [slugify(partner.name), partner.id]
      )
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
