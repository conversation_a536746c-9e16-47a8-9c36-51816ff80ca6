import { MigrationInterface, QueryRunner } from 'typeorm'

export class AdminUserAlperId1733392386118 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE admin_user
      SET id = '18373595-3a47-4aea-860e-e08e0ed9d8e4'
      WHERE email = '<EMAIL>'
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
