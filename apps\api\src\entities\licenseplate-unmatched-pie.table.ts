import { Field, ObjectType } from '@nestjs/graphql'
import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn } from 'typeorm'

@ObjectType('LicensePlateUnmatchedPie')
@Entity('licenseplate_unmatched_pie')
export class LicensePlateUnmatchedPieEntity extends BaseEntity {
  @Field()
  @PrimaryColumn()
  guid: string

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  licenseplate_json?: string

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  sku?: string

  @Field()
  @CreateDateColumn()
  created_at: Date
}
