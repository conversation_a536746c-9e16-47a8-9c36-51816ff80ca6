import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>on, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { WarrantyPeriodUnit } from '@valyuu/api/constants'
import type { IWarranty } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { FC } from 'react'

import { PublishStatus } from '~/components'
import { handleTableRowClick } from '~/utils'

export const WarrantyList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<IWarranty>({
    syncWithLocation: true,
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/warranties', id!)))}
      >
        <Table.Column dataIndex="name__en" title="Name" className="cursor-pointer" />
        <Table.Column
          dataIndex="periodNumber"
          title="Period"
          className="cursor-pointer"
          render={(value, record: { periodUnit: WarrantyPeriodUnit }) => {
            if (record.periodUnit === WarrantyPeriodUnit.LIFETIME) {
              return 'Lifetime'
            }
            const unit = value > 1 ? `${record.periodUnit.toLowerCase()}s` : record.periodUnit.toLowerCase()
            return `${value} ${unit}`
          }}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column<IWarranty>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
