{"name": "@valyuu/admin", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "refine": "refine", "lint": "eslint \"{src,test}/**/*.ts\" --fix", "stories": "ladle serve"}, "alias": {"@refinedev/inferencer/antd": "./node_modules/@refinedev/inferencer/dist/antd.js"}, "dependencies": {"@ant-design/pro-components": "^2.8.1", "@ant-design/pro-form": "^2.31.1", "@ant-design/pro-provider": "^2.15.1", "@cloudinary/url-gen": "^1.21.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@mdxeditor/editor": "^3.14.0", "@react-oauth/google": "^0.12.1", "@refinedev/antd": "^6.0.0", "@refinedev/core": "^4.55.0", "@refinedev/react-router-v6": "^4.6.0", "@sentry/react": "^8.35.0", "@sindresorhus/slugify": "^2.2.1", "@tanstack/react-query": "^4.36.1", "@uppy/core": "^4.2.2", "@uppy/dashboard": "^4.1.1", "@uppy/drag-drop": "^4.0.3", "@uppy/file-input": "^4.0.2", "@uppy/google-drive": "^4.1.0", "@uppy/image-editor": "^3.1.0", "@uppy/progress-bar": "^4.0.0", "@uppy/react": "^4.0.2", "@uppy/webcam": "^4.0.1", "@uppy/xhr-upload": "^4.2.1", "@valyuu/refine-crud-adapter": "workspace:*", "@xyflow/react": "^12.3.2", "antd": "^5.21.6", "antx": "^5.5.0", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "country-flag-icons": "^1.5.13", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "encrypt-storage": "^2.13.3", "fast-json-patch": "^3.1.1", "immer": "^10.1.1", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^6.27.0", "use-deep-compare": "^1.3.0", "use-immer": "^0.10.0", "uuid": "^11.0.2"}, "devDependencies": {"@dyljhd/use-effect-debugger": "^1.2.1", "@ladle/react": "^4.1.2", "@refinedev/cli": "^2.16.39", "@types/lodash": "^4.17.13", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/uuid": "^10.0.0", "@valyuu/api": "workspace:*", "@vitejs/plugin-react-swc": "^3.7.1", "@welldone-software/why-did-you-render": "^8.0.3", "autoprefixer": "10.4.20", "dotenv": "^16.4.5", "globals": "^15.11.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "5.6.3", "vite": "^5.4.10", "vite-plugin-svgr": "^4.2.0"}, "version": "3.25.0"}