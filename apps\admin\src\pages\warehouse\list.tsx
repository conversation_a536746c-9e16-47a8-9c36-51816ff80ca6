import { <PERSON><PERSON>ield, <PERSON>ete<PERSON>utton, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IWarehouse } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const WarehouseList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<IWarehouse>({
    syncWithLocation: true,
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/warehouses', id!)))}
      >
        <Table.Column dataIndex="countryId" title="Country" className="cursor-pointer" />
        <Table.Column dataIndex="companyName" title="Company name" className="cursor-pointer" />
        <Table.Column dataIndex="contactName" title="Contact name" className="cursor-pointer" />
        <Table.Column dataIndex="street" title="Street" className="cursor-pointer" />
        <Table.Column dataIndex="houseNumber" title="House number" className="cursor-pointer" />
        <Table.Column dataIndex="postalCode" title="Postal code" className="cursor-pointer" />
        <Table.Column
          dataIndex="sortOrder"
          title="Order"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
          width="5rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IWarehouse>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
