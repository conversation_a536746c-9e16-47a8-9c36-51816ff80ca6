import { InjectRedis } from '@liaoliaots/nestjs-redis'
import { Controller, Get, Param, Post, Query, Res, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { format } from 'date-fns'
import type { Express } from 'express'
import { Response } from 'express'
import { Redis } from 'ioredis'

import { DATA_EXCHANGE_JOB_REDIS_KEY } from '~admin/constants'
import { AdminJwtAuthGuard } from '~admin/guards'
import { DataExchangeService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Controller('admin/data-exchange')
export class DataExchangeController {
  constructor(
    public service: DataExchangeService,
    @InjectRedis() private readonly redis: Redis
  ) {}

  @Get('status')
  async status() {
    const job = await this.redis.get(DATA_EXCHANGE_JOB_REDIS_KEY)
    return job ? JSON.parse(job) : null
  }

  @Get('variant-price-export/:channel')
  async variantPriceExport(
    @Param('channel') channel: string,
    @Query('format') fileFormat: 'xlsx' | 'json' = 'xlsx',
    @Res() res: Response
  ) {
    if (fileFormat === 'xlsx') {
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      res.header('Access-Control-Expose-Headers', 'Content-Disposition')
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="Valyuu Variant Prices - ${channel} - ${format(new Date(), 'dd-MM-yyyy')}.xlsx"`
      )
    }
    if (fileFormat === 'json') {
      res.setHeader('Content-Type', 'application/json')
    }

    await this.service.variantPriceExport(channel, res, fileFormat)

    res.end()
  }

  @Post('variant-price-import/:channel')
  @UseInterceptors(FileInterceptor('file'))
  async variantPriceImport(
    @Param('channel') channel: string,
    @UploadedFile() file: Express.Multer.File,
    @Res() res: Response
  ) {
    res.setHeader('Content-Type', 'application/json')
    res.write('{')
    try {
      await this.service.variantPriceImport(channel, file)
      res.write(JSON.stringify({ success: true }).slice(1))
    } catch (error) {
      res.write(JSON.stringify({ success: false, error: (error as any).message }).slice(1))
    }
    res.end()
  }

  @Get('order-export')
  async orderExport(@Res() res: Response) {
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    res.header('Access-Control-Expose-Headers', 'Content-Disposition')
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="Valyuu Orders - from 19-04-2022 to ${format(new Date(), 'dd-MM-yyyy')}.xlsx"`
    )

    await this.service.orderExport(res)

    res.end()
  }
}
