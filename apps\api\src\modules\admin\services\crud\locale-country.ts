import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { LocaleCountryEntity } from '~/entities'

export class CrudLocaleCountryService extends TypeOrmCrudService<LocaleCountryEntity> {
  constructor(
    @InjectRepository(LocaleCountryEntity)
    repo: Repository<LocaleCountryEntity>
  ) {
    super(repo)
  }
}
