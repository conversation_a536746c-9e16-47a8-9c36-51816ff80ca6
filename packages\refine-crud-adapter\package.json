{"name": "@valyuu/refine-crud-adapter", "description": "refine Nestjsx Crud data provider. refine is a React-based framework for building internal tools, rapidly. It ships with Ant Design System, an enterprise-level UI toolkit.", "version": "4.6.5", "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "private": false, "files": ["dist", "src", "./refine.config.js"], "engines": {"node": ">=10"}, "scripts": {"start": "tsup --watch --format esm,cjs,iife --legacy-output", "build": "tsup --format esm,cjs,iife --minify --legacy-output", "test": "jest --passWithNoTests --runInBand", "prepare": "npm run build"}, "author": "refine", "module": "dist/esm/index.js", "devDependencies": {"@esbuild-plugins/node-resolve": "^0.2.2", "@refinedev/cli": "^2.16.39", "@refinedev/core": "^4.55.0", "@types/jest": "^29.5.13", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nock": "^13.5.5", "ts-jest": "^29.2.5", "tslib": "^2.8.0", "tsup": "^8.3.0"}, "dependencies": {"@dataui/crud-request": "5.3.4", "axios": "^1.7.7"}, "peerDependencies": {"@refinedev/core": "^4.0.0"}}