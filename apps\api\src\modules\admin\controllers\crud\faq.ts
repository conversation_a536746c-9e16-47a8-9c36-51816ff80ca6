import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { FaqEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudFaqService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: FaqEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
})
@Controller('admin/faqs')
export class CrudFaqController implements CrudController<FaqEntity> {
  constructor(public service: CrudFaqService) {}
}
