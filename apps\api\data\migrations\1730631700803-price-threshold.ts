import { MigrationInterface, QueryRunner } from 'typeorm'

export class PriceThreshold1730631700803 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add price_threshold table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "public"."price_threshold" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "type" varchar COLLATE "pg_catalog"."default" NOT NULL,
        "channel_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
        "currency_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
        "price" numeric NOT NULL,
        "updated_at" timestamp(6) NOT NULL DEFAULT now()
      );
    `)

    // Copy records from recycle_threshold to price_threshold
    await queryRunner.query(`
      INSERT INTO price_threshold (
        type, channel_id, currency_id, price, updated_at
      )
      SELECT 
        'TRADE_IN_RECYCLE', channel_id, currency_id, price, updated_at
      FROM recycle_threshold;
    `)

    // Insert new offer threshold for all channels
    await queryRunner.query(`
      INSERT INTO price_threshold (
        type, channel_id, currency_id, price, updated_at
      )
      SELECT 
        'TRADE_IN_NEW_OFFER',
        id as channel_id,
        currency_id,
        10,
        now()
      FROM channel;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
