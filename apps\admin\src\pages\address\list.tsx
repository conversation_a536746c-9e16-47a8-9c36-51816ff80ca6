import { DateF<PERSON>, EditButton, FilterDropdown, getDefaultSortOrder, List, useSelect, useTable } from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { AddressType } from '@valyuu/api/constants'
import type { IAddress, ILocaleCountry } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { Input, Select } from 'antx'
import { capitalize } from 'lodash'
import type { FC } from 'react'

import { formatSelectOptionsFromEnum, handleTableRowClick } from '~/utils'

export const AddressList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IAddress>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'type', 'firstName', 'lastName', 'countryId', 'createdAt'],
      join: [
        {
          field: 'user',
          select: ['id', 'email'],
        },
        {
          field: 'country',
          select: ['id', 'name__en'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
    },
  })

  const { selectProps: countrySelectProps } = useSelect<ILocaleCountry>({
    resource: 'admin/countries',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/addresses', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex={['user', 'email']}
          title="User"
          className="cursor-pointer"
          align="center"
          defaultFilteredValue={getDefaultFilter('user.email', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 280 }} placeholder="User email" normalize={(value) => value.trim()} />
            </FilterDropdown>
          )}
          width="7rem"
        />
        <Table.Column
          dataIndex="type"
          title="Type"
          className="cursor-pointer"
          render={capitalize}
          defaultFilteredValue={getDefaultFilter('type', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} placeholder="Type" options={formatSelectOptionsFromEnum(AddressType)} />
            </FilterDropdown>
          )}
          width="5rem"
        />
        <Table.Column dataIndex="firstName" title="First name" className="cursor-pointer" />
        <Table.Column dataIndex="lastName" title="Last name" className="cursor-pointer" />
        <Table.Column
          dataIndex="countryId"
          title="Country"
          className="cursor-pointer"
          width="3rem"
          render={(_, row: IAddress) => row.country?.name__en}
          defaultFilteredValue={getDefaultFilter('countryId', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} placeholder="Country" {...(countrySelectProps as any)} />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IAddress>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
