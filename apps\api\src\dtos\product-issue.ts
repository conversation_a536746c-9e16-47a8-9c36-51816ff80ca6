import { ArgsType, Field } from '@nestjs/graphql'
import { IsEmail, IsIn, IsNotEmpty } from 'class-validator'

import { LOCALE_ENABLED_LANGUAGES, ProductIssueType } from '~/constants'

@ArgsType()
export class CreateProductIssueProductCategoryInput {
  @Field()
  @IsNotEmpty()
  @IsEmail()
  email: string

  @Field()
  @IsNotEmpty()
  issue: string
}

@ArgsType()
export class CreateProductIssueNoSearchResultInput {
  @Field()
  @IsNotEmpty()
  @IsEmail()
  email: string

  @Field(() => ProductIssueType, { description: 'Only SELLER_SEARCH or BUYER_SEARCH allowed' })
  @IsIn([ProductIssueType.BUYER_SEARCH, ProductIssueType.SELLER_SEARCH])
  type: ProductIssueType

  @Field()
  @IsNotEmpty()
  issue: string

  @Field()
  @IsNotEmpty()
  keyword: string
}

@ArgsType()
export class CreateProductIssueModelQuestionInput {
  @Field()
  @IsNotEmpty()
  @IsEmail()
  email: string

  @Field()
  @IsNotEmpty()
  issue: string

  @Field()
  @IsNotEmpty()
  modelSlug: string

  @Field()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  lang: string
}
