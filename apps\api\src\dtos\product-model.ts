import { ArgsType, Field, ID, InputType, Int, ObjectType, registerEnumType } from '@nestjs/graphql'
import { IsEnum, IsIn, IsNotEmpty, IsUUID, ValidateIf } from 'class-validator'
import { GraphQLJSONObject } from 'graphql-type-json'
import type { JsonObject } from 'type-fest'

import { LOCALE_ENABLED_LANGUAGES, ProductModelSellerQuestionType, SellerPaymentPeriodUnit } from '~/constants'
import {
  ImageEntity,
  LocaleCurrencyEntity,
  ProductModelAttributeEntity,
  QuestionTypeConditionEntity,
  QuestionTypeImageTextEntity,
  QuestionTypeProblemEntity,
} from '~/entities'

registerEnumType(SellerPaymentPeriodUnit, {
  name: 'SellerPaymentPeriodUnit',
})

@ArgsType()
export class GetProductModelSellerQuestionsInput {
  @Field(() => String, { description: 'Current language in frontend' })
  @IsNotEmpty()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @Field({ description: 'The slug from frontend URL' })
  @IsNotEmpty()
  slug: string
}

@ObjectType()
export class GetProductModelSellerQuestionsAttributeCombinationOutput {
  @Field()
  variantId: string

  @Field(() => GraphQLJSONObject)
  choices: JsonObject
}

@ObjectType()
export class GetProductModelSellerQuestionsOutput {
  @Field(() => ID)
  id: string

  @Field()
  name__en: string

  @Field()
  name__nl: string

  @Field()
  name__de: string

  @Field()
  name__pl: string

  @Field()
  slug__en: string

  @Field()
  slug__nl: string

  @Field()
  slug__de: string

  @Field()
  slug__pl: string

  @Field()
  categorySlug__en: string

  @Field()
  categorySlug__nl: string

  @Field()
  categorySlug__de: string

  @Field()
  categorySlug__pl: string

  @Field()
  brandSlug__en: string

  @Field()
  brandSlug__nl: string

  @Field()
  brandSlug__de: string

  @Field()
  brandSlug__pl: string

  @Field()
  recycle: boolean

  @Field(() => ImageEntity)
  image: ImageEntity

  @Field(() => [ProductModelAttributeEntity])
  attributesQuestions: ProductModelAttributeEntity[]

  @Field(() => [QuestionTypeConditionEntity])
  conditionQuestions: QuestionTypeConditionEntity[]

  @Field(() => [QuestionTypeProblemEntity])
  problemQuestions: QuestionTypeProblemEntity[]

  @Field(() => [QuestionTypeImageTextEntity])
  problemImageTexts: QuestionTypeImageTextEntity[]

  @Field(() => [GetProductModelSellerQuestionsAttributeCombinationOutput])
  attributeCombinations: GetProductModelSellerQuestionsAttributeCombinationOutput[]
}

registerEnumType(ProductModelSellerQuestionType, {
  name: 'ProductModelSellerQuestionType',
})

@InputType()
export class GetProductModelSellerPricesConditionInput {
  @Field()
  @IsUUID('4')
  conditionId: string

  @Field()
  @IsUUID('4')
  optionId: string
}

@ArgsType()
export class GetProductModelSellerPricesInput {
  @Field()
  channelId: string

  @Field()
  @IsUUID('4')
  variantId: string

  @Field()
  @IsUUID('4')
  modelId: string

  @Field(() => ProductModelSellerQuestionType)
  @IsEnum(ProductModelSellerQuestionType)
  type: ProductModelSellerQuestionType

  @Field(() => [String], {
    description: 'Selected problems of the product, only used when type === PROBLEM',
    nullable: true,
  })
  @ValidateIf((o) => o.type === 'PROBLEM')
  @IsNotEmpty({ message: 'Problems are required when type is PROBLEM' })
  @ValidateIf((o) => o.type === 'PROBLEM')
  @IsUUID('4', { each: true, message: 'Problems must be UUID' })
  problems?: string[]

  @Field(() => [GetProductModelSellerPricesConditionInput], {
    description: 'Selected conditions of the product, only used when type === CONDITION',
    nullable: true,
  })
  @ValidateIf((o) => o.type === 'CONDITION')
  @IsNotEmpty({ message: 'Conditions are required when type is CONDITION' })
  conditions?: GetProductModelSellerPricesConditionInput[]
}

registerEnumType(SellerPaymentPeriodUnit, { name: 'SellerPaymentPeriodUnit' })

@ObjectType()
export class GetProductModelSellerPricesItemOutput {
  @Field()
  price: number

  @Field()
  disabled: boolean

  @Field(() => Int, { nullable: true })
  paymentPeriodFrom?: number

  @Field(() => Int)
  paymentPeriodTo: number

  @Field(() => SellerPaymentPeriodUnit)
  paymentPeriodUnit: SellerPaymentPeriodUnit
}

@ObjectType()
export class GetProductModelSellerPricesSavedOutput {
  @Field(() => Int)
  co2: number

  @Field(() => Int)
  totalC2o: number

  @Field(() => Int)
  ewaste: number

  @Field(() => Int)
  totalEwaste: number
}

@ObjectType()
export class GetProductModelSellerPricesOutput {
  @Field()
  recycle: boolean

  @Field()
  currency: LocaleCurrencyEntity

  @Field(() => GetProductModelSellerPricesItemOutput)
  c2c: GetProductModelSellerPricesItemOutput

  @Field(() => GetProductModelSellerPricesItemOutput)
  c2b: GetProductModelSellerPricesItemOutput

  @Field(() => GetProductModelSellerPricesSavedOutput)
  saved: GetProductModelSellerPricesSavedOutput
}
