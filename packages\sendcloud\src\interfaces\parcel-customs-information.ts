import { IParcelCustomsInformationImporterOfRecord } from './parcel-customs-information-importer-of-record'

export type IParcelCustomsInformation = {
  /** Customs invoice number */
  customs_invoice_nr: string
  /** Customs shipment type */
  customs_shipment_type: 0 | 1 | 2 | 3 | 4
  /** Export type */
  export_type?: 'private' | 'commercial_b2c' | 'commercial_b2b'
  /** Invoice date (ISO 8601) */
  invoice_date?: string
  /** Granted discount amount */
  discount_granted?: string
  /** Freight costs */
  freight_costs?: string
  /** Insurance costs */
  insurance_costs?: string
  /** Other costs */
  other_costs?: string
  /** General notes */
  general_notes?: string
  /** Additional declaration statements */
  additional_declaration_statements?: string[]
  /** Importer of record information */
  importer_of_record?: IParcelCustomsInformationImporterOfRecord
  /** Tax numbers */
  tax_numbers: {
    sender: IParcelTaxNumber[]
    receiver: IParcelTaxNumber[]
    importer_of_record: IParcelTaxNumber[]
  }
  /** Return shipment data */
  return_data?: IParcelCustomsInformationReturnData
}

export type IParcelCustomsInformationReturnData = {
  /** Postcode of original delivery */
  return_postal_code?: string
  /** Original shipment tracking number */
  outbound_tracking_number?: string
  /** Original shipment date */
  outbound_shipment_date?: string
  /** Original carrier name */
  outbound_carrier_name?: string
}

export type IParcelTaxNumber = {
  /** Tax number abbreviation */
  name:
    | 'VAT'
    | 'EIN'
    | 'GST'
    | 'SSN'
    | 'EORI'
    | 'DUN'
    | 'FED'
    | 'STA'
    | 'CNP'
    | 'IE'
    | 'INN'
    | 'KPP'
    | 'OGR'
    | 'OKP'
    | 'IOSS'
    | 'FTZ'
    | 'DAN'
    | 'TAN'
    | 'DTF'
    | 'RGP'
    | 'DLI'
    | 'NID'
    | 'PAS'
    | 'MID'
    | 'UKIMS'
  /** Issuing country code (ISO 3166-1 alpha-2 code) */
  country_code: string
  /** Number itself */
  value: string
}
