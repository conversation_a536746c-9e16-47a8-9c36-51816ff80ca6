import { ArgsType, Field, Float, Int, ObjectType, registerEnumType } from '@nestjs/graphql'
import { IsNotEmpty, IsUUID } from 'class-validator'

import {
  SaleItemOfferConfirmationType,
  SaleItemOfferErrorType,
  SaleItemOfferStatus,
  SaleItemPlanType,
  SellerPaymentPeriodUnit,
} from '~/constants'

registerEnumType(SaleItemOfferErrorType, {
  name: 'SaleItemOfferErrorType',
})

registerEnumType(SaleItemOfferConfirmationType, {
  name: 'SaleItemOfferConfirmationType',
})

registerEnumType(SaleItemOfferStatus, {
  name: 'SaleItemOfferStatus',
})

@ObjectType('GetSaleItemOfferDetailsResult')
export class GetSaleItemOfferDetailsResult {
  @Field(() => String)
  saleNumber: string

  @Field(() => String)
  name: string

  @Field(() => String)
  image: string

  @Field(() => [String])
  attributes: string[]

  @Field(() => [String])
  answers: string[]

  @Field(() => SaleItemPlanType)
  plan: SaleItemPlanType

  @Field(() => Float)
  price: number

  @Field(() => Boolean)
  isDonation: boolean

  @Field(() => String)
  donationPaymentPeriodUnit: SellerPaymentPeriodUnit

  @Field(() => Int, { nullable: true })
  donationPaymentPeriodFrom?: number

  @Field(() => Int)
  donationPaymentPeriodTo: number

  @Field(() => String)
  bankTransferPaymentPeriodUnit: SellerPaymentPeriodUnit

  @Field(() => Int, { nullable: true })
  bankTransferPaymentPeriodFrom?: number

  @Field(() => Int)
  bankTransferPaymentPeriodTo: number

  @Field(() => String)
  currency: string

  @Field(() => SaleItemOfferStatus)
  offerStatus: SaleItemOfferStatus

  @Field(() => Date)
  offerExpiresAt: Date
}

@ArgsType()
export class GetSaleItemOfferDetailsInput {
  @Field(() => String, { description: 'Unique identifier of the offer' })
  @IsUUID()
  @IsNotEmpty()
  offerId: string

  @Field(() => String, { description: 'Login key for offer access' })
  @IsNotEmpty()
  authToken: string
}

@ObjectType('GetSaleItemOfferDetails')
export class GetSaleItemOfferDetailsOutput {
  @Field()
  success: boolean

  @Field(() => SaleItemOfferErrorType, { nullable: true })
  error?: SaleItemOfferErrorType

  @Field(() => GetSaleItemOfferDetailsResult, { nullable: true })
  result?: GetSaleItemOfferDetailsResult
}

@ArgsType()
export class ConfirmSaleItemOfferInput {
  @Field(() => String)
  @IsUUID()
  @IsNotEmpty()
  offerId: string

  @Field(() => String)
  @IsNotEmpty()
  authToken: string

  @Field(() => SaleItemOfferConfirmationType)
  @IsNotEmpty()
  confirmationType: SaleItemOfferConfirmationType
}

@ObjectType('ConfirmSaleItemOffer')
export class ConfirmSaleItemOfferOutput {
  @Field()
  success: boolean

  @Field(() => SaleItemOfferErrorType, { nullable: true })
  error?: SaleItemOfferErrorType
}
