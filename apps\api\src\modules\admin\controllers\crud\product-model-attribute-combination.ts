import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { ProductModelAttributeCombinationEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudProductModelAttributeCombinationService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ProductModelAttributeCombinationEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      choices: {},
      'choices.option': {},
      variant: {},
      model: {},
    },
  },
})
@Controller('admin/product-model-attribute-combinations')
export class CrudProductModelAttributeCombinationController
  implements CrudController<ProductModelAttributeCombinationEntity>
{
  constructor(public service: CrudProductModelAttributeCombinationService) {}
}
