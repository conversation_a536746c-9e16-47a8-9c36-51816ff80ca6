.input-content-published.ant-radio-group {
  user-select: none;
  --unpublished-color: #87d068;
  --published-color: #f56a00;
  label.ant-radio-button-wrapper:first-of-type {
    &:hover {
      color: var(--published-color);
    }
    &.ant-radio-button-wrapper-checked {
      &:hover {
        color: white;
      }
      background-color: var(--published-color);
      border-color: var(--published-color);
    }
  }
  label.ant-radio-button-wrapper:last-of-type {
    &:hover {
      color: var(--unpublished-color);
    }
    &.ant-radio-button-wrapper-checked {
      background-color: var(--unpublished-color);
      border-color: var(--unpublished-color);
      &:hover {
        color: white;
      }
      &::before {
        background-color: var(--unpublished-color);
      }
    }
  }
}
