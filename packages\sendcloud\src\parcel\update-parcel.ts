import { sendcloudClientV2 } from '../client'
import { IParcelObject } from '../interfaces'

type IUpdateParcelData = Partial<Omit<IParcelObject, 'id'>>

export const updateParcel = async (id: number, parcel: IUpdateParcelData): Promise<IParcelObject> => {
  const { data: response } = await sendcloudClientV2.put<{ parcel: IParcelObject }>('parcels', {
    parcel: { id, ...parcel },
  })

  return response.parcel
}
