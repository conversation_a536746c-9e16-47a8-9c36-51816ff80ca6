import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBody, ApiProperty, ApiTags } from '@nestjs/swagger'

import { AdminJwtAuthGuard } from '~admin/guards'
import { PricingService } from '~admin/services'

class Condition {
  @ApiProperty({
    type: 'object',
    properties: {},
    additionalProperties: {
      type: 'string',
    },
    description: 'Dynamic key-value pairs for variant choices',
  })
  choices: Record<string, string>

  @ApiProperty()
  market: string

  @ApiProperty()
  price: number
}

class Problem {
  @ApiProperty()
  problemId: string

  @ApiProperty()
  priceReduction: number
}

export class UpdateVariantPriceData {
  @ApiProperty()
  variantId: string

  @ApiProperty()
  channelId: string

  @ApiProperty({ type: [Condition], required: false })
  conditions?: Condition[]

  @ApiProperty({ required: false })
  basePrice?: number

  @ApiProperty({ type: [Problem], required: false })
  problems?: Problem[]
}

export class UpdateSkuPriceData {
  @ApiProperty()
  skuId: string

  @ApiProperty()
  channelId: string

  @ApiProperty({ required: false })
  price?: number

  @ApiProperty({ required: false })
  originalPrice?: number

  @ApiProperty({ required: false })
  brandNewPrice?: number

  @ApiProperty({ required: false })
  isDeal?: boolean

  @ApiProperty({ required: false })
  margin?: number
}

@UseGuards(AdminJwtAuthGuard)
@ApiTags('Pricing')
@Controller('admin/pricing')
export class PricingController {
  constructor(private readonly service: PricingService) {}

  @Post('variant-price')
  @ApiBody({ type: UpdateVariantPriceData })
  async variantPrice(@Body() body: UpdateVariantPriceData) {
    return this.service.updateVariantPrice(body)
  }

  @Post('sku-price')
  @ApiBody({ type: UpdateSkuPriceData })
  async skuPrice(@Body() body: UpdateSkuPriceData) {
    return this.service.updateSkuPrice(body)
  }
}
