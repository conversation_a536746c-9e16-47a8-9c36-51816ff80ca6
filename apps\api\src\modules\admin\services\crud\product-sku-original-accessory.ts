import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { ProductSkuOriginalAccessoryEntity } from '~/entities'

export class CrudProductSkuOriginalAccessoryService extends TypeOrmCrudService<ProductSkuOriginalAccessoryEntity> {
  constructor(
    @InjectRepository(ProductSkuOriginalAccessoryEntity)
    repo: Repository<ProductSkuOriginalAccessoryEntity>
  ) {
    super(repo)
  }
}
