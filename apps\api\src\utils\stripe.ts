import * as currency from 'currency.js'
import { isNil, mapValues } from 'lodash'
import Stripe from 'stripe'

import { envConfig } from '~/configs'
import { StripeAccountApiType, StripeEnabledPaymentMethod } from '~/constants'
import { IAddress, IBankAccount } from '~/entities'

const stripe: Record<StripeAccountApiType, Stripe> = {
  [StripeAccountApiType.LIVE]: new Stripe(envConfig.STRIPE_SK_LIVE, { apiVersion: '2023-10-16' }),
  [StripeAccountApiType.VERIFICATION]: new Stripe(envConfig.STRIPE_SK_VERIFICATION, { apiVersion: '2023-10-16' }),
  [StripeAccountApiType.TEST]: new Stripe(envConfig.STRIPE_SK_TEST, { apiVersion: '2023-10-16' }),
}

const stripeErrorFieldMap: Record<string, string> = {
  country: 'country',
  'individual[phone]': 'phoneNumber',
  'individual[address][postal_code]': 'postalCode',
  email: 'email',
  'external_account[account_number]': 'accountNumber',
  'individual[dob][year]': 'dateOfBirth',
  'individual[dob][month]': 'dateOfBirth',
  'individual[dob][day]': 'dateOfBirth',
}

export const houseNumberWithAddition = (houseNumber: string, addition?: string) => {
  houseNumber = houseNumber?.trim() ?? ''
  addition = addition?.trim() ?? ''
  let result = houseNumber
  if (addition.trim()) {
    // house number does not end with letter
    const a = !/\p{L}$/u.test(houseNumber)
    // addition starts with letter
    const b = /^\p{L}/u.test(addition)
    // XOR, any of the two is true, but not both
    if ((a || b) && !(a && b)) {
      result += '-'
    }
    result += addition
  }
  return result
}

export type StripeCreateAccountData = {
  email: string
  address: Omit<IAddress, 'id' | 'country' | 'type' | 'user' | 'userId' | 'createdAt' | 'updatedAt'>
  bank: Omit<
    IBankAccount,
    'id' | 'country' | 'stripeBankAccount' | 'user' | 'userId' | 'payments' | 'createdAt' | 'updatedAt'
  >
  dob: string
  currencyId: string
  ip: string
}

export type StripeCreateAccountReturn =
  | {
      success: true
      account: Stripe.Account
      error: null
    }
  | {
      success: false
      account: null
      error: {
        field?: string | null
        message: string
        original: Error
      }
    }

export const stripeCreateAccount = async (
  { email, address, bank, dob, currencyId, ip }: StripeCreateAccountData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeCreateAccountReturn> => {
  if (!bank) {
    return {
      success: false,
      account: null,
      error: {
        field: 'bank',
        message: 'Bank account is required',
        original: new Error('Bank account is required'),
      },
    }
  }
  if (!/^([A-Z]{2})(\d{2})([A-Z\d]{11,30})$/i.test(bank.accountNumber?.trim())) {
    return {
      success: false,
      account: null,
      error: {
        field: 'accountNumber',
        message: 'Account number should start with two letters',
        original: new Error('Account number should start with two letters'),
      },
    }
  }
  try {
    const account = await stripe[apiType].accounts.create({
      type: 'custom',
      country: address.countryId,
      email,
      default_currency: currencyId,
      capabilities: {
        transfers: { requested: true },
      },
      business_type: 'individual',
      tos_acceptance: { date: Math.floor(Date.now() / 1000), ip },
      business_profile: {
        mcc: '5732',
        url: 'https://prioont.com',
      },
      external_account: {
        object: 'bank_account',
        country: bank.accountNumber.slice(0, 2),
        currency: currencyId,
        account_holder_type: 'individual',
        account_holder_name: bank.holderName,
        account_number: bank.accountNumber,
      },
      individual: {
        address: {
          city: address.city,
          country: address.countryId,
          line1: address.street,
          line2: houseNumberWithAddition(address.houseNumber, address.addition),
          postal_code: address.postalCode,
        },
        dob: dob
          ? (mapValues(dob.match(/(?<year>\d{4})-(?<month>\d{2})-(?<day>\d{2})/).groups, Number) as {
              year: number
              month: number
              day: number
            })
          : { year: 1990, month: 1, day: 1 },
        first_name: address.firstName,
        last_name: address.lastName,
        phone: address.phoneAreaCode + ' ' + address.phoneNumber,
        email,
      },
      settings: {
        payouts: {
          schedule: {
            interval: 'manual',
          },
        },
      },
    })
    return {
      success: true,
      account,
      error: null,
    }
  } catch (error) {
    // TODO: check sendcloud
    if (error instanceof Stripe.errors.StripeError) {
      return {
        success: false,
        account: null,
        error: {
          field: stripeErrorFieldMap[error.param] ?? null,
          message: error.message,
          original: error,
        },
      }
    }
    return {
      success: false,
      account: null,
      error: {
        field: null,
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeDeleteAccountReturn =
  | {
      success: true
      error: null
    }
  | {
      success: false
      error: {
        message: string
        original: Error
      }
    }

export const stripeDeleteAccount = async (
  accountId: string,
  apiType: StripeAccountApiType
): Promise<StripeDeleteAccountReturn> => {
  try {
    await stripe[apiType].accounts.del(accountId)
    return {
      success: true,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeUpdateAccountData = {
  accountId: string
  data: Stripe.AccountUpdateParams
}

export type StripeUpdateAccountReturn =
  | {
      success: true
      account: Stripe.Account
      error: null
    }
  | {
      success: false
      account: null
      error: {
        field?: string | null
        message: string
        original: Error
      }
    }

export const stripeUpdateAccount = async (
  { accountId, data }: StripeUpdateAccountData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeUpdateAccountReturn> => {
  try {
    const account = await stripe[apiType].accounts.update(accountId, data)
    return {
      success: true,
      account,
      error: null,
    }
  } catch (error) {
    if (error instanceof Stripe.errors.StripeError) {
      return {
        success: false,
        account: null,
        error: {
          field: stripeErrorFieldMap[error.param] ?? null,
          message: error.message,
          original: error,
        },
      }
    }
    return {
      success: false,
      account: null,
      error: {
        field: null,
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeCreateExternalAccountData = {
  accountId: string
  bank: StripeCreateAccountData['bank']
  currencyId: string
}

export type StripeCreateExternalAccountReturn =
  | {
      success: true
      bankAccount: Stripe.ExternalAccount
      error: null
    }
  | {
      success: false
      bankAccount: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeCreateExternalAccount = async (
  { accountId, bank, currencyId }: StripeCreateExternalAccountData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeCreateExternalAccountReturn> => {
  try {
    const bankAccount = await stripe[apiType].accounts.createExternalAccount(accountId, {
      external_account: {
        object: 'bank_account',
        country: bank.accountNumber.slice(0, 2),
        currency: currencyId,
        account_holder_type: 'individual',
        account_holder_name: bank.holderName,
        account_number: bank.accountNumber,
      } as unknown as string,
    })
    return {
      success: true,
      bankAccount,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      bankAccount: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeDeleteExternalAccountData = {
  accountId: string
  bankAccountId: string
}

export type StripeDeleteExternalAccountReturn =
  | {
      success: true
      error: null
    }
  | {
      success: false
      error: {
        message: string
        original: Error
      }
    }

export const stripeDeleteExternalAccount = async (
  { accountId, bankAccountId }: StripeDeleteExternalAccountData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeDeleteExternalAccountReturn> => {
  try {
    await stripe[apiType].accounts.deleteExternalAccount(accountId, bankAccountId)
    return {
      success: true,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeCreateCustomerData = {
  email: string
  name: string
  phone: string
}

export type StripeCreateCustomerReturn =
  | {
      success: true
      customer: Stripe.Customer
      error: null
    }
  | {
      success: false
      customer: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeCreateCustomer = async (
  { email, name, phone }: StripeCreateCustomerData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeCreateCustomerReturn> => {
  try {
    const customer = await stripe[apiType].customers.create({
      email,
      name,
      phone,
    })

    return {
      success: true,
      customer,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      customer: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeDeleteCustomerData = {
  customerId: string
}

export type StripeDeleteCustomerReturn =
  | {
      success: true
      error: null
    }
  | {
      success: false
      error: {
        message: string
        original: Error
      }
    }

export const stripeDeleteCustomer = async (
  { customerId }: StripeDeleteCustomerData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeDeleteCustomerReturn> => {
  try {
    await stripe[apiType].customers.del(customerId)
    return {
      success: true,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeCreatePaymentIntentData = {
  amount: number
  currencyId: string
  language: string
}

export type StripeCreatePaymentIntentReturn =
  | {
      success: true
      paymentIntent: Stripe.PaymentIntent
      error: null
    }
  | {
      success: false
      paymentIntent: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeCreatePaymentIntent = async (
  { amount, currencyId, language }: StripeCreatePaymentIntentData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeCreatePaymentIntentReturn> => {
  const paymentMethods = Object.values(StripeEnabledPaymentMethod)
  const params: Stripe.PaymentIntentCreateParams = {
    amount: currency(amount).multiply(100).value,
    currency: currencyId,
    payment_method_types: paymentMethods,
  }
  try {
    if (['de', 'en', 'fr', 'nl'].includes(language) && paymentMethods.includes(StripeEnabledPaymentMethod.bancontact)) {
      params.payment_method_options = {
        bancontact: {
          preferred_language: language as 'de' | 'en' | 'fr' | 'nl',
        },
      }
    }
    const paymentIntent = await stripe[apiType].paymentIntents.create(params)

    return {
      success: true,
      paymentIntent,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      paymentIntent: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeUpdatePaymentIntentData = {
  paymentIntentId: string
  amount?: number
  metadata?: Stripe.MetadataParam
}

export type StripeUpdatePaymentIntentReturn =
  | {
      success: true
      paymentIntent: Stripe.PaymentIntent
      error: null
    }
  | {
      success: false
      paymentIntent: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeUpdatePaymentIntent = async (
  { paymentIntentId, amount, metadata }: StripeUpdatePaymentIntentData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeUpdatePaymentIntentReturn> => {
  try {
    const paymentIntent = await stripe[apiType].paymentIntents.update(paymentIntentId, {
      ...(!isNil(amount) ? { amount: currency(amount).multiply(100).value } : {}),
      ...(!isNil(metadata) ? { metadata } : {}),
    })

    return {
      success: true,
      paymentIntent,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      paymentIntent: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeCancelPaymentIntentData = {
  paymentIntentId: string
}

export type StripeRetrievePaymentIntentData = {
  paymentIntentId: string
}

export type StripePaymentIntentActionReturn =
  | {
      success: true
      paymentIntent: Stripe.PaymentIntent
      error: null
    }
  | {
      success: false
      paymentIntent: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeCancelPaymentIntent = async (
  { paymentIntentId }: StripeCancelPaymentIntentData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripePaymentIntentActionReturn> => {
  try {
    const paymentIntent = await stripe[apiType].paymentIntents.cancel(paymentIntentId)

    return {
      success: true,
      paymentIntent,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      paymentIntent: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export const stripeRetrievePaymentIntent = async (
  { paymentIntentId }: StripeRetrievePaymentIntentData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripePaymentIntentActionReturn> => {
  try {
    const paymentIntent = await stripe[apiType].paymentIntents.retrieve(paymentIntentId)

    return {
      success: true,
      paymentIntent,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      paymentIntent: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeCreateTransferData = {
  amount: number
  currencyId: string
  sourceTransaction?: string
  destination: string
  metadata: Stripe.MetadataParam
}

export type StripeCreateTransferReturn =
  | {
      success: true
      transfer: Stripe.Transfer
      error: null
    }
  | {
      success: false
      transfer: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeCreateTransfer = async (
  { amount, currencyId, sourceTransaction, destination, metadata }: StripeCreateTransferData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeCreateTransferReturn> => {
  try {
    const transfer = await stripe[apiType].transfers.create({
      amount,
      currency: currencyId,
      source_transaction: sourceTransaction,
      destination,
      metadata,
    })

    return {
      success: true,
      transfer,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      transfer: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeRetrieveTransferReturn =
  | {
      success: true
      transfer: Stripe.Transfer
      error: null
    }
  | {
      success: false
      transfer: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeRetrieveTransfer = async (
  transferId: string,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeRetrieveTransferReturn> => {
  try {
    const transfer = await stripe[apiType].transfers.retrieve(transferId)

    return {
      success: true,
      transfer,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      transfer: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

export type StripeCreatePayoutData = {
  destination: string
  amount: number
  currencyId: string
}

export type StripeCreatePayoutReturn =
  | {
      success: true
      payout: Stripe.Payout
      error: null
    }
  | {
      success: false
      payout: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeCreatePayout = async (
  { destination, amount, currencyId }: StripeCreatePayoutData,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeCreatePayoutReturn> => {
  try {
    const payout = await stripe[apiType].payouts.create({
      amount,
      currency: currencyId,
      destination,
    })

    return {
      success: true,
      payout,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      payout: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}

// stripeRetrieveBalanceTransaction
export type StripeRetrieveBalanceTransactionReturn =
  | {
      success: true
      balanceTransaction: Stripe.BalanceTransaction
      error: null
    }
  | {
      success: false
      balanceTransaction: null
      error: {
        message: string
        original: Error
      }
    }

export const stripeRetrieveBalanceTransaction = async (
  balanceTransactionId: string,
  apiType: StripeAccountApiType = StripeAccountApiType.TEST
): Promise<StripeRetrieveBalanceTransactionReturn> => {
  try {
    const balanceTransaction = await stripe[apiType].balanceTransactions.retrieve(balanceTransactionId)

    return {
      success: true,
      balanceTransaction,
      error: null,
    }
  } catch (error) {
    return {
      success: false,
      balanceTransaction: null,
      error: {
        message: (error as Error)?.message ?? 'Unknown error',
        original: error as Error,
      },
    }
  }
}
