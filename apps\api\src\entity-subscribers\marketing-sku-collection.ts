import { Injectable } from '@nestjs/common'
import * as pMap from 'p-map'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ProductSkuIndex } from '~/algolia-indices'
import { ImageUsedInType } from '~/constants'
import { MarketingSkuCollectionEntity, ProductSkuEntity } from '~/entities'
import { entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'

const imageProperties = [
  'headerBgImageDesktop__en',
  'headerBgImageMobile__en',
  'headerBgImageDesktop__nl',
  'headerBgImageMobile__nl',
  'headerBgImageDesktop__de',
  'headerBgImageMobile__de',
] as const

@Injectable()
@EventSubscriber()
export class MarketingSkuCollectionSubscriber implements EntitySubscriberInterface<MarketingSkuCollectionEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return MarketingSkuCollectionEntity
  }

  async afterInsert(event: InsertEvent<MarketingSkuCollectionEntity>) {
    const { entity, manager } = event

    // process header images
    await pMap(imageProperties, async (property) => {
      return entityImageAutoProcess({
        image: entity[property],
        entityId: entity.id,
        imageUsedIn: ImageUsedInType.MARKETING_SKU_COLLECTION,
        manager,
      })
    })

    if (entity.productSkus && entity.productSkus.length) {
      for (const sku of entity.productSkus) {
        await ProductSkuIndex.updateIndex({ id: sku.id, manager })
      }
    }
  }

  async afterUpdate(event: UpdateEvent<MarketingSkuCollectionEntity>) {
    const { entity, manager, updatedRelations } = event

    // process header images
    await pMap(imageProperties, async (property) => {
      return entityImageAutoProcess({
        image: entity[property],
        entityId: entity.id,
        imageUsedIn: ImageUsedInType.MARKETING_SKU_COLLECTION,
        manager,
      })
    })

    // TODO: implement it
    // if () {
    // await ProductSkuIndex.deleteIndex({ id: entity.id, manager })
    // }
  }

  async afterRemove(event: RemoveEvent<MarketingSkuCollectionEntity>) {
    const { entityId, manager } = event

    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.MARKETING_SKU_COLLECTION,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })

    // await ProductSkuIndex.deleteIndex({ id: entityId, manager })
  }
}
