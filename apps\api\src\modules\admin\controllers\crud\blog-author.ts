import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { BlogAuthorEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudBlogAuthorService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: BlogAuthorEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      avatar: {},
      posts: {},
    },
  },
})
@Controller('admin/blog-authors')
export class CrudBlogAuthorController implements CrudController<BlogAuthorEntity> {
  constructor(public service: CrudBlogAuthorService) {}
}
