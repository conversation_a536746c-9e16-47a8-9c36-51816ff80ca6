import * as Sentry from '@sentry/nestjs'
import { plainToClass } from 'class-transformer'
import { cloneDeep, isEqual, isNil } from 'lodash'
import { EntityManager, InsertEvent, ObjectLiteral, UpdateEvent } from 'typeorm'

import { envConfig } from '~/configs'
import {
  FileProcessNameType,
  FileUsedInType,
  ImageProcessNameType,
  ImageUsedInType,
  LOCALE_ENABLED_LANGUAGES,
} from '~/constants'
import { FileEntity, ImageEntity } from '~/entities'

import { s3DeleteFilesByPrefix, s3GetEntityFilePath, s3GetEntityFolderPath, s3GetUrl, s3RenameFile } from './aws-s3'
import {
  cloudinaryDeleteResourcesByPrefix,
  cloudinaryGetEntityFolderPath,
  cloudinaryGetEntityResourcePath,
  cloudinaryGetUrl,
  cloudinaryRenameResource,
} from './cloudinary'
import { deeplTranslate } from './deepl'

// prevent fake publishedAt on frontend
export const entityFixPublishedAt = ({
  event,
  type,
}:
  | {
      event: InsertEvent<ObjectLiteral>
      type: 'insert'
    }
  | {
      event: UpdateEvent<ObjectLiteral>
      type: 'update'
    }) => {
  if (!event.entity || (type === 'update' && !event.databaseEntity)) {
    return
  }
  if (type === 'insert' && event?.entity?.publishedAt) {
    event.entity.publishedAt = new Date()
  } else if (type === 'update') {
    if (!event.databaseEntity.publishedAt && typeof event?.entity?.publishedAt !== 'undefined') {
      // prevent fake publishedAt date
      if (!event.databaseEntity.publishedAt && event.entity.publishedAt) {
        event.entity.publishedAt = new Date()
      }
    }
  }
}

export const entityImageAutoProcess = async ({
  image,
  entityId,
  imageUsedIn,
  manager,
}: {
  image: ImageEntity
  entityId: string
  imageUsedIn: ImageUsedInType
  manager: EntityManager
}) => {
  if (!image?.id || !entityId || !imageUsedIn) {
    Sentry.captureMessage('Entity image auto process: missing required fields', {
      tags: {
        type: 'utils:entityImageAutoProcess',
      },
      extra: {
        image,
        entityId,
        imageUsedIn,
      },
    })
  }
  const mandatoryFields = ['publicId', 'url', 'width', 'height', 'format', 'isTemp'] as const
  if (mandatoryFields.some((field) => isNil(image[field]))) {
    image = await ImageEntity.findOne({ where: { id: image.id } })
  }
  const oldImage = cloneDeep(image)
  // Update image's usedIn field
  if (imageUsedIn) {
    if (!image.usedIn) {
      image.usedIn = [imageUsedIn]
    } else if (!image.usedIn.includes(imageUsedIn)) {
      image.usedIn.push(imageUsedIn)
    }
  }

  if (image?.processName) {
    const nameFields = ['name__en', 'name__nl', 'name__de'] as const
    const nonEmptyNameFields = nameFields.filter((field) => image[field]?.trim())
    const emptyNameFields = nameFields.filter((field) => !image[field]?.trim())
    if (emptyNameFields.length === 3 || emptyNameFields.length === 0) {
      // all fields have value or all fields are empty
      return
    }
    for (const field of emptyNameFields) {
      switch (image.processName) {
        case ImageProcessNameType.TRANSLATE: {
          const target = field.split('__')[1] as (typeof LOCALE_ENABLED_LANGUAGES)[number]
          image[field] = await deeplTranslate({
            text: image[nonEmptyNameFields[0]],
            target,
            source: nonEmptyNameFields[0].split('__')[1] as (typeof LOCALE_ENABLED_LANGUAGES)[number],
          })
          break
        }
        case ImageProcessNameType.COPY: {
          image[field] = image[nonEmptyNameFields[0]]
          break
        }
      }
    }
  }

  // fix temp image's path
  if (image.isTemp) {
    const oldPublicId = image.publicId
    const newPublicId = cloudinaryGetEntityResourcePath({ imageUsedIn, entityId, imageId: image.id })
    if (oldPublicId !== newPublicId) {
      try {
        await cloudinaryRenameResource(oldPublicId, newPublicId, true)
      } catch {
        if (envConfig.isDev) {
          console.error('Error renaming cloudinary image', oldPublicId)
        }
        return
      }
      if (envConfig.isDev) {
        console.info(`Renamed cloudinary image from ${oldPublicId} to ${newPublicId}`)
      }
      image.publicId = newPublicId
      image.url = cloudinaryGetUrl(image.publicId)
    }
    image.isTemp = false
  }
  if (!isEqual(image, oldImage)) {
    await manager.save(plainToClass(ImageEntity, image))
  }
}

// TODO: put to task queue
export const entityImageDeleteByPrefix = ({
  imageUsedIn,
  entityId,
}: {
  imageUsedIn: ImageUsedInType
  entityId: string
}) => {
  if (!entityId || !imageUsedIn) {
    Sentry.captureMessage('Entity image delete by prefix: missing required fields', {
      tags: {
        type: 'utils:entityImageDeleteByPrefix',
      },
      extra: {
        entityId,
        imageUsedIn,
      },
    })
    return
  }
  const prefix = cloudinaryGetEntityFolderPath({ imageUsedIn, entityId })
  setImmediate(async () => {
    try {
      const result = await cloudinaryDeleteResourcesByPrefix(prefix)
      if (envConfig.isDev) {
        console.info(`Deleted cloudinary images by prefix: ${prefix}`, result)
      }
    } catch (error) {
      Sentry.captureException(error)
    }
  })
}

export const entityFileAutoProcess = async ({
  file,
  entityId,
  fileUsedIn,
  manager,
}: {
  file: FileEntity
  entityId: string
  fileUsedIn: FileUsedInType
  manager: EntityManager
}) => {
  if (!file?.id || !entityId || !fileUsedIn) {
    Sentry.captureMessage('Entity file auto process: missing required fields', {
      tags: {
        type: 'utils:entityFileAutoProcess',
      },
      extra: {
        file,
        entityId,
        fileUsedIn,
      },
    })
    return
  }
  const mandatoryFields = ['url', 'path', 'size', 'mimeType', 'isTemp'] as const
  if (mandatoryFields.some((field) => isNil(file[field]))) {
    file = await FileEntity.findOne({ where: { id: file.id } })
  }
  const oldImage = cloneDeep(file)
  // Update image's usedIn field
  if (fileUsedIn) {
    if (!file.usedIn) {
      file.usedIn = [fileUsedIn]
    } else if (!file.usedIn.includes(fileUsedIn)) {
      file.usedIn.push(fileUsedIn)
    }
  }

  if (file?.processName) {
    const nameFields = ['downloadFilename__en', 'downloadFilename__nl', 'downloadFilename__de'] as const
    const nonEmptyNameFields = nameFields.filter((field) => file[field]?.trim())
    const emptyNameFields = nameFields.filter((field) => !file[field]?.trim())
    if (emptyNameFields.length === nameFields.length || emptyNameFields.length === 0) {
      // all fields have value or all fields are empty
      return
    }
    for (const field of emptyNameFields) {
      switch (file.processName) {
        case FileProcessNameType.TRANSLATE: {
          const target = field.split('__')[1] as (typeof LOCALE_ENABLED_LANGUAGES)[number]
          // get the base name and ext of this entity[nonEmptyNameFields[0]
          let basename: string
          let ext: string = ''
          const matches = file[nonEmptyNameFields[0]].match(/^(?<basename>.*)(?<ext>\..{1,4})$/)
          if (matches?.groups?.basename && matches?.groups?.ext) {
            basename = matches.groups.basename
            ext = matches.groups.ext
          } else {
            basename = file[nonEmptyNameFields[0]]
          }
          file[field] =
            (await deeplTranslate({
              text: basename,
              target,
              source: nonEmptyNameFields[0].split('__')[1] as (typeof LOCALE_ENABLED_LANGUAGES)[number],
            })) + ext
          break
        }
        case FileProcessNameType.COPY: {
          file[field] = file[nonEmptyNameFields[0]]
          break
        }
      }
    }
  }

  // fix temp image's path
  if (file.isTemp) {
    const oldPath = file.path
    const newPath = s3GetEntityFilePath({ fileUsedIn, entityId, fileId: file.id, mimeType: file.mimeType })
    if (oldPath !== newPath) {
      try {
        await s3RenameFile(oldPath, newPath, true)
      } catch {
        if (envConfig.isDev) {
          console.error('Error renaming s3 file', file.path)
        }
        return
      }
      if (envConfig.isDev) {
        console.info(`Renamed s3 file from ${oldPath} to ${newPath}`)
      }
      file.path = newPath
      file.url = s3GetUrl(file.path)
    }
    file.isTemp = false
  }

  if (!isEqual(file, oldImage)) {
    await manager.save(plainToClass(FileEntity, file))
  }
}

export const entityFileDeleteByPrefix = ({
  fileUsedIn,
  entityId,
}: {
  fileUsedIn: FileUsedInType
  entityId: string
}) => {
  if (!entityId || !fileUsedIn) {
    Sentry.captureMessage('Entity file delete by prefix: missing required fields', {
      tags: {
        type: 'utils:entityFileDeleteByPrefix',
      },
      extra: {
        entityId,
        fileUsedIn,
      },
    })
    return
  }
  const prefix = s3GetEntityFolderPath({ fileUsedIn, entityId })
  setImmediate(async () => {
    try {
      const result = await s3DeleteFilesByPrefix(prefix)
      if (envConfig.isDev) {
        console.info(`Deleted s3 files by prefix: ${prefix}`, result)
      }
    } catch (error) {
      Sentry.captureException(error)
    }
  })
}
