export enum ProductSkuGrading {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
}

export enum ProductSkuColor {
  BLACK = 'BLACK',
  WHITE = 'WHITE',
  GREY = 'GREY',
  BLUE = 'BLUE',
  GREEN = 'GREEN',
  RED = 'RED',
  PINK = 'PINK',
  ORANGE = 'ORANGE',
  PURPLE = 'PURPLE',
  YELLOW = 'YELLOW',
  GOLDEN = 'GOLDEN',
  SILVER = 'SILVER',
  BRONZE = 'BRONZE',
}

export enum ProductSkuSourceType {
  C2C = 'C2C',
  C2C_PREPAID = 'C2C_PREPAID',
  C2B_PREPAID = 'C2B_PREPAID',
  EXTERNAL_VALYUU_RETURN = 'EXTERNAL_VALYUU_RETURN',
  EXTERNAL_COOLBLUE = 'EXTERNAL_COOLBLUE',
  EXTERNAL_REFURBED_SURF = 'EXTERNAL_REFURBED_SURF',
  EXTERNAL_OTHER = 'EXTERNAL_OTHER',
}

export enum ProductSkuBtwType {
  NONE = 'NONE',
  '21%' = '21%',
}

export const CProductBtwPercent = {
  [ProductSkuBtwType.NONE]: 0,
  [ProductSkuBtwType['21%']]: 21,
}

export enum ProductSkuExtraAttributeType {
  KEYBOARD = 'KEYBOARD',
  OTHER = 'OTHER',
}

export enum ProductSkuExtraTestedItemType {
  BATTERY = 'BATTERY',
  DEFECTS = 'DEFECTS',
  OTHER = 'OTHER',
}

export const PRODUCT_SKU_MIN_SLUG_NUMBER = 1_000
