/**
 * A carrier represented by a Sendcloud code
 * @example ICarrierCode.POSTNL
 */
export enum CarrierCode {
  BPOST = 'bpost',
  BRT = 'brt',
  BUDBEE = 'budbee',
  CHRONOPOST = 'chronopost',
  COLISPRIVE = 'colisprive',
  COLISSIMO = 'colissimo',
  CORREOS = 'correos',
  CORREOS_EXPRESS = 'correos_express',
  DP = 'dp',
  DELIVENGO = 'delivengo',
  DHL = 'dhl',
  DHL_DE = 'dhl_de',
  DHL_EXPRESS = 'dhl_express',
  DPD = 'dpd',
  DPD_AT = 'dpd_at',
  DPD_GB = 'dpd_gb',
  FADELLO = 'fadello',
  FAIRSENDEN = 'fairsenden',
  GLS_DE = 'gls_de',
  GLS_IT = 'gls_it',
  HERMES_GB = 'hermes_gb',
  HOMERR = 'homerr',
  LETTRESUIVIE = 'lettresuivie',
  MONDIAL_RELAY = 'mondial_relay',
  MRW = 'mrw',
  POSTAT = 'postat',
  POSTE_ITALIANE = 'poste_italiane',
  POSTNL = 'postnl',
  POSTNL_FULFILMENT = 'postnl_fulfilment',
  RJP = 'rjp',
  ROYAL_MAIL = 'royal_mail',
  SANDD = 'sandd',
  SENDCLOUD = 'sendcloud',
  SEUR = 'seur',
  TRUNKRS = 'trunkrs',
  UPS = 'ups',
  VAN_STRAATEN = 'van_straaten',
  VIATIM = 'viatim',
}
