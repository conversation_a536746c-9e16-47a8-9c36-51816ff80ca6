import slugify from '@sindresorhus/slugify'
import { Input, InputProps } from 'antd'
import { create } from 'antx'
import { isNil } from 'lodash'
import { useEffect, useState } from 'react'
import { AiOutlineEdit } from 'react-icons/ai'
import { MdRefresh } from 'react-icons/md'

export type IInputSlugProps = InputProps & {
  value?: string
  onChange?: (value: string) => void
  sourceString?: string
  allowEdit?: boolean
  autoFollow?: boolean
}

export const InputSlug = create(({ value, onChange, sourceString, allowEdit, autoFollow }: IInputSlugProps) => {
  // TODO: solve duplication
  const [editable, setEditable] = useState(allowEdit ?? true)
  const [slugValue, setSlugValue] = useState<string | undefined>(value)

  useEffect(() => {
    if (autoFollow && !isNil(sourceString)) {
      setSlugValue(slugify(sourceString.replace(/\+/g, 'plus').toLowerCase()))
    }
  }, [sourceString, autoFollow])
  useEffect(() => {
    if (!isNil(slugValue) && onChange) {
      onChange(slugValue)
    }
  }, [slugValue, onChange])

  return (
    <Input
      onChange={({ target: { value } }) => setSlugValue(value)}
      disabled={!editable}
      value={slugValue}
      className="[&.ant-input-group-wrapper_.ant-input-group-addon>span]:flex [&.ant-input-group-wrapper_.ant-input-group-addon>span]:h-[30px] [&.ant-input-group-wrapper_.ant-input-group-addon>span]:w-9 [&.ant-input-group-wrapper_.ant-input-group-addon>span]:cursor-pointer [&.ant-input-group-wrapper_.ant-input-group-addon>span]:items-center [&.ant-input-group-wrapper_.ant-input-group-addon>span]:justify-center [&.ant-input-group-wrapper_.ant-input-group-addon]:bg-black/[0.02] [&.ant-input-group-wrapper_.ant-input-group-addon]:p-0 [&.ant-input-group-wrapper_.ant-input-group-addon]:text-black/[0.88]"
      addonAfter={
        editable ? (
          <span
            title="Generate slug"
            onClick={() =>
              !isNil(sourceString) && setSlugValue(slugify(sourceString.replace(/\+/g, 'plus').toLowerCase()))
            }
          >
            <MdRefresh />
          </span>
        ) : (
          <span onClick={() => setEditable(true)} title="Edit slug">
            <AiOutlineEdit />
          </span>
        )
      }
    />
  )
})
