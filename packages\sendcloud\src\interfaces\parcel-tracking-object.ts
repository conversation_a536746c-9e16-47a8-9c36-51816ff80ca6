import { CarrierCode } from '../constants'

// A definition of a detailed tracking blob.
export type IParcelTrackingResponse = {
  // Unique ID for the parcel
  // Example: '629'
  parcel_id: string
  // A carrier represented by a Sendcloud code
  // Example: 'postnl'
  carrier_code: string
  // Timestamp (ISO format) that indicates when the parcel was first tracked by Sendcloud's systems.
  // Example: '2022-01-25 14:42:03.277703+00:00'
  created_at: string
  // Link to the carrier's Tracking page for this parcel
  // Example: 'https://tracking.sendcloud.sc/forward?carrier=postnl&code=3SYZXG132912330&destination=NL&lang=en-us&source=NL&type=parcel&verification=5611+EM&servicepoint_verification=&created_at=2022-01-25'
  carrier_tracking_url: string
  // Link to Sendcloud's Tracking page for this parcel. Returns a valid URL only if the tracking page for the brand associated with the parcel has been published.
  // Example: 'https://my-bakery.shipping-portal.com/tracking/?country=nl&tracking_number=3SYZXG132912330&postal_code=5611+EM'
  sendcloud_tracking_url: string
  // Indicates whether this parcel is a return (incoming) shipment to the merchant.
  // Example: false
  is_return: boolean
  // Indicates whether this parcel is delivered to a service point (e.g. a supermarket, as opposed to a home address)
  // Example: false
  is_to_service_point: boolean
  // Indicates whether this parcel will be delivered to a mail box
  // Example: false
  is_mail_box: boolean
  // Day when the parcel will be delivered (estimate, no timestamp)
  // Example: '2022-01-26'
  expected_delivery_date: string | Date
  // List of shipping statuses for the parcel
  statuses: IParcelTrackingStatus[]
}

export type IParcelTrackingStatus = {
  // Timestamp (ISO format): when did the carrier move the parcel to this status
  // Example: '2022-01-25 16:42:03.277703+00:00'
  carrier_update_timestamp: string
  // ID of the historical status
  // Example: '1270787363'
  parcel_status_history_id: string
  // Current delivery status of the parcel. Aggregates variations of status messages sent by multiple carriers with similar meanings, into one user-friendly status message issued by Sendcloud.
  // Example: 'delivered'
  parent_status: string
  // A carrier represented by a Sendcloud code
  // Example: 'postnl'
  carrier_code: CarrierCode
  // Status description specified by the carrier, more detailed than parent_status
  // Example: 'Shipment delivered, no signature'
  carrier_message: string
}
