// TODO: to be deleted
import { ObjectType } from '@nestjs/graphql'
import { BaseEntity, Column, Entity, Index, ManyToOne, PrimaryColumn, Relation, RelationId } from 'typeorm'

import {
  type IProductVariantConditionCombination,
  type IQuestionTypeCondition,
  type IQuestionTypeConditionOption,
  ProductVariantConditionCombinationEntity,
  QuestionTypeConditionEntity,
  QuestionTypeConditionOptionEntity,
} from '~/entities'

@ObjectType('ProductVariantConditionCombinationChoice')
@Entity('product_variant_condition_combination_choice')
export class ProductVariantConditionCombinationChoiceEntity extends BaseEntity {
  @ManyToOne(() => QuestionTypeConditionEntity, { nullable: false })
  condition: Relation<QuestionTypeConditionEntity>

  @PrimaryColumn()
  @RelationId((choice: ProductVariantConditionCombinationChoiceEntity) => choice.condition)
  conditionId: string

  @ManyToOne(() => QuestionTypeConditionOptionEntity, { nullable: false, onDelete: 'CASCADE' })
  option: Relation<QuestionTypeConditionOptionEntity>

  @PrimaryColumn()
  @RelationId((choice: ProductVariantConditionCombinationChoiceEntity) => choice.option)
  optionId: string

  @ManyToOne(() => ProductVariantConditionCombinationEntity, (combination) => combination.choices, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  combination: Relation<ProductVariantConditionCombinationEntity>

  @PrimaryColumn()
  @RelationId((choice: ProductVariantConditionCombinationChoiceEntity) => choice.combination)
  combinationId: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number
}

export type IProductVariantConditionCombinationChoice = Omit<
  ProductVariantConditionCombinationChoiceEntity,
  keyof BaseEntity
> & {
  condition: IQuestionTypeCondition
  option: IQuestionTypeConditionOption
  combination: IProductVariantConditionCombination
}
