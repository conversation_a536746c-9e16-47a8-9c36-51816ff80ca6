import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { ProductSkuOriginalAccessoryEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudProductSkuOriginalAccessoryService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ProductSkuOriginalAccessoryEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      thumbnail: {},
    },
  },
})
@Controller('admin/original-accessories')
export class CrudProductSkuOriginalAccessoryController implements CrudController<ProductSkuOriginalAccessoryEntity> {
  constructor(public service: CrudProductSkuOriginalAccessoryService) {}

  get base(): CrudController<ProductSkuOriginalAccessoryEntity> {
    return this
  }
}
