import { Create, useForm, useSelect } from '@refinedev/antd'
import type { HttpError, IResourceComponentsProps } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type { IBlogAuthor, IBlogPost, IBlogTag } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber, Select, TextArea, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import {
  InputImage,
  InputLanguageTab,
  InputMDXEditor,
  InputMultiLang,
  InputPublish,
  InputSlug,
  LanguageTabChoices,
} from '~/components'

export const BlogPostCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<IBlogPost, HttpError, IBlogPost>()
  const id = useMemo(() => uuid(), [])
  const publishedAt = Form.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const { selectProps: tagSelectProps } = useSelect<IBlogTag>({
    resource: 'admin/blog-tags',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: authorSelectProps } = useSelect<IBlogAuthor>({
    resource: 'admin/blog-authors',
    optionLabel: 'name',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Watch list={['title__en', 'title__nl', 'title__de', 'title__pl']}>
          {([title__en, title__nl, title__de, title__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: title__en, nl: title__nl, de: title__de, pl: title__pl }}
                targetLang="en"
                label="Name (English)"
                name="title__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={title__en}
                className={clsHideEn}
                autoFollow
              />
              <TextArea
                label="Summary (English)"
                name="summary__en"
                rules={['required']}
                className={cx(clsHideEn, 'col-span-2')}
              />
              <InputMDXEditor
                label="Content (English)"
                name="content__en"
                rules={['required']}
                className={cx(clsHideEn, 'col-span-2')}
              />
              <InputMultiLang
                sources={{ en: title__en, nl: title__nl, de: title__de, pl: title__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="title__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={title__nl}
                className={clsHideNl}
                autoFollow
              />
              <TextArea
                label="Summary (Dutch)"
                name="summary__nl"
                rules={['required']}
                className={cx(clsHideNl, 'col-span-2')}
              />
              <InputMDXEditor
                label="Content (Dutch)"
                name="content__nl"
                rules={['required']}
                className={cx(clsHideNl, 'col-span-2')}
              />
              <InputMultiLang
                sources={{ en: title__en, nl: title__nl, de: title__de, pl: title__pl }}
                targetLang="de"
                label="Name (German)"
                name="title__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={title__de}
                className={clsHideDe}
                autoFollow
              />
              <TextArea
                label="Summary (German)"
                name="summary__de"
                rules={['required']}
                className={cx(clsHideDe, 'col-span-2')}
              />
              <InputMDXEditor
                label="Content (German)"
                name="content__de"
                rules={['required']}
                className={cx(clsHideDe, 'col-span-2')}
              />
              <InputMultiLang
                sources={{ en: title__en, nl: title__nl, de: title__de, pl: title__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="title__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
              <InputSlug
                label="Slug (Polish)"
                name="slug__pl"
                rules={['required']}
                allowEdit={false}
                sourceString={title__pl}
                className={clsHidePl}
                autoFollow
              />
              <TextArea
                label="Summary (Polish)"
                name="summary__pl"
                rules={['required']}
                className={cx(clsHidePl, 'col-span-2')}
              />
              <InputMDXEditor
                label="Content (Polish)"
                name="content__pl"
                rules={['required']}
                className={cx(clsHidePl, 'col-span-2')}
              />
              <Select label="Tag" name="tagId" rules={['required']} {...(tagSelectProps as any)} />
              <Select label="Author" name="authorId" rules={['required']} {...(authorSelectProps as any)} />
              <InputNumber label="Minutes to read" precision={0} min={0} name="minuteRead" rules={['required']} />
              <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
              <InputImage
                label="Hero image"
                name="heroImage"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.BLOG_POST}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en: title__en, name__nl: title__nl, name__de: title__de }}
              />
            </>
          )}
        </Watch>
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
