import { DataSource, DataSourceOptions } from 'typeorm'
import { SnakeNamingStrategy } from 'typeorm-naming-strategies'

import { envConfig, redisConfig } from '~/configs'
import * as entityImports from '~/entities'

import * as migrationImports from '../../data/migrations'

const entities = Object.values(entityImports).filter((entity) => entity?.name?.endsWith('Entity'))

const migrations = Object.values(migrationImports).filter((migration) => /\d{13}$/.test(migration?.name ?? ''))

export const typeOrmConfig = {
  type: 'postgres',
  url: envConfig.DATABASE_URL,
  entities,
  migrations,
  migrationsTableName: 'typeorm_migration',
  namingStrategy: new SnakeNamingStrategy(),
  synchronize: true,
  logging: false,
  dropSchema: false,
  cache: {
    type: 'ioredis',
    options: redisConfig,
    ignoreErrors: envConfig.isProd,
  },
} as DataSourceOptions

// For migration generation
const AppDataSource = new DataSource(typeOrmConfig)

export default AppDataSource
