import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { CharityEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudCharityService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: CharityEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      bankAccount: {},
    },
  },
})
@Controller('admin/charities')
export class CrudCharityController implements CrudController<CharityEntity> {
  constructor(public service: CrudCharityService) {}
}
