import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateVariantQuestionTypeId1712892873531 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    if (await queryRunner.hasTable('product_variant')) {
      await queryRunner.query(`
        UPDATE product_variant
        SET question_type_id = (
          SELECT product_model.question_type_id
          FROM product_model
          WHERE product_model.id = product_variant.model_id
        );
      `)
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
