import { Args, Mutation, Resolver } from '@nestjs/graphql'

import { ProductIssueType } from '~/constants'
import {
  CreateProductIssueModelQuestionInput,
  CreateProductIssueNoSearchResultInput,
  CreateProductIssueProductCategoryInput,
} from '~/dtos'
import { ProductIssueEntity } from '~/entities'
import { ProductIssueService } from '~/services'

@Resolver()
export class ProductIssueResolver {
  constructor(private readonly service: ProductIssueService) {}

  @Mutation(() => ProductIssueEntity)
  async createProductIssueNoSearchResult(
    @Args() { email, type, issue, keyword }: CreateProductIssueNoSearchResultInput
  ) {
    return this.service.create({ email, type, productIssue: issue.trim(), keyword })
  }

  @Mutation(() => ProductIssueEntity)
  async createProductIssueCategory(@Args() { email, issue }: CreateProductIssueProductCategoryInput) {
    return this.service.create({
      email,
      type: ProductIssueType.SELLER_CATEGORY,
      productIssue: issue.trim(),
    })
  }

  @Mutation(() => ProductIssueEntity)
  async createProductIssueModelQuestion(
    @Args() { email, issue, modelSlug, lang }: CreateProductIssueModelQuestionInput
  ) {
    return this.service.create({
      email,
      type: ProductIssueType.SELLER_MODEL_QUESTION,
      productIssue: issue.trim(),
      slug: modelSlug,
      lang,
    })
  }
}
