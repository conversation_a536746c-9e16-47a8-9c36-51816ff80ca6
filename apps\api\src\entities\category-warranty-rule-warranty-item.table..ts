import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { CategoryWarrantyRuleEntity, type ICategoryWarrantyRule, type IWarranty, WarrantyEntity } from '~/entities'

@Entity('category_warranty_rule_warranty_item')
export class CategoryWarrantyRuleWarrantyItemEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Index()
  @Min(0)
  price: number

  @ManyToOne(() => WarrantyEntity, { nullable: false, onDelete: 'CASCADE' })
  warranty: Relation<WarrantyEntity>

  @Column()
  @RelationId((categoryWarrantyRule: CategoryWarrantyRuleWarrantyItemEntity) => categoryWarrantyRule.warranty)
  warrantyId: string

  @Column()
  isDefault: boolean = false

  @ManyToOne(() => CategoryWarrantyRuleEntity, (warrantyByCategory) => warrantyByCategory.warrantyItems, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  rule: Relation<CategoryWarrantyRuleEntity>

  @Column()
  @RelationId((categoryWarrantyRule: CategoryWarrantyRuleWarrantyItemEntity) => categoryWarrantyRule.rule)
  ruleId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ICategoryWarrantyRuleWarrantyItem = Omit<CategoryWarrantyRuleWarrantyItemEntity, keyof BaseEntity> & {
  warranty: IWarranty
  rule: ICategoryWarrantyRule
}
