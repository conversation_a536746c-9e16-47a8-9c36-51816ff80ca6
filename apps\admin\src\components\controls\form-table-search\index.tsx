import { Form, Popover } from 'antd'
import { FormProps } from 'antd/lib'
import { Input, Watch } from 'antx'
import { FC, KeyboardEvent } from 'react'
import { AiOutlineClose, AiOutlineSearch } from 'react-icons/ai'

export type IFormTableSearchProps = {
  searchFormProps: Partial<FormProps>
  title?: string
}

export const FormTableSearch: FC<IFormTableSearchProps> = ({ searchFormProps, title }) => {
  const [form] = Form.useForm()

  return (
    <Form {...searchFormProps} form={form} layout="inline" className="!block">
      <Watch name="search">
        {(search) => (
          <Popover content={title}>
            <Input
              prefix={<AiOutlineSearch className="text-gray-300" />}
              suffix={
                search ? (
                  <AiOutlineClose
                    className="cursor-pointer text-gray-500"
                    onClick={() => {
                      form.setFieldValue('search', '')
                      form.submit()
                    }}
                  />
                ) : null
              }
              placeholder="Search..."
              className="!mr-0 w-52"
              name="search"
              onKeyUp={({ key }: KeyboardEvent<HTMLInputElement>) => {
                if (key === 'Enter') {
                  form.submit()
                }
              }}
            />
          </Popover>
        )}
      </Watch>
    </Form>
  )
}
