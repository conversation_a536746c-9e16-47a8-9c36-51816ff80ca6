import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageUsedInType } from '~/constants'
import { BrandEntity } from '~/entities'
import { entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'
import { entityFixPublishedAt } from '~/utils/entity-subscriber'

@Injectable()
@EventSubscriber()
export class BrandSubscriber implements EntitySubscriberInterface<BrandEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return BrandEntity
  }

  async beforeInsert(event: InsertEvent<BrandEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async afterInsert(event: InsertEvent<BrandEntity>) {
    const { entity, manager } = event

    // process image
    await entityImageAutoProcess({
      image: entity.image,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.BRAND,
      manager,
    })
  }

  async beforeUpdate(event: UpdateEvent<BrandEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }

  async afterUpdate(event: UpdateEvent<BrandEntity>) {
    const { entity, manager } = event

    // process image
    await entityImageAutoProcess({
      image: entity.image ?? { id: entity.imageId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.BRAND,
      manager,
    })
  }

  async afterRemove(event: RemoveEvent<BrandEntity>) {
    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.BRAND,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
