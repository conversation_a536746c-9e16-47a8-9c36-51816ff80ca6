import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IProductModel,
  type IProductModelAttributeOption,
  ProductModelAttributeOptionEntity,
  ProductModelEntity,
} from '~/entities'

@ObjectType('ProductModelAttribute')
@Entity('product_model_attribute')
export class ProductModelAttributeEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column()
  question__en: string

  @Field()
  @Column()
  question__nl: string

  @Field()
  @Column()
  question__de: string

  @Field()
  @Column()
  question__pl: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__en: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__nl: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__de: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__pl: string

  @Column({ default: true })
  hasPriceImpact: boolean

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @ManyToOne(() => ProductModelEntity, (productModel) => productModel.attributes, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  model: Relation<ProductModelEntity>

  @Column()
  @RelationId((attribute: ProductModelAttributeEntity) => attribute.model)
  modelId: string

  @Field(() => [ProductModelAttributeOptionEntity])
  @OneToMany(() => ProductModelAttributeOptionEntity, (option) => option.attribute, { nullable: false, cascade: true })
  options: Relation<ProductModelAttributeOptionEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductModelAttribute = Omit<ProductModelAttributeEntity, keyof BaseEntity> & {
  options: IProductModelAttributeOption[]
  model: IProductModel
}
