import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { LocaleLanguageEntity } from '~/entities'

export class CrudLocaleLanguageService extends TypeOrmCrudService<LocaleLanguageEntity> {
  constructor(
    @InjectRepository(LocaleLanguageEntity)
    repo: Repository<LocaleLanguageEntity>
  ) {
    super(repo)
  }
}
