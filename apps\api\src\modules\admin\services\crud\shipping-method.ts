import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import {
  CarrierCode,
  CountryCode,
  findShippingOptions,
  findShippingProducts,
  getShippingPrice,
  IShippingOption,
  IShippingProduct,
} from '@valyuu/sendcloud'
import { Repository } from 'typeorm'

import { type IShippingMethod, ShippingMethodEntity } from '~/entities'

export class CrudShippingMethodService extends TypeOrmCrudService<ShippingMethodEntity> {
  constructor(@InjectRepository(ShippingMethodEntity) repo: Repository<ShippingMethodEntity>) {
    super(repo)
  }

  async findBetweenCountries({
    fromCountry,
    toCountry,
    carrier,
    isReturn,
    isProduction,
    paperless,
  }: {
    fromCountry: string
    toCountry: string
    carrier?: CarrierCode
    isReturn: boolean
    isProduction: boolean
    paperless?: boolean
  }): Promise<Omit<IShippingMethod, 'createdAt' | 'updatedAt'>[]> {
    if (!isReturn) {
      paperless = undefined
    }

    // Use findShippingOptions for Poland to Poland shipments
    // if (fromCountry === CountryCode.PL && toCountry === CountryCode.PL) {
    //   const options = await findShippingOptions({
    //     from_country_code: CountryCode.PL,
    //     to_country_code: CountryCode.PL,
    //     shipping_options_filter: {
    //       returns: isReturn,
    //     },
    //   })

    //   return (options ?? [])
    //     .map((option: IShippingOption) => {
    //       return {
    //         id: String(option.id),
    //         name: option.name,
    //         carrier: option.carrier as CarrierCode,
    //         shippingProductName: option.shipping_product_name || option.name,
    //         shippingProductCode: option.id, // Using option id as product code since it's not available in shipping options
    //         minWeightG: option.weight_range?.min_weight ?? 0,
    //         maxWeightG: option.weight_range?.max_weight ?? 30000,
    //         isPaperless: option.functionalities?.manually ? false : true, // Assuming non-manual means paperless
    //         isReturn,
    //       }
    //     })
    //     .filter(
    //       (method) =>
    //         method.minWeightG < 10 &&
    //         (paperless === undefined ? true : method.isPaperless === paperless) &&
    //         (isProduction ? method.carrier !== CarrierCode.SENDCLOUD : true)
    //     )
    // }

    // Use existing findShippingProducts for other country combinations
    const products = await findShippingProducts({
      from_country: fromCountry as CountryCode,
      to_country: toCountry as CountryCode,
      returns: isReturn,
      ...(carrier ? { carrier } : isProduction ? {} : { carrier: CarrierCode.SENDCLOUD }),
    })

    return (products ?? []).flatMap((product: IShippingProduct) =>
      (product.methods ?? [])
        .map((method) => {
          return {
            id: String(method.id),
            name: method.name,
            carrier: product.carrier as CarrierCode,
            shippingProductName: product.name,
            shippingProductCode: product.code,
            minWeightG: method.properties?.min_weight ?? product.weight_range.min_weight,
            maxWeightG: method.properties?.max_weight ?? product.weight_range.max_weight,
            isPaperless: method.functionalities?.labelless ? true : false,
            isReturn,
          }
        })
        .filter(
          (method) =>
            method.minWeightG < 10 &&
            (paperless === undefined ? true : method.isPaperless === paperless) &&
            (isProduction ? method.carrier !== CarrierCode.SENDCLOUD : true)
        )
    )
  }

  async getPrice({
    fromCountry,
    toCountry,
    methodId,
    weightKg,
  }: {
    fromCountry: string
    toCountry: string
    methodId: number | string
    weightKg: number
  }) {
    return (
      await getShippingPrice({
        from_country: fromCountry as CountryCode,
        to_country: toCountry as CountryCode,
        shipping_method_id: methodId,
        weight: weightKg,
        weight_unit: 'kilogram',
      })
    )?.[0]
  }
}
