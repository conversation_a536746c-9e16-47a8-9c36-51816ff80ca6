import { MigrationInterface, QueryRunner } from 'typeorm'

/*
I want to create some PostgreSQL commands to update records in question_type_problem table. Here is the table structure:
```sql
create table "question_type"
(
	id  	uuid default uuid_generate_v4() not null,
	internal_name  	varchar not null,
	created_at  	timestamp default now() not null,
	updated_at  	timestamp default now() not null,
	published_at  	timestamp,
	constraint PK_8ee0ca6ea5ac1770d54b7ff5ca4 primary key (id)

) tablespace pg_default;
create table "question_type_problem"
(
	id  	varchar not null,
	key  	varchar not null,
	name__en  	varchar not null,
	name__nl  	varchar not null,
	name__de  	varchar not null,
	sort_order  	integer default 0 not null,
	question_type_id  	uuid not null,
	created_at  	timestamp default now() not null,
	updated_at  	timestamp default now() not null,
	desc__en  	varchar,
	desc__nl  	varchar,
	desc__de  	varchar,
	constraint PK_271b64330ef64ec3a0809c6212f primary key (id),
	constraint FK_e7b45f6e05e657a1b9bb9132639 foreign key (question_type_id) references question_type(id) on delete cascade

) tablespace pg_default;
```
Now here comes the data:
```tsv
Key	Question types	Name (EN) - New	Name (NL) - New	Name (DE) - New	Desc (EN)	Desc (NL)	Desc (DE)																			
CELLULARSIGNAL	iPhone1, iPhone3, SamsungTabs, AndroidPhone2, AndroidFlipandFold, iPhone2, iPhone4, AndroidPhone1, iPad2, iPad1	Cellular signal	Mobiel netwerk	Mobilfunksignal	No issues with cellular signal reception or network connectivity	Geen problemen met mobiel signaalontvangst of netwerkverbinding	Keine Probleme mit Teile																			
```
What I want to do is:
1. Empty desc__en, desc__nl, desc__de fields, set to null for all question_type_problem records
2. Update question_type_problem based on the data in the above tsv, the matching rule is like: in the tsv, Question types column means this row represents all question_type_problem records which their related question_type table's internal_name is one of its comman (and space) separated values. So one row can provide the data for many question_type_problem records. And the Key column is the question_type_problem used to identify the record. If you can find  the records, then update the records' name__{lang} and desc__{lang} fields according to the respective tsv columns
3. Update question_type_problem.key to WIFIBLUETOOTH if its value is WIFI in question_type AppleWatch


Write migration scripts to update records, in full, do not omit any rows, there should be 24 rows
*/

export class UpdateProblemDesc1732844916750 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add desc__en if it doesn't exist
    await queryRunner.query(`
      ALTER TABLE question_type_problem 
      ADD COLUMN IF NOT EXISTS desc__en varchar
    `)

    // Add desc__nl if it doesn't exist
    await queryRunner.query(`
      ALTER TABLE question_type_problem 
      ADD COLUMN IF NOT EXISTS desc__nl varchar
    `)

    // Add desc__de if it doesn't exist
    await queryRunner.query(`
      ALTER TABLE question_type_problem 
      ADD COLUMN IF NOT EXISTS desc__de varchar
    `)

    // First clear all descriptions
    await queryRunner.query(`
      UPDATE question_type_problem 
      SET desc__en = NULL, desc__nl = NULL, desc__de = NULL
    `)

    // Then update records based on the TSV data
    const updates = [
      {
        key: 'CELLULARSIGNAL',
        questionTypes: [
          'iPhone1',
          'iPhone3',
          'SamsungTabs',
          'AndroidPhone2',
          'AndroidFlipandFold',
          'iPhone2',
          'iPhone4',
          'AndroidPhone1',
          'iPad2',
          'iPad1',
        ],
        names: ['Cellular signal', 'Mobiel netwerk', 'Mobilfunksignal'],
        descs: [
          'No issues with cellular signal reception or network connectivity',
          'Geen problemen met mobiel signaalontvangst of netwerkverbinding',
          'Keine Probleme mit Mobilfunkempfang oder Netzwerkverbindung',
        ],
      },
      {
        key: 'BUTTONS',
        questionTypes: [
          'iPhone1',
          'iPhone3',
          'SamsungTabs',
          'AndroidPhone2',
          'iPhone2',
          'AndroidPhone1',
          'AndroidFlipandFold',
          'iPad1',
          'iPad2',
          'iPhone4',
        ],
        names: ['Buttons', 'Knoppen', 'Buttons'],
        descs: [
          'All physical buttons work properly and are not damaged',
          'Alle fysieke knoppen werken correct en zijn niet beschadigd',
          'Alle physischen Tasten funktionieren ordnungsgemäß und sind nicht beschädigt',
        ],
      },
      {
        key: 'CAMERALENSDEFECT',
        questionTypes: [
          'iPad1',
          'iPhone1',
          'iPhone3',
          'SamsungTabs',
          'AndroidPhone2',
          'iPad2',
          'AndroidFlipandFold',
          'iPhone2',
          'AndroidPhone1',
          'iPhone4',
        ],
        names: ['Camera', 'Camera', 'Kamera'],
        descs: [
          'No lens defects or broken camera',
          'Geen lensdefecten of kapotte camera',
          'Keine Linsendefekte oder defekte Kamera',
        ],
      },
      {
        key: 'MAGNETICCHARGERMISSINGNOTWORKING',
        questionTypes: ['AppleWatch'],
        names: ['Magnetic charger', 'Magnetische oplader', 'Magnetisches Ladegerät'],
        descs: [
          'Magnetic charging connection works properly and charges device',
          'Magnetische oplaadverbinding werkt correct en laadt apparaat op',
          'Magnetische Ladeverbindung funktioniert und lädt das Gerät',
        ],
      },
      {
        key: 'TOUCHPENSTYLUS',
        questionTypes: ['AndroidPhone2', 'AndroidFlipandFold', 'AndroidPhone1', 'SamsungTabs'],
        names: ['Touch pen (stylus)', 'Touchpen (stylus)', 'Eingabestift (Stylus)'],
        descs: [
          'Stylus is included and works properly',
          'Touchpen is meegeleverd en werkt correct',
          'Eingabestift ist enthalten und funktioniert einwandfrei',
        ],
      },
      {
        key: 'ADAPTERUSBHDMIPORTS',
        questionTypes: ['Switch(OLED)', 'Switch(Lite&2017&2019)'],
        names: ['Adapter/USB/HDMI port(s)', 'Adapter/USB/HDMI-poort(en)', 'Adapter/USB/HDMI-Anschlüsse'],
        descs: [
          'All ports are working properly and can successfully connect to external devices',
          'Alle poorten werken correct en kunnen succesvol verbinding maken met externe apparaten',
          'Alle Anschlüsse funktionieren einwandfrei und können erfolgreich mit externen Geräten verbunden werden',
        ],
      },
      {
        key: 'DISKSDCARDREADER',
        questionTypes: ['Macbook2', 'Macbook1'],
        names: ['Disk or SD card reader', 'Schijf- of SD-kaartlezer', 'Festplatten- oder SD-Kartenleser'],
        descs: [
          'Card reader recognizes and can read/write data from external storage devices',
          'Kaartlezer herkent en kan data lezen/schrijven van externe opslagapparaten',
          'Kartenleser erkennt externe Speichergeräte und kann Daten lesen/schreiben',
        ],
      },
      {
        key: 'BLUETOOTHWIFI',
        questionTypes: [
          'iPhone1',
          'iPhone3',
          'SamsungTabs',
          'iPad2',
          'AndroidFlipandFold',
          'AndroidPhone1',
          'iPhone4',
          'iPad1',
          'iPhone2',
          'AndroidPhone2',
        ],
        names: ['Bluetooth or Wi-Fi', 'Bluetooth of Wi-Fi', 'Bluetooth oder Wi-Fi'],
        descs: [
          'No connection issues with Bluetooth and Wi-Fi functionality',
          'Geen verbindingsproblemen met Bluetooth en Wi-Fi functionaliteit',
          'Keine Verbindungsprobleme mit Bluetooth- und Wi-Fi-Funktionalität',
        ],
      },
      {
        key: 'SCREEN',
        questionTypes: ['SamsungTabs', 'iPad2', 'iPad1'],
        names: ['Screen', 'Scherm', 'Bildschirm'],
        descs: [
          'No burnt screen, pixel errors, color deviations, touch functionality issues or non-original parts',
          'Geen verbrand scherm, pixelfouten, kleurafwijkingen, problemen met touch-functionaliteit of niet originele onderdelen',
          'Kein eingebranntes Display, keine Pixelfehler, Farbabweichungen, Touch-Funktionalitätsprobleme oder nicht-originale Teile',
        ],
      },
      {
        key: 'CONNECTINGTOCOMPUTER',
        questionTypes: [
          'AndroidPhone2',
          'AndroidFlipandFold',
          'iPhone2',
          'iPhone4',
          'iPad2',
          'iPad1',
          'AndroidPhone1',
          'iPhone3',
          'iPhone1',
          'SamsungTabs',
        ],
        names: ['Connecting to computer', 'Aansluiten op computer', 'Verbindung zum Computer'],
        descs: [
          'Device connects and communicates properly with computers',
          'Apparaat maakt correct verbinding en communiceert met computers',
          'Gerät verbindet und kommuniziert ordnungsgemäß mit Computern',
        ],
      },
      {
        key: 'FIRMWARELOCKBIOSPASSWORD',
        questionTypes: ['Macbook2', 'Macbook1'],
        names: [
          'Firmware lock/BIOS password',
          'Firmware-vergrendeling/BIOS-wachtwoord',
          'Firmware-Sperre/BIOS-Passwort',
        ],
        descs: [
          'Device is not locked with firmware lock or BIOS password',
          'Apparaat is niet vergrendeld met firmware-vergrendeling of BIOS-wachtwoord',
          'Gerät ist nicht durch Firmware-Sperre oder BIOS-Passwort gesperrt',
        ],
      },
      {
        key: 'SOUNDSPEAKERSMICROPHONE',
        questionTypes: [
          'SamsungTabs',
          'AndroidPhone2',
          'iPad2',
          'AndroidFlipandFold',
          'AndroidPhone1',
          'iPad1',
          'iPhone4',
          'iPhone2',
          'iPhone3',
          'iPhone1',
        ],
        names: ['Speakers or microphone', 'Speakers of microfoon', 'Lautsprecher oder Mikrofon'],
        descs: [
          'No problems with speakers, microphones or audio quality',
          'Geen problemen met luidsprekers, microfoons of audiokwaliteit',
          'Keine Probleme mit Lautsprechern, Mikrofonen oder Audioqualität',
        ],
      },
      {
        key: 'PORTSUSBDATAETHERNETOROTHERS',
        questionTypes: ['Macbook2', 'Macbook1'],
        names: [
          'Ports (USB, data, ethernet, or others)',
          'Poorten (USB, data, ethernet of andere)',
          'Anschlüsse (USB, Daten, Ethernet oder andere)',
        ],
        descs: [
          'All ports are functioning correctly and can establish stable connections',
          'Alle poorten functioneren correct en kunnen stabiele verbindingen tot stand brengen',
          'Alle Anschlüsse funktionieren korrekt und können stabile Verbindungen herstellen',
        ],
      },
      {
        key: 'HEAVYUSAGEINFOLDINGAREA',
        questionTypes: ['AndroidFlipandFold'],
        names: ['Folding area', 'Vouwgebied', 'Faltbereich'],
        descs: [
          'No damage or malfunction in the folding mechanism',
          'Geen schade of storing in het vouwmechanisme',
          'Keine Beschädigung oder Fehlfunktion im Faltmechanismus',
        ],
      },
      {
        key: 'NOTFULLYFUNCTIONAL',
        questionTypes: ['AirpodsMax', 'Airpods(exceptMax)'],
        names: ['Functionalities', 'Functionaliteiten', 'Funktionen'],
        descs: [
          'All functions work properly as intended',
          'Alle functies werken correct zoals bedoeld',
          'Alle Funktionen arbeiten ordnungsgemäß wie vorgesehen',
        ],
      },
      {
        key: 'CAMERA',
        questionTypes: [
          'iPad1',
          'iPhone1',
          'AndroidFlipandFold',
          'iPhone2',
          'iPhone4',
          'AndroidPhone1',
          'SamsungTabs',
          'iPad2',
          'AndroidPhone2',
          'iPhone3',
        ],
        names: ['Camera', 'Camera', 'Kamera'],
        descs: [
          'No lens defects or broken camera',
          'Geen lensdefecten of kapotte camera',
          'Keine Linsendefekte oder defekte Kamera',
        ],
      },
      {
        key: 'CONSOLECASE',
        questionTypes: ['Playstation1'],
        names: ['Console case', 'Consolekast', 'Konsolengehäuse'],
        descs: [
          'Console case is intact without significant damage or missing parts',
          'Consolekast is intact zonder significante schade of ontbrekende onderdelen',
          'Konsolengehäuse ist intakt ohne erhebliche Schäden oder fehlende Teile',
        ],
      },
      {
        key: 'LIGHTDISTANCESENSOR',
        questionTypes: [
          'iPhone1',
          'SamsungTabs',
          'iPhone4',
          'AndroidPhone2',
          'AndroidFlipandFold',
          'iPhone2',
          'iPad1',
          'iPad2',
          'AndroidPhone1',
          'iPhone3',
        ],
        names: ['Light and distance sensor', 'Licht- en afstandssensor', 'Licht- und Abstandssensor'],
        descs: [
          'Sensors accurately detect light levels and proximity',
          'Sensoren detecteren accuraat lichtniveaus en nabijheid',
          'Sensoren erkennen Lichtstärke und Nähe korrekt',
        ],
      },
      {
        key: 'WATERDAMAGED',
        questionTypes: ['Macbook1', 'Macbook2'],
        names: ['Water damage', 'Waterschade', 'Wasserschäden'],
        descs: [
          'Device is free from water damage',
          'Apparaat is vrij van waterschade',
          'Gerät ist frei von Wasserschäden',
        ],
      },
      {
        key: 'ACTIVATIONLOCKFINDDEVICES',
        questionTypes: ['Airpods(exceptMax)', 'AppleWatch', 'AirpodsMax'],
        names: [
          'Activation lock and find device',
          'Activeringsslot en vind apparaat',
          'Aktivierungssperre und Gerät finden',
        ],
        descs: [
          'Device is not locked to another account or findable by previous owner',
          'Apparaat is niet vergrendeld aan een ander account of vindbaar door vorige eigenaar',
          'Gerät ist nicht mit anderem Konto verknüpft oder vom Vorbesitzer auffindbar',
        ],
      },
      {
        key: 'CHARGERANDCABLEMISSINGNOTWORKING',
        questionTypes: ['Macbook1', 'Macbook2'],
        names: ['Charger and cable', 'Oplader en kabel', 'Ladegerät und Kabel'],
        descs: [
          'Charger and cable work properly and can charge the device',
          'Oplader en kabel werken correct en kunnen het apparaat opladen',
          'Ladegerät und Kabel funktionieren einwandfrei und können das Gerät aufladen',
        ],
      },
      {
        key: 'USBPORTS',
        questionTypes: ['MacMini1', 'Playstation1'],
        names: ['USB port(s)', 'USB-poort(en)', 'USB-Anschlüsse'],
        descs: [
          'All USB ports are working properly and can connect to external devices',
          'Alle USB-poorten werken correct en kunnen verbinding maken met externe apparaten',
          'Alle USB-Anschlüsse funktionieren einwandfrei und können mit externen Geräten verbunden werden',
        ],
      },
      {
        key: 'BATTERYMISSINGSWOLLENTOOMANYCYCLES',
        questionTypes: ['Macbook1', 'Macbook2'],
        names: ['Battery', 'Batterij', 'Batterie'],
        descs: [
          'Charges normally, does not drain quickly and is not bloated',
          'Laadt normaal op, loopt niet snel leeg en is niet opgezwollen',
          'Lädt normal, entlädt sich nicht schnell und ist nicht aufgebläht',
        ],
      },
      {
        key: 'NOMACOSSYSTEM',
        questionTypes: ['Macbook2', 'Macbook1'],
        names: ['MacOS system', 'MacOS-systeem', 'MacOS-System'],
        descs: [
          'MacOS is functioning correctly without system errors',
          'MacOS functioneert correct zonder systeemfouten',
          'MacOS funktioniert einwandfrei ohne Systemfehler',
        ],
      },
      {
        key: 'FOLDPROBLEMS',
        questionTypes: ['AndroidFlipandFold'],
        names: ['Folding area', 'Vouwgebied', 'Faltbereich'],
        descs: [
          'No damage or malfunction in the folding mechanism',
          'Geen schade of storing in het vouwmechanisme',
          'Keine Beschädigung oder Fehlfunktion im Faltmechanismus',
        ],
      },
      {
        key: 'HDMIPORT',
        questionTypes: ['Playstation1', 'MacMini1'],
        names: ['HDMI port(s)', 'HDMI-poort(en)', 'HDMI-Anschlüsse'],
        descs: [
          'HDMI ports function correctly and can output display signal',
          'HDMI-poorten functioneren correct en kunnen beeldsignaal uitvoeren',
          'HDMI-Anschlüsse funktionieren korrekt und können Bildsignale ausgeben',
        ],
      },
      {
        key: 'NFC',
        questionTypes: [
          'iPhone4',
          'iPad1',
          'iPhone1',
          'iPhone3',
          'SamsungTabs',
          'AndroidFlipandFold',
          'iPhone2',
          'AndroidPhone1',
          'iPad2',
          'AndroidPhone2',
        ],
        names: ['Bluetooth or Wi-Fi', 'Bluetooth of Wi-Fi', 'Bluetooth oder Wi-Fi'],
        descs: [
          'No connection issues with Bluetooth and Wi-Fi functionality',
          'Geen verbindingsproblemen met Bluetooth en Wi-Fi functionaliteit',
          'Keine Verbindungsprobleme mit Bluetooth- und Wi-Fi-Funktionalität',
        ],
      },
      {
        key: 'FACEIDTOUCHIDFINGERPRINT',
        questionTypes: [
          'iPhone1',
          'SamsungTabs',
          'AndroidPhone2',
          'AndroidFlipandFold',
          'iPhone2',
          'AndroidPhone1',
          'iPhone3',
          'iPad2',
          'iPad1',
          'iPhone4',
        ],
        names: ['Face ID or Touch ID', 'Face ID of Touch ID', 'Face ID oder Touch ID'],
        descs: [
          'No problems with Face ID, Touch ID or user authentication',
          'Geen problemen met Face ID, Touch ID of gebruikersverificatie',
          'Keine Beschädigung oder Fehlfunktion im Faltmechanismus',
        ],
      },
      {
        key: 'AUDIOPORT',
        questionTypes: [
          'SamsungTabs',
          'iPhone1',
          'AndroidPhone2',
          'iPad2',
          'AndroidFlipandFold',
          'iPhone2',
          'AndroidPhone1',
          'iPhone4',
          'iPhone3',
          'iPad1',
        ],
        names: ['Audio port', 'Audiopoort', 'Audioanschluss'],
        descs: [
          'Audio port functions correctly without connection issues',
          'Audiopoort functioneert correct zonder verbindingsproblemen',
          'Audioanschluss funktioniert einwandfrei ohne Verbindungsprobleme',
        ],
      },
      {
        key: 'SCREEN',
        questionTypes: ['iPhone3', 'iPhone1', 'iPhone4', 'iPhone2'],
        names: ['Screen', 'Scherm', 'Bildschirm'],
        descs: [
          'No burnt screen, pixel errors, color deviations, touch functionality issues or non-original parts',
          'Geen verbrand scherm, pixelfouten, kleurafwijkingen, problemen met touch-functionaliteit of niet originele onderdelen',
          'Kein eingebranntes Display, keine Pixelfehler, Farbabweichungen, Touch-Funktionalitätsprobleme oder nicht-originale Teile',
        ],
      },
      {
        key: 'TOUCHBARTOUCHID',
        questionTypes: ['Macbook2', 'Macbook1'],
        names: ['Touch Bar (Touch ID)', 'Touch Bar (Touch ID)', 'Touch Bar (Touch ID)'],
        descs: [
          'Touch Bar and Touch ID sensor function properly',
          'Touch Bar en Touch ID-sensor functioneren correct',
          'Touch Bar und Touch ID-Sensor funktionieren einwandfrei',
        ],
      },
      {
        key: 'CASE',
        questionTypes: ['MacMini1'],
        names: ['Case', 'Behuizing', 'Gehäuse'],
        descs: [
          'Case is intact without damage or missing parts',
          'Behuizing is intact zonder schade of ontbrekende onderdelen',
          'Gehäuse ist intakt ohne Schäden oder fehlende Teile',
        ],
      },
      {
        key: 'BATTERY',
        questionTypes: ['AppleWatch'],
        names: ['Battery', 'Batterij', 'Batterie'],
        descs: [
          'Charges normally, does not drain quickly and is not bloated',
          'Laadt normaal op, loopt niet snel leeg en is niet opgezwollen',
          'Lädt normal, entlädt sich nicht schnell und ist nicht aufgebläht',
        ],
      },
      {
        key: 'SCREEN',
        questionTypes: ['Macbook1', 'Switch(OLED)', 'Switch(Lite&2017&2019)', 'Macbook2'],
        names: ['Screen', 'Scherm', 'Bildschirm'],
        descs: [
          'Screen displays properly without dead pixels or visual defects',
          'Scherm werkt correct zonder dode pixels of visuele defecten',
          'Bildschirm funktioniert einwandfrei ohne tote Pixel oder visuelle Defekte',
        ],
      },
      {
        key: 'VIBRATION',
        questionTypes: [
          'SamsungTabs',
          'iPhone3',
          'AndroidPhone2',
          'iPad2',
          'AndroidPhone1',
          'iPad1',
          'AndroidFlipandFold',
          'iPhone2',
          'iPhone1',
          'iPhone4',
        ],
        names: ['Vibration', 'Trillingen', 'Vibration'],
        descs: [
          'Vibration function works normally and consistently',
          'Trilfunctie werkt normaal en consistent',
          'Vibrationsfunktion arbeitet normal und gleichmäßig',
        ],
      },
      {
        key: 'CANNOTBESWITCHEDON',
        questionTypes: [
          'iPhone4',
          'AndroidPhone2',
          'Playstation1',
          'iPad2',
          'Macbook1',
          'iPhone2',
          'AndroidPhone1',
          'AppleWatch',
          'Switch(Lite&2017&2019)',
          'Macbook2',
          'MacMini1',
          'iPhone1',
          'AndroidFlipandFold',
          'Switch(OLED)',
          'SamsungTabs',
          'iPad1',
          'iPhone3',
        ],
        names: ['Switch on', 'Inschakelen', 'Einschalten'],
        descs: [
          'Device turns on and functions normally',
          'Apparaat schakelt in en functioneert normaal',
          'Gerät schaltet sich ein und funktioniert normal',
        ],
      },
      {
        key: 'WIFIBLUETOOTH',
        questionTypes: ['Macbook1', 'MacMini1', 'Switch(OLED)', 'Macbook2', 'Switch(Lite&2017&2019)', 'Playstation1'],
        names: ['Wi-Fi and bluetooth', 'Wi-Fi en bluetooth', 'WLAN und Bluetooth'],
        descs: [
          'Wi-Fi and Bluetooth connections work properly',
          'Wi-Fi- en Bluetooth-verbindingen werken correct',
          'WLAN- und Bluetooth-Verbindungen funktionieren einwandfrei',
        ],
      },
      {
        key: 'CAMERA',
        questionTypes: ['Macbook1', 'Macbook2'],
        names: ['Camera', 'Camera', 'Kamera'],
        descs: [
          'Camera functions properly and provides clear image',
          'Camera functioneert correct en levert een helder beeld',
          'Kamera funktioniert einwandfrei und liefert ein klares Bild',
        ],
      },
      {
        key: 'DOCKISDAMAGEDNOTFULLYFUNCTIONAL',
        questionTypes: ['Switch(OLED)'],
        names: ['Dock', 'Dock', 'Dock'],
        descs: [
          'Dock connects and charges device properly',
          'Dock maakt correct verbinding en laadt het apparaat op',
          'Dock verbindet und lädt das Gerät ordnungsgemäß',
        ],
      },
      {
        key: 'SENSOR',
        questionTypes: ['AppleWatch'],
        names: ['Sensor', 'Sensor', 'Sensor'],
        descs: [
          'All device sensors function properly',
          'Alle apparaatsensoren functioneren correct',
          'Alle Gerätesensoren funktionieren ordnungsgemäß',
        ],
      },
      {
        key: 'KEYBOARDTRACKPAD',
        questionTypes: ['Macbook1', 'Macbook2'],
        names: ['Keyboard and trackpad', 'Toetsenbord en trackpad', 'Tastatur und Trackpad'],
        descs: [
          'Keyboard and trackpad respond correctly to all inputs',
          'Toetsenbord en trackpad reageren correct op alle invoer',
          'Tastatur und Trackpad reagieren korrekt auf alle Eingaben',
        ],
      },
      {
        key: 'CRACKONTHEBODY',
        questionTypes: ['Macbook1', 'Macbook2'],
        names: ['Body condition', 'Behuizing conditie', 'Gehäusezustand'],
        descs: [
          'Device body is intact without cracks',
          'Behuizing is intact zonder scheuren',
          'Gerätegehäuse ist intakt ohne Risse',
        ],
      },
      {
        key: 'SCREENTOUCHFUNCTIONDEFECT',
        questionTypes: [
          'iPhone1',
          'iPad2',
          'AndroidFlipandFold',
          'SamsungTabs',
          'iPhone2',
          'iPhone4',
          'iPhone3',
          'AndroidPhone2',
          'AppleWatch',
          'AndroidPhone1',
          'iPad1',
        ],
        names: ['Touch screen', 'Touch-functie', 'Touch-Funktion'],
        descs: [
          'Touch screen responds accurately to input',
          'Touchscreen reageert nauwkeurig op aanrakingen',
          'Touchscreen reagiert präzise auf Eingaben',
        ],
      },
      {
        key: 'SCREEN',
        questionTypes: ['AppleWatch'],
        names: ['Screen', 'Scherm', 'Bildschirm'],
        descs: [
          'No burnt screen, pixel errors, color deviations, touch functionality issues or non-original parts',
          'Geen verbrand scherm, pixelfouten, kleurafwijkingen, problemen met touch-functionaliteit of niet originele onderdelen',
          'Kein eingebranntes Display, keine Pixelfehler, Farbabweichungen, Touch-Funktionalitätsprobleme oder nicht-originale Teile',
        ],
      },
      {
        key: 'CHARGINGPORT',
        questionTypes: [
          'SamsungTabs',
          'AndroidPhone2',
          'iPad2',
          'AndroidFlipandFold',
          'iPhone2',
          'iPhone3',
          'iPhone1',
          'iPhone4',
          'AndroidPhone1',
          'iPad1',
        ],
        names: ['Charging port', 'Oplaadpoort', 'Ladeanschluss'],
        descs: [
          'Charging port connects and charges properly',
          'Oplaadpoort maakt verbinding en laadt correct op',
          'Ladeanschluss verbindet und lädt ordnungsgemäß',
        ],
      },
      {
        key: 'SCREEN',
        questionTypes: ['AndroidPhone2', 'AndroidPhone1', 'AndroidFlipandFold'],
        names: ['Screen', 'Scherm', 'Bildschirm'],
        descs: [
          'No burnt screen, pixel errors, color deviations, touch functionality issues or non-original parts',
          'Geen verbrand scherm, pixelfouten, kleurafwijkingen, problemen met touch-functionaliteit of niet originele onderdelen',
          'Kein eingebranntes Display, keine Pixelfehler, Farbabweichungen, Touch-Funktionalitätsprobleme oder nicht-originale Teile',
        ],
      },
      {
        key: 'READCD',
        questionTypes: ['Playstation1'],
        names: ['CD reader', 'CD-lezer', 'CD-Laufwerk'],
        descs: [
          'CD reader recognizes and reads discs properly',
          'CD-lezer herkent en leest schijven correct',
          'CD-Laufwerk erkennt und liest Discs korrekt',
        ],
      },
      {
        key: 'WATCHBAND',
        questionTypes: ['AppleWatch'],
        names: ['Original band', 'Originele band', 'Originalband'],
        descs: [
          'Original band is not missing or broken',
          'Originele band ontbreekt niet en is niet kapot',
          'Originalband ist vorhanden und ist nicht beschädigt',
        ],
      },
      {
        key: 'FANSOUNDSABNORMAL',
        questionTypes: ['Macbook1', 'Macbook2'],
        names: ['Fan', 'Ventilator', 'Lüfter'],
        descs: [
          'Fan operates normally without unusual noise',
          'Ventilator werkt normaal zonder ongebruikelijk geluid',
          'Lüfter funktioniert normal ohne ungewöhnliche Geräusche',
        ],
      },
      {
        key: 'EARCUSHION',
        questionTypes: ['AirpodsMax'],
        names: ['Ear cushion', 'Oorkussens', 'Ohrpolster'],
        descs: [
          'Ear cushions are intact and properly attached',
          'Oorkussens zijn intact en correct bevestigd',
          'Ohrpolster sind intakt und richtig befestigt',
        ],
      },
      {
        key: 'CONSOLECASE',
        questionTypes: ['Switch(OLED)', 'Switch(Lite&2017&2019)'],
        names: ['Console case', 'Consolekast', 'Konsolengehäuse'],
        descs: [
          'Console case is intact without damage or missing parts',
          'Consolekast is intact zonder schade of ontbrekende onderdelen',
          'Konsolengehäuse ist intakt ohne Schäden oder fehlende Teile',
        ],
      },
      {
        key: 'BATTERYNOCHARGINGFASTDRAININGSWOLLEN',
        questionTypes: [
          'iPhone3',
          'AndroidFlipandFold',
          'iPad1',
          'SamsungTabs',
          'AndroidPhone1',
          'iPhone1',
          'iPad2',
          'iPhone4',
          'AndroidPhone2',
          'iPhone2',
        ],
        names: ['Battery', 'Batterij', 'Batterie'],
        descs: [
          'Charges normally, does not drain quickly and is not bloated',
          'Laadt normaal op, loopt niet snel leeg en is niet opgezwollen',
          'Lädt normal, entlädt sich nicht schnell und ist nicht aufgebläht',
        ],
      },
      {
        key: 'DOESNTREADGAMECARDS',
        questionTypes: ['Switch(OLED)', 'Switch(Lite&2017&2019)'],
        names: ['Game card reader', 'Spelkaartlezer', 'Spielkartenleser'],
        descs: [
          'Game card reader recognizes and reads game cards properly',
          'Spelkaartlezer herkent en leest spelkaarten correct',
          'Spielkartenleser erkennt und liest Spielkarten korrekt',
        ],
      },
      {
        key: 'SOFTWARE',
        questionTypes: ['MacMini1', 'Switch(OLED)', 'Switch(Lite&2017&2019)', 'Playstation1'],
        names: ['Software', 'Software', 'Software'],
        descs: [
          'Software runs properly without system errors',
          'Software werkt correct zonder systeemfouten',
          'Software läuft einwandfrei ohne Systemfehler',
        ],
      },
      {
        key: 'CROWN',
        questionTypes: ['AppleWatch'],
        names: ['Crown', 'Kroon', 'Krone'],
        descs: [
          'The crown rotates and functions properly',
          'De kroon draait en functioneert naar behoren',
          'Die Krone dreht sich und funktioniert einwandfrei',
        ],
      },
      {
        key: 'SOUNDMICROPHONESPEAKER',
        questionTypes: ['Macbook2', 'Macbook1'],
        names: ['Microphone and speaker', 'Microfoon en speaker', 'Mikrofon und Lautsprecher'],
        descs: [
          'Microphone and speaker work properly without audio issues',
          'Microfoon en speaker werken correct zonder audioproblemen',
          'Mikrofon und Lautsprecher funktionieren einwandfrei ohne Audioprobleme',
        ],
      },
      {
        key: 'WIFI',
        questionTypes: ['AppleWatch'],
        names: ['Bluetooth or Wi-Fi', 'Bluetooth of Wi-Fi', 'Bluetooth oder Wi-Fi'],
        descs: [
          'No connection issues with Bluetooth and Wi-Fi functionality',
          'Geen verbindingsproblemen met Bluetooth en Wi-Fi functionaliteit',
          'Keine Verbindungsprobleme mit Bluetooth- und Wi-Fi-Funktionalität',
        ],
      },
      {
        key: 'FRAMEANDORBACK',
        questionTypes: [
          'iPhone4',
          'iPhone1',
          'iPhone3',
          'SamsungTabs',
          'AndroidPhone2',
          'iPad2',
          'AndroidFlipandFold',
          'AndroidPhone1',
          'iPhone2',
          'iPad1',
        ],
        names: ['Frame or back', 'Frame of achterkant', 'Rahmen oder Rückseite'],
        descs: [
          'No loose or non-original parts',
          'Geen loszittende- of niet originele onderdelen',
          'Keine losen oder nicht-originalen Teile',
        ],
      },
      {
        key: 'SCREENHINGESTUCKLOOSEMISSINGMAKESSOUND',
        questionTypes: ['Macbook1', 'Macbook2'],
        names: ['Screen hinge', 'Schermscharnier', 'Bildschirmscharnier'],
        descs: [
          'Screen hinge works properly without any loose parts',
          'Schermscharnier werkt correct zonder losse onderdelen',
          'Bildschirmscharnier funktioniert einwandfrei ohne lockere Teile',
        ],
      },
    ]

    for (const update of updates) {
      const questionTypeCondition = update.questionTypes.map((qt) => `'${qt}'`).join(', ')

      await queryRunner.query(
        `
        UPDATE question_type_problem qtp
        SET 
          name__en = $1,
          name__nl = $2,
          name__de = $3,
          desc__en = $4,
          desc__nl = $5,
          desc__de = $6
        FROM question_type qt
        WHERE qtp.question_type_id = qt.id
        AND qt.internal_name IN (${questionTypeCondition})
        AND qtp.key = $7
      `,
        [
          update.names[0],
          update.names[1],
          update.names[2],
          update.descs[0],
          update.descs[1],
          update.descs[2],
          update.key,
        ]
      )
    }

    // Update WIFI to WIFIBLUETOOTH for AppleWatch
    await queryRunner.query(`
      UPDATE question_type_problem qtp
      SET key = 'WIFIBLUETOOTH'
      FROM question_type qt
      WHERE qtp.question_type_id = qt.id
      AND qt.internal_name = 'AppleWatch'
      AND qtp.key = 'WIFI'
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // If needed, you can write a rollback script here
  }
}
