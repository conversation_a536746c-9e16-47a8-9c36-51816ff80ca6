import { fa } from '@faker-js/faker/.'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { type IOrganizationBankAccount, OrganizationBankAccountEntity, PartnerEntity } from '~/entities'

@Entity('charity')
export class CharityEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  name__en: string

  @Column()
  name__nl: string

  @Column()
  name__de: string

  @Column()
  name__pl: string

  @OneToOne(() => OrganizationBankAccountEntity, (bankAccount) => bankAccount.charity, { nullable: false })
  @JoinColumn()
  bankAccount: Relation<OrganizationBankAccountEntity>

  @Column()
  @RelationId((charity: CharityEntity) => charity.bankAccount)
  bankAccountId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ICharity = Omit<CharityEntity, keyof BaseEntity> & {
  bankAccount?: IOrganizationBankAccount
}
