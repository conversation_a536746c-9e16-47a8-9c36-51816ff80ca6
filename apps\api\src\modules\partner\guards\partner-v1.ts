import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common'
import ms from 'ms'
import { ClsService } from 'nestjs-cls'

import { envConfig } from '~/configs'
import { PartnerEntity } from '~/entities'
import { ClsStoreType } from '~/interfaces'

@Injectable()
export class V1PartnerApiKeyAuthGuard implements CanActivate {
  constructor(private readonly cls: ClsService<ClsStoreType>) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()

    if (!request?.headers?.['x-api-key']) {
      throw new UnauthorizedException('Authentication failed')
    }
    if (request?.headers?.['x-api-secret']) {
      throw new UnauthorizedException('Secret is not allowed')
    }

    const partner = await PartnerEntity.findOne({
      where: { id: request.headers['x-api-key'] },
      select: { id: true, channelId: true },
      cache: envConfig.isProd ? ms('5 minutes') : false,
    })
    if (!partner) {
      throw new UnauthorizedException('Authentication failed')
    }

    this.cls.set('partnerId', partner.id)

    request.partnerId = partner.id
    request.channelId = partner.channelId

    return true
  }
}

@Injectable()
export class V1PartnerApiSecretAuthGuard implements CanActivate {
  constructor(private readonly cls: ClsService<ClsStoreType>) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()

    if (!request?.headers?.['x-api-key'] || !request?.headers?.['x-api-secret']) {
      throw new UnauthorizedException('Authentication failed')
    }

    const partner = await PartnerEntity.findOne({
      where: { id: request.headers['x-api-key'], secret: request.headers['x-api-secret'] },
      select: { id: true, channelId: true },
      cache: envConfig.isProd ? ms('5 minutes') : false,
    })
    if (!partner) {
      throw new UnauthorizedException('Authentication failed')
    }

    this.cls.set('partnerId', partner.id)

    request.partnerId = partner.id
    request.channelId = partner.channelId

    return true
  }
}
