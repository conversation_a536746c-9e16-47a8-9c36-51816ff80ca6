import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { MarketingBannerType } from '~/constants/marketing'
import {
  CategoryEntity,
  ChannelEntity,
  type ICategory,
  type IChannel,
  type IImage,
  ImageEntity,
  type IMarketingSkuCollection,
  type IProductModel,
  MarketingSkuCollectionEntity,
  MarketingTagEntity,
  ProductModelEntity,
} from '~/entities'

registerEnumType(MarketingBannerType, { name: 'MarketingBannerType' })

@ObjectType('MarketingBanner')
@Entity('marketing_banner')
export class MarketingBannerEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field(() => ChannelEntity)
  @ManyToMany(() => ChannelEntity, { nullable: false })
  @JoinTable({ name: 'marketing_banner_channels' })
  channels: Relation<ChannelEntity>[]

  @Field(() => MarketingBannerType)
  @Column({ type: 'varchar', nullable: false })
  type: MarketingBannerType

  @ManyToOne(() => CategoryEntity, { nullable: true })
  category?: Relation<CategoryEntity> | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  @RelationId((banner: MarketingTagEntity) => banner.category)
  categoryId?: string | null

  @ManyToOne(() => ProductModelEntity, { nullable: true })
  model?: Relation<ProductModelEntity> | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  @RelationId((banner: MarketingTagEntity) => banner.model)
  modelId?: string | null

  @ManyToOne(() => MarketingSkuCollectionEntity, { nullable: false })
  collection?: Relation<MarketingSkuCollectionEntity> | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  @RelationId((banner: MarketingTagEntity) => banner.collection)
  collectionId?: string | null

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingBannerImageDesktop__en, { nullable: false, cascade: true })
  @JoinColumn({ name: 'image_desktop_id__en' })
  imageDesktop__en: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingBannerEntity) => banner.imageDesktop__en)
  imageDesktopId__en: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingBannerImageMobile__en, { nullable: false, cascade: true })
  @JoinColumn({ name: 'image_mobile_id__en' })
  imageMobile__en: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingBannerEntity) => banner.imageMobile__en)
  imageMobileId__en: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingBannerImageDesktop__nl, { nullable: false, cascade: true })
  @JoinColumn({ name: 'image_desktop_id__nl' })
  imageDesktop__nl: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingBannerEntity) => banner.imageDesktop__nl)
  imageDesktopId__nl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingBannerImageMobile__nl, { nullable: false, cascade: true })
  @JoinColumn({ name: 'image_mobile_id__nl' })
  imageMobile__nl: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingBannerEntity) => banner.imageMobile__nl)
  imageMobileId__nl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingBannerImageDesktop__de, { nullable: false, cascade: true })
  @JoinColumn({ name: 'image_desktop_id__de' })
  imageDesktop__de: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingBannerEntity) => banner.imageDesktop__de)
  imageDesktopId__de: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingBannerImageMobile__de, { nullable: false, cascade: true })
  @JoinColumn({ name: 'image_mobile_id__de' })
  imageMobile__de: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingBannerEntity) => banner.imageMobile__de)
  imageMobileId__de: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingBannerImageDesktop__pl, { nullable: false, cascade: true })
  @JoinColumn({ name: 'image_desktop_id__pl' })
  imageDesktop__pl: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingBannerEntity) => banner.imageDesktop__pl)
  imageDesktopId__pl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingBannerImageMobile__pl, { nullable: false, cascade: true })
  @JoinColumn({ name: 'image_mobile_id__pl' })
  imageMobile__pl: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingBannerEntity) => banner.imageMobile__pl)
  imageMobileId__pl: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IMarketingBanner = Omit<MarketingBannerEntity, keyof BaseEntity> & {
  channels: IChannel[]
  category?: ICategory
  model?: IProductModel
  collection?: IMarketingSkuCollection
  imageDesktop__en: IImage
  imageMobile__en: IImage
  imageDesktop__nl: IImage
  imageMobile__nl: IImage
  imageDesktop__de: IImage
  imageMobile__de: IImage
  imageDesktop__pl: IImage
  imageMobile__pl: IImage
}
