import { DateField, FilterDropdown, getDefaultSortOrder, useTable } from '@refinedev/antd'
import { getDefaultFilter, useApiUrl } from '@refinedev/core'
import { EmailType, LOCALE_LANGUAGE_NAMES } from '@valyuu/api/constants'
import type { IEmailHistory } from '@valyuu/api/entities'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Table } from 'antd'
import { Modal } from 'antd'
import { Select } from 'antx'
import { capitalize } from 'lodash'
import type { FC } from 'react'
import { useEffect, useState } from 'react'

const getTypeName = (type: EmailType) => capitalize(type).replace(/-/g, ' ')

export type BlockEmailHistoryProps = {
  userId?: string
  relatedIds?: string[]
}

export const BlockEmailHistory: FC<BlockEmailHistoryProps> = ({ userId, relatedIds = [] }: BlockEmailHistoryProps) => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [selectedEmail, setSelectedEmail] = useState<IEmailHistory | null>(null)
  const [iframeSrc, setIframeSrc] = useState<string | null>(null)

  const { tableProps, sorters, tableQueryResult, filters } = useTable<IEmailHistory>({
    resource: 'admin/email-histories',
    meta: {
      fields: ['id', 'userId', 'email', 'subject', 'type', 'relatedIds', 'lang', 'isResend', 'createdAt'],
    },
    filters: {
      initial: [
        ...(userId ? [{ field: 'userId', operator: 'eq' as const, value: userId }] : []),
        ...(relatedIds?.length
          ? [
              {
                operator: 'or' as const,
                value: [
                  ...relatedIds.map((id: string) => ({ field: 'relatedIds', operator: 'eq' as const, value: id })),
                ],
              },
            ]
          : []),
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
    },
  })

  const records = tableQueryResult?.data?.data ?? []

  const apiUrl = useApiUrl()

  const handleModalClose = () => {
    setIsModalVisible(false)
    setSelectedEmail(null)
    setIframeSrc(null) // Reset iframe source on close
  }

  useEffect(() => {
    const fetchEmailPreview = async () => {
      if (selectedEmail) {
        try {
          const response = await axios.get(`${apiUrl}/admin/email-histories/preview/${selectedEmail.id}`)
          setIframeSrc(response.data) // Set the fetched content as iframe source
        } catch (error) {
          console.error('Error fetching email preview:', error)
        }
      }
    }
    fetchEmailPreview()
  }, [selectedEmail, apiUrl])

  return (
    <>
      <Table
        {...tableProps}
        rowKey="id"
        onRow={({ id }) => ({
          onClick: async () => {
            setSelectedEmail(records.find((record) => record.id === id) ?? null)
            setIsModalVisible(true)
          },
        })}
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
      >
        <Table.Column
          dataIndex="type"
          title="Type"
          className="cursor-pointer"
          width="5rem"
          render={(value) => getTypeName(value)}
          defaultFilteredValue={getDefaultFilter('type', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 280 }}
                placeholder="Type"
                options={Object.values(EmailType).map((type) => ({ value: type, label: getTypeName(type) }))}
              />
            </FilterDropdown>
          )}
        />
        <Table.Column dataIndex="email" title="Email" className="cursor-pointer" align="center" width="7rem" />
        <Table.Column
          dataIndex="subject"
          title="Subject"
          className="cursor-pointer"
          width="10rem"
          render={(text) => (
            <span className="truncate" title={text}>
              {text}
            </span>
          )}
        />
        <Table.Column
          dataIndex="lang"
          title="Language"
          className="cursor-pointer"
          width="5rem"
          render={(value) => LOCALE_LANGUAGE_NAMES[value as keyof typeof LOCALE_LANGUAGE_NAMES]}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
      </Table>
      <Modal title={selectedEmail?.subject} open={isModalVisible} onCancel={handleModalClose} footer={null} width={640}>
        {iframeSrc && (
          <iframe
            srcDoc={iframeSrc} // Use srcDoc to set the content of the iframe
            className="h-[1000px] max-h-[80vh] w-full border-none"
            title="Email Preview"
          />
        )}
      </Modal>
    </>
  )
}
