import { Field, ID, Int, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { ImageProcessNameType, ImageUsedInType } from '~/constants'
import {
  AccessoryProductEntity,
  BlogAuthorEntity,
  BlogPostEntity,
  BrandEntity,
  CategoryEntity,
  MarketingBannerEntity,
  MarketingSkuCollectionEntity,
  MarketingTagEntity,
  ProductModelEntity,
  ProductSkuEntity,
  ProductSkuOriginalAccessoryEntity,
  QuestionTypeImageTextEntity,
} from '~/entities'

@ObjectType('Image', { description: 'Cloudinary image' })
@Entity('image')
export class ImageEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field({ description: 'Public ID from Cloudinary' })
  @Column()
  publicId: string

  @Field()
  @Column()
  url: string

  @Field({ nullable: true, description: "Image's alternative text in English, can be used as alt prop" })
  @Column({ nullable: true })
  name__en?: string | null

  @Field({ nullable: true, description: "Image's alternative text in Dutch, can be used as alt prop" })
  @Column({ nullable: true })
  name__nl?: string | null

  @Field({ nullable: true, description: "Image's alternative text in German, can be used as alt prop" })
  @Column({ nullable: true })
  name__de?: string | null

  @Field({ nullable: true, description: "Image's alternative text in Polish, can be used as alt prop" })
  @Column({ nullable: true })
  name__pl?: string | null

  @Column({ nullable: true })
  originalFilename?: string | null

  @Field(() => Int, { description: 'Image original width in pixels' })
  @Column({ type: 'int2', unsigned: true })
  width: number

  @Field(() => Int, { description: 'Image original height in pixels' })
  @Column({ type: 'int2', unsigned: true })
  height: number

  @Field({ description: 'Image format' })
  @Column()
  format: string

  @Index()
  @Column({ type: 'varchar', array: true, nullable: true })
  usedIn?: ImageUsedInType[] | null

  @Column({ default: true })
  isTemp: boolean

  @OneToOne(() => AccessoryProductEntity, (accessoryProduct) => accessoryProduct.heroImage, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  accessoryProductHeroImage?: Relation<AccessoryProductEntity>

  @OneToOne(() => BlogAuthorEntity, (blogAuthor) => blogAuthor.avatar, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  blogAuthorAvatar?: Relation<BlogAuthorEntity>

  @OneToOne(() => BlogPostEntity, (blogPost) => blogPost.heroImage, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  blogPostHeroImage?: Relation<BlogPostEntity>

  @OneToOne(() => BrandEntity, (brand) => brand.image, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  brandImage?: Relation<BrandEntity>

  @OneToOne(() => CategoryEntity, (brand) => brand.icon, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  categoryIcon?: Relation<CategoryEntity>

  @OneToOne(() => CategoryEntity, (brand) => brand.image, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  categoryImage?: Relation<CategoryEntity>

  @OneToOne(() => MarketingBannerEntity, (marketingBanner) => marketingBanner.imageDesktop__en, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingBannerImageDesktop__en?: Relation<MarketingBannerEntity>

  @OneToOne(() => MarketingBannerEntity, (marketingBanner) => marketingBanner.imageMobile__en, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingBannerImageMobile__en?: Relation<MarketingBannerEntity>

  @OneToOne(() => MarketingBannerEntity, (marketingBanner) => marketingBanner.imageDesktop__nl, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingBannerImageDesktop__nl?: Relation<MarketingBannerEntity>

  @OneToOne(() => MarketingBannerEntity, (marketingBanner) => marketingBanner.imageMobile__nl, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingBannerImageMobile__nl?: Relation<MarketingBannerEntity>

  @OneToOne(() => MarketingBannerEntity, (marketingBanner) => marketingBanner.imageDesktop__de, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingBannerImageDesktop__de?: Relation<MarketingBannerEntity>

  @OneToOne(() => MarketingBannerEntity, (marketingBanner) => marketingBanner.imageMobile__de, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingBannerImageMobile__de?: Relation<MarketingBannerEntity>

  @OneToOne(() => MarketingBannerEntity, (marketingBanner) => marketingBanner.imageDesktop__pl, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingBannerImageDesktop__pl?: Relation<MarketingBannerEntity>

  @OneToOne(() => MarketingBannerEntity, (marketingBanner) => marketingBanner.imageMobile__pl, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingBannerImageMobile__pl?: Relation<MarketingBannerEntity>

  @OneToOne(() => MarketingSkuCollectionEntity, (collection) => collection.headerBgImageDesktop__en, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingSkuCollectionHeaderBgImageDesktop__en?: Relation<MarketingSkuCollectionEntity>

  @OneToOne(() => MarketingSkuCollectionEntity, (collection) => collection.headerBgImageMobile__en, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingSkuCollectionHeaderBgImageMobile__en?: Relation<MarketingSkuCollectionEntity>

  @OneToOne(() => MarketingSkuCollectionEntity, (collection) => collection.headerBgImageDesktop__nl, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingSkuCollectionHeaderBgImageDesktop__nl?: Relation<MarketingSkuCollectionEntity>

  @OneToOne(() => MarketingSkuCollectionEntity, (collection) => collection.headerBgImageMobile__nl, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingSkuCollectionHeaderBgImageMobile__nl?: Relation<MarketingSkuCollectionEntity>

  @OneToOne(() => MarketingSkuCollectionEntity, (collection) => collection.headerBgImageDesktop__de, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingSkuCollectionHeaderBgImageDesktop__de?: Relation<MarketingSkuCollectionEntity>

  @OneToOne(() => MarketingSkuCollectionEntity, (collection) => collection.headerBgImageMobile__de, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingSkuCollectionHeaderBgImageMobile__de?: Relation<MarketingSkuCollectionEntity>

  @OneToOne(() => MarketingSkuCollectionEntity, (collection) => collection.headerBgImageDesktop__pl, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingSkuCollectionHeaderBgImageDesktop__pl?: Relation<MarketingSkuCollectionEntity>

  @OneToOne(() => MarketingSkuCollectionEntity, (collection) => collection.headerBgImageMobile__pl, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingSkuCollectionHeaderBgImageMobile__pl?: Relation<MarketingSkuCollectionEntity>

  @OneToOne(() => MarketingTagEntity, (marketingTag) => marketingTag.image, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  marketingTagImage?: Relation<MarketingTagEntity>

  @OneToOne(() => ProductModelEntity, (productModel) => productModel.image, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  productModelImage?: Relation<ProductModelEntity>

  @OneToOne(
    () => ProductSkuOriginalAccessoryEntity,
    (productSkuOriginalAccessory) => productSkuOriginalAccessory.thumbnail,
    {
      nullable: true,
      onDelete: 'CASCADE',
    }
  )
  productSkuOriginalAccessoryThumbnail?: Relation<ProductSkuOriginalAccessoryEntity>

  @OneToMany(() => ProductSkuEntity, (productSku) => productSku.heroImage, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  productSkuHeroImages?: Relation<ProductSkuEntity>[]

  @ManyToMany(() => ProductSkuEntity, (productSku) => productSku.images, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  productSkuImages?: Relation<ProductSkuEntity>[]

  @OneToOne(() => QuestionTypeImageTextEntity, (questionTypeProblemImageText) => questionTypeProblemImageText.image, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  questionTypeImageText?: Relation<QuestionTypeImageTextEntity>

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  processName?: Relation<ImageProcessNameType> | null = null
}

export type IImage = Omit<ImageEntity, keyof BaseEntity> & Pick<ImageEntity, 'processName'>
