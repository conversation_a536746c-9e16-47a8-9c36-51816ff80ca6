import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { MarketingSkuCollectionEntity } from '~/entities'

export class CrudMarketingSkuCollectionService extends TypeOrmCrudService<MarketingSkuCollectionEntity> {
  constructor(@InjectRepository(MarketingSkuCollectionEntity) repo: Repository<MarketingSkuCollectionEntity>) {
    super(repo)
  }
}
