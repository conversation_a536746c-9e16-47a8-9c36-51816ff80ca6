import { MigrationInterface, QueryRunner } from 'typeorm'

export class RenameSaleItemPayment1730969880386 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sale_item_payment" RENAME TO "sale_item_stripe_payment"`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sale_item_stripe_payment" RENAME TO "sale_item_payment"`)
  }
}
