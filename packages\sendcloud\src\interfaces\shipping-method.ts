/**
 * Interface representing a shipping method from Sendcloud API
 */
export type IShippingMethod = {
  /** Unique identifier of the shipping method
   * @example 8
   */
  id: number

  /** Name of the shipping method, it should give an idea what the shipping method can be used for
   * @example "Standard Shipping"
   */
  name: string

  /** A carrier_code which will indicate which carrier provides the shipping method
   * @example "ups"
   */
  carrier: string

  /** Minimum allowed weight of the parcel for this shipping method
   * @example "0.000"
   */
  min_weight: string

  /** Maximum allowed weight of the parcel for this shipping method
   * @example "20.000"
   */
  max_weight: string

  /** Will be either 'required' when the shipping method is meant to ship a parcel to a service point, or 'none' when this is not the case
   * @example "none"
   */
  service_point_input: 'required' | 'none'

  /** A list of countries that you can ship to with the shipping method
   * @example [{ id: 150, name: "Netherlands", price: 995, iso_2: "NL", iso_3: "NLD", lead_time_hours: 24 }]
   */
  countries: {
    /** Countries that you can ship to with the shipping method
     * @example 150
     */
    id: number
    /** Name of the country
     * @example "Netherlands"
     */
    name: string
    /** Indicates what it will cost to use the shipping method. Will be 0 if the user has enabled a direct contract for corresponding the carrier
     * @example 995
     */
    price: number
    /** ISO 3166-1 alpha-2 code of the country
     * @example "NL"
     */
    iso_2: string
    /** ISO 3166-1 alpha-3 code of the country
     * @example "NLD"
     */
    iso_3: string
    /** Lead time of the shipping method in hours
     * @example 24
     */
    lead_time_hours?: number
  }[]

  /** A Sendcloud shipping price breakdown
   * @example [{ type: "price_without_insurance", label: "Initial price per parcel", value: 6.4 }]
   */
  price_breakdown: {
    /** Type of the price. It is an identifier of category of the price
     * @example "price_without_insurance"
     */
    type: string
    /** This label is a friendly name for type of the price type and can be used to represent it
     * @example "Initial price per parcel"
     */
    label: string
    /** Price amount of the breakdown item
     * @example 6.4
     */
    value: number
  }[]
}
