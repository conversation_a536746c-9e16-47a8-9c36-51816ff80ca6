import { Create, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps } from '@refinedev/core'
import type { ILocaleCountry, ILocaleLanguage } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useState } from 'react'

import { InputLanguageTab, InputMultiEntitySelect, InputMultiLang, LanguageTabChoices } from '~/components'

export const LocaleCountryCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps } = useForm<ILocaleCountry, HttpError, ILocaleCountry>()

  const { selectProps: languageSelectProps } = useSelect<ILocaleLanguage>({
    resource: 'admin/languages',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })
  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input
          label="ID"
          name="id"
          rules={['required']}
          normalize={(value) =>
            value
              .replace(/[^a-zA-Z]/g, '')
              .substring(0, 2)
              .toUpperCase()
          }
        />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
              <Input label="Country Area Code" name="countryAreaCode" rules={['required']} />
            </>
          )}
        </Watch>
        <InputMultiEntitySelect
          label="Languages"
          name="languages"
          rules={['required']}
          {...(languageSelectProps as any)}
        />
      </Form>
    </Create>
  )
}
