/**
 * Represents carrier-specific errors returned by SendCloud API
 * when creating or updating parcels.
 *
 * @property non_field_errors - Array of error messages not associated with specific fields
 */
export type IParcelCarrierErrors = {
  /**
   * General errors that are not tied to a specific field
   * These can include carrier validation errors, service availability issues,
   * or other general shipping-related problems
   */
  non_field_errors: string[]
}
