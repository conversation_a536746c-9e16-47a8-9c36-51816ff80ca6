import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
} from 'typeorm'

import { EmailType, LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { AdminUserEntity, IUser, UserEntity } from '~/entities'

@Entity('email_history')
export class EmailHistoryEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  email: string

  @Column()
  subject: string

  @ManyToOne(() => UserEntity)
  user: Relation<UserEntity>

  @Column()
  @RelationId((emailHistory: EmailHistoryEntity) => emailHistory.user)
  userId: string

  @Index()
  @Column({ nullable: false })
  type: EmailType

  @Index()
  @Column({ type: 'uuid', array: true })
  relatedIds: string[]

  @Column({ type: 'varchar', nullable: false })
  lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @Column({ type: 'jsonb' })
  data: Record<string, any>

  @Column({ nullable: false, default: false })
  isResend: boolean

  @Column({ nullable: true })
  templatePrefix?: string

  @ManyToOne(() => AdminUserEntity, { nullable: true })
  changedBy?: Relation<AdminUserEntity>

  @Column({ nullable: true })
  @RelationId((emailHistory: EmailHistoryEntity) => emailHistory.changedBy)
  changedById?: string

  @CreateDateColumn()
  @Index()
  createdAt: Date
}

export type IEmailHistory = Omit<EmailHistoryEntity, keyof BaseEntity> & {
  user: IUser
}
