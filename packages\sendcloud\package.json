{"name": "@valyuu/sendcloud", "version": "0.1.0", "private": true, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "sideEffects": false, "license": "MIT", "engines": {"node": ">=20.0.0"}, "scripts": {"lint": "eslint \"src/**/*.ts*\"", "build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "prepare": "npm run build"}, "devDependencies": {"@eslint/js": "^9.13.0", "@eslint/json": "^0.5.0", "@types/node": "22.8.1", "typescript-eslint": "^8.11.0", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^15.11.0", "prettier": "^3.3.3", "typescript": "^5.6.3", "tsup": "^8.3.5"}, "dependencies": {"axios": "^1.7.7"}, "files": ["dist"]}