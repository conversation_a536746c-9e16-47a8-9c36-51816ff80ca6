import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, Get, Param, ParseUUIDPipe, Res, UseGuards } from '@nestjs/common'
import { Response } from 'express'

import { ShipmentEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudShipmentService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ShipmentEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      shippingMethod: {},
      warehouse: {},
      customerAddress: {},
      sale: {},
      order: {},
      saleItem: {},
      partner: {},
    },
  },
})
@Controller('admin/shipments')
export class CrudShipmentController implements CrudController<ShipmentEntity> {
  constructor(public service: CrudShipmentService) {}

  @Get(':id/preview-document')
  async previewDocument(@Param('id', ParseUUIDPipe) id: string) {
    return this.service.previewDocument(id)
  }

  @Get(':id/download-document')
  async downloadDocument(@Param('id', ParseUUIDPipe) id: string, @Res() res: Response) {
    const buffer = await this.service.downloadDocument(id)
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': 'attachment; filename="shipping-label.pdf"',
      'Content-Length': buffer.length,
    })
    res.end(buffer)
  }
}
