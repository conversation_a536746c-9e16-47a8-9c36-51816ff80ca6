import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { OrderSkuItemStatus } from '~/constants'
import {
  ChannelEntity,
  type IChannel,
  type ILocaleCurrency,
  type IOrder,
  type IProductModel,
  type IProductSku,
  type IWarranty,
  LocaleCurrencyEntity,
  OrderEntity,
  ProductModelEntity,
  ProductSkuEntity,
  WarrantyEntity,
} from '~/entities'

registerEnumType(OrderSkuItemStatus, { name: 'OrderSkuItemStatusType' })

@ObjectType('OrderSkuItem')
@Entity('order_sku_item')
export class OrderSkuItemEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  unitPrice: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((item: OrderSkuItemEntity) => item.channel)
  channelId: string

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: OrderSkuItemEntity) => item.currency)
  currencyId: string

  // TODO: this should not be nullable
  @Index()
  @Column({ type: 'uuid', nullable: true })
  cartItemId: string

  @Field(() => WarrantyEntity)
  @ManyToOne(() => WarrantyEntity, { nullable: false })
  warranty: Relation<WarrantyEntity>

  @Column()
  warrantyId: string

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  warrantyPrice: number

  @Field(() => OrderSkuItemStatus)
  @Index()
  @Column({ type: 'varchar' })
  status: OrderSkuItemStatus

  @ManyToOne(() => OrderEntity, (order) => order.orderSkuItems, { nullable: false, onDelete: 'CASCADE' })
  order: Relation<OrderEntity>

  @Column()
  @RelationId((item: OrderSkuItemEntity) => item.order)
  orderId: string

  @ManyToOne(() => ProductSkuEntity, (product) => product.orderSkuItems, { nullable: false })
  productSku: Relation<ProductSkuEntity>

  @Column()
  @RelationId((item: OrderSkuItemEntity) => item.productSku)
  productSkuId: string

  @ManyToOne(() => ProductModelEntity, (model) => model.orderItems, { nullable: false })
  productModel: Relation<ProductModelEntity>

  @Column()
  @RelationId((saleItem: OrderSkuItemEntity) => saleItem.productModel)
  productModelId: string

  @Column({ nullable: true })
  note?: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IOrderSkuItem = Omit<OrderSkuItemEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
  order: IOrder
  warranty: IWarranty
  productSku: IProductSku
  productModel: IProductModel
}
