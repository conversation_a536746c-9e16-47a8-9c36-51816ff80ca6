import {
  Date<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  EditButton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { FaqCollectionType } from '@valyuu/api/constants'
import type { IFaq } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { Select } from 'antx'
import type { FC } from 'react'

import { PublishStatus } from '~/components'
import { formatSelectOptionsFromEnum, handleTableRowClick } from '~/utils'

export const FaqList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IFaq>({
    syncWithLocation: true,
    meta: {
      fields: ['collections', 'question__en', 'sortOrder', 'createdAt', 'publishedAt'],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/faqs', id!)))}
      >
        <Table.Column dataIndex="question__en" title="Question (English)" className="cursor-pointer" />
        <Table.Column
          dataIndex="collections"
          title="Collections"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('collections', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                options={formatSelectOptionsFromEnum(FaqCollectionType)}
                placeholder="Collection"
              />
            </FilterDropdown>
          )}
          render={(value) => value.join(', ')}
        />
        <Table.Column
          dataIndex="sortOrder"
          title="Order"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
          width="5rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IFaq>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
