import { Handle, NodeProps, Position } from '@xyflow/react'

export type WarehouseNodeProps = NodeProps & {
  data: {
    label: string
    hasBuyerCountries: boolean
    hasSellerCountries: boolean
  }
}

export const WarehouseNode = ({ data }: WarehouseNodeProps) => {
  return (
    <>
      {data.hasSellerCountries && <Handle type="target" position={Position.Left} />}
      {data.label}
      {data.hasBuyerCountries && <Handle type="source" position={Position.Right} />}
    </>
  )
}
