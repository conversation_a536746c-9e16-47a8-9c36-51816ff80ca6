import { Args, Query, Resolver } from '@nestjs/graphql'

import { ProductModelSellerQuestionType, SellerPaymentPeriodUnit } from '~/constants'
import {
  GetProductModelSellerPricesInput,
  GetProductModelSellerPricesOutput,
  GetProductModelSellerQuestionsInput,
  GetProductModelSellerQuestionsOutput,
} from '~/dtos'
import { ChannelService, FrontpageMiscService, ProductModelService, ProductVariantService } from '~/services'

@Resolver()
export class ProductModelResolver {
  constructor(
    private readonly modelService: ProductModelService,
    private readonly variantService: ProductVariantService,
    private readonly frontpageMiscService: FrontpageMiscService,
    private readonly channelService: ChannelService
  ) {}

  @Query(() => GetProductModelSellerQuestionsOutput)
  async getProductModelSellerQuestions(@Args() { lang, slug }: GetProductModelSellerQuestionsInput) {
    return this.modelService.findSellerQuestions({ lang, slug })
  }

  @Query(() => GetProductModelSellerPricesOutput)
  async getProductModelSellerPrices(
    @Args() { channelId, modelId, variantId, type, problems, conditions }: GetProductModelSellerPricesInput
  ): Promise<GetProductModelSellerPricesOutput> {
    const currency = await this.channelService.findCurrencyByChannel(channelId)
    const frontpageMisc = await this.frontpageMiscService.findOne()
    const result: GetProductModelSellerPricesOutput = {
      recycle: false,
      currency,
      c2c: {
        price: 0,
        disabled: false,
        paymentPeriodFrom: undefined,
        paymentPeriodTo: 14,
        paymentPeriodUnit: SellerPaymentPeriodUnit.DAYS,
      },
      c2b: {
        price: 0,
        disabled: false,
        paymentPeriodFrom: 3,
        paymentPeriodTo: 5,
        paymentPeriodUnit: SellerPaymentPeriodUnit.DAYS,
      },
      saved: {
        co2: 0,
        totalC2o: frontpageMisc.totalSavedCo2,
        ewaste: 0,
        totalEwaste: frontpageMisc.totalSavedEwaste,
      },
    }

    const prices =
      type === ProductModelSellerQuestionType.CONDITION
        ? await this.variantService.findPricesForConditions({ modelId, variantId, channelId, conditions })
        : await this.variantService.findPricesForProblems({ modelId, variantId, channelId, problems })
    result.recycle = prices.recycle
    Object.assign(result.c2c, prices.c2c)
    Object.assign(result.c2b, prices.c2b)
    Object.assign(result.saved, prices.saved)

    return result
  }
}
