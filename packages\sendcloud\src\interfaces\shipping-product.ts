import { IShippingProductMethod } from './shipping-product-method'
import { IShippingProductMethodFunctionalities } from './shipping-product-method-functionalities'

/**
 * Represents a shipping product configuration
 */
export type IShippingProduct = {
  /** A human friendly name for the functionality */
  name: string

  /** The carrier name */
  carrier: string

  /** The carrier's service points identifier */
  service_points_carrier: string

  /** A unique identifier for the shipping product */
  code: string

  /** Indicates the minimal and maximal weight of the shipping product, in grams (bounds are inclusive) */
  weight_range: {
    min_weight: number
    max_weight: number
  }

  /** Indicate the shipping methods belonging to this shipping product */
  methods: IShippingProductMethod[]

  /** Available shipping functionalities for this product */
  available_functionalities: IShippingProductMethodFunctionalities
}
