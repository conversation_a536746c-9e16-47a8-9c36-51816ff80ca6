import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

import { WarrantyPeriodUnit } from '~/constants'

@Entity('warranty')
export class WarrantyEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Column({ type: 'int', unsigned: true })
  periodNumber: number

  @Column({ type: 'varchar', nullable: false })
  periodUnit: WarrantyPeriodUnit

  @Column()
  name__en: string

  @Column()
  name__nl: string

  @Column()
  name__de: string

  @Column()
  name__pl: string

  @Column()
  labelWithPrice__en: string

  @Column()
  labelWithPrice__nl: string

  @Column()
  labelWithPrice__de: string

  @Column()
  labelWithPrice__pl: string

  @Column()
  labelFree__en: string

  @Column()
  labelFree__nl: string

  @Column()
  labelFree__de: string

  @Column()
  labelFree__pl: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IWarranty = Omit<WarrantyEntity, keyof BaseEntity>
