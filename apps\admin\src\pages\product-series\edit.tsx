import { Edit, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useUpdate } from '@refinedev/core'
import { type IBrand, type ICategory, type IProductSeries, ITestedItemList } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, InputNumber, Select, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useState } from 'react'

import { InputLanguageTab, InputMultiLang, InputPublish, InputSlug, LanguageTabChoices } from '~/components'

export const ProductSeriesEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, id, saveButtonProps, query, formLoading } = useForm<
    IProductSeries,
    HttpError,
    IProductSeries
  >()
  const { selectProps: brandSelectProps } = useSelect<IBrand>({
    resource: 'admin/brands',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: categorySelectProps } = useSelect<ICategory>({
    resource: 'admin/categories',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: testedItemListSelectProps } = useSelect<ITestedItemList>({
    resource: 'admin/tested-item-lists',
    optionLabel: 'internalName',
    optionValue: 'id',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const record = query?.data?.data
  if (formProps.initialValues?.publishedAt) {
    formProps.initialValues.publishedAt = new Date(formProps.initialValues.publishedAt)
  }
  const { mutate } = useUpdate<IProductSeries, HttpError, Partial<IProductSeries>>()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!record?.publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
            mutate({
              resource: 'admin/product-series',
              id: id! as string,
              values: {
                publishedAt: value ? new Date() : null,
              },
              successNotification: (data) => {
                return {
                  type: 'success',
                  message: `Product series has been ${value ? 'published' : 'unpublished'} successfully`,
                }
              },
              errorNotification: () => {
                return {
                  type: 'error',
                  message: `Failed to ${value ? 'publish' : 'unpublish'} product series`,
                }
              },
            })
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Select {...(brandSelectProps as any)} label="Brand" name="brandId" rules={['required']} />
        <Select {...(categorySelectProps as any)} label="Category" name="categoryId" rules={['required']} />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Polish)"
                name="slug__pl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__pl}
                className={clsHidePl}
              />
            </>
          )}
        </Watch>
        <Select
          {...(testedItemListSelectProps as any)}
          label="Tested items list"
          name="testComponentListId"
          rules={['required']}
        />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
        <Input noStyle type="datetime" name="publishedAt" hidden />
      </Form>
    </Edit>
  )
}
