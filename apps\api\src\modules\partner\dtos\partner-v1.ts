import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator'

import {
  FaqCollectionType,
  LOCALE_ENABLED_LANGUAGES,
  SaleItemOfferConfirmationType,
  SaleItemOfferErrorType,
  SaleItemOfferStatus,
  SaleItemPlanType,
  SaleItemStatus,
  SalePaymentType,
  SaleStatus,
  SellerPaymentPeriodUnit,
} from '~/constants'
import { PartnerPlatform } from '~partner/constants'

export class ResponseWrapper<T> {
  @ApiProperty({ description: 'Indicates the success of the operation', type: Boolean })
  success: boolean

  data?: T

  @ApiPropertyOptional({ description: 'Error message if the operation fails' })
  @IsOptional()
  @IsString()
  message?: string

  @ApiProperty({ description: 'HTTP status code of the operation' })
  statusCode: number
}

export class VersionedDataResponseWrapper<T> extends ResponseWrapper<T> {
  @ApiProperty({ description: 'Indicates if the data has updates since the last version' })
  hasUpdates: boolean
}

export class V1GetCategoriesInput {
  @IsOptional()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @ApiPropertyOptional({
    enum: LOCALE_ENABLED_LANGUAGES,
    description: 'Language code. Defaults to "nl"',
    enumName: 'SupportedLangues',
  })
  lang?: (typeof LOCALE_ENABLED_LANGUAGES)[number]
}

export class V1GetCategoriesItemOutput {
  @ApiProperty({ description: 'Unique identifier for the category', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the category' })
  name: string

  @ApiProperty({ description: 'URL of the category icon' })
  icon: string

  @ApiProperty({ description: 'URL of the category image' })
  image: string
}

export class V1WrappedGetCategoriesOutput extends ResponseWrapper<V1GetCategoriesItemOutput[]> {
  @ApiProperty({ description: 'Category list', type: V1GetCategoriesItemOutput, isArray: true })
  data?: V1GetCategoriesItemOutput[]
}

export class V1GetBrandsInput {
  @IsOptional()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @ApiPropertyOptional({
    enum: LOCALE_ENABLED_LANGUAGES,
    description: 'Language code. Defaults to "nl"',
    enumName: 'SupportedLangues',
  })
  lang?: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @IsOptional()
  @IsUUID(4)
  @ApiPropertyOptional({ description: 'Category ID to filter brands' })
  categoryId?: string
}

export class V1GetBrandsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the brand', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the brand' })
  name: string

  @ApiProperty({ description: 'Image URL for the brand' })
  image: string
}

export class V1WrappedGetBrandsOutput extends ResponseWrapper<V1GetBrandsItemOutput[]> {
  @ApiProperty({ description: 'Brand list', type: V1GetBrandsItemOutput, isArray: true })
  data?: V1GetBrandsItemOutput[]
}

export class V1GetSeriesInput {
  @IsOptional()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @ApiPropertyOptional({
    enum: LOCALE_ENABLED_LANGUAGES,
    description: 'Language code. Defaults to "nl"',
    enumName: 'SupportedLangues',
  })
  lang?: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @IsOptional()
  @IsUUID(4)
  @ApiPropertyOptional({ description: 'Category ID to filter series' })
  categoryId?: string

  @IsOptional()
  @IsUUID(4)
  @ApiPropertyOptional({ description: 'Brand ID to filter series' })
  brandId?: string
}

export class V1GetSeriesItemOutput {
  @ApiProperty({ description: 'Unique identifier for the brand', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the brand' })
  name: string
}

export class V1WrappedGetSeriesOutput extends ResponseWrapper<V1GetSeriesItemOutput[]> {
  @ApiProperty({ description: 'Brand list', type: V1GetSeriesItemOutput, isArray: true })
  data?: V1GetSeriesItemOutput[]
}

export class V1GetFaqsInput {
  @IsOptional()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @ApiPropertyOptional({
    enum: LOCALE_ENABLED_LANGUAGES,
    description: 'Language code. Defaults to "nl"',
    enumName: 'SupportedLangues',
  })
  lang?: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @IsOptional()
  @IsEnum(FaqCollectionType)
  @ApiProperty({
    enum: FaqCollectionType,
    description: 'Faq collection',
    enumName: 'FaqCollectionType',
  })
  collection: FaqCollectionType
}

export class V1GetFaqsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the FAQ', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Faq question' })
  question: string

  @ApiProperty({ description: 'Answer of the FAQ question' })
  answer: string
}

export class V1WrappedGetFaqsOutput extends ResponseWrapper<V1GetFaqsItemOutput[]> {
  @ApiProperty({ description: 'Faq list', type: V1GetFaqsItemOutput, isArray: true })
  data?: V1GetFaqsItemOutput[]
}

export class V1GetModelsInput {
  @IsOptional()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @ApiPropertyOptional({
    enum: LOCALE_ENABLED_LANGUAGES,
    description: 'Language code. Defaults to "nl"',
    enumName: 'SupportedLangues',
  })
  lang?: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @IsNotEmpty()
  @IsUUID(4)
  @ApiProperty({ description: 'Category ID to filter models' })
  categoryId: string

  @IsOptional()
  @IsUUID(4)
  @ApiPropertyOptional({ description: 'Brand ID to filter models' })
  brandId?: string

  @IsIn(['TRUE', 'FALSE', 'true', 'false', null, ''])
  @IsOptional()
  @ApiPropertyOptional({
    description:
      'Flag to only show eligible models, which means that unavailable items will be hidden. Defaults to false',
    type: Boolean,
  })
  onlyShowEligible: boolean
}

export class V1GetModelsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the model', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the model' })
  name: string

  @ApiPropertyOptional({
    description:
      'Additional brand name for the model. Used when the brand name is not included in the model name, ensuring the model can be found when searching by brand name.',
  })
  additionalBrandName?: string

  @ApiProperty({ description: 'Image URL for the model' })
  image: string
}

export class V1WrappedGetModelsOutput extends ResponseWrapper<V1GetModelsItemOutput[]> {
  @ApiProperty({ description: 'Model list', type: V1GetModelsItemOutput, isArray: true })
  data?: V1GetModelsItemOutput[]
}

export class V1GetModelQuestionsInput {
  @IsOptional()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @ApiPropertyOptional({
    enum: LOCALE_ENABLED_LANGUAGES,
    description: 'Language code. Defaults to "nl"',
    enumName: 'SupportedLangues',
  })
  lang?: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @IsNotEmpty()
  @IsUUID(4)
  @ApiProperty({ description: 'Model ID to filter questions' })
  modelId: string
}

export class V1GetModelQuestionsAttributeQuestionsOptionItemOutput {
  @ApiProperty({ description: 'Unique identifier for the option', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the option' })
  name: string
}

export class V1GetModelQuestionsAttributeQuestionsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the attribute question', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the attribute question' })
  name: string

  @ApiProperty({ description: 'Question text' })
  question: string

  @ApiPropertyOptional({ description: 'Description of the attribute question' })
  description?: string

  @ApiProperty({
    description: 'Options for the attribute question',
    type: [V1GetModelQuestionsAttributeQuestionsOptionItemOutput],
  })
  options: V1GetModelQuestionsAttributeQuestionsOptionItemOutput[]
}

export class V1GetModelQuestionsAttributeCombinationsChoicesItemOutput {
  @ApiProperty({ description: 'Unique identifier for the attribute', format: 'uuid' })
  attributeId: string

  @ApiProperty({ description: 'Unique identifier for the option', format: 'uuid' })
  optionId: string
}

export class V1GetModelQuestionsAttributeCombinationsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the combination', format: 'uuid' })
  id: string

  @ApiProperty({
    description: 'Choices for the attribute combination',
    type: [V1GetModelQuestionsAttributeCombinationsChoicesItemOutput],
  })
  choices: V1GetModelQuestionsAttributeCombinationsChoicesItemOutput[]
}

export class V1GetModelQuestionsConditionQuestionsOptionsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the option', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the option' })
  name: string

  @ApiProperty({ description: 'Description of the option' })
  description: string

  @ApiPropertyOptional({ description: 'Percentage of users who chose this option' })
  percentOfChoice?: number
}

export class V1GetModelQuestionsConditionQuestionsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the condition question', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the condition question' })
  name: string

  @ApiProperty({ description: 'Question text' })
  question: string

  @ApiPropertyOptional({ description: 'Description of the condition question' })
  description?: string

  @ApiProperty({
    description: 'Options for the condition question',
    type: [V1GetModelQuestionsConditionQuestionsOptionsItemOutput],
  })
  options: V1GetModelQuestionsConditionQuestionsOptionsItemOutput[]
}

export class V1GetModelQuestionsProblemQuestionsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the problem', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the problem' })
  name: string

  @ApiPropertyOptional({ description: 'Description of the problem' })
  description?: string
}

export class V1GetModelQuestionsProblemImageTextsItemOutput {
  @ApiProperty({ description: 'Unique identifier for the problem image text', format: 'uuid' })
  id: string

  @ApiProperty({ description: 'Name of the problem image text' })
  name: string

  @ApiProperty({ description: 'Image URL for the problem image text' })
  image: string
}

export class V1GetModelQuestionsOutput {
  @ApiProperty({ description: 'Name of the model' })
  name: string

  @ApiProperty({ description: 'Image URL for the model' })
  image: string

  @ApiProperty({ description: 'Indicates if the model is eligible for trade-in', type: Boolean })
  isEligibleForTradeIn: boolean

  @ApiProperty({
    description: 'Attribute questions for the model',
    type: [V1GetModelQuestionsAttributeQuestionsItemOutput],
  })
  attributeQuestions: V1GetModelQuestionsAttributeQuestionsItemOutput[]

  @ApiProperty({
    description: 'Attribute combinations for the model',
    type: [V1GetModelQuestionsAttributeCombinationsItemOutput],
  })
  attributeCombinations: V1GetModelQuestionsAttributeCombinationsItemOutput[]

  @ApiProperty({
    description: 'Condition questions for the model',
    type: [V1GetModelQuestionsConditionQuestionsItemOutput],
  })
  conditionQuestions: V1GetModelQuestionsConditionQuestionsItemOutput[]

  @ApiProperty({
    description: 'Problem questions for the model',
    type: [V1GetModelQuestionsProblemQuestionsItemOutput],
  })
  problemQuestions: V1GetModelQuestionsProblemQuestionsItemOutput[]

  @ApiProperty({
    description: 'Problem image texts for the model',
    type: [V1GetModelQuestionsProblemImageTextsItemOutput],
  })
  problemImageTexts: V1GetModelQuestionsProblemImageTextsItemOutput[]
}

export class V1WrappedGetModelQuestionsOutput extends ResponseWrapper<V1GetModelQuestionsOutput> {
  @ApiProperty({ description: 'General questions for the model', type: V1GetModelQuestionsOutput })
  data?: V1GetModelQuestionsOutput
}

export class V1GetTradeInItemDataAttributeItemInput {
  @IsNotEmpty()
  @IsUUID(4)
  @ApiProperty({ description: 'Unique identifier for the condition', format: 'uuid' })
  attributeId: string

  @IsNotEmpty()
  @IsUUID(4)
  @ApiProperty({ description: 'Unique identifier for the option', format: 'uuid' })
  optionId: string
}

export class V1GetTradeInItemDataConditionItemInput {
  @IsNotEmpty()
  @IsUUID(4)
  @ApiProperty({ description: 'Unique identifier for the condition', format: 'uuid' })
  conditionId: string

  @IsNotEmpty()
  @IsUUID(4)
  @ApiProperty({ description: 'Unique identifier for the option', format: 'uuid' })
  optionId: string
}

export class V1GetTradeInItemDataInput {
  @IsOptional()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @ApiPropertyOptional({
    enum: LOCALE_ENABLED_LANGUAGES,
    description: 'Language code. Defaults to "nl"',
    enumName: 'SupportedLangues',
  })
  lang?: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @IsBoolean()
  @ApiProperty({ description: 'Indicates if the product is functional', type: Boolean })
  isProductFunctional: boolean

  @IsNotEmpty()
  @IsUUID(4)
  @ApiProperty({ description: 'Unique identifier for the model', format: 'uuid' })
  modelId: string

  @ValidateNested({ each: true })
  @Type(() => V1GetTradeInItemDataAttributeItemInput)
  @IsArray()
  @ApiProperty({
    description: 'Model attributes',
    type: [V1GetTradeInItemDataAttributeItemInput],
  })
  attributes: V1GetTradeInItemDataAttributeItemInput[]

  @ValidateIf(({ isProductFunctional }) => isProductFunctional)
  @ValidateNested({ each: true })
  @Type(() => V1GetTradeInItemDataConditionItemInput)
  @ArrayNotEmpty()
  @ApiProperty({
    description: 'Conditions of the product if functional',
    type: [V1GetTradeInItemDataConditionItemInput],
  })
  conditions: V1GetTradeInItemDataConditionItemInput[]

  @ValidateIf(({ isProductFunctional }) => !isProductFunctional)
  @IsUUID(4, { each: true })
  @ArrayNotEmpty()
  @ApiProperty({ description: 'Problems of the product if not functional', type: [String], format: 'uuid' })
  problems: string[]
}

export class V1GetTradeInItemDataPaymentPlanItemOutput {
  @ApiProperty({ description: 'Payment plan, now only C2B is', type: String })
  plan: SaleItemPlanType

  @ApiProperty({ description: 'Currency of the price (for display purposes only)' })
  currency: string

  @ApiProperty({
    description:
      'Trade-in price in pure number format (for both display and as an input for the API call "createTradeIn")',
  })
  price: number

  @ApiProperty({
    description: 'The minimum number of time units before payment is made',
  })
  minPaymentTime?: number

  @ApiProperty({
    description: 'The maximum number of time units before payment is made',
  })
  maxPaymentTime: number

  @ApiProperty({
    enum: SellerPaymentPeriodUnit,
    description: 'The time unit for the payment time',
    enumName: 'PaymentTimeUnit',
  })
  paymentTimeUnit: SellerPaymentPeriodUnit
}

export class V1GetTradeInItemDataEcoSavingsOutput {
  @IsNumber()
  @Min(0)
  @ApiProperty({ description: 'CO2 savings of the item, in g' })
  savedCo2: number

  @IsNumber()
  @Min(0)
  @ApiProperty({ description: 'E-waste savings of the item, in g' })
  savedEwaste: number
}

export class V1GetTradeInItemDataOutput {
  @ApiProperty({
    description: 'Unique identifier for the variant (used as an input for the API call "createTradeIn")',
    format: 'uuid',
  })
  variantId: string

  @ApiProperty({
    description: 'Indicates if the product is functional (used as an input for the API call "createTradeIn")',
    type: Boolean,
  })
  isProductFunctional: boolean

  @ApiPropertyOptional({
    description:
      'Unique identifier for the condition combination, only available when isProductFunctional is true (used as an input for the API call "createTradeIn")',
    format: 'uuid',
  })
  conditionCombinationId?: string

  @ApiPropertyOptional({
    description:
      'Unique identifier for the problems, only available when isProductFunctional is false (used as an input for the API call "createTradeIn")',
    type: [String],
    format: 'uuid',
  })
  problemIds?: string[]

  @ApiProperty({ description: 'Product model name (for display purposes only)' })
  name: string

  @ApiProperty({ description: 'Product model image (for display purposes only)' })
  image: string

  @ApiProperty({ description: 'Product model attributes (for display purposes only)', type: [String] })
  attributes: string[]

  @ApiProperty({ description: "User's answers (for display purposes only)", type: [String] })
  answers: string[]

  @ApiProperty({
    description:
      "All payment plans and prices a user can get, with processing time, currently only C2B is supported. If it's an empty array, then this product is not eligible for trade-in",
    type: [V1GetTradeInItemDataPaymentPlanItemOutput],
  })
  paymentPlans: V1GetTradeInItemDataPaymentPlanItemOutput[]

  @ApiProperty({ description: 'Indicates if the item is eligible for trade-in and has trade-in value' })
  isEligibleForTradeIn: boolean

  @ValidateNested()
  @Type(() => V1GetTradeInItemDataEcoSavingsOutput)
  @ApiProperty({ description: 'Eco savings of the item', type: V1GetTradeInItemDataEcoSavingsOutput })
  ecoSavings: V1GetTradeInItemDataEcoSavingsOutput
}

export class V1WrappedGetTradeInItemDataOutput extends ResponseWrapper<V1GetTradeInItemDataOutput> {
  @ApiProperty({ description: 'Get product item data output', type: V1GetTradeInItemDataOutput })
  data?: V1GetTradeInItemDataOutput
}

export class V1CreateTradeInShippingAddressInput {
  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'First name of the recipient' })
  firstName: string

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Last name of the recipient' })
  lastName: string

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Country of the recipient' })
  country: string

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Postal code of the recipient' })
  postalCode: string

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'House number of the recipient' })
  houseNumber: string

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: 'Additional address information' })
  addition?: string

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Street name of the recipient' })
  street: string

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'City of the recipient' })
  city: string

  @ValidateIf((o) => !!o.phoneNumber || !!o.phoneAreaCode)
  @IsNotEmpty()
  @IsString()
  @Matches(/^\+\d{1,4}$/, { message: 'phoneAreaCode must be a valid European country code' })
  @ApiPropertyOptional({ description: 'Country code of the recipient, for example +31' })
  phoneAreaCode?: string

  @ValidateIf((o) => !!o.phoneNumber || !!o.phoneAreaCode)
  @IsNotEmpty()
  @Matches(/^\d{6,}$/, { message: 'phoneNumber must be a string of numbers and more than 5 digits.' })
  @ApiPropertyOptional({
    description: "Pure number format of the recipient's phone number, without letters or separators",
  })
  phoneNumber?: string
}

export class V1CreateTradeInBankAccountInput {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: 'Name of the account holder' })
  holderName?: string

  @IsNotEmpty()
  @IsString()
  @ApiProperty({ description: 'Bank account number' })
  accountNumber: string
}

export class V1CreateTradeInItemInput {
  @IsUUID(4)
  @IsNotEmpty()
  @ApiProperty({ description: 'Unique identifier for the variant', format: 'uuid' })
  variantId: string

  @IsNumber()
  @Min(0)
  @ApiProperty({ description: 'Price of the item' })
  price: number

  @IsIn(['C2B', 'C2C'])
  @ApiProperty({ description: 'Plan of the item' })
  plan: SaleItemPlanType

  @IsBoolean()
  @ApiProperty({ description: 'Indicates if the product is functional' })
  isProductFunctional: boolean

  @ValidateIf(({ isProductFunctional }) => isProductFunctional)
  @IsNotEmpty()
  @ApiPropertyOptional({
    description: 'Condition combination ID of the product if functional',
    format: 'uuid',
  })
  conditionCombinationId?: string

  @ValidateIf(({ isProductFunctional }) => !isProductFunctional)
  @IsUUID(4, { each: true })
  @ArrayNotEmpty()
  @ApiPropertyOptional({ description: 'Problem ids of the product if not functional', type: [String], format: 'uuid' })
  problemIds?: string[]
}

export class V1CreateTradeInInput {
  @IsOptional()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @ApiPropertyOptional({
    enum: LOCALE_ENABLED_LANGUAGES,
    description: 'Language code. Defaults to "nl"',
    enumName: 'SupportedLangues',
  })
  lang?: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @IsEmail()
  @ApiProperty({ description: 'Email of the seller' })
  email: string

  @ValidateIf((o) => o.paymentType === SalePaymentType.BANK_TRANSFER && o.dateOfBirth != null)
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'dateOfBirth must be in the format of YYYY-MM-DD',
  })
  @ApiProperty({
    description: 'Date of birth of the seller in the format YYYY-MM-DD',
    required: false,
  })
  dateOfBirth?: string

  @ValidateNested()
  @Type(() => V1CreateTradeInShippingAddressInput)
  @ApiProperty({ description: 'Shipping address of the seller', type: V1CreateTradeInShippingAddressInput })
  shippingAddress: V1CreateTradeInShippingAddressInput

  @ValidateNested({ each: true })
  @Type(() => V1CreateTradeInItemInput)
  @ArrayNotEmpty()
  @ApiProperty({ description: 'List of items to be sold', type: [V1CreateTradeInItemInput] })
  items: V1CreateTradeInItemInput[]

  @IsEnum(SalePaymentType)
  @IsOptional()
  @ApiPropertyOptional({
    enum: SalePaymentType,
    description: 'Payment type, defaults to BANK_TRANSFER',
    enumName: 'TradeInPaymentType',
  })
  paymentType?: SalePaymentType

  @ValidateIf(({ paymentType }) => paymentType === SalePaymentType.BANK_TRANSFER)
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => V1CreateTradeInBankAccountInput)
  @ApiPropertyOptional({
    description: 'Bank account information of the seller. Only required if paymentType is set to BANK_TRANSFER',
    type: V1CreateTradeInBankAccountInput,
  })
  bankAccount?: V1CreateTradeInBankAccountInput

  @IsEnum(PartnerPlatform)
  @IsOptional()
  @ApiPropertyOptional({
    enum: PartnerPlatform,
    description: "Partner's platform type: standalone sub-site or embedded solution. Defaults to EMBEDDED",
    enumName: 'PartnerPlatform',
    default: PartnerPlatform.EMBEDDED,
  })
  partnerPlatform?: PartnerPlatform
}

export class V1CreateTradeInOutput {
  @ApiProperty({ description: 'Unique identifier for the trade-in', format: 'uuid' })
  tradeInId: string

  @ApiProperty({ description: 'Unique identifier for the user', format: 'uuid' })
  userId: string

  @ApiProperty({ description: 'Human readable order number associated with the trade-in' })
  tradeInOrderNumber: string

  @ApiProperty({
    description: 'Version number for this trade-in order',
  })
  version: number
}

export class V1WrappedCreateTradeInOutput extends ResponseWrapper<V1CreateTradeInOutput> {
  @ApiProperty({ description: 'Trade-in output', type: V1CreateTradeInOutput })
  data?: V1CreateTradeInOutput
}

export class V1GetTradeInOrderItemDataInput {
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === '') return undefined
    return Number(value)
  })
  @IsNumber()
  @ApiPropertyOptional({
    description:
      'Version number to check for trade-in item data changes (distinct from trade-in order version, they are separate). If provided and data is unchanged since this version, response will have empty data. If omitted, returns latest data',
  })
  version?: number
}
export class V1TradeInOrderItemDataReturnShipmentOutput {
  @ApiProperty({ description: 'Tracking number of the return shipment' })
  trackingNumber?: string

  @ApiProperty({ description: 'Tracking URL of the return shipment' })
  trackingUrl?: string
}

export class V1TradeInOrderItemDataOutput {
  @ApiProperty({ description: 'Revision version number of the item' })
  tradeInItemVersion: number

  @ApiProperty({ description: 'Unique identifier for the trade-in', format: 'uuid' })
  tradeInId: string

  @ApiProperty({ description: 'Unique identifier for the trade-in item', format: 'uuid' })
  tradeInItemId: string

  @ApiProperty({
    enum: SaleItemStatus,
    description: 'Status of the item',
    enumName: 'TradeInOrderItemStatus',
  })
  status: SaleItemStatus

  @ApiPropertyOptional({
    description: 'Tracking number of the return shipment',
    type: V1TradeInOrderItemDataReturnShipmentOutput,
  })
  returnShipment?: V1TradeInOrderItemDataReturnShipmentOutput

  @ApiPropertyOptional({ description: "Unique identifier for the problem trade-in item's new offer", format: 'uuid' })
  offerId?: string

  @ApiProperty({
    enum: SaleItemOfferStatus,
    description: 'Offer status of the item',
    enumName: 'TradeInOrderItemOfferStatus',
  })
  offerStatus: SaleItemOfferStatus

  @ApiPropertyOptional({ description: 'Auth token for offer confirmation in frontend' })
  offerAuthToken?: string

  @ApiPropertyOptional({ description: 'Offer expiration date' })
  offerExpiresAt?: Date

  @ApiPropertyOptional({ description: 'Offer note of the item' })
  offerGradingNote?: string

  @ApiProperty({
    description: 'Unique identifier for the variant (used as an input for the API call "createTradeIn")',
    format: 'uuid',
  })
  variantId: string

  @ApiProperty({
    description: 'Indicates if the product is functional (used as an input for the API call "createTradeIn")',
    type: Boolean,
  })
  isProductFunctional: boolean

  @ApiProperty({ description: 'Product model name (for display purposes only)' })
  name: string

  @ApiProperty({ description: 'Product model image (for display purposes only)' })
  image: string

  @ApiProperty({ description: 'Product model attributes (for display purposes only)', type: [String] })
  attributes: string[]

  @ApiProperty({ description: "User's answers (for display purposes only)", type: [String] })
  answers: string[]

  @ApiProperty({
    description: 'Payment plan and prices a user can get, with processing time, currently only C2B is supported',
    type: V1GetTradeInItemDataPaymentPlanItemOutput,
  })
  paymentPlan: V1GetTradeInItemDataPaymentPlanItemOutput
}

export class V1GetTradeInOrderDataInput {
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === '') return undefined
    return Number(value)
  })
  @IsNumber()
  @ApiPropertyOptional({
    description:
      'Version number to check for trade-in order data changes (distinct from trade-in item version, they are separate). If provided and data is unchanged since this version, response will have empty data. If omitted, returns latest data',
  })
  version?: number

  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === '') return undefined
    return value === 'true' || value === 'TRUE' || value === true
  })
  @IsBoolean()
  @ApiPropertyOptional({ description: 'Flag to return trade-in items. Defaults to false' })
  returnTradeInItems?: boolean
}

export class V1TradeInOrderDataOutput {
  @ApiProperty({ description: 'Revision version number of the trade-in' })
  tradeInVersion: number

  @ApiProperty({ description: 'Unique identifier for the trade-in', format: 'uuid' })
  tradeInId: string

  @ApiProperty({ description: 'Unique identifier for the user', format: 'uuid' })
  userId: string

  @ApiProperty({ description: 'Human readable order number associated with the trade-in' })
  tradeInOrderNumber: string

  @ApiProperty({
    enum: SaleStatus,
    description: 'Status of the trade-in',
    enumName: 'TradeInOrderStatus',
  })
  status: SaleStatus

  @ApiProperty({ description: 'Indicates if the shipping label is paperless' })
  isShippingLabelPaperless: boolean

  @ApiProperty({ description: 'Tracking number of the shipping label' })
  trackingNumber?: string

  @ApiProperty({ description: 'Tracking URL of the shipping label' })
  trackingUrl?: string

  @ApiProperty({
    description: 'List of items in the trade-in. If returnTradeInItems is false, this will be undefined',
    type: [V1TradeInOrderItemDataOutput],
  })
  items?: V1TradeInOrderItemDataOutput[]
}

export class V1WrappedGetTradeInOrderDataOutput extends VersionedDataResponseWrapper<V1TradeInOrderDataOutput> {
  @ApiProperty({ description: 'Get trade-in order data', type: V1TradeInOrderDataOutput })
  data?: V1TradeInOrderDataOutput
}

export class V1WrappedGetTradeInOrderItemDataOutput extends VersionedDataResponseWrapper<V1TradeInOrderItemDataOutput> {
  @ApiProperty({ description: 'Get trade-in order item data', type: V1TradeInOrderItemDataOutput })
  data?: V1TradeInOrderItemDataOutput
}

export class V1WrappedCancelTradeInOutput extends ResponseWrapper<undefined> {}

export class V1ConfirmTradeInItemOfferInput {
  @IsEnum(SaleItemOfferConfirmationType)
  @ApiProperty({
    enum: SaleItemOfferConfirmationType,
    description: 'Confirmation type',
    enumName: 'TradeInItemOfferConfirmationType',
  })
  confirmationType: SaleItemOfferConfirmationType

  @IsString()
  @ApiProperty({ description: 'Auth token for the confirmation' })
  authToken: string
}

export class V1ConfirmTradeInItemOfferOutput {
  @ApiProperty({
    enum: SaleItemOfferErrorType,
    description: 'Error reason',
    enumName: 'TradeInItemOfferErrorType',
  })
  errorReason: SaleItemOfferErrorType | null
}

export class V1WrappedConfirmTradeInItemOfferOutput extends ResponseWrapper<V1ConfirmTradeInItemOfferOutput> {}

export class V1ValidateSellerInfoInput {
  @ApiProperty({ description: 'Email of the seller' })
  email: string

  @ApiProperty({ description: 'First name of the seller' })
  firstName: string

  @ApiProperty({ description: 'Last name of the seller' })
  lastName: string

  @ApiPropertyOptional({ description: 'Phone area code of the seller' })
  phoneAreaCode?: string

  @ApiPropertyOptional({ description: 'Phone number of the seller' })
  phoneNumber?: string

  @ApiProperty({ description: 'Country of the seller' })
  country: string

  @ApiProperty({ description: 'Postal code of the seller' })
  postalCode: string

  @ApiProperty({ description: 'House number of the seller' })
  houseNumber: string

  @ApiPropertyOptional({ description: 'Additional address information' })
  addition?: string

  @ApiProperty({ description: 'Street name of the seller' })
  street: string

  @ApiProperty({ description: 'City of the seller' })
  city: string

  @ApiPropertyOptional({ description: 'Date of birth of the seller in the format YYYY-MM-DD' })
  dateOfBirth?: string

  @ApiProperty({ description: 'Name of the account holder' })
  holderName: string

  @ApiProperty({ description: 'Bank account number' })
  accountNumber: string
}

export class V1ValidateSellerInfoOutput {
  @ApiProperty({ description: 'Whether the seller info is valid' })
  success: boolean

  @ApiPropertyOptional({ description: 'Field that caused the error' })
  @IsOptional()
  @IsString()
  field?: string

  @ApiPropertyOptional({ description: 'Error message if the seller info is invalid' })
  @IsOptional()
  @IsString()
  errorMessage?: string
}

export class V1WrappedValidateSellerInfoOutput extends ResponseWrapper<V1ValidateSellerInfoOutput> {
  @ApiProperty({ description: 'Validate seller info output', type: V1ValidateSellerInfoOutput })
  data?: V1ValidateSellerInfoOutput
}
