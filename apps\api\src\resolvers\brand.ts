import { Args, Info, Query, Resolver } from '@nestjs/graphql'
import { GraphQLResolveInfo } from 'graphql'
import { GraphRelationBuilder } from 'typeorm-relations-graphql'

import { GetBrandsByCategoryInput, GetBrandsOutput } from '~/dtos'
import { BrandEntity } from '~/entities'
import { BrandService } from '~/services'

@Resolver()
export class BrandResolver {
  constructor(
    private readonly relationBuilder: GraphRelationBuilder,
    private readonly service: BrandService
  ) {}

  @Query(() => [GetBrandsOutput])
  async getBrands(@Info() info: GraphQLResolveInfo) {
    const relations = this.relationBuilder.buildForQuery(BrandEntity, info).toFindOptionsRelations()

    return this.service.findAll(relations)
  }

  @Query(() => [GetBrandsOutput])
  async getBrandsByCategory(@Info() info: GraphQLResolveInfo, @Args() { lang, slug }: GetBrandsByCategoryInput) {
    const relations = this.relationBuilder.buildForQuery(BrandEntity, info).toFindOptionsRelations()

    return this.service.findAllByCategory({ lang, slug, relations })
  }
}
