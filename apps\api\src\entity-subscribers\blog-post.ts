import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageUsedInType } from '~/constants'
import { BlogPostEntity } from '~/entities'
import { entityFixPublishedAt, entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'

@Injectable()
@EventSubscriber()
export class BlogPostSubscriber implements EntitySubscriberInterface<BlogPostEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return BlogPostEntity
  }

  async beforeInsert(event: InsertEvent<BlogPostEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async afterInsert(event: InsertEvent<BlogPostEntity>) {
    const { entity, manager } = event
    // process heroImage
    await entityImageAutoProcess({
      image: entity.heroImage,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.BLOG_POST,
      manager,
    })
  }

  async beforeUpdate(event: UpdateEvent<BlogPostEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }

  async afterUpdate(event: UpdateEvent<BlogPostEntity>) {
    const { entity, manager } = event

    // process heroImage
    await entityImageAutoProcess({
      image: entity.heroImage ?? { id: entity.heroImageId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.BLOG_POST,
      manager,
    })
  }

  async afterRemove(event: RemoveEvent<BlogPostEntity>) {
    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.BLOG_POST,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
