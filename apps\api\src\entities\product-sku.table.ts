import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import * as Sentry from '@sentry/nestjs'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  EntityManager,
  Index,
  IsNull,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  Not,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  Unique,
  UpdateDateColumn,
} from 'typeorm'

import {
  ProductSkuBtwType,
  ProductSkuColor,
  ProductSkuExtraAttributeType,
  ProductSkuExtraTestedItemType,
  ProductSkuGrading,
  ProductSkuSourceType,
} from '~/constants'
import {
  BrandEntity,
  CategoryEntity,
  CategoryWarrantyRuleEntity,
  ChannelEntity,
  FileEntity,
  type IBrand,
  type ICategory,
  type ICategoryWarrantyRuleWarrantyItem,
  type IFile,
  type IImage,
  ImageEntity,
  type IMarketingSkuCollection,
  type IOrderSkuItem,
  type IPreOrder,
  type IProductModel,
  type IProductModelMetrics,
  type IProductSkuOriginalAccessory,
  type IProductSkuPrice,
  type IProductVariant,
  type IStock,
  MarketingSkuCollectionEntity,
  OrderSkuItemEntity,
  PreOrderEntity,
  ProductModelEntity,
  ProductModelMetricsEntity,
  ProductSkuOriginalAccessoryEntity,
  ProductSkuPriceEntity,
  ProductVariantEntity,
  SaleItemEntity,
  StockEntity,
} from '~/entities'

registerEnumType(ProductSkuGrading, { name: 'ProductGradingType' })
registerEnumType(ProductSkuExtraAttributeType, { name: 'ProductSkuExtraAttributeType' })
registerEnumType(ProductSkuExtraTestedItemType, { name: 'ProductSkuExtraFieldType' })

@ObjectType()
export class ProductSkuExtraTestedItem {
  @Field(() => ProductSkuExtraTestedItemType)
  type: ProductSkuExtraTestedItemType = ProductSkuExtraTestedItemType.OTHER

  @Field()
  name__en: string

  @Field()
  name__nl: string

  @Field()
  name__de: string

  @Field()
  name__pl: string
}

@ObjectType()
export class ProductSkuExtraAttribute {
  @Field(() => ProductSkuExtraAttributeType)
  type: ProductSkuExtraAttributeType = ProductSkuExtraAttributeType.OTHER

  @Field()
  name__en: string

  @Field()
  name__nl: string

  @Field()
  name__de: string

  @Field()
  name__pl: string
}

@ObjectType('ProductSku')
@Entity('product_sku')
@Unique(['slug__en', 'slugNumber__en'])
@Unique(['slug__nl', 'slugNumber__nl'])
@Unique(['slug__de', 'slugNumber__de'])
@Unique(['slug__pl', 'slugNumber__pl'])
export class ProductSkuEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @OneToOne(() => StockEntity, (stock) => stock.saleItem, { nullable: false })
  @JoinColumn()
  stock: Relation<StockEntity>

  @Column({ nullable: true })
  @RelationId((saleItem: SaleItemEntity) => saleItem.stock)
  stockId: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column()
  slug__en: string

  @Field()
  @Column()
  slug__nl: string

  @Field()
  @Column()
  slug__de: string

  @Field()
  @Column()
  slug__pl: string

  @Field()
  @Column({
    type: 'int4',
    unsigned: true,
    transformer: { from: (value) => String(value), to: (value) => value },
  })
  @Index()
  slugNumber__en: string

  @Field()
  @Column({
    type: 'int4',
    unsigned: true,
    transformer: { from: (value) => String(value), to: (value) => value },
  })
  @Index()
  slugNumber__nl: string

  @Field()
  @Column({
    type: 'int4',
    unsigned: true,
    transformer: { from: (value) => String(value), to: (value) => value },
  })
  @Index()
  slugNumber__de: string

  @Field()
  @Column({
    type: 'int4',
    unsigned: true,
    transformer: { from: (value) => String(value), to: (value) => value },
  })
  @Index()
  slugNumber__pl: string

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  desc__en?: string | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  desc__nl?: string | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  desc__de?: string | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  desc__pl?: string | null

  @OneToMany(() => ProductSkuPriceEntity, (price) => price.sku, { nullable: false, cascade: true })
  prices: Relation<ProductSkuPriceEntity>[]

  @Field()
  @Column({ type: 'varchar' })
  @Index()
  grading: ProductSkuGrading

  @Field()
  @Column({ type: 'varchar' })
  @Index()
  color: ProductSkuColor

  @Field()
  @Column()
  displayColor__en: string

  @Field()
  @Column()
  displayColor__nl: string

  @Field()
  @Column()
  displayColor__de: string

  @Field()
  @Column()
  displayColor__pl: string

  @Column()
  mpn: string

  @Column({ type: 'varchar' })
  btw: ProductSkuBtwType

  @Column()
  serialNumber: string

  @Field(() => [ProductSkuExtraAttribute], { defaultValue: [] })
  @Column({ type: 'jsonb', default: [] })
  extraAttributes: ProductSkuExtraAttribute[]

  @Field(() => [ProductSkuExtraTestedItem], { defaultValue: [] })
  @Column({ type: 'jsonb', default: [] })
  extraTestedItems: ProductSkuExtraTestedItem[]

  @Field()
  @Column({ default: false })
  @Index()
  sold: boolean

  @Field()
  @Column()
  isInternal: boolean

  @Field(() => ImageEntity)
  @ManyToOne(() => ImageEntity, (image) => image.productSkuHeroImages, { nullable: false, cascade: true })
  @JoinColumn()
  heroImage: Relation<ImageEntity>

  @Column()
  @RelationId((productSku: ProductSkuEntity) => productSku.heroImage)
  heroImageId: string

  @Field(() => [ImageEntity])
  @ManyToMany(() => ImageEntity, (image) => image.productSkuImages, { nullable: false, cascade: true })
  @JoinTable({ name: 'product_sku_images' })
  images: Relation<ImageEntity>[]

  @Column({ type: 'varchar' })
  source: ProductSkuSourceType

  @Column({ nullable: true })
  note?: string | null

  @Field(() => ProductVariantEntity)
  @ManyToOne(() => ProductVariantEntity, (variant) => variant.productSkus, { nullable: false })
  variant: Relation<ProductVariantEntity>

  @Column()
  @RelationId((productSku: ProductSkuEntity) => productSku.variant)
  variantId: string

  @Field(() => ProductModelEntity)
  @ManyToOne(() => ProductModelEntity, (model) => model.skus, { nullable: false })
  model: Relation<ProductModelEntity>

  @Column()
  @RelationId((productSku: ProductSkuEntity) => productSku.model)
  modelId: string

  @Field(() => CategoryEntity)
  @ManyToOne(() => CategoryEntity, { nullable: false })
  category: Relation<CategoryEntity>

  @Column()
  @RelationId((productSku: ProductSkuEntity) => productSku.category)
  categoryId: string

  @Field(() => BrandEntity)
  @ManyToOne(() => BrandEntity, { nullable: false })
  brand: Relation<BrandEntity>

  @Column()
  @RelationId((productSku: ProductSkuEntity) => productSku.brand)
  brandId: string

  @Field(() => FileEntity, { nullable: true })
  @OneToOne(() => FileEntity, (file) => file.productSkuTestReport, { nullable: true, cascade: true })
  @JoinColumn()
  testReport?: Relation<FileEntity> | null

  @Column({ nullable: true })
  @RelationId((productSku: ProductSkuEntity) => productSku.testReport)
  testReportId?: string | null

  @Field(() => [ProductSkuOriginalAccessoryEntity])
  @ManyToMany(() => ProductSkuOriginalAccessoryEntity, (accessory) => accessory.skus, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinTable({ name: 'product_sku_original_accessories' })
  originalAccessories: Relation<ProductSkuOriginalAccessoryEntity>[]

  @Field(() => [CategoryWarrantyRuleEntity])
  @ManyToMany(() => CategoryWarrantyRuleEntity, { nullable: false })
  @JoinTable({ name: 'product_sku_warranty_rules' })
  warrantyRules: Relation<CategoryWarrantyRuleEntity>[]

  @OneToMany(() => OrderSkuItemEntity, (orderSkuItem) => orderSkuItem.productSku)
  orderSkuItems: Relation<OrderSkuItemEntity>[]

  @ManyToMany(() => PreOrderEntity, (preOrder) => preOrder.productSkus)
  preOrders: Relation<PreOrderEntity>[]

  @OneToOne(() => SaleItemEntity, (saleItem) => saleItem.productSku, { nullable: true })
  @JoinColumn()
  saleItem?: Relation<SaleItemEntity> | null

  @Column({ nullable: true })
  @RelationId((productSku: ProductSkuEntity) => productSku.saleItem)
  saleItemId?: string | null

  @Column({ default: false })
  excludeFromBestseller: boolean

  @Column({ default: false })
  excludeFromMvp: boolean

  @ManyToMany(() => MarketingSkuCollectionEntity, (collection) => collection.productSkus, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinTable({ name: 'product_sku_marketing_sku_collections' })
  marketingSkuCollections: Relation<MarketingSkuCollectionEntity>[]

  @ManyToOne(() => ProductModelMetricsEntity, (metrics) => metrics.skus, { nullable: false })
  modelMetrics: Relation<ProductModelMetricsEntity>

  @Column()
  @RelationId((productSku: ProductSkuEntity) => productSku.modelMetrics)
  modelMetricsId: string

  @Field()
  @Column({ default: '2022' })
  sellerJoinedYear: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null

  static async getWarrantyRuleIds({
    id,
    manager = CategoryEntity.getRepository().manager,
    allowSold = true,
  }: {
    id: string
    manager?: EntityManager
    allowSold?: boolean
  }): Promise<string[]> {
    // TODO: if disableSeller / disableBuyer is changed, then what to do with the data?
    const channels = await manager.find(ChannelEntity, {
      where: { publishedAt: Not(IsNull()), disableBuyer: false },
      select: { id: true },
    })
    const sku: ProductSkuEntity = await manager.findOne(ProductSkuEntity, {
      where: { id },
      relations: { prices: true, model: { category: { warrantyRules: true } } },
      select: {
        id: true,
        sold: true,
        grading: true,
        prices: {
          id: true,
          channelId: true,
          originalPrice: true,
        },
        model: {
          id: true,
          category: {
            id: true,
            warrantyRules: {
              id: true,
              channelId: true,
              eligibleGrades: true,
              eligibleMinSkuPrice: true,
              eligibleMaxSkuPrice: true,
            },
          },
        },
      },
    })
    if (!sku?.prices?.[0]?.id || (!allowSold && sku.sold)) {
      Sentry.captureException(new Error('sku not found or sold'), {
        tags: {
          type: 'product-sku:warranty-rule-ids-not-found',
        },
        extra: {
          skuId: sku.id,
        },
        level: 'warning',
      })
      return []
    }
    const category = sku.model.category
    if (!category?.warrantyRules?.[0]) {
      Sentry.captureException(new Error('category has no warranty rules'), {
        tags: {
          type: 'product-sku:warranty-rule-ids-not-found',
        },
        extra: {
          categoryId: category.id,
        },
      })
      return []
    }
    const ruleIds: string[] = []
    for (const channel of channels) {
      const skuPrice = sku.prices.find((price) => price.channelId === channel.id)
      if (!skuPrice) {
        continue
      }
      const channelRule = category.warrantyRules
        .filter((rule) => rule.channelId === channel.id)
        .find(({ eligibleGrades, eligibleMaxSkuPrice, eligibleMinSkuPrice }) => {
          if (!eligibleGrades?.includes(sku.grading)) {
            return false
          }
          if (
            skuPrice.originalPrice > (eligibleMaxSkuPrice ?? Infinity) ||
            skuPrice.originalPrice < (eligibleMinSkuPrice ?? 0)
          ) {
            return false
          }
          return true
        })
      if (channelRule) {
        ruleIds.push(channelRule.id)
      }
    }
    return ruleIds
  }
}

export type IProductSku = Omit<ProductSkuEntity, keyof BaseEntity> & {
  prices: IProductSkuPrice[]
  heroImage: IImage
  images: IImage[]
  variant: IProductVariant
  model: IProductModel
  category: ICategory
  brand: IBrand
  testReport: IFile | null
  originalAccessories: IProductSkuOriginalAccessory[]
  preOrders: IPreOrder[]
  warranties: ICategoryWarrantyRuleWarrantyItem[]
  orderSkuItems: IOrderSkuItem[]
  marketingCollections: IMarketingSkuCollection[]
  modelMetrics: IProductModelMetrics
  stock: IStock
}
