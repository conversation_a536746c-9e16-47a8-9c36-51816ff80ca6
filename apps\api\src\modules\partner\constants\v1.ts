export enum PartnerPlatform {
  STANDALONE = 'STANDALONE',
  EMBEDDED = 'EMBEDDED',
}

export enum PartnerApiLogDirection {
  INBOUND = 'INBOUND',
  OUTBOUND = 'OUTBOUND',
}

export enum PartnerShippingLabelFormat {
  PDF = 'PDF',
  PNG = 'PNG',
}

export enum PartnerWebhookEventType {
  TRADE_IN_CREATED = 'TRADE_IN_CREATED',
  TRADE_IN_CREATION_FAILED = 'TRADE_IN_CREATION_FAILED',
  TRADE_IN_STATUS_CHANGED = 'TRADE_IN_STATUS_CHANGED',
  TRADE_IN_ITEM_STATUS_CHANGED = 'TRADE_IN_ITEM_STATUS_CHANGED',
  TRADE_IN_ITEM_OFFER_STATUS_CHANGED = 'TRADE_IN_ITEM_OFFER_STATUS_CHANGED',
}
