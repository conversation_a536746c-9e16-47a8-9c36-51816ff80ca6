import { CrudRequest } from '@dataui/crud'
import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { plainToClass } from 'class-transformer'
import { isNil } from 'lodash'
import * as pMap from 'p-map'
import { DeepPartial, In, Repository } from 'typeorm'

import {
  ProductVariantConditionCombinationChoiceEntity,
  ProductVariantConditionCombinationEntity,
  QuestionTypeConditionEntity,
  QuestionTypeConditionOptionEntity,
  QuestionTypeEntity,
  QuestionTypeProblemEntity,
} from '~/entities'

export class CrudQuestionTypeService extends TypeOrmCrudService<QuestionTypeEntity> {
  constructor(@InjectRepository(QuestionTypeEntity) repo: Repository<QuestionTypeEntity>) {
    super(repo)
  }

  // for removing deleted conditions, options, problemsetc.
  public async updateOne(req: CrudRequest, dto: DeepPartial<QuestionTypeEntity>): Promise<QuestionTypeEntity> {
    const { allowParamsOverride, returnShallow } = req.options.routes.updateOneBase
    const paramsFilters = this.getParamFilters(req.parsed)
    // disable cache while updating
    req.options.query.cache = false
    let updated: QuestionTypeEntity

    // for publish/unpublish from refine
    const isPublishUnpublish = Object.hasOwnProperty.call(dto, 'publishedAt') && Object.keys(dto).length === 1
    await QuestionTypeEntity.getRepository().manager.transaction(async (manager) => {
      if (!isPublishUnpublish) {
        const existingQuestionType = await manager.findOneOrFail(QuestionTypeEntity, {
          where: paramsFilters,
          relations: {
            conditions: {
              options: true,
            },
            problems: true,
          },
        })
        const oldConditions: QuestionTypeConditionEntity[] = existingQuestionType.conditions.flatMap((condition) =>
          condition.id ? [condition] : []
        )
        const newConditionIds: string[] =
          dto.conditions?.flatMap((condition) => (condition.id ? [condition.id] : [])) ?? []
        const oldConditionsToDelete = oldConditions.filter((condition) => !newConditionIds.includes(condition.id))

        if (oldConditionsToDelete.length) {
          await pMap(
            oldConditionsToDelete,
            async (condition) => {
              await manager.remove(condition)
            },
            { concurrency: 5 }
          )
        }

        const oldProblems: QuestionTypeProblemEntity[] = existingQuestionType.problems.flatMap((problem) =>
          problem.id ? [problem] : []
        )
        const newProblemIds: string[] = dto.problems?.flatMap((problem) => (problem.id ? [problem.id] : [])) ?? []
        const oldProblemsToDelete = oldProblems.filter((problem) => !newProblemIds.includes(problem.id))

        if (oldProblemsToDelete.length) {
          await pMap(
            oldProblemsToDelete,
            async (problem) => {
              await manager.remove(problem)
            },
            { concurrency: 5 }
          )
        }

        const oldOptions: QuestionTypeConditionOptionEntity[] = existingQuestionType.conditions.flatMap((condition) =>
          condition.options.flatMap((option) => (option.id ? [option] : []))
        )
        const newOptionIds: string[] =
          dto.conditions
            ?.flatMap((condition) => condition.options.flatMap((option) => (option.id ? [option.id] : [])))
            .flat() ?? []

        const oldOptionsToDelete = oldOptions.filter((option) => !newOptionIds.includes(option.id))

        if (oldOptionsToDelete.length) {
          const conditionCombinationsToDelete = (
            await manager.find(ProductVariantConditionCombinationChoiceEntity, {
              where: { optionId: In(oldOptionsToDelete.map((o) => o.id)) },
              select: { combinationId: true },
            })
          ).reduce((acc, { combinationId }) => {
            return acc.includes(combinationId) ? acc : [...acc, combinationId]
          }, [] as string[])
          await pMap(conditionCombinationsToDelete, async (combinationId) => {
            const combination = await manager.findOneBy(ProductVariantConditionCombinationEntity, { id: combinationId })
            if (combination) {
              await manager.remove(combination)
            }
          })
          await pMap(
            oldOptionsToDelete,
            async (option) => {
              await manager.remove(option)
            },
            { concurrency: 5 }
          )
        }
      }

      updated = await manager.save(
        QuestionTypeEntity,
        plainToClass(
          this.entityType,
          { ...dto, ...(allowParamsOverride ? {} : paramsFilters), ...req.parsed.authPersist },
          req.parsed.classTransformOptions
        ) as unknown as DeepPartial<QuestionTypeEntity>
      )
    })

    if (returnShallow) {
      return updated
    } else {
      req.parsed.paramsFilter.forEach((filter) => {
        filter.value = updated[filter.field as keyof QuestionTypeEntity]
      })

      return this.getOneOrFail(req)
    }
  }

  // only for fixing the bug of the crud library that does not support nested cascade creation, model is created after main entity so there's an error
  async createOne(req: CrudRequest, dto: DeepPartial<QuestionTypeEntity>): Promise<QuestionTypeEntity> {
    const { returnShallow } = req.options.routes.createOneBase
    const entity = this.prepareEntityBeforeSave(dto, req.parsed)

    /* istanbul ignore if */
    if (!entity) {
      this.throwBadRequestException(`Empty data. Nothing to save.`)
    }

    let saved: QuestionTypeEntity
    await QuestionTypeEntity.getRepository().manager.transaction(async (manager) => {
      saved = await manager.save(QuestionTypeEntity, entity)
    })

    if (returnShallow) {
      return saved
    } else {
      const primaryParams = this.getPrimaryParams(req.options)

      /* istanbul ignore next */
      if (!primaryParams.length && primaryParams.some((p) => isNil(saved[p as keyof QuestionTypeEntity]))) {
        return saved
      } else {
        req.parsed.search = primaryParams.reduce(
          (acc, p) => ({ ...acc, [p]: saved[p as keyof QuestionTypeEntity] }),
          {}
        )
        return this.getOneOrFail(req)
      }
    }
  }
}
