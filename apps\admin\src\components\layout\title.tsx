import type { RefineLayoutThemedTitleProps } from '@refinedev/antd'
import { useLink } from '@refinedev/core'
import type { FC } from 'react'

import Logo from '~/assets/images/logo.svg?react'

export const ThemedTitleV2: FC<RefineLayoutThemedTitleProps> = ({ collapsed, wrapperStyles }) => {
  const Link = useLink()

  return (
    <Link to="/" className="mt-5 flex justify-center no-underline">
      <Logo />
    </Link>
  )
}
