export enum AdminUserProviderType {
  GOOGLE = 'GOOGLE',
  INTERNAL = 'INTERNAL',
}

export enum AdminRoleType {
  SUPER_ADMIN = 'SUPER_ADMIN', // Has access to everything
  CONTENT_ADMIN = 'CONTENT_ADMIN', // Can manage content
  CATALOG_MANAGER = 'CATALOG_MANAGER', // Can manage catalog
  SKU_MANAGER = 'SKU_MANAGER', // Can manage SKUs
  COPYWRITER = 'COPYWRITER', // Can manage copy
  CUSTOMER_SUPPORT = 'CUSTOMER_SUPPORT', // Can manage customers
  MARKETING_MANAGER = 'MARKETING_MANAGER', // Can manage marketing
  FINANCE_MANAGER = 'FINANCE_MANAGER', // Can manage finance
  PRODUCT_TESTER = 'PRODUCT_TESTER', // Can test products
  LOGISTICS = 'LOGISTICS', // Can manage logistics
  API_SUPER_ADMIN = 'API_SUPER_ADMIN', // Has access to everything
}

export const ADMIN_JWT_ACCESS_TOKEN_EXPIRATION = '7d'
