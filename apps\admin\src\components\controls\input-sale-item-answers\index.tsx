import { useApiUrl, useCustom } from '@refinedev/core'
import { SellerConditionQuestionResponse, SellerProblemQuestionResponse } from '@valyuu/api/admin-interfaces'
import { ProductModelSellerQuestionType } from '@valyuu/api/constants'
import { Radio, Space } from 'antd'
import { CheckboxGroup, RadioGroup } from 'antx'
import currency from 'currency.js'
import { cloneDeep, upperFirst } from 'lodash'
import { useEffect } from 'react'
import { useImmer } from 'use-immer'

export type InputSaleItemAnswerType = {
  type: ProductModelSellerQuestionType.CONDITION | ProductModelSellerQuestionType.PROBLEM
  CONDITION: { conditionId: string; optionId: string }[]
  PROBLEM: string[]
  EXTRA_PROBLEM?: { id: string; variables?: Record<string, string> }[]
}

export type InputSaleItemAnswersProps = {
  value?: InputSaleItemAnswerType
  disabled?: boolean
  variantId?: string
  variantChanged?: boolean
  channelId?: string
  partnerId?: string
  onChange?: (value: InputSaleItemAnswerType) => void
  onPriceEstimated?: (c2bPrice: number | null, c2cPrice: number | null) => void
}

export const InputSaleItemAnswers = ({
  value,
  disabled,
  variantId,
  variantChanged = false,
  channelId,
  partnerId,
  onChange,
  onPriceEstimated,
}: InputSaleItemAnswersProps) => {
  if (!value) {
    value = {
      type: ProductModelSellerQuestionType.CONDITION,
      CONDITION: [],
      PROBLEM: [],
      EXTRA_PROBLEM: [],
    }
  }

  const [answers, setAnswers] = useImmer<InputSaleItemAnswerType>(value)

  useEffect(() => {
    // reset answers when variant changed
    if (variantChanged == true) {
      setAnswers((draft) => {
        draft.CONDITION = []
        draft.PROBLEM = []
      })
      setTimeout(() => {
        onPriceEstimated?.(null, null)
      })
    }
  }, [variantChanged, variantId])

  const apiUrl = useApiUrl()

  const isIntact =
    answers?.type === ProductModelSellerQuestionType.CONDITION
      ? true
      : answers?.type === ProductModelSellerQuestionType.PROBLEM
        ? false
        : undefined

  const queriesEnabled = isIntact !== undefined && !!variantId && !!channelId

  const { data: sellerConditionQuestionsData } = useCustom<SellerConditionQuestionResponse>({
    url: `${apiUrl}/admin/seller-questions/condition`,
    method: 'get',
    config: {
      query: {
        'variant-id': variantId,
        'channel-id': channelId,
        'partner-id': partnerId,
        lang: 'en',
      },
    },
    queryOptions: {
      enabled: queriesEnabled && isIntact,
    },
  })

  const conditionQuestions = sellerConditionQuestionsData?.data?.questions ?? []
  const conditionCombinations = sellerConditionQuestionsData?.data?.combinations ?? []

  const { data: sellerProblemQuestionsData } = useCustom<SellerProblemQuestionResponse>({
    url: `${apiUrl}/admin/seller-questions/problem`,
    method: 'get',
    config: {
      query: {
        'variant-id': variantId,
        'channel-id': channelId,
        'partner-id': partnerId,
        lang: 'en',
      },
    },
    queryOptions: {
      enabled: queriesEnabled && !isIntact,
    },
  })

  const problemQuestions = sellerProblemQuestionsData?.data?.problems ?? []
  const basePrice = sellerProblemQuestionsData?.data?.basePrice ?? 0

  const conditionAnswers = answers?.type === ProductModelSellerQuestionType.CONDITION ? answers.CONDITION : []

  const problemAnswers = answers?.type === ProductModelSellerQuestionType.PROBLEM ? answers.PROBLEM : []

  const conditionQuestionCount = isIntact === true ? (conditionQuestions.length ?? 0) : 0

  useEffect(() => {
    if (isIntact === true && conditionAnswers.length === conditionQuestionCount) {
      const combination = conditionCombinations.find((combination) =>
        combination?.choices?.every((choice) =>
          conditionAnswers.some(
            (answer) => answer.conditionId === choice.conditionId && answer.optionId === choice.optionId
          )
        )
      )

      if (combination) {
        onPriceEstimated?.(combination.c2bPrice, combination.c2cPrice)
      }
    } else if (isIntact === false && (problemAnswers.length ?? 0) > 0) {
      const c2bPrice = Math.max(
        currency(basePrice ?? 0).subtract(
          problemQuestions
            .filter((problem) => problemAnswers.includes(problem?.id))
            .reduce((acc: number, problem) => currency(acc).add(problem.priceReduction).value, 0)
        ).value,
        0
      )
      onPriceEstimated?.(c2bPrice, null)
    } else if (isIntact === false && (problemAnswers.length ?? 0) === 0) {
      onPriceEstimated?.(null, null)
    }
  }, [
    isIntact,
    conditionAnswers,
    conditionQuestionCount,
    conditionCombinations,
    problemAnswers,
    problemQuestions,
    basePrice,
  ])

  return (
    <div className="space-y-4">
      <RadioGroup
        required
        disabled={disabled}
        label="Is the product fully working?"
        options={[
          { label: 'Yes', value: true },
          { label: 'No', value: false },
        ]}
        value={isIntact}
        onChange={(event) => {
          const intact = event.target.value
          setAnswers((draft) => {
            draft.type = intact ? ProductModelSellerQuestionType.CONDITION : ProductModelSellerQuestionType.PROBLEM
            onChange?.(cloneDeep(draft))
          })
        }}
      />

      {isIntact === true && (
        <div className="space-y-4">
          {conditionQuestions.map((condition) => (
            <RadioGroup
              required
              key={condition.id}
              disabled={disabled}
              label={upperFirst(condition.name)}
              value={conditionAnswers.find((answer) => answer.conditionId === condition.id)?.optionId}
              onChange={(event) => {
                setAnswers((draft) => {
                  draft.CONDITION = draft.CONDITION?.filter((answer) => answer.conditionId !== condition.id)
                  draft.CONDITION?.push({
                    conditionId: condition.id,
                    optionId: event.target.value,
                  })
                  onChange?.(cloneDeep(draft))
                })
              }}
            >
              <Space direction="vertical">
                {condition.options.map((option) => (
                  <Radio key={option.id} value={option.id}>
                    {option.name}
                  </Radio>
                ))}
              </Space>
            </RadioGroup>
          ))}
        </div>
      )}

      {isIntact === false && (
        <CheckboxGroup
          required
          disabled={disabled}
          label="What are the problems of this product?"
          options={problemQuestions.map((problem) => ({
            label: problem.name,
            value: problem.id,
          }))}
          value={problemAnswers}
          onChange={(value: unknown[]) => {
            setAnswers((draft) => {
              draft.PROBLEM = value as string[]
              onChange?.(cloneDeep(draft))
            })
          }}
        />
      )}
    </div>
  )
}
