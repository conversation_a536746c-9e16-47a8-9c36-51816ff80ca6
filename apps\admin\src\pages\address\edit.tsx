import { Edit, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { AddressType } from '@valyuu/api/constants'
import type { IAddress, ILocaleCountry } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, Select } from 'antx'
import { type FC } from 'react'

import { LabelWithDetails } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const AddressEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, query, formLoading } = useForm<IAddress, HttpError, IAddress>({
    meta: {
      join: [
        {
          field: 'user',
          select: ['id', 'email'],
        },
      ],
    },
  })

  const record = query?.data?.data

  const { selectProps: countrySelectProps } = useSelect<ILocaleCountry>({
    resource: 'admin/countries',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: countryAreaCodeSelectProps } = useSelect<ILocaleCountry>({
    resource: 'admin/countries',
    optionLabel: 'countryAreaCode',
    optionValue: 'countryAreaCode',
    meta: {
      fields: ['id', 'countryAreaCode'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl } = useNavigation()

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Select
          label={
            <LabelWithDetails
              link={record?.userId ? editUrl('admin/users', record.userId) : undefined}
              label={'User'}
            />
          }
          name="userId"
          rules={['required']}
          options={record?.user ? [{ value: record.userId, label: record.user.email }] : []}
          disabled
        />
        <Select
          label="Type"
          name="type"
          disabled
          rules={['required']}
          options={formatSelectOptionsFromEnum(AddressType)}
        />
        <Input label="First name" name="firstName" rules={['required']} />
        <Input label="Last name" name="lastName" rules={['required']} />
        <Select label="Phone area code" name="phoneAreaCode" {...(countryAreaCodeSelectProps as any)} />
        <Select label="Phone number" name="phoneNumber" />
        <Input label="Postal code" name="postalCode" rules={['required']} />
        <Input
          label="House Number"
          name="houseNumber"
          rules={['required']}
          normalize={(value) => value.replace(/[^0-9]/g, '')}
        />
        <Input label="Addition" name="addition" />
        <Input label="Street" name="street" rules={['required']} />
        <Input label="City" name="city" rules={['required']} />
        <Select label="Country" name="countryId" rules={['required']} {...(countrySelectProps as any)} />
      </Form>
    </Edit>
  )
}
