.input-multi-lang-wrapper.ant-input-group-wrapper {
  .ant-input-group-addon {
    background-color: rgba(0, 0, 0, 0.02);
    color: rgba(0, 0, 0, 0.88);
    padding: 0;
    > span {
      width: 36px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
  &.ant-input-group-wrapper-disabled span {
    cursor: not-allowed;
  }
}
.textarea-multi-lang-wrapper {
  position: relative;
  border-bottom: 0;
  margin-bottom: 0;
  textarea {
    min-height: 60px;
  }
  .addon {
    disabled: flex;
    position: absolute;
    z-index: 100;
    top: 1px;
    right: 1px;
    height: 30px;
    width: 36px;
    background-color: #fafafa;
    border-left: 1px solid #d9d9d9;
    border-bottom: 1px solid #d9d9d9;
    border-top-right-radius: 5px;
    border-bottom-left-radius: 3px;
    padding: 0;
    cursor: pointer;
    svg {
      position: absolute;
      top: 8px;
      right: 11px;
      width: 14px;
      height: 14px;
    }
  }
  &.ant-input-group-wrapper-disabled .addon {
    cursor: not-allowed;
  }
}
