import { unlink } from 'node:fs/promises'
import { Readable } from 'node:stream'

import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import type { Express } from 'express'
import { Repository } from 'typeorm'
import { v4 as uuid } from 'uuid'

import { envConfig } from '~/configs'
import { ImageEntity } from '~/entities'
import { cloudinaryUploadResourceFromStream } from '~/utils'

export class ImageService extends TypeOrmCrudService<ImageEntity> {
  constructor(@InjectRepository(ImageEntity) repo: Repository<ImageEntity>) {
    super(repo)
  }

  async uploadImage({ file, originalFilename }: { file: Express.Multer.File; originalFilename: string }) {
    // TODO: cloudinary image limit of 10M
    const id = uuid()
    const fileStream = new Readable()
    fileStream.push(file.buffer)
    fileStream.push(null)

    try {
      const {
        public_id: publicId,
        secure_url: url,
        width,
        height,
        format,
      } = await cloudinaryUploadResourceFromStream(fileStream, `${envConfig.CLOUDINARY_PREFIX}temp/${id}`)
      return ImageEntity.save(
        ImageEntity.create({
          id,
          publicId,
          url,
          width,
          height,
          format,
          originalFilename,
          isTemp: true,
        })
      )
    } finally {
      if (file.path) {
        await unlink(file.path)
      }
      fileStream.destroy()
    }
  }
}
