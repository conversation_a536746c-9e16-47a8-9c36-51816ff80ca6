import '@refinedev/antd/dist/reset.css'
import '~/assets/styles/index.css'

import { createIntl, ProProvider } from '@ant-design/pro-provider'
import enUSPro from '@ant-design/pro-provider/es/locale/en_US'
import { GoogleOAuthProvider } from '@react-oauth/google'
import { AuthPage, notificationProvider, RefineThemes, ThemedLayoutV2 } from '@refinedev/antd'
import { Authenticated, ErrorComponent, Refine } from '@refinedev/core'
import routerProvider, { CatchAllNavigate, NavigateToResource } from '@refinedev/react-router-v6'
import dataProvider, { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { ConfigProvider } from 'antd'
import enUS from 'antd/es/locale/en_US'
import dayjs from 'dayjs'
import { type FC, useContext } from 'react'
import { BrowserRouter, Outlet, Route, Routes } from 'react-router-dom'

import { authProvider } from '~/auth-provider'
import { ThemedHeaderV2, ThemedSiderV2 } from '~/components'
import * as pageExports from '~/pages'
import { pageResources } from '~/resources'

axios.interceptors.request.use((config) => {
  const userJson = localStorage.getItem('user')
  if (config.headers && userJson) {
    try {
      const user = JSON.parse(userJson)
      const expiresIn = dayjs(user.expiresIn)
      const currentTime = dayjs()
      if (currentTime.isAfter(expiresIn.subtract(1, 'hour'))) {
        localStorage.removeItem('user')
        window.location.reload()
        return config
      }
      config.headers['Authorization'] = `Bearer ${user.token}`
    } catch {
      console.error('Error parsing user from localStorage', userJson)
    }
  }
  return config
})

const pages = Object.entries(pageExports)
  .filter(([key]) => key.endsWith('Page'))
  .map(([, page]) => page)

const App: FC = () => {
  return (
    <BrowserRouter>
      <ConfigProvider theme={{ ...RefineThemes.Blue }} locale={enUS}>
        <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_OAUTH_CLIENT_ID}>
          <ProProvider.Provider value={{ ...useContext(ProProvider), intl: createIntl('en_US', enUSPro) }}>
            <Refine
              authProvider={authProvider}
              routerProvider={routerProvider}
              dataProvider={dataProvider(import.meta.env.VITE_API_URL as string)}
              notificationProvider={notificationProvider}
              resources={pageResources}
              options={{
                syncWithLocation: true,
                disableTelemetry: true,
              }}
            >
              <Routes>
                <Route
                  element={
                    <Authenticated key="authenticated-inner" fallback={<CatchAllNavigate to="/login" />}>
                      <ThemedLayoutV2
                        Header={() => <ThemedHeaderV2 />}
                        Sider={(props) => <ThemedSiderV2 {...props} fixed />}
                      >
                        <div className="mx-auto max-w-[100rem]">
                          <Outlet />
                        </div>
                      </ThemedLayoutV2>
                    </Authenticated>
                  }
                >
                  <Route index element="" />
                  {pages.map((page) =>
                    page.withLayout === true ? (
                      <Route path={page.path} key={page.path}>
                        {page.index ? <Route index element={<page.index />} /> : null}
                        {page.create ? <Route path="create" element={<page.create />} /> : null}
                        {page.show ? <Route path=":id" element={<page.show />} /> : null}
                        {page.edit ? <Route path="edit/:id" element={<page.edit />} /> : null}
                      </Route>
                    ) : null
                  )}
                  <Route
                    element={
                      <Authenticated key="authenticated-outer" fallback={<Outlet />}>
                        <NavigateToResource />
                      </Authenticated>
                    }
                  />
                  <Route path="*" element={<ErrorComponent />} />
                </Route>
                {pages.map((page) =>
                  page.withLayout === false ? <Route path={page.path} key={page.path} element={<page.index />} /> : null
                )}
                <Route path="/login" element={<AuthPage type="login" />} />
              </Routes>
            </Refine>
          </ProProvider.Provider>
        </GoogleOAuthProvider>
      </ConfigProvider>
    </BrowserRouter>
  )
}

export default App
