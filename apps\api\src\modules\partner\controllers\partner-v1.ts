import { Body, Controller, Get, Param, Post, Query, Req, Res, UseGuards } from '@nestjs/common'
import { ApiExtraModels, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger'
import { ParcelDocumentFormat } from '@valyuu/sendcloud'
import { isIP } from 'class-validator'
import type { Request, Response } from 'express'

import { PartnerShippingLabelFormat } from '~partner/constants'
import {
  V1ConfirmTradeInItemOfferInput,
  V1CreateTradeInInput,
  V1GetBrandsInput,
  V1GetCategoriesInput,
  V1GetFaqsInput,
  V1GetModelQuestionsInput,
  V1GetModelsInput,
  V1GetSeriesInput,
  V1GetTradeInItemDataInput,
  V1GetTradeInOrderDataInput,
  V1GetTradeInOrderItemDataInput,
  V1ValidateSellerInfoInput,
  V1WrappedConfirmTradeInItemOfferOutput,
  V1WrappedCreateTradeInOutput,
  V1WrappedGetBrandsOutput,
  V1WrappedGetCategoriesOutput,
  V1WrappedGetFaqsOutput,
  V1WrappedGetModelQuestionsOutput,
  V1WrappedGetModelsOutput,
  V1WrappedGetSeriesOutput,
  V1WrappedGetTradeInItemDataOutput,
  V1WrappedGetTradeInOrderDataOutput,
  V1WrappedGetTradeInOrderItemDataOutput,
  V1WrappedValidateSellerInfoOutput,
} from '~partner/dtos'
import { V1PartnerApiKeyAuthGuard, V1PartnerApiSecretAuthGuard } from '~partner/guards'
import { V1PartnerService } from '~partner/services'

@Controller({ path: 'partner/v1', version: '1' })
export class V1PartnerController {
  constructor(private readonly service: V1PartnerService) {}

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Get('categories')
  @ApiOperation({ summary: 'Get categories' })
  @ApiExtraModels(V1GetCategoriesInput)
  @ApiResponse({ status: 200, description: 'Get categories output', type: V1WrappedGetCategoriesOutput })
  async getCategories(@Query() data: V1GetCategoriesInput) {
    return this.service.getCategories(data)
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Get('brands')
  @ApiOperation({ summary: 'Get brands' })
  @ApiExtraModels(V1GetBrandsInput)
  @ApiResponse({ status: 200, description: 'Get brands output', type: V1WrappedGetBrandsOutput })
  async getBrands(@Query() data: V1GetBrandsInput) {
    return this.service.getBrands(data)
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Get('series')
  @ApiOperation({ summary: 'Get series' })
  @ApiExtraModels(V1GetSeriesInput)
  @ApiResponse({ status: 200, description: 'Get series output', type: V1WrappedGetSeriesOutput })
  async getSeries(@Query() data: V1GetSeriesInput) {
    return this.service.getSeries(data)
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Get('models')
  @ApiOperation({ summary: 'Get product models' })
  @ApiExtraModels(V1GetModelsInput)
  @ApiResponse({ status: 200, description: 'Get product models output', type: V1WrappedGetModelsOutput })
  async getModels(@Query() data: V1GetModelsInput) {
    if (typeof data.onlyShowEligible === 'string') {
      data.onlyShowEligible = /^true|TRUE$/.test(data.onlyShowEligible)
    }
    return this.service.getModels(data)
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Get('faqs')
  @ApiOperation({ summary: 'Get FAQs' })
  @ApiExtraModels(V1GetFaqsInput)
  @ApiResponse({ status: 200, description: 'Get FAQs output', type: V1WrappedGetFaqsOutput })
  async getFaqs(@Query() data: V1GetFaqsInput) {
    return this.service.getFaqs(data)
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Get('model-questions')
  @ApiOperation({ summary: 'Get model questions' })
  @ApiExtraModels(V1GetModelQuestionsInput)
  @ApiResponse({
    status: 200,
    description: 'Get general questions output',
    type: V1WrappedGetModelQuestionsOutput,
  })
  async getModelQuestions(@Query() data: V1GetModelQuestionsInput) {
    return this.service.getModelQuestions(data)
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Post('trade-in-item-data')
  @ApiOperation({ summary: 'Get trade-in item data' })
  @ApiResponse({
    status: 200,
    description: 'Get trade-in item data output',
    type: V1WrappedGetTradeInItemDataOutput,
  })
  async getTradeInItemData(
    @Body() data: V1GetTradeInItemDataInput,
    @Req() request: Request & { partnerId: string; channelId: string }
  ) {
    return this.service.getTradeInItemData({
      ...data,
      channelId: request.channelId,
      partnerId: request.partnerId,
    })
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Post('trade-in')
  @ApiOperation({ summary: 'Create trade-in' })
  @ApiResponse({
    status: 200,
    description: 'Create trade-in output',
    type: V1WrappedCreateTradeInOutput,
  })
  async createTradeIn(
    @Body() data: V1CreateTradeInInput,
    @Req() request: Request & { partnerId: string; channelId: string }
  ) {
    return this.service.createTradeIn({
      ...data,
      channelId: request.channelId,
      partnerId: request.partnerId,
      ip: isIP(request.ip) ? request.ip : '***********',
    })
  }

  @ApiTags('Backend API')
  @ApiSecurity('APIKeyAuth')
  @ApiSecurity('APISecretAuth')
  @UseGuards(V1PartnerApiSecretAuthGuard)
  @Get('trade-in/:tradeInId/shipping-label')
  @ApiOperation({ summary: 'Get trade-in shipping label PDF' })
  @ApiParam({ name: 'tradeInId', description: 'The ID of the item', type: 'string', format: 'uuid' })
  @ApiQuery({
    name: 'format',
    enum: PartnerShippingLabelFormat,
    required: false,
    description: 'Format of the shipping label, defaults to PNG',
  })
  @ApiResponse({
    status: 200,
    description: 'File containing the shipping label',
    content: {
      'application/pdf': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
      'image/png': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Shipping label not found' })
  @ApiResponse({ status: 500, description: 'Cannot get shipping label due to internal server error' })
  async getShippingLabel(
    @Param('tradeInId') tradeInId: string,
    @Query('format') format: PartnerShippingLabelFormat = PartnerShippingLabelFormat.PDF,
    @Req() request: Request & { partnerId: string },
    @Res() res: Response
  ) {
    const contentType = format === PartnerShippingLabelFormat.PDF ? ParcelDocumentFormat.PDF : ParcelDocumentFormat.PNG
    const extension = format === PartnerShippingLabelFormat.PDF ? 'pdf' : 'png'

    const file = await this.service.getTradeInShippingLabel(tradeInId, request.partnerId, contentType)

    res.setHeader('Content-Type', contentType)
    res.setHeader('Content-Disposition', `attachment; filename="ShippingLabel.${extension}"`)
    res.end(file, 'binary')
  }

  @ApiTags('Backend API')
  @ApiSecurity('APIKeyAuth')
  @ApiSecurity('APISecretAuth')
  @UseGuards(V1PartnerApiSecretAuthGuard)
  @Get('trade-in/:tradeInId/paperless-code')
  @ApiOperation({ summary: 'Get trade-in paperless shipping code' })
  @ApiParam({ name: 'tradeInId', description: 'The ID of the item', type: 'string', format: 'uuid' })
  @ApiQuery({
    name: 'format',
    enum: PartnerShippingLabelFormat,
    required: false,
    description: 'Format of the paperless code, defaults to PNG',
  })
  @ApiResponse({
    status: 200,
    description: 'File containing the paperless code',
    content: {
      'application/pdf': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
      'image/png': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Paperless code not found' })
  @ApiResponse({ status: 500, description: 'Cannot get paperless code due to internal server error' })
  async getPaperlessCode(
    @Param('tradeInId') tradeInId: string,
    @Query('format') format: PartnerShippingLabelFormat = PartnerShippingLabelFormat.PNG,
    @Req() request: Request & { partnerId: string },
    @Res() res: Response
  ) {
    const contentType = format === PartnerShippingLabelFormat.PDF ? ParcelDocumentFormat.PDF : ParcelDocumentFormat.PNG
    const extension = format === PartnerShippingLabelFormat.PDF ? 'pdf' : 'png'

    const file = await this.service.getTradeInPaperlessCode(tradeInId, request.partnerId, contentType)

    res.setHeader('Content-Type', contentType)
    res.setHeader('Content-Disposition', `attachment; filename="ShippingPaperlessCode.${extension}"`)
    res.end(file, 'binary')
  }

  @ApiTags('Backend API')
  @ApiSecurity('APIKeyAuth')
  @ApiSecurity('APISecretAuth')
  @UseGuards(V1PartnerApiSecretAuthGuard)
  @Get('trade-in/:tradeInId')
  @ApiOperation({ summary: 'Get trade-in order data' })
  @ApiResponse({ status: 200, description: 'Get trade-in order data output', type: V1WrappedGetTradeInOrderDataOutput })
  @ApiResponse({ status: 404, description: 'Trade-in order not found' })
  @ApiResponse({ status: 500, description: 'Cannot get trade-in order data due to internal server error' })
  async getTradeInOrderData(
    @Param('tradeInId') tradeInId: string,
    @Req() request: Request & { partnerId: string },
    @Query() data: V1GetTradeInOrderDataInput
  ) {
    return this.service.getTradeInOrderData(tradeInId, request.partnerId, data.returnTradeInItems, data.version)
  }

  @ApiTags('Backend API')
  @ApiSecurity('APIKeyAuth')
  @ApiSecurity('APISecretAuth')
  @UseGuards(V1PartnerApiSecretAuthGuard)
  @Get('trade-in-item/:tradeInItemId')
  @ApiOperation({ summary: 'Get trade-in item data' })
  @ApiResponse({
    status: 200,
    description: 'Get trade-in item data output',
    type: V1WrappedGetTradeInOrderItemDataOutput,
  })
  @ApiResponse({ status: 404, description: 'Trade-in item not found' })
  @ApiResponse({ status: 500, description: 'Cannot get trade-in item data due to internal server error' })
  async getTradeInOrderItemData(
    @Param('tradeInItemId') tradeInItemId: string,
    @Req() request: Request & { partnerId: string },
    @Query() data: V1GetTradeInOrderItemDataInput
  ) {
    return this.service.getTradeInOrderItemData(tradeInItemId, request.partnerId, data.version)
  }

  @ApiTags('Backend API')
  @ApiSecurity('APIKeyAuth')
  @ApiSecurity('APISecretAuth')
  @UseGuards(V1PartnerApiSecretAuthGuard)
  @Post('trade-in/:tradeInId/cancel')
  @ApiOperation({ summary: 'Cancel trade-in. Only trade-ins with SUBMITTED status can be cancelled.' })
  @ApiResponse({ status: 200, description: 'Trade-in cancelled' })
  @ApiResponse({ status: 400, description: 'Trade-in is not in SUBMITTED status' })
  @ApiResponse({ status: 404, description: 'Trade-in not found' })
  @ApiResponse({ status: 500, description: 'Cannot cancel trade-in due to internal server error' })
  async cancelTradeIn(@Param('tradeInId') tradeInId: string, @Req() request: Request & { partnerId: string }) {
    return this.service.cancelTradeIn(tradeInId, request.partnerId)
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Post('trade-in-item-offer/:tradeInItemOfferId/confirm')
  @ApiOperation({ summary: 'Confirm trade-in item offer' })
  @ApiResponse({
    status: 200,
    description: 'Trade-in item offer confirmed',
    type: V1WrappedConfirmTradeInItemOfferOutput,
  })
  async confirmTradeInItemOffer(
    @Param('tradeInItemOfferId') tradeInItemOfferId: string,
    @Req() request: Request & { partnerId: string },
    @Body() data: V1ConfirmTradeInItemOfferInput
  ): Promise<V1WrappedConfirmTradeInItemOfferOutput> {
    return this.service.confirmTradeInItemOffer({
      ...data,
      tradeInItemOfferId,
      partnerId: request.partnerId,
    })
  }

  @ApiTags('Frontend API')
  @ApiSecurity('APIKeyAuth')
  @UseGuards(V1PartnerApiKeyAuthGuard)
  @Post('trade-in/validate-seller')
  @ApiOperation({ summary: 'Validate seller information before creating a trade-in' })
  @ApiResponse({
    status: 200,
    description: 'Validate seller info output',
    type: V1WrappedValidateSellerInfoOutput,
  })
  async validateSellerInfo(
    @Body() data: V1ValidateSellerInfoInput,
    @Req() request: Request & { partnerId: string; channelId: string }
  ): Promise<V1WrappedValidateSellerInfoOutput> {
    return this.service.validateSellerInfo(
      {
        ...data,
        channelId: request.channelId,
      },
      isIP(request.ip) ? request.ip : '***********'
    )
  }
}
