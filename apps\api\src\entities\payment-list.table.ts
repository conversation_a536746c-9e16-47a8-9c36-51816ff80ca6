import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { PaymentListStatus, PaymentListType } from '~/constants'
import {
  type IPaymentListBulkItem,
  type IPaymentListIndividualItem,
  PaymentListBulkItemEntity,
  PaymentListIndividualItemEntity,
} from '~/entities'

@Entity('payment_list')
export class PaymentListEntity extends BaseEntity {
  @PrimaryColumn()
  id: string

  @Index()
  @Column()
  type: PaymentListType

  @Index()
  @Column()
  dueDate: Date

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  amount: number

  @Column({ type: 'integer', unsigned: true, default: 0 })
  itemsCount: number

  @Index()
  @Column({ default: PaymentListStatus.PENDING_REVIEW })
  status: PaymentListStatus

  @Column({ default: false })
  isUpdated: boolean

  @OneToMany(
    () => PaymentListIndividualItemEntity,
    (paymentListIndividualItem) => paymentListIndividualItem.paymentList
  )
  individualItems: Relation<PaymentListIndividualItemEntity[]>

  @OneToMany(() => PaymentListBulkItemEntity, (paymentListBulkItem) => paymentListBulkItem.paymentList)
  bulkItems: Relation<PaymentListBulkItemEntity[]>

  @Column({ nullable: true })
  referenceNumber?: string

  @Column({ nullable: true })
  paidAt?: Date

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IPaymentList = Omit<PaymentListEntity, keyof BaseEntity> & {
  individualItems: IPaymentListIndividualItem[]
  bulkItems: IPaymentListBulkItem[]
}
