import '@mdxeditor/editor/style.css'

import {
  headingsPlugin,
  imagePlugin,
  linkDialogPlugin,
  linkPlugin,
  listsPlugin,
  markdownShortcutPlugin,
  MDXEditor,
  toolbarPlugin,
} from '@mdxeditor/editor'
import { create } from 'antx'

import { InputMDXToolbar } from './toolbar'

export type InputMDXEditorProps = {
  value?: string
  onChange?: (value: string) => void
  imagePluginParams?: Parameters<typeof imagePlugin>[0]
}

export const InputMDXEditor = create(({ value, onChange, imagePluginParams }: InputMDXEditorProps) => {
  if (!value) {
    value = ''
  }
  return (
    <MDXEditor
      markdown={value}
      onChange={onChange}
      plugins={[
        headingsPlugin(),
        linkDialogPlugin(),
        linkPlugin(),
        listsPlugin(),
        markdownShortcutPlugin(),
        ...(imagePluginParams ? [imagePlugin(imagePluginParams)] : []),
        toolbarPlugin({
          toolbarContents: () => <InputMDXToolbar />,
        }),
      ]}
      className="[&_.mdxeditor-root-contenteditable]:max-h-[500px] [&_.mdxeditor-root-contenteditable]:overflow-y-auto"
    />
  )
})
