import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { FrontpageMiscEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudFrontpageMiscService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: FrontpageMiscEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
  query: {},
})
@Controller('admin/frontpage-misc')
export class CrudFrontpageMiscController implements CrudController<FrontpageMiscEntity> {
  constructor(public service: CrudFrontpageMiscService) {}
}
