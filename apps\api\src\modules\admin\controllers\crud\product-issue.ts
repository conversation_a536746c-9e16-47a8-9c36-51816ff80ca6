import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { ProductIssueEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudProductIssueService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ProductIssueEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
})
@Controller('admin/product-issues')
export class CrudProductIssueController implements CrudController<ProductIssueEntity> {
  constructor(public service: CrudProductIssueService) {}
}
