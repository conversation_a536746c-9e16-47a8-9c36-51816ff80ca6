import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PartnerEntity, PriceThresholdEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudPriceThresholdService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: PartnerEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
})
@Controller('admin/price-thresholds')
export class CrudPriceThresholdsController implements CrudController<PriceThresholdEntity> {
  constructor(public service: CrudPriceThresholdService) {}
}
