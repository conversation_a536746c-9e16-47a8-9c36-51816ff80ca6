import { Create, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { OrganizationBankAccountType, SaleItemPlanType, SalePaymentType } from '@valyuu/api/constants'
import type { IChannel, IOrganizationBankAccount, IPartner } from '@valyuu/api/entities'
import { Button, Form } from 'antd'
import { Input, RadioGroup, Select, Watch } from 'antx'
import type { FC } from 'react'
import { useState } from 'react'
import { FiPlus, FiRefreshCw } from 'react-icons/fi'

import { InputSlug } from '~/components'

export const PartnerCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<IPartner, HttpError, IPartner>()

  const { selectProps: channelSelectProps } = useSelect<IChannel>({
    resource: 'admin/channels',
    optionLabel: 'name',
    meta: {
      fields: ['id', 'name'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: bankAccountSelectProps, queryResult: bankAccountQueryResult } =
    useSelect<IOrganizationBankAccount>({
      resource: 'admin/organization-bank-accounts',
      optionLabel: 'holderName',
      optionValue: 'id',
      meta: {
        fields: ['id', 'holderName'],
        join: [
          {
            field: 'charity',
            select: ['id'],
          },
        ],
      },
      filters: [
        {
          field: 'type',
          operator: 'eq',
          value: OrganizationBankAccountType.PARTNER,
        },
      ],
    })
  if (bankAccountQueryResult.data?.data?.length) {
    // filter out charitySelectProps.options for any records with a bankAccount
    bankAccountSelectProps.options = bankAccountSelectProps.options?.filter((option) =>
      bankAccountQueryResult.data?.data.some(
        (bankAccount) => bankAccount.id === option.value && !bankAccount.partner && !bankAccount.charity
      )
    )
  }

  const { createUrl } = useNavigation()
  const [createBankAccountOpen, setCreateBankAccountOpen] = useState(false)

  const handleCreateBankAccount = () => {
    if (createBankAccountOpen) {
      bankAccountQueryResult.refetch()
    } else {
      const bankAccountCreateUrl = createUrl('admin/organization-bank-accounts')
      window.open(bankAccountCreateUrl, 'create-bank-account')?.focus()
    }
    setCreateBankAccountOpen(!createBankAccountOpen)
  }

  const noOptionsContent = (
    <Button
      type="primary"
      icon={createBankAccountOpen ? <FiRefreshCw /> : <FiPlus />}
      onClick={handleCreateBankAccount}
      block
    >
      {createBankAccountOpen ? 'Refresh' : 'Create Bank Account'}
    </Button>
  )

  const supportedPaymentTypes = Form.useWatch('supportedPaymentTypes', form)

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Watch name="name">
          {(name) => (
            <>
              <Input label="Name" name="name" rules={['required']} value={name} />
              <InputSlug
                label="Slug"
                name="slug"
                rules={['required']}
                sourceString={name}
                allowEdit={false}
                autoFollow
              />
            </>
          )}
        </Watch>
        <Select label="Channel" name="channelId" rules={['required']} {...(channelSelectProps as any)} />
        <RadioGroup
          label="Manage customer Emails"
          name="manageCustomerEmails"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
          initialValue={true}
        />
        <Select
          label="Supported plan types"
          name="supportedPlanTypes"
          mode="multiple"
          options={Object.values(SaleItemPlanType).map((type) => ({
            label: type,
            value: type,
          }))}
          rules={['required']}
        />
        <Select
          label="Supported payment types"
          name="supportedPaymentTypes"
          mode="multiple"
          options={Object.values(SalePaymentType).map((type) => ({
            label: type,
            value: type,
          }))}
          rules={['required']}
        />
        {supportedPaymentTypes?.includes(SalePaymentType.BULK_SETTLEMENT) ? (
          <Select
            label="Bank account"
            name="bankAccountId"
            rules={['required']}
            {...(bankAccountSelectProps as any)}
            notFoundContent={!bankAccountSelectProps.options?.length ? noOptionsContent : undefined}
          />
        ) : null}
        <Input
          label="API endpoint URL"
          name="apiEndpointUrl"
          type="url"
          rules={[
            {
              type: 'url',
              message: 'Please enter a valid URL',
            },
          ]}
        />
        <Input
          label="Standalone site URL"
          name="standaloneSiteUrl"
          type="url"
          rules={[
            {
              type: 'url',
              message: 'Please enter a valid URL',
            },
          ]}
        />
      </Form>
    </Create>
  )
}
