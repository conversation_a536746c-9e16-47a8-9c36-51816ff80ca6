import { useList } from '@refinedev/core'
import type { IQuestionExtraProblem } from '@valyuu/api/entities'
import { Checkbox, Form } from 'antd'
import { Input } from 'antx'
import { create } from 'antx'
import { cloneDeep } from 'lodash'

import type { InputSaleItemAnswerType } from '~/components'

export type IInputExtraProblemsProps = {
  value?: InputSaleItemAnswerType['EXTRA_PROBLEM']
  onChange?: (value: InputSaleItemAnswerType['EXTRA_PROBLEM']) => void
  disabled?: boolean
}

export const InputExtraProblems = create(({ value, onChange, disabled }: IInputExtraProblemsProps) => {
  const form = Form.useFormInstance()
  const { data: { data: extraProblems } = {} } = useList<IQuestionExtraProblem>({
    resource: 'admin/question-extra-problems',
    meta: { fields: ['id', 'internalName', 'template__en'] },
  })

  return extraProblems ? (
    <div className="flex flex-wrap gap-2">
      {extraProblems?.map(({ id, internalName, template__en }) => {
        const variables =
          template__en?.match(/\{\{([a-zA-Z0-9]+(?: [a-zA-Z0-9]+)*)\}\}/g)?.map((match) => match.slice(2, -2)) || []
        const checked = (value ?? []).some((valueItem) => valueItem.id === id)

        return (
          <div key={id} className="inline-flex flex-nowrap items-start justify-start gap-1">
            <Checkbox
              className="flex-none"
              value={id}
              rootClassName="mt-1.5"
              checked={checked}
              disabled={disabled}
              onChange={({ target }) => {
                const valueClone = cloneDeep(value ?? [])
                if (target.checked) {
                  const existingVariables: Record<string, string> = {}
                  variables.forEach((variable) => {
                    const fieldValue = form.getFieldValue(['validation', id, variable])
                    if (fieldValue) {
                      existingVariables[variable] = fieldValue
                    }
                  })
                  valueClone?.push({ id, variables: existingVariables })
                  onChange?.(valueClone ?? [])
                } else {
                  onChange?.(valueClone?.filter((valueItem) => valueItem.id !== id) ?? [])
                }
              }}
            >
              {internalName}
            </Checkbox>
            {checked &&
              variables.map((variable) => (
                <Input
                  className="mb-0"
                  key={`${id}-${variable}`}
                  name={['validation', id, variable]}
                  rules={[{ required: true, message: `Please enter ${variable}` }]}
                  validateFirst
                  validateTrigger={['onChange']}
                  placeholder={variable}
                  disabled={disabled}
                  initialValue={value?.find((valueItem) => valueItem.id === id)?.variables?.[variable]}
                  onChange={({ target }) => {
                    const valueClone = cloneDeep(value)
                    valueClone?.forEach((valueItem) => {
                      if (valueItem.id === id) {
                        valueItem.variables = { ...valueItem.variables, [variable]: target.value }
                      }
                    })
                    onChange?.(valueClone ?? [])
                  }}
                />
              ))}
          </div>
        )
      })}
    </div>
  ) : (
    <span>Extra problems not found</span>
  )
})
