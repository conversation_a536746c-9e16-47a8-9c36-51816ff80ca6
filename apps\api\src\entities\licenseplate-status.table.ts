import { Field, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm'

import { LicensePlateStatusEnum } from '~/constants/licenseplate-status'

import { LicensePlateDeviceEntity } from './licenseplate-device.table'

@ObjectType('LicensePlateStatus')
@Entity('licenseplate_status')
@Unique(['guid', 'status'])
export class LicensePlateStatusEntity extends BaseEntity {
  @Field()
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  guid: string

  @Field(() => LicensePlateStatusEnum)
  @Column({ type: 'enum', enum: LicensePlateStatusEnum })
  status: LicensePlateStatusEnum

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  note?: string

  @Field()
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date

  @Field()
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date

  @ManyToOne(() => LicensePlateDeviceEntity, (device) => device.status, { nullable: false, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'guid', referencedColumnName: 'guid' })
  device: LicensePlateDeviceEntity
}

export type ILicensePlateStatus = Omit<LicensePlateStatusEntity, keyof BaseEntity> & {
  device: LicensePlateDeviceEntity
}
