import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { OrganizationBankAccountEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudOrganizationBankAccountService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: OrganizationBankAccountEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      partner: {},
      charity: {},
    },
  },
})
@Controller('admin/organization-bank-accounts')
export class CrudOrganizationBankAccountController implements CrudController<OrganizationBankAccountEntity> {
  constructor(public service: CrudOrganizationBankAccountService) {}
}
