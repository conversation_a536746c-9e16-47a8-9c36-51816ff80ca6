import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { ProductVariantEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudProductVariantService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ProductVariantEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
  query: {
    join: {
      basePrices: {},
      attributeCombination: {},
      conditionCombinations: {},
      problems: {},
      model: {},
      category: {},
      questionType: {},
      productSkus: {},
      saleItems: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/product-variants')
export class CrudProductVariantController implements CrudController<ProductVariantEntity> {
  constructor(public service: CrudProductVariantService) {}
}
