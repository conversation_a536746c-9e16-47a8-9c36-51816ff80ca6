import { Field, ID, ObjectType } from '@nestjs/graphql'
import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  Unique,
  UpdateDateColumn,
} from 'typeorm'

import {
  ChannelEntity,
  type IChannel,
  type ILocaleCurrency,
  type IProductSku,
  LocaleCurrencyEntity,
  ProductSkuEntity,
} from '~/entities'

@ObjectType('ProductSkuPrice')
@Entity('product_sku_price')
@Unique(['channelId', 'skuId'])
export class ProductSkuPriceEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Index()
  @Min(0)
  price: number

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Index()
  originalPrice: number

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  brandNewPrice: number

  @Field()
  @Column({ default: false })
  @Index()
  isDeal: boolean

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Index()
  @Min(0)
  margin: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Index()
  @Column()
  @RelationId((problem: ProductSkuPriceEntity) => problem.channel)
  channelId: string

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: ProductSkuPriceEntity) => item.currency)
  currencyId: string

  @ManyToOne(() => ProductSkuEntity, (productSku) => productSku.prices, { nullable: false, onDelete: 'CASCADE' })
  sku: Relation<ProductSkuEntity>

  @Column()
  @RelationId((problem: ProductSkuPriceEntity) => problem.sku)
  skuId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductSkuPrice = Omit<ProductSkuPriceEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
  productSku: IProductSku
}
