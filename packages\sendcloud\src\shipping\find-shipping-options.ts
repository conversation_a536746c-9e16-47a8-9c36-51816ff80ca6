import { sendcloudClientV3 } from '../client'
import { CountryCode } from '../constants'
import { IShippingOption } from '../interfaces'

/**
 * Lead time filter object for shipping options
 */
export type ILeadTimeFilter = {
  /** Filter lead times greater than the specified number */
  gt?: number
  /** Filter lead times greater than or equal to the specified number */
  gte?: number
  /** Filter lead times equal to the specified number */
  eq?: number
  /** Filter lead times less than the specified number */
  lt?: number
  /** Filter lead times less than or equal to the specified number */
  lte?: number
}

/**
 * Weight object for shipping options
 */
export type IWeightFilter = {
  /** Weight value as string (required) */
  value: string
  /** Weight unit (required) - allowed values: kg, g, lbs, oz */
  unit: 'kg' | 'g' | 'lbs' | 'oz'
}

/**
 * Dimension object for shipping options
 */
export type IDimensionFilter = {
  /** Length in specified unit (required) */
  length: string
  /** Width in specified unit (required) */
  width: string
  /** Height in specified unit (required) */
  height: string
  /** Dimensional unit (required) - allowed values: cm, mm, m, yd, ft, in */
  unit: 'cm' | 'mm' | 'm' | 'yd' | 'ft' | 'in'
}

/**
 * Parameters for finding shipping options using V3 API
 */
export type IFindShippingOptionsParams = {
  /** Origin country code in ISO 3166-1 alpha-2 format (required) */
  from_country_code: CountryCode

  /** Destination country code in ISO 3166-1 alpha-2 format (required) */
  to_country_code: CountryCode

  /** Origin postal code */
  from_postal_code?: string

  /** Destination postal code */
  to_postal_code?: string

  /** Weight filter object */
  weight?: IWeightFilter

  /** Dimension filter object */
  dimension?: IDimensionFilter

  /** Lead time filter object */
  lead_time?: ILeadTimeFilter

  /** Shipping options filter - extensive list of functionality filters */
  shipping_options_filter?: {
    /** Age check minimum age */
    age_check?: number
    /** Business to business */
    b2b?: boolean
    /** Business to consumer */
    b2c?: boolean
    /** Whether the shipment fits in a box */
    boxable?: boolean
    /** Suitable for bulky goods */
    bulky_goods?: boolean
    /** Carrier billing type */
    carrier_billing_type?: 'country' | 'zonal'
    /** Cash on delivery maximum value */
    cash_on_delivery?: number
    /** Dangerous goods allowed */
    dangerous_goods?: boolean
    /** Delivery attempts */
    delivery_attempts?: number
    /** Delivery before time */
    delivery_before_time?: boolean
    /** Delivery deadline */
    delivery_deadline?: string
    /** Direct contract only */
    direct_contract_only?: boolean
    /** Eco delivery */
    eco_delivery?: boolean
    /** First mile pickup */
    first_mile?: boolean
    /** Flexible goods */
    flexible_goods?: boolean
    /** Fresh goods */
    fresh_goods?: boolean
    /** Harmonized label */
    harmonized_label?: boolean
    /** ID check required */
    id_check?: boolean
    /** Insurance available */
    insurance?: boolean
    /** Last mile delivery */
    last_mile?: boolean
    /** Manually attach label */
    manually?: boolean
    /** Multicollo support */
    multicollo?: boolean
    /** Neighbor delivery */
    neighbor_delivery?: boolean
    /** Non-conveyable */
    non_conveyable?: boolean
    /** Personalized delivery */
    personalized_delivery?: boolean
    /** Premium service */
    premium?: boolean
    /** Priority level */
    priority?: string
    /** Registered delivery */
    registered_delivery?: boolean
    /** Returns */
    returns?: boolean
    /** Respect functionality */
    respect?: boolean
    /** Service area */
    service_area?: string
    /** Signature required */
    signature?: boolean
    /** Size category */
    size?: string
    /** Sorted */
    sorted?: boolean
    /** Surcharge */
    surcharge?: boolean
    /** Tracked */
    tracked?: boolean
    /** Tyres allowed */
    tyres?: boolean
    /** Weekend delivery */
    weekend_delivery?: string
    /** Era */
    era?: boolean
  }
}

/**
 * Find available shipping options using SendCloud V3 API
 * @param params - Search parameters for finding shipping options
 * @returns Promise resolving to shipping options array
 *
 * @example
 * ```typescript
 * const options = await findShippingOptions({
 *   from_country_code: CountryCode.NL,
 *   to_country_code: CountryCode.DE,
 *   from_postal_code: '1000AA',
 *   to_postal_code: '10115',
 *   weight: { value: '1.5', unit: 'kg' },
 *   dimension: { length: '20', width: '15', height: '10', unit: 'cm' },
 *   lead_time: { lte: 5 },
 *   shipping_options_filter: {
 *     tracked: true,
 *     signature: true,
 *     b2c: true
 *   }
 * })
 * ```
 */
export const findShippingOptions = async (params: IFindShippingOptionsParams): Promise<IShippingOption[]> => {
  const { data: response } = await sendcloudClientV3.post('/fetch-shipping-options', params)

  // Based on the API documentation, the response has a 'data' property containing the array
  return response.data || []
}