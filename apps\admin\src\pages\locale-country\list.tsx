import { DateField, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { ILocaleCountry, ILocaleLanguage } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import type { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const LocaleCountryList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<ILocaleCountry>({
    syncWithLocation: true,
    meta: {
      join: {
        field: 'languages',
      },
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/countries', id!)))}
      >
        <Table.Column dataIndex="id" title="ID" className="cursor-pointer" />
        <Table.Column dataIndex="name__en" title="Name" className="cursor-pointer" />
        <Table.Column dataIndex="countryAreaCode" title="Country Area Code" className="cursor-pointer" />
        <Table.Column
          dataIndex="languages"
          title="Languages"
          className="cursor-pointer"
          render={(value: ILocaleLanguage[]) => value.map((language) => language.name__en).join(', ')}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<ILocaleCountry>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
