import { InjectQueue } from '@nestjs/bullmq'
import { Injectable } from '@nestjs/common'
import { Cron } from '@nestjs/schedule'
import type { MailDataRequired } from '@sendgrid/mail'
import * as Sentry from '@sentry/nestjs'
import { cancelParcel, getParcel, getParcelDocument, ParcelDocumentFormat, ParcelDocumentType } from '@valyuu/sendcloud'
import type { Queue } from 'bullmq'
import * as currency from 'currency.js'
import { addDays, subDays } from 'date-fns'
import { startOfDay } from 'date-fns'
import { I18nService } from 'nestjs-i18n'
import * as pMap from 'p-map'
import { ILike, In, LessThan } from 'typeorm'

import { envConfig } from '~/configs'
import {
  AddressType,
  EmailType,
  LOCALE_ENABLED_LANGUAGES,
  OFFER_EXPIRATION_DAYS,
  PartnerWebhookEntityType,
  PartnerWebhookEvent,
  ProductModelSellerQuestionType,
  SALE_REMINDER_1_WAIT_DAYS,
  SALE_REMINDER_2_WAIT_DAYS,
  SaleItemOfferStatus,
  SaleItemPlanType,
  SaleItemStatus,
  SalePaymentType,
  SaleShippingLabelType,
  SaleStatus,
  SHIPMENT_BEFORE_CUSTOMER_DROP_OFF_STATUSES,
  ShipmentType,
  StockType,
  StripeAccountApiType,
} from '~/constants'
import {
  CreateSaleInput,
  CreateSaleOutput,
  GetSaleCartItemPricesInput,
  GetSaleCartItemPricesOutput,
  ValidateSellerInfoInput,
  ValidateSellerInfoOutput,
} from '~/dtos'
import {
  AddressEntity,
  BankAccountEntity,
  CharityEntity,
  EmailHistoryEntity,
  OrderEntity,
  ProductVariantEntity,
  QuestionTypeConditionEntity,
  QuestionTypeConditionOptionEntity,
  QuestionTypeProblemEntity,
  SaleEntity,
  SaleItemEntity,
  StockEntity,
  UserEntity,
} from '~/entities'
import { IEmailSellerNewSaleEmailData, IEmailSellerReminderData } from '~/interfaces'
import { PartnerPlatform } from '~/modules/partner/constants'
import {
  ChannelService,
  PartnerWebhookService,
  ProductVariantService,
  SaleItemOfferService,
  ShipmentService,
} from '~/services'
import { getEmailTemplatePrefix, sendmail, sendmailGetAttachment } from '~/utils'
import {
  cloudinaryGetResizedImageUrl,
  formatDate,
  formatMoney,
  generateSaleNumber,
  sendcloudGetPaperlessCode,
  sendcloudGetShippingLabel,
  stripeCreateAccount,
} from '~/utils'

@Injectable()
export class SaleService {
  constructor(
    private readonly variantService: ProductVariantService,
    private readonly channelService: ChannelService,
    private readonly shipmentService: ShipmentService,
    private readonly saleItemOfferService: SaleItemOfferService,
    private readonly partnerWebhookService: PartnerWebhookService,
    private readonly i18n: I18nService,
    @InjectQueue('stripe') private readonly stripeQueue: Queue
  ) {}
  @Cron('0 15 * * *') // every day at 15:00
  async cronSaleReminderEmails1st() {
    return this.sendSaleReminderEmails(0)
  }
  @Cron('0 17 * * *') // every day at 17:00
  async cronSaleReminderEmails2nd() {
    return this.sendSaleReminderEmails(1)
  }

  async getSaleCartItemPrices({ channelId, items }: GetSaleCartItemPricesInput): Promise<GetSaleCartItemPricesOutput> {
    const result = {
      hasPriceChange: false,
      hasRecycled: false,
      items: [] as GetSaleCartItemPricesOutput['items'],
      totalAmount: 0,
    }
    await pMap(items, async (item) => {
      const { cartItemId, oldPrice } = item
      const { modelId, variantId, type, problems, conditions } = item
      const prices =
        type === 'CONDITION'
          ? await this.variantService.findPricesForConditions({ modelId, variantId, channelId, conditions })
          : await this.variantService.findPricesForProblems({ modelId, variantId, channelId, problems })
      const hasRecycled = prices.recycle
      let newPrice = 0
      let hasPriceChange = false
      if (hasRecycled) {
        result.hasRecycled = true
        if (oldPrice !== newPrice) {
          hasPriceChange = true
          result.hasPriceChange = true
        }
      } else if (item.plan === SaleItemPlanType.C2C) {
        newPrice = prices.c2c.price
        if (oldPrice !== newPrice) {
          hasPriceChange = true
          result.hasPriceChange = true
        }
      } else if (item.plan === SaleItemPlanType.C2B) {
        newPrice = prices.c2b.price
        if (oldPrice !== newPrice) {
          hasPriceChange = true
          result.hasPriceChange = true
        }
      }
      result.totalAmount = currency(result.totalAmount).add(newPrice).value
      result.items.push({
        cartItemId,
        hasPriceChange,
        hasRecycled,
        oldPrice,
        newPrice,
      })
    })
    return result
  }
  async createSale({
    input,
    partnerId = null,
    partnerPlatform = null,
  }: {
    input: CreateSaleInput
    shouldSendCustomerEmail?: boolean
    partnerId?: string
    partnerPlatform?: PartnerPlatform
  }): Promise<CreateSaleOutput> {
    const currency = await this.channelService.findCurrencyByChannel(input.channelId)
    if (!currency) {
      Sentry.captureException(new Error('Channel or currency not found'), {
        tags: {
          type: 'sale:createSale',
        },
        extra: {
          channelId: input.channelId,
        },
        level: 'fatal',
      })
      throw new Error('Channel or currency not found')
    }

    // hack to fix frontend problems
    input.items.forEach((item) => {
      if (item.type === ProductModelSellerQuestionType.CONDITION) {
        item.problems = []
      } else if (item.type === ProductModelSellerQuestionType.PROBLEM) {
        item.conditions = []
      }
    })
    const email = input.email.trim()
    const dateOfBirth = input.paymentType === SalePaymentType.BANK_TRANSFER ? input.dateOfBirth?.trim() : undefined
    const address = {
      firstName: input.address.firstName?.trim(),
      lastName: input.address.lastName?.trim(),
      phoneAreaCode: input.address.phoneAreaCode,
      phoneNumber: input.address.phoneNumber?.trim(),
      countryId: input.address.country,
      postalCode: input.address.postalCode?.trim(),
      houseNumber: input.address.houseNumber?.trim(),
      addition: input.address.addition?.trim() ?? null,
      street: input.address.street?.trim(),
      city: input.address.city,
      type: AddressType.SHIPPING,
    } as const
    const bank =
      input.paymentType === SalePaymentType.BANK_TRANSFER
        ? ({
            holderName: input.bankAccount.holderName.trim(),
            accountNumber: input.bankAccount.accountNumber.trim(),
          } as const)
        : undefined

    let userRecord: UserEntity
    let bankAccountRecord: BankAccountEntity
    let addressRecord: AddressEntity
    let saleRecord: SaleEntity
    const saleItemRecords: SaleItemEntity[] = []
    let saleNumber: string
    const createdAt = new Date()

    try {
      await SaleEntity.getRepository().manager.transaction(async (manager) => {
        userRecord = await manager.findOne(UserEntity, { where: { email: ILike(email.trim()) } })
        // There's an existing user
        if (userRecord) {
          userRecord.languageId = input.language
          await manager.save(userRecord)
        } else {
          // Create a new user
          userRecord = await manager.save(
            manager.create(UserEntity, {
              email,
              from: input.from,
              fromPartnerId: partnerId,
              languageId: input.language,
              dateOfBirth: input.paymentType === SalePaymentType.BANK_TRANSFER ? new Date(dateOfBirth) : null,
            })
          )
        }
        // TODO: something seems wrong here
        if (input.paymentType === SalePaymentType.BANK_TRANSFER && bank) {
          // Read or create bank account
          bankAccountRecord = await manager.findOne(BankAccountEntity, {
            where: { accountNumber: bank.accountNumber, userId: userRecord.id },
          })
          if (!bankAccountRecord) {
            bankAccountRecord = await manager.save(
              manager.create(BankAccountEntity, { ...bank, userId: userRecord.id })
            )
          }
        }
        // Read or create shipping address
        addressRecord = await manager.findOne(AddressEntity, { where: { ...address, userId: userRecord.id } })
        if (!addressRecord) {
          addressRecord = await manager.save(manager.create(AddressEntity, { ...address, userId: userRecord.id }))
        }
        // TODO: fix this workaround to get a real unique saleId
        for (let i = 0; i < 1000 && !saleNumber; ++i) {
          saleNumber = generateSaleNumber()
          const existingSale = await manager.findOne(SaleEntity, { where: { saleNumber }, select: ['id'] })
          if (existingSale) {
            saleNumber = undefined
          }
        }
        const hasPreviousOrder = await manager.findOne(OrderEntity, {
          where: { userId: userRecord.id, createdAt: LessThan(subDays(createdAt, 1)) },
        })
        const hasPreviousSale = await manager.findOne(SaleEntity, {
          where: { userId: userRecord.id, createdAt: LessThan(subDays(createdAt, 1)) },
        })
        saleRecord = await manager.save(
          manager.create(SaleEntity, {
            saleNumber,
            status: SaleStatus.SUBMITTED,
            shippingLabel: input.shippingLabel,
            isLabelSent: false,
            addressId: addressRecord.id,
            bankAccountId: bankAccountRecord?.id,
            isReturningCustomer: Boolean(hasPreviousOrder || hasPreviousSale),
            channelId: input.channelId,
            currencyId: currency.id,
            userId: userRecord.id,
            from: input.from,
            paymentType: input.paymentType,
            partnerId,
            partnerPlatform,
            createdAt,
            languageId: userRecord.languageId,
          })
        )
        await pMap(input.items, async (item) => {
          const answers = {
            type: item.type,
            [ProductModelSellerQuestionType.CONDITION]:
              item.type === ProductModelSellerQuestionType.CONDITION ? item.conditions : [],
            [ProductModelSellerQuestionType.PROBLEM]:
              item.type === ProductModelSellerQuestionType.PROBLEM ? item.problems : [],
          }
          let price: number
          if (item.type === ProductModelSellerQuestionType.CONDITION) {
            const { c2c, c2b } = await this.variantService.findPricesForConditions({
              modelId: item.modelId,
              variantId: item.variantId,
              channelId: input.channelId,
              conditions: item.conditions,
            })
            if (item.plan === SaleItemPlanType.C2B) {
              price = c2b.disabled ? 0 : c2b.price
            } else {
              price = c2c.disabled ? 0 : c2c.price
            }
          } else if (item.type === ProductModelSellerQuestionType.PROBLEM) {
            const { c2c, c2b } = await this.variantService.findPricesForProblems({
              modelId: item.modelId,
              variantId: item.variantId,
              channelId: input.channelId,
              problems: item.problems,
            })
            if (item.plan === SaleItemPlanType.C2B) {
              price = c2b.disabled ? 0 : c2b.price
            } else {
              price = c2c.disabled ? 0 : c2c.price
            }
          }

          const stockId = await StockEntity.getNewStockId(StockType.SALE)
          const stock = await manager.save(manager.create(StockEntity, { id: stockId, type: StockType.SALE }))

          // TODO: handle price mismatch
          const saleItemRecord = await manager.save(
            manager.create(SaleItemEntity, {
              productVariantId: item.variantId,
              productModelId: item.modelId,
              saleId: saleRecord.id,
              userId: userRecord.id,
              status: SaleItemStatus.SUBMITTED,
              stock,
              answers,
              type: item.plan,
              originalType: item.plan,
              price,
              initialPrice: price,
              channelId: input.channelId,
              currencyId: currency.id,
              offerStatus: SaleItemOfferStatus.INITIAL,
              partnerId,
              returnAddressId: addressRecord.id,
              offerAcceptedAt: createdAt,
            })
          )
          saleItemRecords.push(saleItemRecord)
        })
      })
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          type: 'sale:createSale',
        },
        extra: {
          input,
          partnerId,
          partnerPlatform,
        },
      })
      return {
        success: false,
        createdAt: new Date(),
        error: (error as Error).message,
        userId: null,
        saleId: null,
        saleNumber: null,
      }
    }

    setImmediate(async () => {
      try {
        const shipment = await this.shipmentService.create({
          type: ShipmentType.SALE,
          orderNumber: saleNumber,
          customerEmail: email,
          channelId: input.channelId,
          isReturn: true,
          customerAddress: addressRecord,
          partnerId,
          saleId: saleRecord.id,
        })
        saleRecord.shipmentId = shipment.id
        if (!shipment.trackingNumber || !shipment.parcelId) {
          const error = new Error('Error creating parcel: Missing tracking number or parcel ID')
          Sentry.captureException(error, {
            tags: {
              type: 'sale:createSale',
            },
            extra: {
              sale: saleRecord,
              shipment,
            },
            level: 'error',
          })
          if (saleRecord.partnerId) {
            await this.partnerWebhookService.callWebhook({
              event: PartnerWebhookEvent.TRADE_IN_SHIPMENT_CREATION_FAILED,
              entityType: PartnerWebhookEntityType.TRADE_IN,
              entityId: saleRecord.id,
              partnerId: saleRecord.partnerId,
              version: saleRecord.version,
            })
          }
          return
        }
        if (saleRecord.partnerId) {
          await this.partnerWebhookService.callWebhook({
            event: PartnerWebhookEvent.TRADE_IN_CREATED,
            entityType: PartnerWebhookEntityType.TRADE_IN,
            entityId: saleRecord.id,
            partnerId: saleRecord.partnerId,
            version: saleRecord.version,
            onSuccess: async () => {
              for (const saleItemRecord of saleItemRecords) {
                await this.partnerWebhookService.callWebhook({
                  event: PartnerWebhookEvent.TRADE_IN_ITEM_CREATED,
                  entityType: PartnerWebhookEntityType.TRADE_IN_ITEM,
                  entityId: saleItemRecord.id,
                  partnerId: saleRecord.partnerId,
                  version: saleItemRecord.version,
                })
              }
            },
          })
        }
        await this.sendSellerSaleEmail(saleRecord.id)
        if (saleRecord.shippingLabel === SaleShippingLabelType.EMAIL) {
          await SaleEntity.update(saleRecord.id, { isLabelSent: true })
        }
      } catch (error) {
        const message = 'Error creating parcel'
        console.error('Error creating parcel', error)
        // await strapi.service('api::error-log.error-log').create({ data: { type: 'sendcloud:create-parcel', message, data: { saleId, error }, severity: 'high' } });
        if (saleRecord.partnerId) {
          await this.partnerWebhookService.callWebhook({
            event: PartnerWebhookEvent.TRADE_IN_SHIPMENT_CREATION_FAILED,
            entityType: PartnerWebhookEntityType.TRADE_IN,
            entityId: saleRecord.id,
            partnerId: saleRecord.partnerId,
            version: saleRecord.version,
          })
        }
      }
    })

    return {
      success: true,
      createdAt: new Date(),
      error: null,
      saleId: saleRecord.id,
      saleNumber,
      userId: saleRecord.userId,
    }
  }

  async validateSellerInfo(
    {
      channelId,
      email,
      firstName,
      lastName,
      phoneAreaCode,
      phoneNumber,
      country,
      postalCode,
      houseNumber,
      addition,
      street,
      city,
      dateOfBirth,
      holderName,
      accountNumber,
    }: ValidateSellerInfoInput,
    ip: string
  ): Promise<ValidateSellerInfoOutput> {
    if (!phoneAreaCode) {
      phoneAreaCode = '+31'
    }
    if (!phoneNumber) {
      phoneNumber = '**********'
    }

    if (!dateOfBirth) {
      dateOfBirth = '1990-01-01'
    }
    const currency = await this.channelService.findCurrencyByChannel(channelId)
    const { success, account, error } = await stripeCreateAccount(
      {
        email,
        address: {
          firstName,
          lastName,
          phoneAreaCode,
          phoneNumber,
          countryId: country,
          postalCode,
          houseNumber,
          addition,
          street,
          city,
        },
        bank: {
          holderName,
          accountNumber,
        },
        dob: dateOfBirth,
        currencyId: currency.id,
        ip,
      },
      StripeAccountApiType.VERIFICATION
    )
    if (account)
      setImmediate(async () => {
        try {
          await this.stripeQueue.add('deleteAccount', {
            accountId: account.id,
            apiType: StripeAccountApiType.VERIFICATION,
          })
        } catch (error) {
          Sentry.captureException(error, {
            tags: {
              type: 'stripe:createAccount',
            },
            extra: {
              accountId: account.id,
              apiType: StripeAccountApiType.VERIFICATION,
            },
            level: 'error',
          })
        }
      })
    return {
      success,
      field: error?.field,
      errorMessage: error?.message,
    }
  }
  async sendSaleReminderEmails(reminderSentCount: number) {
    const waitDays = [SALE_REMINDER_1_WAIT_DAYS, SALE_REMINDER_2_WAIT_DAYS][reminderSentCount]
    const attempts = reminderSentCount + 1
    const sales = await SaleEntity.find({
      where: {
        status: SaleStatus.SUBMITTED,
        createdAt: LessThan(subDays(startOfDay(new Date()), waitDays)),
        reminderSentCount,
      },
      relations: {
        saleItems: true,
        user: true,
        address: true,
        partner: true,
        shipment: true,
      },
      select: {
        id: true,
        currencyId: true,
        createdAt: true,
        parcelId: true,
        reminderSentCount: true,
        saleNumber: true,
        languageId: true,
        user: {
          id: true,
          email: true,
        },
        address: {
          id: true,
          firstName: true,
          countryId: true,
        },
        saleItems: {
          id: true,
          price: true,
        },
        partner: {
          id: true,
          manageCustomerEmails: true,
          slug: true,
        },
        shipment: {
          id: true,
          parcelId: true,
        },
        isLegacy: true,
      },
    })
    for (const sale of sales) {
      try {
        const { user, address, partner } = sale
        if (!(partner?.manageCustomerEmails ?? true) || sale.isLegacy) {
          continue
        }
        // prevent test emails from being sent to real customer
        if (!envConfig.isProd && !user.email.endsWith('@valyuu.com') && !user.email.endsWith('@guapa.nl')) {
          continue
        }
        const lang = sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]

        const attachments: MailDataRequired['attachments'] = []
        if (sale.shipment?.parcelId) {
          const parcel = await getParcel(sale.shipment.parcelId)

          if (SHIPMENT_BEFORE_CUSTOMER_DROP_OFF_STATUSES.includes(parcel.status?.id)) {
            const hasLabel = parcel.documents?.some(({ type }) => type === ParcelDocumentType.LABEL)
            const hasPaperlessCode = parcel.documents?.some(({ type }) => type === ParcelDocumentType.QR)
            if (hasLabel) {
              const label = await getParcelDocument(sale.shipment.parcelId, ParcelDocumentType.LABEL)
              if (label) {
                attachments.push({
                  filename: this.i18n.t('emails.sellerNewSaleAttachmentShippingLabel', { lang }) + '.pdf',
                  content: Buffer.from(label).toString('base64'),
                })
              }
            }
            if (hasPaperlessCode) {
              const paperlessCode = await getParcelDocument(sale.shipment.parcelId, ParcelDocumentType.QR, {
                format: ParcelDocumentFormat.PNG,
              })
              if (paperlessCode) {
                attachments.push({
                  filename: this.i18n.t('emails.sellerNewSaleAttachmentPaperlessCode', { lang }) + '.png',
                  content: Buffer.from(paperlessCode).toString('base64'),
                })
              }
            }
          } else {
            // If parcel doesn't exist or is not ready to send (hasn't been sent out), skip
            Sentry.captureMessage(!parcel ? 'Parcel not found' : `Invalid parcel status: ${parcel.status.id}`, {
              tags: {
                type: 'sale:sendSaleReminderEmails',
              },
              extra: {
                saleId: sale.id,
                parcelId: sale.shipment?.parcelId,
                parcelStatus: parcel?.status?.id,
              },
              level: 'error',
            })
            sale.reminderSentCount = 2
            await sale.save()
            continue
          }
        } else {
          Sentry.captureMessage('No parcel found for sale', {
            tags: {
              type: 'sale:sendSaleReminderEmails',
            },
            extra: {
              saleId: sale.id,
            },
          })
          sale.reminderSentCount = 2
          await sale.save()
          continue
        }

        const type = [1, 2].includes(attempts) ? EmailType.SELLER_REMINDER : undefined
        if (!type) {
          Sentry.captureMessage('Invalid reminder sent count', {
            tags: {
              type: 'sale:sendSaleReminderEmails',
            },
            extra: {
              saleId: sale.id,
              attempts,
            },
            level: 'warning',
          })
          continue
        }

        const subject = this.i18n.t(`emails.sellerReminderSubject`, { lang })
        const locale = `${lang}-${sale.address.countryId}`
        const emailData: IEmailSellerReminderData = {
          shippingFirstName: address.firstName,
          saleNumber: sale.saleNumber,
          expirationDate: formatDate(addDays(sale.createdAt, OFFER_EXPIRATION_DAYS), locale, {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          }),
          estimatedPrice: formatMoney(
            sale.saleItems.reduce((acc, item) => currency(acc).add(item.price).value, 0),
            sale.currencyId,
            locale
          ),
        }

        sale.reminderSentCount = attempts
        await sale.save()

        const templatePrefix = await getEmailTemplatePrefix({
          isPartner: !!partner,
          partnerSlug: partner?.slug,
          type,
          lang,
        })
        await sendmail(
          {
            type,
            lang,
            to: user.email,
            subject,
            data: emailData,
            attachments,
            templatePrefix,
          },
          3
        )
        await EmailHistoryEntity.create({
          email: user.email,
          subject,
          userId: user.id,
          type,
          relatedIds: [sale.id, ...sale.saleItems.map(({ id }) => id)],
          lang,
          data: emailData,
          isResend: false,
          templatePrefix,
        }).save()
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            saleId: sale.id,
            type: 'sale:email-reminder',
          },
          extra: {
            sale,
          },
          level: 'error',
        })
      }
    }
  }
  async sendSellerSaleEmail(saleId: string) {
    const sale = await SaleEntity.findOne({
      where: { id: saleId },
      relations: {
        saleItems: true,
        user: true,
        address: true,
        shipment: true,
        partner: true,
        bankAccount: true,
      },
      select: {
        id: true,
        shippingLabel: true,
        isLabelSent: true,
        paymentType: true,
        saleNumber: true,
        createdAt: true,
        parcelId: true,
        currencyId: true,
        languageId: true,
        user: {
          id: true,
          email: true,
        },
        saleItems: {
          id: true,
          productVariantId: true,
          answers: {},
          price: true,
          type: true,
        },
        address: {
          id: true,
          firstName: true,
          countryId: true,
        },
        bankAccount: {
          id: true,
          accountNumber: true,
        },
        shipment: {
          id: true,
          parcelId: true,
        },
        partner: {
          id: true,
          slug: true,
          manageCustomerEmails: true,
        },
        isLegacy: true,
      },
    })
    if (!(sale.partner?.manageCustomerEmails ?? true) || sale.isLegacy) {
      return
    }
    const lang = sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]

    const variantIds = sale.saleItems.map(({ productVariantId }) => productVariantId)
    const variants: Record<string, ProductVariantEntity> = (
      await ProductVariantEntity.find({
        where: { id: In(variantIds) },
        relations: { model: { image: true }, attributeCombination: { choices: { attribute: true, option: true } } },
        select: {
          id: true,
          model: { id: true, [`name__${lang}`]: true, image: { id: true, publicId: true } },
          attributeCombination: {
            id: true,
            choices: {
              id: true,
              option: { id: true, [`name__${lang}`]: true },
            },
          },
        },
      })
    ).reduce((acc, variant) => Object.assign(acc, { [variant.id]: variant }), {})

    const conditionIds = sale.saleItems.flatMap((item) => item.answers.CONDITION.map(({ conditionId }) => conditionId))
    const conditions: Record<string, QuestionTypeConditionEntity> = (
      await QuestionTypeConditionEntity.find({
        where: { id: In(conditionIds) },
        select: { id: true, [`name__${lang}`]: true },
      })
    ).reduce((acc, condition) => Object.assign(acc, { [condition.id]: condition }), {})

    const conditionOptionIds = sale.saleItems.flatMap((item) => item.answers.CONDITION.map(({ optionId }) => optionId))
    const options: Record<string, QuestionTypeConditionOptionEntity> = (
      await QuestionTypeConditionOptionEntity.find({
        where: { id: In(conditionOptionIds) },
        select: { id: true, [`name__${lang}`]: true },
      })
    ).reduce((acc, option) => Object.assign(acc, { [option.id]: option }), {})

    const problemIds = sale.saleItems.flatMap((item) => item.answers.PROBLEM)
    const problems: Record<string, string> = (
      await QuestionTypeProblemEntity.find({ where: { id: In(problemIds) } })
    ).reduce((acc, problem) => Object.assign(acc, { [problem.id]: problem[`name__${lang}`] }), {})

    const locale = `${sale.languageId}-${sale.address.countryId}`

    let paymentPeriodC2b: string
    let paymentPeriodC2c: string
    let paymentPeriodDonation: string
    const paymentType = sale.paymentType

    if (paymentType === SalePaymentType.DONATION) {
      paymentPeriodDonation = await this.saleItemOfferService.getPaymentPeriod({
        channelId: sale.channelId,
        plan: SaleItemPlanType.C2B,
        lang,
        paymentType: sale.paymentType,
      })
    } else {
      if (sale.saleItems.some(({ type }) => type === SaleItemPlanType.C2B)) {
        paymentPeriodC2b = await this.saleItemOfferService.getPaymentPeriod({
          channelId: sale.channelId,
          plan: SaleItemPlanType.C2B,
          lang,
          paymentType,
        })
      }
      if (sale.saleItems.some(({ type }) => type === SaleItemPlanType.C2C)) {
        paymentPeriodC2c = await this.saleItemOfferService.getPaymentPeriod({
          channelId: sale.channelId,
          plan: SaleItemPlanType.C2C,
          lang,
          paymentType,
        })
      }
    }

    let charityName: string = undefined
    let iban: string = undefined
    if (sale.paymentType === SalePaymentType.DONATION) {
      // TODO: get charity name from sale
      const charity = await CharityEntity.findOneOrFail({ where: {} })
      charityName = charity[`name__${sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]}`]
    } else {
      iban = sale.paymentType === SalePaymentType.BANK_TRANSFER ? sale.bankAccount?.accountNumber : undefined
    }

    const emailData: IEmailSellerNewSaleEmailData = {
      items: sale.saleItems.map((item) => {
        const variant = variants[item.productVariantId]
        return {
          name: variant.model[`name__${lang}`],
          attributes: variant.attributeCombination.choices.map((choice) => choice.option[`name__${lang}`]),
          conditionType: item.answers.type,
          conditions:
            item.answers.type === ProductModelSellerQuestionType.CONDITION
              ? item.answers.CONDITION.map(({ conditionId, optionId }) => ({
                  question: conditions[conditionId][`name__${lang}`],
                  answer: options[optionId][`name__${lang}`],
                }))
              : [],
          problems:
            item.answers.type === ProductModelSellerQuestionType.PROBLEM
              ? item.answers[ProductModelSellerQuestionType.PROBLEM].map((problemId) => problems[problemId])
              : [],
          thumbnail: cloudinaryGetResizedImageUrl({
            publicId: variant.model.image.publicId,
            width: 100,
            height: 100,
          }),
          type:
            sale.paymentType === SalePaymentType.DONATION
              ? this.i18n.t('emails.sellerNewPlanTypeDonation', { lang })
              : this.i18n.t(`emails.sellerNewPlanType${item.type}`, { lang }),
          price: formatMoney(item.price, sale.currencyId, locale),
          isFunctional: item.answers.type === ProductModelSellerQuestionType.CONDITION,
        }
      }),
      saleNumber: sale.saleNumber,
      createdAt: formatDate(sale.createdAt, locale, { dateStyle: 'medium' }),
      isDonation: sale.paymentType === SalePaymentType.DONATION,
      shippingFirstName: sale.address.firstName,
      isNL: sale.address.countryId === 'NL',
      isBE: sale.address.countryId === 'BE',
      isDE: sale.address.countryId === 'DE',
      paymentPeriodC2b,
      paymentPeriodC2c,
      paymentPeriodDonation,
      iban,
      charityName,
    }
    const attachments: MailDataRequired['attachments'] = []
    const parcel = await getParcel(sale.shipment?.parcelId)
    if (SHIPMENT_BEFORE_CUSTOMER_DROP_OFF_STATUSES.includes(parcel.status.id)) {
      const hasLabel = parcel?.documents?.some(({ type }) => type === ParcelDocumentType.LABEL)
      const hasPaperlessCode = parcel?.documents?.some(({ type }) => type === ParcelDocumentType.QR)
      if (hasLabel) {
        const label = await sendcloudGetShippingLabel(sale.shipment.parcelId)
        if (label) {
          attachments.push({
            filename: this.i18n.t('emails.sellerNewSaleAttachmentShippingLabel', { lang }) + '.pdf',
            content: Buffer.from(label).toString('base64'),
          })
        }
      }
      if (hasPaperlessCode) {
        const paperlessCode = await sendcloudGetPaperlessCode(sale.shipment.parcelId)
        if (paperlessCode) {
          attachments.push({
            filename: this.i18n.t('emails.sellerNewSaleAttachmentPaperlessCode', { lang }) + '.png',
            content: Buffer.from(paperlessCode).toString('base64'),
          })
        }
      }
    } else {
      // If parcel doesn't exist or is not ready to send (hasn't been sent out), skip
      const error = new Error(!parcel ? 'Parcel not found' : `Invalid parcel status: ${parcel.status.id}`)
      Sentry.captureException(error, {
        tags: {
          saleId: sale.id,
          parcelId: sale.shipment?.parcelId,
        },
        extra: {
          parcelStatus: parcel?.status?.id,
        },
        level: 'error',
      })
    }

    try {
      const templatePrefix = await getEmailTemplatePrefix({
        isPartner: !!sale.partner,
        partnerSlug: sale.partner?.slug,
        type: EmailType.SELLER_NEW_SALE,
        lang,
      })
      const subject = this.i18n.t('emails.sellerNewSaleSubject', { lang, args: { saleNumber: sale.saleNumber } })
      await sendmail(
        {
          type: EmailType.SELLER_NEW_SALE,
          lang,
          to: sale.user.email,
          subject,
          data: emailData,
          attachments,
          templatePrefix,
        },
        3
      )
      await EmailHistoryEntity.create({
        email: sale.user.email,
        subject,
        userId: sale.user.id,
        type: EmailType.SELLER_NEW_SALE,
        relatedIds: [sale.id, ...sale.saleItems.map(({ id }) => id)],
        lang,
        data: emailData,
        isResend: false,
        templatePrefix,
      }).save()
      if (sale.shippingLabel === SaleShippingLabelType.EMAIL && !sale.isLabelSent) {
        await SaleEntity.update(sale.id, { isLabelSent: true })
      }
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          type: 'sale:sendSellerSaleEmail',
        },
        extra: {
          sale,
          emailData,
        },
        level: 'error',
      })
    }
  }
  async cancelSales() {
    // If a sale has been submitted for more than 30 days, cancel it
    const sales = await SaleEntity.find({
      where: { status: SaleStatus.SUBMITTED, createdAt: LessThan(subDays(new Date(), 30)) },
    })
    for (const sale of sales) {
      sale.status = SaleStatus.CANCELLED
      await sale.save()
      if (sale.shipment.parcelId) {
        await cancelParcel(sale.shipment.parcelId).catch(console.error)
      }
    }
  }
}
