import { limitFit } from '@cloudinary/url-gen/actions/resize'
import {
  <PERSON><PERSON>ield,
  DeleteButton,
  EditButton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IProductModel, IProductVariant } from '@valyuu/api/entities'
import { Input, Space, Table } from 'antd'
import type { FC } from 'react'

import { PublishStatus } from '~/components'
import { cloudinary, filterSearchIcon, handleTableRowClick } from '~/utils'

export const ProductModelList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IProductModel>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'name__en', 'slug__en', 'sortOrder', 'createdAt', 'publishedAt'],
      join: [
        {
          field: 'image',
        },
        {
          field: 'variants',
          select: ['id', 'name__en'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    filters: {
      initial: [
        {
          field: 'name__en',
          operator: 'contains',
          value: '',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/product-models', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="image"
          title="Image"
          className="cursor-pointer"
          render={(value) => (
            <img
              src={cloudinary.image(value.publicId).resize(limitFit(30, 30)).toURL()}
              className="h-auto max-h-[30px] max-w-[30px]"
              alt=""
            />
          )}
          align="center"
          width={40}
        />
        <Table.Column
          dataIndex="name__en"
          title="Name (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('name__en', filters, 'contains')}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Name (English)" />
            </FilterDropdown>
          )}
          filterIcon={filterSearchIcon}
        />
        <Table.Column
          dataIndex="slug__en"
          title="Slug (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('slug__en')}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Slug (English)" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="variants"
          title="Variants"
          className="cursor-pointer"
          render={(value) => (
            <div title={value.map((variant: IProductVariant) => variant.name__en).join('\n')}>
              <span className="underline">
                {value.length} {value.length === 1 ? 'item' : 'items'}
              </span>
            </div>
          )}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IProductModel>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
