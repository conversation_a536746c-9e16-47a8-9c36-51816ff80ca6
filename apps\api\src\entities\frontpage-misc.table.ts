import { Field, ID, ObjectType } from '@nestjs/graphql'
import { BaseEntity, Column, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm'

@ObjectType('FrontpageMisc')
@Entity('frontpage_misc')
export class FrontpageMiscEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryColumn({ type: 'char', length: 2 })
  id: 'ID'

  @Field()
  @Column()
  totalMembers: number

  @Field()
  @Column('float')
  totalSavedCo2: number

  @Field()
  @Column('float')
  totalSavedCo2Km: number

  @Field()
  @Column('float')
  totalSavedEwaste: number

  @Field()
  @Column('float')
  totalSavedEwasteElephant: number

  @Field()
  @Column('decimal')
  totalDonation: number

  @UpdateDateColumn()
  updatedAt: Date
}

export type IFrontpageMisc = Omit<FrontpageMiscEntity, keyof BaseEntity>
