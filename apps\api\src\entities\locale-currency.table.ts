import { Field, ID, ObjectType } from '@nestjs/graphql'
import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryColumn, UpdateDateColumn } from 'typeorm'

@ObjectType('LocaleCurrency')
@Entity('locale_currency')
export class LocaleCurrencyEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryColumn()
  id: string

  @Field()
  @Column({ nullable: false })
  name: string

  @Field()
  @Column({ nullable: false })
  symbol: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ILocaleCurrency = Omit<LocaleCurrencyEntity, keyof BaseEntity>
