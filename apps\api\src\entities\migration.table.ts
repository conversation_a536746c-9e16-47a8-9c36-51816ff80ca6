import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryColumn, UpdateDateColumn } from 'typeorm'

import { MigrationType } from '~/constants'

@Entity('migration')
export class MigrationEntity extends BaseEntity {
  @PrimaryColumn({ type: 'enum', enum: MigrationType })
  type: MigrationType

  @PrimaryColumn()
  oldId: string

  @Column()
  @Index()
  newId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IMigration = Omit<MigrationEntity, keyof BaseEntity>
