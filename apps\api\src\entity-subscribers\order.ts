import { Injectable } from '@nestjs/common'
import { cancelParcel } from '@valyuu/sendcloud'
import * as pMap from 'p-map'
import type { EntitySubscriberInterface, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { OrderSkuItemStatus, OrderStatus } from '~/constants'
import { OrderEntity, OrderSkuItemEntity } from '~/entities'

@Injectable()
@EventSubscriber()
export class OrderSubscriber implements EntitySubscriberInterface<OrderEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return OrderEntity
  }

  async afterUpdate(event: UpdateEvent<OrderEntity>) {
    const { entity, databaseEntity, manager } = event

    if (!entity?.id || !databaseEntity?.id) {
      return
    }

    // restock if order is cancelled
    if (entity.status === OrderStatus.CANCELLED && databaseEntity.status !== OrderStatus.CANCELLED) {
      const orderItems = await manager.find(OrderSkuItemEntity, { where: { orderId: entity.id } })
      await pMap(orderItems, async (orderItem) => {
        orderItem.status = OrderSkuItemStatus.CANCELLED
        return manager.save(orderItem)
      })
    } else if (entity.status === OrderStatus.REFUNDED && databaseEntity.status !== OrderStatus.REFUNDED) {
      const orderItems = await manager.find(OrderSkuItemEntity, { where: { orderId: entity.id } })
      await pMap(orderItems, async (orderItem) => {
        orderItem.status = OrderSkuItemStatus.REFUNDED
        return manager.save(orderItem)
      })
    }

    if (databaseEntity.status !== OrderStatus.CANCELLED && entity.status === OrderStatus.CANCELLED) {
      const order = await OrderEntity.findOne({
        where: { id: entity.id },
        relations: {
          shipment: true,
        },
      })
      if (order.shipment?.parcelId) {
        await cancelParcel(order.shipment.parcelId).catch(() => {})
      }
    }
  }
}
