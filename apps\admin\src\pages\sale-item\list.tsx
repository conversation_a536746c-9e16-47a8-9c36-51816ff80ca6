import { DateF<PERSON>, EditButton, FilterDropdown, getDefaultSortOrder, List, useSelect, useTable } from '@refinedev/antd'
import { getDefaultFilter, HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { SaleItemOfferStatus, SaleItemStatus } from '@valyuu/api/constants'
import type { ISaleItem } from '@valyuu/api/entities'
import type { IPartner } from '@valyuu/api/entities'
import { Badge, Popover, Radio, Space, Table, Tag } from 'antd'
import { Input, Select } from 'antx'
import { capitalize } from 'lodash'
import { type FC } from 'react'

import { FormTableSearch } from '~/components'
import { formatMoney, formatSelectOptionsFromEnum, handleTableRowClick, isUUID } from '~/utils'

const statusColorMap = {
  [SaleItemStatus.SUBMITTED]: 'default', // Starting state
  [SaleItemStatus.RECEIVED]: 'processing', // In progress
  [SaleItemStatus.CANCELLED]: 'default', // Cancelled
  [SaleItemStatus.PENDING_OFFER]: 'orange', // Waiting action
  [SaleItemStatus.PENDING_PAYMENT]: 'cyan', // Financial state
  [SaleItemStatus.PENDING_RETURN]: 'magenta', // Return process
  [SaleItemStatus.PENDING_RECYCLE]: 'volcano', // Recycle process
  [SaleItemStatus.RETURNED]: 'geekblue', // Completed return
  [SaleItemStatus.PAYMENT_IN_PROCESS]: 'gold', // Financial processing
  [SaleItemStatus.PAYMENT_FAILED]: 'error', // Error state
  [SaleItemStatus.NOT_RECEIVED]: 'red', // Problem state
  [SaleItemStatus.PAID]: 'success', // Success state
  [SaleItemStatus.ON_HOLD]: 'purple', // On hold
  [SaleItemStatus.CUSTOMER_ABANDONED]: 'grey', // Customer abandoned
}

const offerStatusColorMap = {
  [SaleItemOfferStatus.INITIAL]: 'default', // Initial state
  [SaleItemOfferStatus.PENDING]: 'blue', // Waiting state
  [SaleItemOfferStatus.ACCEPTED]: 'green', // Positive outcome
  [SaleItemOfferStatus.DECLINED_RECYCLE]: 'volcano', // Recycle outcome
  [SaleItemOfferStatus.DECLINED_RETURN]: 'orange', // Return outcome
  [SaleItemOfferStatus.EXPIRED]: 'default', // Inactive state
}

export const SaleItemList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters, searchFormProps } = useTable<ISaleItem, HttpError, { search: string }>({
    syncWithLocation: true,
    meta: {
      join: [
        {
          field: 'user',
          select: ['email'],
        },
        {
          field: 'sale',
          select: ['id', 'saleNumber', 'partnerPlatform', 'receivedAt'],
        },
        {
          field: 'productVariant',
          select: ['name__en'],
        },
        {
          field: 'sale.shipment',
          select: ['id', 'trackingNumber'],
        },
        {
          field: 'partner',
          select: ['id', 'name'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    onSearch: ({ search }) => {
      search = (search ?? '').trim()
      switch (true) {
        case !search:
          return [
            {
              operator: 'or',
              value: [],
            },
          ]
        case /^(S|E)\d+$/.test(search):
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'stockId',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        case isUUID(search):
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'id',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        case /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$/.test(search): // email
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'user.email',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        default:
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'sale.saleNumber',
                  operator: 'eq',
                  value: search,
                },
                {
                  field: 'sale.shipment.trackingNumber',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
      }
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { selectProps: partnerSelectProps } = useSelect<IPartner>({
    resource: 'admin/partners',
    optionLabel: 'name',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List
      headerButtons={({ defaultButtons }) => (
        <Space>
          <FormTableSearch
            searchFormProps={searchFormProps}
            title="Search for a sale item by its ID, sale number, stock ID, tracking number or user's email address"
          />
          {defaultButtons}
        </Space>
      )}
    >
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/sale-items', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value, row: ISaleItem) => (
            <>
              {row.isLegacy && (
                <Badge.Ribbon
                  color="gray"
                  text="legacy"
                  placement="start"
                  className="absolute -top-5 left-0 -ml-2 origin-top-left scale-75"
                />
              )}
              <span title={value}>{value}</span>
            </>
          )}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex={['sale', 'saleNumber']}
          title="Sale number"
          className="cursor-pointer"
          width="8rem"
          defaultFilteredValue={getDefaultFilter('saleNumber', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Sale number" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex={['user', 'email']}
          title="User"
          className="cursor-pointer"
          align="center"
          defaultFilteredValue={getDefaultFilter('user.email', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 280 }} placeholder="User email" normalize={(value) => value.trim()} />
            </FilterDropdown>
          )}
          width="7rem"
        />
        <Table.Column
          dataIndex="productVariant"
          title="Product variant"
          className="cursor-pointer"
          width="9rem"
          render={(value) => value.name__en}
        />
        <Table.Column
          dataIndex="stockId"
          title="Stock ID"
          className="cursor-pointer"
          width="6rem"
          defaultFilteredValue={getDefaultFilter('stockId', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Stock ID" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="imei"
          title="IMEI"
          className="cursor-pointer"
          width="6rem"
          defaultFilteredValue={getDefaultFilter('imei', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="IMEI" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="price"
          title="Price"
          className="cursor-pointer"
          width="5rem"
          render={(value, row: ISaleItem) => formatMoney(value, row.currencyId)}
        />
        <Table.Column
          dataIndex="status"
          title="Status"
          className="cursor-pointer"
          width="4rem"
          defaultFilteredValue={getDefaultFilter('status', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                placeholder="Status"
                options={formatSelectOptionsFromEnum(SaleItemStatus)}
              />
            </FilterDropdown>
          )}
          render={(value) => (
            <Tag color={statusColorMap[value as keyof typeof statusColorMap]}>
              {capitalize(value.replace(/_/g, ' '))}
            </Tag>
          )}
        />
        <Table.Column
          dataIndex="offerStatus"
          title="Offer status"
          className="cursor-pointer"
          width="4rem"
          defaultFilteredValue={getDefaultFilter('offerStatus', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                placeholder="Offer Status"
                options={formatSelectOptionsFromEnum(SaleItemOfferStatus)}
              />
            </FilterDropdown>
          )}
          render={(value, row: ISaleItem) => (
            <Tag color={offerStatusColorMap[value as keyof typeof offerStatusColorMap]}>
              {row?.offerId ? '⚠️' : ''}
              {capitalize(value.replace(/_/g, ' '))}
            </Tag>
          )}
        />
        <Table.Column
          dataIndex="isTested"
          title="Tested"
          className="cursor-pointer"
          width="4rem"
          defaultFilteredValue={getDefaultFilter('isTested', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Radio.Group
                options={[
                  {
                    label: 'Yes',
                    value: 1,
                  },
                  {
                    label: 'No',
                    value: 0,
                  },
                ]}
              />
            </FilterDropdown>
          )}
          render={(value) => <Tag color={value ? 'success' : 'warning'}>{value ? 'Yes' : 'No'}</Tag>}
        />
        <Table.Column
          dataIndex="note"
          title="Note"
          className="cursor-pointer overflow-hidden"
          ellipsis={true}
          render={(value) => (
            <Popover content={value} placement="topLeft">
              <div className="max-w-16 truncate">{value}</div>
            </Popover>
          )}
        />
        <Table.Column
          dataIndex={['partner', 'id']}
          title="Source"
          className="cursor-pointer"
          width="6rem"
          render={(_, row: ISaleItem) =>
            row?.partner?.name
              ? capitalize(row.partner.name) +
                (row?.sale?.partnerPlatform ? ' (' + capitalize(row.sale.partnerPlatform) + ')' : '')
              : 'Prioont'
          }
          defaultFilteredValue={getDefaultFilter('partner.id', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} placeholder="Partner" {...(partnerSelectProps as any)} />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex={['sale', 'receivedAt']}
          title="Received at"
          render={(value) => (value ? <DateField value={value} format="DD-MM-YYYY HH:mm" /> : '-')}
          defaultSortOrder={getDefaultSortOrder('receivedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<ISaleItem>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
