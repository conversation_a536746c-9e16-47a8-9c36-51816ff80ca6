import { SelectProps } from 'antd'
import { create, Select } from 'antx'

export type IInputMultiEntitySelectProps = Omit<SelectProps, 'value' | 'mode'> & {
  value?: { id: string }[]
  onChange?: (value: { id: string }[]) => void
  mode?: 'multiple' | 'tags'
}

export const InputMultiEntitySelect = create(
  ({ value, onChange, mode = 'multiple', ...selectProps }: IInputMultiEntitySelectProps) => {
    return (
      <Select
        {...(selectProps as any)}
        mode={mode}
        value={value?.map((item) => item.id) ?? []}
        onChange={(value: string[]) => {
          onChange?.(value.map((id) => ({ id })))
        }}
      />
    )
  }
)
