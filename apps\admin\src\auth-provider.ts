import { AuthBindings } from '@refinedev/core'

import type { AdminUserFrontendType } from '~/interfaces'

// TODO: change localstorage to encrypted storage or encrypted stage management with persistent storage
export const authProvider: AuthBindings = {
  login: async (user: AdminUserFrontendType) => {
    if (user?.id && user?.email && user?.token) {
      localStorage.setItem('user', JSON.stringify(user))

      return {
        success: true,
        successNotification: {
          message: 'Login successful',
          description: 'Welcome back!',
        },
        redirectTo: '/',
      }
    }

    return {
      success: false,
      error: {
        message: 'Login failed',
        name: 'Contact site administrator to enable your account',
      },
    }
  },
  logout: async () => {
    const userJson = localStorage.getItem('user')

    if (userJson && typeof window !== 'undefined') {
      localStorage.removeItem('user')
    }

    return {
      success: true,
      redirectTo: '/login?to=' + window.location.pathname + window.location.search,
    }
  },
  onError: async (error) => {
    console.error(error)
    return { error }
  },
  check: async () => {
    const userJson = localStorage.getItem('user')

    if (userJson) {
      const user = JSON.parse(userJson) as AdminUserFrontendType
      return {
        authenticated: !!user.token,
      }
    }

    return {
      authenticated: false,
      error: {
        message: 'Check failed',
        name: 'Token not found',
      },
      logout: true,
      redirectTo: '/login?to=' + window.location.pathname + window.location.search,
    }
  },
  getPermissions: async () => null,
  getIdentity: async () => {
    const userJson = localStorage.getItem('user')
    if (userJson) {
      return JSON.parse(userJson) as AdminUserFrontendType
    }

    return null
  },
}
