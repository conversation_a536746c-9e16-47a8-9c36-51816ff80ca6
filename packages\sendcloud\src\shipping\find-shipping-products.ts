import { sendcloudClientV2 } from '../client'
import { CarrierCode, CountryCode } from '../constants'
import { IShippingProduct } from '../interfaces'

/**
 * Parameters for finding shipping products
 */
export type IFindShippingProductParams = {
  /** A carrier code
   * @example postnl
   */
  carrier?: CarrierCode

  /** The contract id used for shipping product/method filtering. It has a similar effect as using the carrier parameter.
   * If the contract_pricing parameter is used, the carrier is used to provide the correct contract pricing.
   * Only required if multiple contract prices exist for the same shipping method.
   * @example 123
   */
  contract?: number

  /** Whether to include contract prices in the response. This parameter is only available to users upon request. Please contact customer support.
   * @example false
   */
  contract_pricing?: boolean

  /** The height of the shipment. Required if the "width" and/or "length" parameters are provided.
   * @example 10
   * >= 0
   */
  height?: number

  /** The unit for the shipment height. Required if the "height" parameter is provided.
   * @example centimeter
   */
  height_unit?: 'millimeter' | 'centimeter' | 'meter'

  /** Filter shipping products by their transit time. This is the estimated time a shipment takes between pickup and delivery.
   * @example 24
   */
  lead_time_hours?: number

  /** The length of the shipment. Required if the "width" and/or "height" parameters are provided.
   * @example 100
   * >= 0
   */
  length?: number

  /** The unit for the shipment length. Required if the "length" parameter is provided.
   * @example centimeter
   */
  length_unit?: 'millimeter' | 'centimeter' | 'meter'

  /** A country ISO 2 code for the recipient country. Required if the carrier is zonal or contract_pricing parameter is provided.
   * @example NL
   */
  to_country?: CountryCode

  /** The unit for the shipment weight. Required if the "weight" parameter is provided.
   * @example gram
   */
  weight_unit?: 'gram' | 'kilogram'

  /** The width of the shipment. Required if the "length" and/or "height" parameters are provided.
   * @example 50
   * >= 0
   */
  width?: number

  /** A country ISO 2 code for a from country (origin country).
   * @example NL
   * @required
   */
  from_country: CountryCode

  /** Postal code of the sender. Required if the carrier is zonal.
   * @example 01000
   * <= 12 characters
   */
  from_postal_code?: string

  /** If set to true, the endpoint will return shipping methods which can be used for making a return shipment.
   * @default false
   */
  returns?: boolean

  /** Postal code of the recipient. Required if the carrier is zonal.
   * @example 01000
   * <= 12 characters
   */
  to_postal_code?: string

  /** The weight of the shipment.
   * @example 1500
   * >= 0
   */
  weight?: number

  /** The unit for the shipment width. Required if the "width" parameter is provided.
   * @example centimeter
   */
  width_unit?: 'millimeter' | 'centimeter' | 'meter'
}

/**
 * Find available shipping products based on given parameters
 * @param params - Search parameters for finding shipping products
 * @returns Promise resolving to array of shipping products
 */
export const findShippingProducts = async (params: IFindShippingProductParams): Promise<IShippingProduct[]> => {
  const { data } = await sendcloudClientV2.get('/shipping-products', {
    params,
  })

  return data
}
