import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { ParcelDocumentFormat } from '@valyuu/sendcloud'
import { Repository } from 'typeorm'

import { ShipmentEntity } from '~/entities'
import { sendcloudGetPaperlessCode, sendcloudGetShippingLabel } from '~/utils'

export class CrudShipmentService extends TypeOrmCrudService<ShipmentEntity> {
  constructor(@InjectRepository(ShipmentEntity) repo: Repository<ShipmentEntity>) {
    super(repo)
  }

  async previewDocument(id: string): Promise<string> {
    const shipment = await this.repo.findOne({ where: { id } })

    if (!shipment) {
      // Create a simple SVG for not found case
      const notFoundSvg =
        '<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="200" fill="#F3F4F6"/><path d="M100 80L120 120H80L100 80Z" fill="#EF4444"/><circle cx="100" cy="112" r="2" fill="white"/><rect x="98" y="95" width="4" height="12" fill="white"/></svg>'
      return `data:image/svg+xml;base64,${Buffer.from(notFoundSvg).toString('base64')}`
    }

    const buffer = await (shipment.isPaperless
      ? sendcloudGetPaperlessCode(shipment.parcelId, ParcelDocumentFormat.PNG)
      : sendcloudGetShippingLabel(shipment.parcelId, ParcelDocumentFormat.PNG))

    return `data:image/png;base64,${Buffer.from(buffer).toString('base64')}`
  }

  async downloadDocument(id: string): Promise<Buffer> {
    const shipment = await this.repo.findOne({ where: { id } })

    if (!shipment) {
      throw new NotFoundException('Shipment not found')
    }

    const document = shipment.isPaperless
      ? await sendcloudGetPaperlessCode(shipment.parcelId, ParcelDocumentFormat.PDF)
      : await sendcloudGetShippingLabel(shipment.parcelId, ParcelDocumentFormat.PDF)

    if (!document) {
      throw new NotFoundException('Shipment not found')
    }
    return Buffer.from(document)
  }
}
