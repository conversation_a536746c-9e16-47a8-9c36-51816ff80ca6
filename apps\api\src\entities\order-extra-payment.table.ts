import { Field, ID, ObjectType } from '@nestjs/graphql'
import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  ChannelEntity,
  type IChannel,
  type ILocaleCurrency,
  type IOrder,
  LocaleCurrencyEntity,
  OrderEntity,
} from '~/entities'
@ObjectType('OrderExtraPayment')
@Entity('order_extra_payment')
export class OrderExtraPaymentEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  amount: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((payment: OrderExtraPaymentEntity) => payment.channel)
  channelId: string

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: OrderExtraPaymentEntity) => item.currency)
  currencyId: string

  @Column()
  success: boolean

  @Field()
  @Column()
  stripeCharge: `${'py' | 'ch'}_${string}`

  @ManyToOne(() => OrderEntity, (order) => order.extraPayments, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  order: Relation<OrderEntity>

  @Column()
  @RelationId((payment: OrderExtraPaymentEntity) => payment.order)
  orderId: string

  @Column()
  reason: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IOrderExtraPayment = Omit<OrderExtraPaymentEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
  order: IOrder
}
