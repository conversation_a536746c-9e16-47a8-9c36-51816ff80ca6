import cx from 'clsx'
import { FC } from 'react'
import { Link } from 'react-router-dom'

export type TrelloLinkProps = {
  id: string
  className?: string
}

export const getTrelloLink = (id: string) => `https://trello.com/c/${id}`

export const LinkTrello: FC<TrelloLinkProps> = ({ id, className }) => {
  return id ? (
    <Link
      to={getTrelloLink(id)}
      onClick={(event) => event.stopPropagation()}
      target="_blank"
      rel="noreferrer noopener"
      title="Open in Trello"
      className={cx(className, 'inline-block overflow-hidden text-ellipsis')}
    >
      {id}
    </Link>
  ) : null
}
