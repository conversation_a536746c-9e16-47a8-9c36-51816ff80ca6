import { Injectable, NotFoundException } from '@nestjs/common'
import ms from 'ms'
import { FindOptionsRelations, IsNull, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { BlogPostEntity } from '~/entities'

@Injectable()
export class BlogService {
  async findAll(relations: FindOptionsRelations<BlogPostEntity>) {
    return BlogPostEntity.find({
      where: { publishedAt: Not(IsNull()) },
      relations,
      select: {
        id: true,
        title__en: true,
        title__nl: true,
        title__de: true,
        title__pl: true,
        slug__en: true,
        slug__nl: true,
        slug__de: true,
        slug__pl: true,
        summary__en: true,
        summary__nl: true,
        summary__de: true,
        summary__pl: true,
        createdAt: true,
        tag: {
          id: true,
          color: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          name__pl: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          slug__pl: true,
        },
      },
      cache: envConfig.isProd ? ms('30 minutes') : false,
    })
  }

  async findOne({
    lang,
    slug,
    relations,
  }: {
    lang: string
    slug: string
    relations: FindOptionsRelations<BlogPostEntity>
  }) {
    let blog = await BlogPostEntity.findOne({
      where: { publishedAt: Not(IsNull()), [`slug__${lang}`]: slug },
      relations,
      cache: envConfig.isProd ? ms('30 minutes') : false,
    })
    // Check other languages, make sure language switching has no issue
    if (!blog) {
      blog = await BlogPostEntity.findOne({
        where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({
          publishedAt: Not(IsNull()),
          [`slug__${l}`]: slug,
        })),
        relations,
      })
    }

    if (!blog) {
      throw new NotFoundException('Blog post not found')
    }

    const related = await BlogPostEntity.find({
      where: { tagId: blog.tagId, id: Not(blog.id) },
      take: 5,
      select: {
        id: true,
        title__en: true,
        title__nl: true,
        title__de: true,
        title__pl: true,
        slug__en: true,
        slug__nl: true,
        slug__de: true,
        slug__pl: true,
        summary__en: true,
        summary__nl: true,
        summary__de: true,
        summary__pl: true,
      },
      relations: {
        heroImage: true,
      },
    })

    return Object.assign(blog, { related })
  }
}
