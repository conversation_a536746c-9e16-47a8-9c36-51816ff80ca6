import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { BadRequestException, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { cancelParcel } from '@valyuu/sendcloud'
import { escapeRegExp, isEqual } from 'lodash'
import * as pMap from 'p-map'
import { In, Repository } from 'typeorm'

import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { ShipmentType } from '~/constants'
import {
  ProductVariantEntity,
  QuestionExtraProblemEntity,
  QuestionTypeConditionEntity,
  QuestionTypeConditionOptionEntity,
  QuestionTypeProblemEntity,
  SaleItemEntity,
} from '~/entities'
import { ShipmentService } from '~/services'

const SALE_ITEM_COMPARE_FIELDS = [
  'answers',
  'customerNote',
  'type',
  'status',
  'offerStatus',
  'price',
  'isTested',
  'productModelId',
  'productVariantId',
] as const

type HistoryChange = {
  field: (typeof SALE_ITEM_COMPARE_FIELDS)[number]
  from: unknown
  to: unknown
}

type HistoryChangeItem = {
  version: number
  changes: HistoryChange[]
  changedBy?: {
    id: string
    name: string
    type: string
  }
  changedAt: Date
}

type SaleItemAnswers = {
  CONDITION?: Array<{ conditionId: string; optionId: string }>
  PROBLEM?: string[]
  EXTRA_PROBLEM?: Array<{ id: string; variables?: Record<string, string> }>
}

type TransformedHistoryChange = {
  version: number
  changedBy?: {
    id: string
    name: string
    type: string
  }
  changedAt: Date
  product?: {
    from?: string
    to?: string
  }
  conditions?: {
    from?: string[]
    to?: string[]
  }
  problems?: {
    from?: string[]
    to?: string[]
  }
  extraProblems?: {
    from?: string[]
    to?: string[]
  }
}

export class CrudSaleItemService extends TypeOrmCrudService<SaleItemEntity> {
  constructor(
    @InjectRepository(SaleItemEntity) repo: Repository<SaleItemEntity>,
    private readonly shipmentService: ShipmentService
  ) {
    super(repo)
  }

  async getHistories(id: string) {
    const saleItem = await SaleItemEntity.findOne({
      where: { id },
      relations: {
        histories: {
          changedByAdminUser: true,
          changedByPartner: true,
        },
        sale: true,
      },
    })

    if (!saleItem) {
      throw new NotFoundException('Sale item not found')
    }

    const lang = saleItem.sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]

    // Sort histories by version in ascending order for processing
    const sortedHistories = saleItem.histories.sort((a, b) => a.version - b.version)
    const historyChanges: HistoryChangeItem[] = []

    // Process each history item
    for (const currentHistory of sortedHistories) {
      const changes: HistoryChange[] = []

      // If this is the only history or the last history, compare with current sale item state
      const compareWithCurrent =
        sortedHistories.length === 1 || currentHistory === sortedHistories[sortedHistories.length - 1]

      const nextVersion = compareWithCurrent ? saleItem : sortedHistories[sortedHistories.indexOf(currentHistory) + 1]

      // Compare fields between current and next version
      for (const field of SALE_ITEM_COMPARE_FIELDS) {
        const fromValue = currentHistory[field]
        const toValue = nextVersion[field]

        if (!isEqual(fromValue, toValue)) {
          changes.push({
            field,
            from: fromValue,
            to: toValue,
          })
        }
      }

      const changedBy = currentHistory.changedBy
      const historyChange: HistoryChangeItem = {
        version: currentHistory.version + 1, // Version number represents the version that changed TO
        changes,
        changedAt: currentHistory.createdAt,
      }

      if (changedBy) {
        historyChange.changedBy = {
          id: changedBy.id,
          name: 'name' in changedBy ? changedBy.name : '',
          type: currentHistory.changedByType,
        }
      }

      historyChanges.push(historyChange)
    }

    // Get all unique variant IDs from both histories and current state
    const variantIds = new Set(
      [
        ...historyChanges.flatMap((item) =>
          item.changes
            .filter((change) => change.field === 'productVariantId')
            .flatMap((change) => [change.from, change.to])
        ),
        saleItem.productVariantId,
      ].filter(Boolean) as string[]
    )

    // Get all answer-related IDs from both histories and current state
    const allAnswers = [
      ...historyChanges.flatMap((item) =>
        item.changes.filter((change) => change.field === 'answers').flatMap((change) => [change.from, change.to])
      ),
      saleItem.answers,
    ].filter(Boolean) as SaleItemAnswers[]

    const conditionIds = new Set(
      allAnswers.flatMap((answers) => answers.CONDITION?.map((condition) => condition.conditionId) ?? [])
    )

    const conditionOptionIds = new Set(
      allAnswers.flatMap((answers) => answers.CONDITION?.map((condition) => condition.optionId) ?? [])
    )

    const problemIds = new Set(allAnswers.flatMap((answers) => answers.PROBLEM ?? []))

    const extraProblemIds = new Set(allAnswers.flatMap((answers) => answers.EXTRA_PROBLEM?.map((ep) => ep.id) ?? []))

    // Fetch all required data
    const [variants, conditions, conditionOptions, problems, extraProblems] = await Promise.all([
      ProductVariantEntity.find({
        where: { id: In([...variantIds]) },
        select: { id: true, [`name__${lang}`]: true },
      }),
      QuestionTypeConditionEntity.find({
        where: { id: In([...conditionIds]) },
        select: { id: true, [`name__${lang}`]: true },
      }),
      QuestionTypeConditionOptionEntity.find({
        where: { id: In([...conditionOptionIds]) },
        select: { id: true, [`name__${lang}`]: true },
      }),
      QuestionTypeProblemEntity.find({
        where: { id: In([...problemIds]) },
        select: { id: true, [`name__${lang}`]: true },
      }),
      QuestionExtraProblemEntity.find({
        where: { id: In([...extraProblemIds]) },
        select: { id: true, [`template__${lang}`]: true },
      }),
    ])

    // Create maps for quick lookups
    const variantsMap = variants.reduce(
      (acc, v) => ({ ...acc, [v.id]: v[`name__${lang}`] }),
      {} as Record<string, string>
    )
    const conditionsMap = conditions.reduce(
      (acc, c) => ({ ...acc, [c.id]: c[`name__${lang}`] }),
      {} as Record<string, string>
    )
    const conditionOptionsMap = conditionOptions.reduce(
      (acc, co) => ({ ...acc, [co.id]: co[`name__${lang}`] }),
      {} as Record<string, string>
    )
    const problemsMap = problems.reduce(
      (acc, p) => ({ ...acc, [p.id]: p[`name__${lang}`] }),
      {} as Record<string, string>
    )
    const extraProblemsMap = extraProblems.reduce(
      (acc, ep) => ({ ...acc, [ep.id]: ep[`template__${lang}`] }),
      {} as Record<string, string>
    )

    // Transform the history changes
    const result = await pMap(historyChanges, async (historyChange) => {
      const transformedChanges: TransformedHistoryChange = {
        version: historyChange.version,
        changedBy: historyChange.changedBy,
        changedAt: historyChange.changedAt,
      }

      // Transform product variant changes
      const variantChange = historyChange.changes.find((c) => c.field === 'productVariantId')
      if (variantChange) {
        transformedChanges.product = {
          from: variantsMap[variantChange.from as string],
          to: variantsMap[variantChange.to as string],
        }
      }

      // Transform answers changes
      const answersChange = historyChange.changes.find((c) => c.field === 'answers')
      if (answersChange) {
        const fromAnswers = answersChange.from as SaleItemAnswers
        const toAnswers = answersChange.to as SaleItemAnswers

        // Transform conditions
        if (fromAnswers?.CONDITION || toAnswers?.CONDITION) {
          transformedChanges.conditions = {
            from: fromAnswers?.CONDITION?.map(
              (c) => `${conditionsMap[c.conditionId]}: ${conditionOptionsMap[c.optionId]}`
            ),
            to: toAnswers?.CONDITION?.map((c) => `${conditionsMap[c.conditionId]}: ${conditionOptionsMap[c.optionId]}`),
          }
        }

        // Transform problems
        if (fromAnswers?.PROBLEM || toAnswers?.PROBLEM) {
          transformedChanges.problems = {
            from: fromAnswers?.PROBLEM?.map((p) => problemsMap[p]),
            to: toAnswers?.PROBLEM?.map((p) => problemsMap[p]),
          }
        }

        // Transform extra problems
        if (fromAnswers?.EXTRA_PROBLEM || toAnswers?.EXTRA_PROBLEM) {
          transformedChanges.extraProblems = {
            from: fromAnswers?.EXTRA_PROBLEM?.map((ep) => {
              let message = extraProblemsMap[ep.id]
              Object.entries(ep.variables ?? {}).forEach(([key, value]) => {
                message = message.replace(new RegExp(`{{${escapeRegExp(key)}}}`, 'g'), value)
              })
              return message
            }),
            to: toAnswers?.EXTRA_PROBLEM?.map((ep) => {
              let message = extraProblemsMap[ep.id]
              Object.entries(ep.variables ?? {}).forEach(([key, value]) => {
                message = message.replace(new RegExp(`{{${escapeRegExp(key)}}}`, 'g'), value)
              })
              return message
            }),
          }
        }
      }

      // Handle customer note
      const customerNoteChange = historyChange.changes.find((c) => c.field === 'customerNote')
      if (customerNoteChange) {
        if (!transformedChanges.extraProblems) {
          transformedChanges.extraProblems = {
            from: customerNoteChange.from ? [customerNoteChange.from as string] : undefined,
            to: customerNoteChange.to ? [customerNoteChange.to as string] : undefined,
          }
        } else {
          if (customerNoteChange.from) {
            transformedChanges.extraProblems.from = [
              ...(transformedChanges.extraProblems.from || []),
              customerNoteChange.from as string,
            ]
          }
          if (customerNoteChange.to) {
            transformedChanges.extraProblems.to = [
              ...(transformedChanges.extraProblems.to || []),
              customerNoteChange.to as string,
            ]
          }
        }
      }

      return transformedChanges
    })

    return result.sort((a, b) => b.version - a.version)
  }

  async createReturnShipment(id: string, override = false) {
    const saleItem = await SaleItemEntity.findOne({
      where: { id },
      relations: { user: true, sale: true, returnAddress: true, returnShipment: true },
    })
    if (!saleItem) {
      throw new NotFoundException('Sale item not found')
    }

    if (!saleItem.user || !saleItem.returnAddress) {
      throw new BadRequestException('Sale item must have user and return address information')
    }

    if (!saleItem.channelId) {
      throw new BadRequestException('Sale must have channel information')
    }

    if (override && saleItem.returnShipment?.parcelId) {
      await cancelParcel(saleItem.returnShipment.parcelId).catch(console.error)
    }

    await this.shipmentService.create({
      type: ShipmentType.SALE_ITEM_RETURN,
      orderNumber: saleItem.stockId,
      customerEmail: saleItem.user.email,
      customerAddress: saleItem.returnAddress,
      isReturn: false,
      channelId: saleItem.channelId,
      partnerId: saleItem.sale.partnerId,
      saleItemId: saleItem.id,
    })
  }

  async reuseReturnShipment(id: string, shipmentId: string) {
    const saleItem = await SaleItemEntity.findOne({
      where: { id },
    })
    if (!saleItem) {
      throw new NotFoundException('Sale item not found')
    }

    if (!shipmentId) {
      throw new BadRequestException('Shipment ID is required')
    }

    saleItem.returnShipmentId = shipmentId
    await saleItem.save()
  }
}
