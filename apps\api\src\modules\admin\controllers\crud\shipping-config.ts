import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { BadRequestException, Body, Controller, Post, UseGuards } from '@nestjs/common'
import { isNil } from 'lodash'

import { ShippingConfigEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudShippingConfigService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ShippingConfigEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      warehouse: {},
      shippingMethod: {},
      channel: {},
    },
  },
})
@Controller('admin/shipping-configs')
export class CrudShippingConfigController implements CrudController<ShippingConfigEntity> {
  constructor(public service: CrudShippingConfigService) {}

  @Post('save-for-channel')
  saveForChannel(
    @Body('channelId') channelId: string | null,
    @Body('configs') configs: Partial<ShippingConfigEntity>[]
  ) {
    if (!channelId && channelId !== null) {
      throw new BadRequestException('Channel ID is required')
    }
    if (!configs || !Array.isArray(configs)) {
      throw new BadRequestException('Configs are required')
    }
    if (
      configs.some(
        (config) =>
          !config.warehouseId ||
          (!config.shippingMethodId && !config.shippingMethod?.id) ||
          !config.customerCountryId ||
          !config.customerCountryId ||
          isNil(config.sortOrder) ||
          isNil(config.isReturn)
      )
    ) {
      throw new BadRequestException('Invalid config format')
    }
    return this.service.saveForChannel(channelId, configs)
  }
}
