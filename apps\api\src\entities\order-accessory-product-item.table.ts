import { Field, ID, Int, ObjectType } from '@nestjs/graphql'
import { IsInt, Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  AccessoryProductEntity,
  ChannelEntity,
  type IAccessoryProduct,
  type IChannel,
  type ILocaleCurrency,
  type IOrder,
  LocaleCurrencyEntity,
  OrderEntity,
} from '~/entities'

@ObjectType('OrderAccessoryProductItem')
@Entity('order_accessory_product_item')
export class OrderAccessoryProductItemEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field(() => Int)
  @Column({ type: 'int2' })
  @IsInt()
  @Min(0)
  quantity: number

  @Field()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  unitPrice: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((order: OrderEntity) => order.channel)
  channelId: string

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: OrderAccessoryProductItemEntity) => item.currency)
  currencyId: string

  // TODO: this should not be nullable
  @Index()
  @Column({ type: 'uuid', nullable: true })
  cartItemId: string

  @ManyToOne(() => OrderEntity, (order) => order.orderAccessoryItems, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  order: Relation<OrderEntity>

  @Column()
  @RelationId((item: OrderAccessoryProductItemEntity) => item.order)
  orderId: string

  @ManyToOne(() => AccessoryProductEntity, { nullable: false })
  accessoryProduct: Relation<AccessoryProductEntity>

  @Column()
  @RelationId((item: OrderAccessoryProductItemEntity) => item.accessoryProduct)
  accessoryProductId: string

  @Column({ nullable: true })
  note?: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IOrderAccessoryProductItem = Omit<
  OrderAccessoryProductItemEntity,
  keyof BaseEntity
> & {
  channel: IChannel
  currency: ILocaleCurrency
  order: IOrder
  accessoryProduct: IAccessoryProduct
}
