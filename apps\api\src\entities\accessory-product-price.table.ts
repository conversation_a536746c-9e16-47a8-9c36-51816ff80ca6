import { Field, ID, ObjectType } from '@nestjs/graphql'
import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  AccessoryProductEntity,
  ChannelEntity,
  type IAccessoryProduct,
  type IChannel,
  type ILocaleCurrency,
  LocaleCurrencyEntity,
} from '~/entities'

@ObjectType('AccessoryProductPrice')
@Entity('accessory_product_price')
export class AccessoryProductPriceEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  price: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((problem: AccessoryProductPriceEntity) => problem.channel)
  channelId: string

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: AccessoryProductPriceEntity) => item.currency)
  currencyId: string

  @ManyToOne(() => AccessoryProductEntity, { nullable: false, onDelete: 'CASCADE' })
  accessoryProduct: Relation<AccessoryProductEntity>

  @Column()
  @RelationId((item: AccessoryProductPriceEntity) => item.accessoryProduct)
  accessoryProductId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IAccessoryProductPrice = Omit<AccessoryProductPriceEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
  accessoryProduct: IAccessoryProduct
}
