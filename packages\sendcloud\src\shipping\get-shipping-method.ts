import { sendcloudClientV2 } from '../client'
import { CountryCode } from '../constants'
import { IShippingMethod } from '../interfaces'

export type IGetShippingMethodParams = {
  /** Postal code of the sender (max 12 characters) */
  from_postal_code?: string
  /** The ID of the sender address */
  sender_address?: string
  /** The ID of the service point */
  service_point_id?: number
  /** Country ISO 2 code for the recipient country */
  to_country?: CountryCode
  /** Postal code of the recipient (max 12 characters) */
  to_postal_code?: string
  /** If true, endpoint will return the shipping method only if it is a return shipping method */
  is_return?: boolean
}

/**
 * Get available shipping method based on provided parameters
 * @param params - Shipping method query parameters
 * @returns Promise with shipping method details
 */
export const getShippingMethod = async (
  id: number | string,
  params: IGetShippingMethodParams = {}
): Promise<IShippingMethod> => {
  const { data } = await sendcloudClientV2.get(`/shipping-methods/${id}`, {
    params,
  })

  return data.shipping_method
}
