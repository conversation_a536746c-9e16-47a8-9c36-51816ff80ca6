import { BadRequestException, Injectable } from '@nestjs/common'
import { <PERSON>ron } from '@nestjs/schedule'
import * as Sentry from '@sentry/nestjs'
import { getParcel, ParcelStatus } from '@valyuu/sendcloud'
import * as currency from 'currency.js'
import { startOfDay, subDays, subHours } from 'date-fns'
import { isEqual, pick } from 'lodash'
import ms from 'ms'
import { I18nService } from 'nestjs-i18n'
import * as pMap from 'p-map'
import Stripe from 'stripe'
import { ILike, In, IsNull, LessThan, MoreThan, Not } from 'typeorm'

import { envConfig } from '~/configs'
import {
  AddressType,
  EmailType,
  LOCALE_ENABLED_LANGUAGES,
  OrderSkuItemStatus,
  OrderStatus,
  PreOrderStatus,
  ShipmentType,
  StripeAccountApiType,
} from '~/constants'
import {
  createPaymentIntentInput,
  CreatePreOrderInput,
  CreatePreOrderOutput,
  GetOrderCartItemPricesInput,
  GetOrderResultOutput,
} from '~/dtos'
import {
  AccessoryProductEntity,
  AddressEntity,
  CategoryWarrantyRuleWarrantyItemEntity,
  ChannelEntity,
  EmailHistoryEntity,
  OrderAccessoryProductItemEntity,
  OrderEntity,
  OrderSkuItemEntity,
  PreOrderEntity,
  ProductSkuEntity,
  SaleEntity,
  UserEntity,
} from '~/entities'
import type { IEmailBuyerNewOrderData, IEmailBuyerNewOrderInternalData } from '~/interfaces'
import { ChannelService, ShipmentService } from '~/services'
import { getEmailTemplatePrefix, sendmail } from '~/utils'
import {
  cloudinaryGetResizedImageUrl,
  formatDate,
  formatMoney,
  generateOrderNumber,
  houseNumberWithAddition,
  stripeCancelPaymentIntent,
  stripeCreateCustomer,
  stripeCreatePaymentIntent,
  stripeDeleteCustomer,
  stripeRetrievePaymentIntent,
  stripeUpdatePaymentIntent,
} from '~/utils'

type ItemNewPrice = Record<string, { sold: boolean; newPrice: number }>

const apiType = envConfig.isProd ? StripeAccountApiType.LIVE : StripeAccountApiType.TEST

@Injectable()
export class OrderService {
  constructor(
    private readonly channelService: ChannelService,
    private readonly shipmentService: ShipmentService,
    private readonly i18n: I18nService
  ) {}
  // add a cron which runs every hour, update PreOrderEntity to cancelled if it's older than 22 hours
  @Cron('20 * * * *') // every hour at 20 minutes
  async cronCancelPreOrders() {
    const cancelledPreOrders = await PreOrderEntity.find({
      where: { status: PreOrderStatus.PENDING_PAYMENT, createdAt: LessThan(subHours(new Date(), 20)) },
    })
    await pMap(
      cancelledPreOrders,
      async (canceledPreOrder) => {
        canceledPreOrder.status = PreOrderStatus.CANCELLED
        canceledPreOrder.reason = 'Payment expired'
        await PreOrderEntity.save(canceledPreOrder)
      },
      { concurrency: 5 }
    )
  }
  @Cron('0 12 * * *') // every day at 12:00
  async cronCompleteOrders() {
    return this.completeOrders()
  }

  async getOrderCartItemPrices({ channelId, skuItems, accessoryItems }: GetOrderCartItemPricesInput) {
    const skus = await ProductSkuEntity.find({
      where: { id: In(skuItems.map(({ id }) => id)), prices: { channelId } },
      relations: {
        prices: true,
      },
      select: {
        id: true,
        modelId: true,
        sold: true,
        prices: {
          id: true,
          price: true,
          channelId: true,
        },
      },
    })
    if (skus.some((sku) => !sku.prices.length)) {
      Sentry.captureMessage('Price not found for sku', {
        tags: {
          type: 'order:getOrderCartItemPrices',
        },
      })
    }
    const skuModels = skus.reduce(
      (acc, sku) => Object.assign(acc, { [sku.id]: sku.modelId }),
      {} as Record<string, string>
    )
    const skuPrices = skus.reduce(
      (acc, sku) => Object.assign(acc, { [sku.id]: { sold: sku.sold, newPrice: sku?.prices?.[0]?.price } }),
      {} as ItemNewPrice
    )

    // TODO: what if the user changes channel ID?
    const warrantyItemPrices = (
      await CategoryWarrantyRuleWarrantyItemEntity.find({
        where: { id: In(skuItems.map(({ warrantyItemId }) => warrantyItemId)) },
      })
    ).reduce(
      (acc, warrantyItem) => Object.assign(acc, { [warrantyItem.id]: { newPrice: warrantyItem.price } }),
      {} as ItemNewPrice
    )

    const accessoryItemPrices = (
      await AccessoryProductEntity.find({
        where: { id: In(accessoryItems.map(({ id }) => id)), prices: { channelId } },
        relations: {
          prices: true,
        },
        select: {
          prices: {
            id: true,
            price: true,
          },
        },
      })
    ).reduce(
      (acc, accessory) =>
        Object.assign(acc, { [accessory.id]: { sold: false, newPrice: accessory?.prices?.[0].price } }),
      {} as ItemNewPrice
    )

    const skuItemsOutput = skuItems.map((skuItem) => {
      const newPriceItem = skuPrices[skuItem.id] ?? { sold: true, newPrice: skuItem.price }
      const newWarrantyPriceItem = warrantyItemPrices[skuItem.warrantyItemId] ?? {
        sold: true,
        newPrice: skuItem.warrantyPrice,
      }
      return {
        id: skuItem.id,
        modelId: skuModels[skuItem.id],
        cartItemId: skuItem.cartItemId,
        hasPriceChange: newPriceItem.newPrice !== skuItem.price,
        newPrice: newPriceItem.newPrice,
        oldPrice: skuItem.price,
        sold: newPriceItem.sold,
        warrantyItemId: skuItem.warrantyItemId,
        warrantyHasPriceChange: newWarrantyPriceItem.sold || newWarrantyPriceItem.newPrice !== skuItem.warrantyPrice,
        warrantyNewPrice: newWarrantyPriceItem.newPrice,
        warrantyOldPrice: skuItem.warrantyPrice,
      }
    })

    const accessoryItemsOutput = accessoryItems.map((accessoryItem) => {
      const newPriceItem = accessoryItemPrices[accessoryItem.id] ?? {
        sold: true,
        newPrice: accessoryItem.price,
      }
      return {
        id: accessoryItem.id,
        cartItemId: accessoryItem.cartItemId,
        hasPriceChange: newPriceItem.sold || newPriceItem.newPrice !== accessoryItem.price,
        newPrice: newPriceItem.newPrice,
        oldPrice: accessoryItem.price,
        sold: newPriceItem.sold,
        quantity: accessoryItem.quantity,
      }
    })

    return {
      skuItems: skuItemsOutput,
      accessoryItems: accessoryItemsOutput,
      hasPriceChange:
        skuItemsOutput.some((item) => item.hasPriceChange || item.warrantyHasPriceChange) ||
        accessoryItemsOutput.some((item) => item.hasPriceChange),
      hasSoldItems: skuItemsOutput.some((item) => item.sold) || accessoryItemsOutput.some((item) => item.sold),
      totalAmount: currency(
        skuItemsOutput.reduce((acc, item) => currency(acc).add(item.newPrice).add(item.warrantyNewPrice).value, 0)
      ).add(
        accessoryItemsOutput.reduce(
          (acc, item) => currency(acc).add(currency(item.newPrice).multiply(item.quantity).value).value,
          0
        )
      ).value,
    }
  }

  async createPreOrder(input: CreatePreOrderInput): Promise<CreatePreOrderOutput> {
    const stripePaymentIntent = input.clientSecret.replace(/_secret.*?$/, '')

    const email = input.email.trim()
    const shippingAddress = {
      firstName: input.shippingAddress.firstName?.trim(),
      lastName: input.shippingAddress.lastName?.trim(),
      phoneAreaCode: input.shippingAddress.phoneAreaCode,
      phoneNumber: input.shippingAddress.phoneNumber?.trim(),
      countryId: input.shippingAddress.country,
      postalCode: input.shippingAddress.postalCode?.trim(),
      houseNumber: input.shippingAddress.houseNumber?.trim(),
      addition: input.shippingAddress.addition?.trim() ?? null,
      street: input.shippingAddress.street?.trim(),
      city: input.shippingAddress.city,
      type: AddressType.SHIPPING,
    } as const
    const billingAddress = {
      firstName: input.billingAddress.firstName?.trim(),
      lastName: input.billingAddress.lastName?.trim(),
      phoneAreaCode: input.billingAddress.phoneAreaCode,
      phoneNumber: input.billingAddress.phoneNumber?.trim(),
      countryId: input.billingAddress.country,
      postalCode: input.billingAddress.postalCode?.trim(),
      houseNumber: input.billingAddress.houseNumber?.trim(),
      addition: input.billingAddress.addition?.trim() ?? null,
      street: input.billingAddress.street?.trim(),
      city: input.billingAddress.city,
      type: AddressType.BILLING,
    } as const

    const existingPreOrder = await PreOrderEntity.findOne({
      where: { stripePaymentIntent },
      relations: {
        billingAddress: true,
        shippingAddress: true,
      },
    })
    const prices = await this.getOrderCartItemPrices({ ...pick(input, ['channelId', 'skuItems', 'accessoryItems']) })

    const { success, paymentIntent } = await stripeRetrievePaymentIntent(
      { paymentIntentId: stripePaymentIntent },
      apiType
    )
    if (success && paymentIntent.status !== 'requires_payment_method') {
      return {
        success: false,
        renewPaymentIntent: true,
        ...prices,
      }
    }

    if (existingPreOrder) {
      if (!isEqual(pick(existingPreOrder.shippingAddress, Object.keys(shippingAddress)), shippingAddress)) {
        Object.assign(existingPreOrder.shippingAddress, shippingAddress)
        await AddressEntity.save(existingPreOrder.shippingAddress)
      }
      if (!isEqual(pick(existingPreOrder.billingAddress, Object.keys(billingAddress)), billingAddress)) {
        Object.assign(existingPreOrder.billingAddress, billingAddress)
        await AddressEntity.save(existingPreOrder.billingAddress)
      }

      await stripeUpdatePaymentIntent(
        {
          paymentIntentId: stripePaymentIntent,
          amount: prices.totalAmount,
        },
        apiType
      )
      return {
        success: true,
        renewPaymentIntent: false,
        ...prices,
      }
    } else {
      const channel = await ChannelEntity.findOne({
        where: { id: input.channelId, publishedAt: Not(IsNull()) },
        cache: ms('1d'),
      })
      if (!channel) {
        Sentry.captureException(new Error('Channel not found'), {
          tags: {
            type: 'order:createPreOrder',
          },
          extra: {
            channelId: input.channelId,
          },
        })
        throw new Error('Channel not found')
      }
      const { currencyId } = channel

      let userRecord: UserEntity
      let userCreated = false
      let stripeCustomer: string
      let stripeCustomerCreated: boolean
      let shippingAddressRecord: AddressEntity
      let billingAddressRecord: AddressEntity
      let preOrderRecord: PreOrderEntity

      if (prices.hasSoldItems || prices.hasPriceChange) {
        await stripeUpdatePaymentIntent(
          {
            paymentIntentId: stripePaymentIntent,
            amount: prices.totalAmount,
          },
          apiType
        )
        return {
          success: false,
          renewPaymentIntent: false,
          ...prices,
        }
      }

      try {
        await PreOrderEntity.getRepository().manager.transaction(async (manager) => {
          userRecord = await manager.findOne(UserEntity, { where: { email: ILike(input.email.trim()) } })
          if (userRecord) {
            userRecord.languageId = input.language
            await manager.save(userRecord)
          } else {
            userRecord = await manager.save(
              manager.create(UserEntity, { email, from: input.from, languageId: input.language })
            )
            userCreated = true
          }
          if (userRecord.stripeCustomer) {
            stripeCustomer = userRecord.stripeCustomer
          } else {
            try {
              const { customer } = await stripeCreateCustomer(
                {
                  email,
                  name: (billingAddress.firstName + ' ' + billingAddress.lastName).trim(),
                  phone: billingAddress.phoneAreaCode + ' ' + billingAddress.phoneNumber,
                },
                apiType
              )

              if (customer) {
                stripeCustomer = customer.id
                stripeCustomerCreated = true
                userRecord.stripeCustomer = stripeCustomer
                await manager.save(userRecord)
              }
            } catch {
              Sentry.captureException(new Error('Error creating stripe customer'), {
                tags: {
                  type: 'order:createPreOrder',
                },
                extra: {
                  email,
                  name: (billingAddress.firstName + ' ' + billingAddress.lastName).trim(),
                  phone: billingAddress.phoneAreaCode + ' ' + billingAddress.phoneNumber,
                },
                level: 'warning',
              })
            }
          }
          shippingAddressRecord = await manager.findOne(AddressEntity, {
            where: { ...shippingAddress, userId: userRecord.id },
          })
          if (!shippingAddressRecord) {
            shippingAddressRecord = await manager.save(
              manager.create(AddressEntity, { ...shippingAddress, userId: userRecord.id })
            )
          }
          billingAddressRecord = await manager.findOne(AddressEntity, {
            where: { ...billingAddress, userId: userRecord.id },
          })
          if (!billingAddressRecord) {
            billingAddressRecord = await manager.save(
              manager.create(AddressEntity, { ...billingAddress, userId: userRecord.id })
            )
          }

          preOrderRecord = await manager.save(
            manager.create(PreOrderEntity, {
              channelId: channel.id,
              currencyId,
              stripePaymentIntent,
              totalAmount: prices.totalAmount,
              cart: { skuItems: input.skuItems, accessoryItems: input.accessoryItems },
              status: PreOrderStatus.PENDING_PAYMENT,
              email,
              userId: userRecord.id,
              languageId: input.language,
              anonymousId: input.anonymousId,
              from: input.from,
              shippingAddressId: shippingAddressRecord.id,
              toPickupPoint: input.toPickupPoint,
              billingAddressId: billingAddressRecord.id,
              productSkus: input.skuItems.map(({ id }) => ({ id })),
            })
          )

          await stripeUpdatePaymentIntent(
            {
              paymentIntentId: stripePaymentIntent,
              amount: prices.totalAmount,
              metadata: { pre_order_id: preOrderRecord.id },
            },
            apiType
          )
        })
      } catch (error) {
        if (stripeCustomerCreated) {
          await stripeDeleteCustomer({ customerId: stripeCustomer }, apiType)
        }

        Sentry.captureException(error, {
          tags: {
            type: 'order:createPreOrder',
          },
          extra: {
            email,
            cartSkus: input.skuItems,
            cartAccessories: input.accessoryItems,
            address: {
              shipping: shippingAddress,
              billing: billingAddress,
            },
            from: input.from,
            stripePaymentIntent,
            total: prices.totalAmount,
            soldSkus: prices.hasSoldItems,
            availableSkus: prices.hasPriceChange,
          },
        })

        return
      }
      return {
        success: true,
        renewPaymentIntent: false,
        ...prices,
      }
    }
  }

  async getOrderResult(paymentIntent: string): Promise<GetOrderResultOutput> {
    const preOrder = await PreOrderEntity.findOneOrFail({
      where: { stripePaymentIntent: paymentIntent },
      relations: {
        order: true,
        productSkus: {
          model: {
            category: true,
          },
        },
      },
      select: {
        id: true,
        email: true,
        createdAt: true,
        totalAmount: true,
        order: {
          id: true,
          orderNumber: true,
        },
        cart: {},
        productSkus: {
          id: true,
          model: {
            id: true,
            category: {
              id: true,
              savedCo2: true,
              savedEwaste: true,
            },
          },
        },
      },
    })

    let savedCo2 = 0
    let savedEwaste = 0

    preOrder.productSkus.forEach((sku) => {
      savedCo2 += sku.model.category.savedCo2
      savedEwaste += sku.model.category.savedEwaste
    })

    return {
      orderNumber: preOrder.order?.orderNumber,
      skuItems: preOrder.cart.skuItems,
      accessoryItems: preOrder.cart.accessoryItems,
      savedCo2,
      savedEwaste,
      total: preOrder.totalAmount,
      createdAt: preOrder.createdAt,
      email: preOrder.email,
    }
  }

  async cancelPreOrder({
    preOrderId,
    checkStripeStatus = false,
    reason,
    paymentMethod,
  }: {
    preOrderId: string
    checkStripeStatus: boolean
    reason: string
    paymentMethod?: Stripe.PaymentMethod.Type
  }) {
    const preOrder = await PreOrderEntity.findOne({ where: { id: preOrderId } })
    if (!preOrder) {
      throw new BadRequestException(`Pre-order ${preOrderId} not found`)
    }
    if (preOrder.status === PreOrderStatus.CANCELLED) {
      // order has already been cancelled
      return
    }
    preOrder.status = PreOrderStatus.CANCELLED
    preOrder.reason = reason
    if (paymentMethod) {
      preOrder.paymentMethod = paymentMethod
    }
    await PreOrderEntity.save(preOrder)
    if (preOrder.stripePaymentIntent) {
      let shouldCancel = true
      try {
        if (checkStripeStatus) {
          const { success, paymentIntent } = await stripeRetrievePaymentIntent(
            { paymentIntentId: preOrder.stripePaymentIntent },
            apiType
          )
          if (success && paymentIntent.status === 'canceled') {
            shouldCancel = false
          }
        }
        if (shouldCancel) {
          await stripeCancelPaymentIntent({ paymentIntentId: preOrder.stripePaymentIntent }, apiType)
        }
      } catch {
        Sentry.captureException(new Error('Error canceling stripe payment intent'), {
          tags: {
            type: 'order:cancelPreOrder',
          },
          extra: {
            paymentIntentId: preOrder.stripePaymentIntent,
          },
          level: 'warning',
        })
      }
    }
  }

  async createOrder({
    preOrderId,
    amount,
    stripeCharge,
    paymentMethod,
  }: {
    preOrderId: string
    amount: number
    stripeCharge: string
    paymentMethod: string
  }) {
    const preOrder = await PreOrderEntity.findOne({
      where: { id: preOrderId },
      relations: { productSkus: true, shippingAddress: true },
    })
    if (!preOrder) {
      const message = `Pre-Order ${preOrderId} with Stripe charge ID ${stripeCharge} doesn't exist`
      Sentry.captureException(new Error(message), {
        tags: {
          type: 'order:createOrder',
        },
        extra: {
          id: preOrderId,
          amount,
          stripeCharge,
        },
        contexts: {
          error: {
            message,
          },
        },
      })
      throw new Error(message)
    }

    const lang = preOrder.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]

    setImmediate(async () => {
      try {
        const pendingPreOrders = await PreOrderEntity.find({
          where: {
            id: Not(preOrderId),
            productSkus: { id: In(preOrder.productSkus.map(({ id }) => id)) },
            status: PreOrderStatus.PENDING_PAYMENT,
          },
        })
        await pMap(pendingPreOrders, (pendingPreOrder) => {
          return this.cancelPreOrder({
            preOrderId: pendingPreOrder.id,
            checkStripeStatus: false,
            reason: 'Canceled by pre-order ' + preOrder.id,
          })
        })
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'order:createOrder',
          },
          extra: {
            preOrderId,
            amount,
            stripeCharge,
          },
        })
      }
    })

    const { totalAmount, hasSoldItems, hasPriceChange, skuItems, accessoryItems } = await this.getOrderCartItemPrices({
      channelId: preOrder.channelId,
      skuItems: preOrder.cart.skuItems,
      accessoryItems: preOrder.cart.accessoryItems,
    })

    if (currency(totalAmount).multiply(100).value !== currency(amount).value) {
      const message = `Payment amount ${amount / 100} is not correct`
      Sentry.captureException(new Error(message), {
        tags: {
          type: 'order:createOrder',
        },
        extra: {
          preOrderId,
          amount,
          stripeCharge,
        },
        level: 'warning',
      })
    }

    let orderRecord: OrderEntity
    let orderNumber: string
    const createdAt = new Date()

    // If there's sold item then return updated items
    if (hasSoldItems) {
      // TODO: handle sold items
    } else if (hasPriceChange) {
      // TODO: handle price change
    }
    const warrantyItemIds = preOrder.cart.skuItems.map(({ warrantyItemId }) => warrantyItemId)

    const warrantyMap: Record<string, { warrantyId: string; name: string }> = (
      await CategoryWarrantyRuleWarrantyItemEntity.find({
        where: { id: In(warrantyItemIds) },
        relations: { warranty: true },
        select: {
          id: true,
          warranty: { id: true, [`name__${lang}`]: true },
        },
      })
    ).reduce(
      (acc, { warranty, id }) =>
        Object.assign(acc, {
          [id]: { warrantyId: warranty.id, name: warranty[`name__${lang}`] },
        }),
      {} as Record<string, { warrantyId: string; name: string }>
    )

    if (Object.keys(warrantyMap).length !== warrantyItemIds.length) {
      Sentry.captureException(new Error('Warranty items not found'), {
        tags: {
          type: 'order:createOrder',
        },
        extra: {
          warrantyItemIds,
          warrantyMap,
        },
        level: 'warning',
      })
    }

    try {
      await OrderEntity.getRepository().manager.transaction(async (manager) => {
        const hasPreviousOrder = await manager.findOne(OrderEntity, {
          where: { userId: preOrder.userId, createdAt: LessThan(subDays(createdAt, 1)) },
        })
        const hasPreviousSale = await manager.findOne(SaleEntity, {
          where: { userId: preOrder.userId, createdAt: LessThan(subDays(createdAt, 1)) },
        })

        const isReturningCustomer = Boolean(hasPreviousOrder || hasPreviousSale)

        // TODO: fix this workaround to get a real unique saleId
        for (let i = 0; i < 1000 && !orderNumber; ++i) {
          orderNumber = generateOrderNumber()
          const existingOrder = await manager.findOne(OrderEntity, { where: { orderNumber } })
          if (existingOrder) {
            orderNumber = undefined
          }
        }

        orderRecord = await manager.save(
          manager.create(OrderEntity, {
            orderNumber,
            channelId: preOrder.channelId,
            currencyId: preOrder.currencyId,
            total: totalAmount,
            status: OrderStatus.PENDING_SHIPMENT,
            userId: preOrder.userId,
            stripeCharge,
            isReturningCustomer,
            languageId: preOrder.languageId,
            from: preOrder.from,
            shippingAddressId: preOrder.shippingAddressId,
            toPickupPoint: preOrder.toPickupPoint,
            billingAddressId: preOrder.billingAddressId,
            preOrder,
            createdAt,
          })
        )

        await pMap(skuItems, async (skuItem) => {
          await manager.save(
            manager.create(OrderSkuItemEntity, {
              channelId: preOrder.channelId,
              currencyId: preOrder.currencyId,
              orderId: orderRecord.id,
              cartItemId: skuItem.cartItemId,
              productSkuId: skuItem.id,
              productModelId: skuItem.modelId,
              unitPrice: skuItem.newPrice,
              warrantyId: warrantyMap[skuItem.warrantyItemId].warrantyId,
              warrantyPrice: skuItem.warrantyNewPrice,
              status: OrderSkuItemStatus.NORMAL,
            })
          )
          const skuRecord = await manager.findOne(ProductSkuEntity, { where: { id: skuItem.id } })
          if (skuRecord) {
            skuRecord.sold = true
            await manager.save(skuRecord)
          }
        })

        await pMap(accessoryItems, async (accessoryItem) => {
          await manager.save(
            manager.create(OrderAccessoryProductItemEntity, {
              channelId: preOrder.channelId,
              currencyId: preOrder.currencyId,
              orderId: orderRecord.id,
              cartItemId: accessoryItem.cartItemId,
              accessoryProductId: accessoryItem.id,
              quantity: accessoryItem.quantity,
              unitPrice: accessoryItem.newPrice,
            })
          )
        })
      })
    } catch (error) {
      const message = 'Error creating order'
      Sentry.captureException(error, {
        tags: {
          type: 'order:createOrder',
        },
        extra: {
          preOrderId,
          amount,
          stripeCharge,
          skuItems,
          accessoryItems,
          totalAmount,
          orderNumber,
        },
        contexts: {
          error: {
            message,
          },
        },
        level: 'fatal',
      })

      return
    }

    setImmediate(async () => {
      try {
        // create sendcloud parcel

        const shipment = await this.shipmentService.create({
          type: ShipmentType.ORDER,
          orderNumber,
          customerEmail: preOrder.email,
          channelId: preOrder.channelId,
          isReturn: false,
          customerAddress: preOrder.shippingAddress,
          orderId: orderRecord.id,
        })

        if (shipment) {
          orderRecord.trackingNumber = shipment.trackingNumber
          orderRecord.parcelId = shipment.parcelId
          orderRecord.shipmentId = shipment.id
          await OrderEntity.save(orderRecord)
        } else {
          Sentry.captureMessage('Error creating parcel', {
            tags: {
              type: 'order:createOrder',
            },
            extra: {
              order: orderRecord,
              shipment,
            },
          })
          return
        }

        // Send email
        try {
          await this.sendBuyerOrderEmail(orderRecord.id, true)
        } catch (error) {
          Sentry.captureMessage('Error sending mail', {
            tags: {
              type: 'order:createOrder',
            },
            extra: {
              order: orderRecord,
            },
          })
        }
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'order:createOrder',
          },
          extra: {
            order: orderRecord,
            message: 'Error sending mail',
          },
        })
      }
    })

    setImmediate(async () => {
      try {
        await stripeUpdatePaymentIntent(
          { paymentIntentId: preOrder.stripePaymentIntent, metadata: { order_id: orderRecord.id } },
          apiType
        )
        preOrder.status = PreOrderStatus.COMPLETED
        preOrder.reason = 'Payment completed'
        preOrder.paymentMethod = paymentMethod
        await PreOrderEntity.save(preOrder)
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'order:createOrder',
          },
          extra: {
            order: orderRecord,
            message: 'Error updating payment intent',
          },
        })
      }
    })
  }

  async createPaymentIntent({ language, channelId, skuItems, accessoryItems }: createPaymentIntentInput) {
    const { id: currencyId } = (await this.channelService.findCurrencyByChannel(channelId)) ?? { id: null }
    if (!currencyId) {
      Sentry.captureMessage('Currency not found', {
        tags: {
          type: 'order:createPaymentIntent',
        },
        extra: {
          channelId,
        },
      })
    }
    const { totalAmount } = await this.getOrderCartItemPrices({ channelId, skuItems, accessoryItems })
    const { success, paymentIntent } = await stripeCreatePaymentIntent(
      { amount: totalAmount, language, currencyId },
      apiType
    )
    if (!success) {
      return {
        success: false,
      }
    } else {
      return {
        success: true,
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: totalAmount,
      }
    }
  }

  async completeOrders() {
    // filter orders that are updated within 7 days
    const orders = await OrderEntity.find({
      where: {
        status: OrderStatus.SHIPPING,
        updatedAt: MoreThan(subDays(startOfDay(new Date()), 14)),
        parcelId: Not(IsNull()),
      },
      relations: {
        user: true,
        shippingAddress: true,
      },
    })
    for (const order of orders) {
      const { user } = order
      if (!envConfig.isProd && !user.email.endsWith('@valyuu.com') && !user.email.endsWith('@guapa.nl')) {
        continue
      }
      try {
        const parcel = await getParcel(order.parcelId)
        if (parcel.status?.id === ParcelStatus.DELIVERED) {
          order.status = OrderStatus.COMPLETED
          await OrderEntity.save(order)
          // TODO: send buyer survey email in event listener
          // const lang = user.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]
          // const emailData = {
          //   firstName: shippingAddress.firstName,
          //   orderNumber: order.orderNumber,
          //   year,
          //   imagePrefix,
          //   trustpilot,
          // }
          // try {
          //   await sendmail({
          //     type: EmailType.BUYER_PACKAGE_RECEIVED,
          //     lang,
          //     to: user.email,
          //     subject: this.i18n.t('emails.buyerPackageReceived', { lang }),
          //     data: emailData,
          //   })
          // } catch (error) {
          //   // TODO: sentry
          // }
        }
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'order:completeOrders',
          },
          extra: {
            order,
            message: 'Error completing order',
          },
        })
      }
    }
  }

  async sendBuyerOrderEmail(orderId: string, sendLogisticsEmail = false) {
    const order = await OrderEntity.findOne({
      where: { id: orderId },
      relations: {
        user: true,
        orderSkuItems: {
          warranty: true,
        },
        orderAccessoryItems: true,
        shippingAddress: true,
        shipment: true,
      },
    })
    const lang = order.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]

    const skuIds = order.orderSkuItems.map(({ productSkuId }) => productSkuId)

    const skus: Record<string, ProductSkuEntity> = (
      await ProductSkuEntity.find({
        where: { id: In(skuIds), prices: { channelId: order.channelId } },
        relations: {
          prices: true,
          heroImage: true,
          model: true,
          saleItem: { sale: true },
          originalAccessories: true,
          variant: {
            attributeCombination: {
              choices: {
                option: true,
              },
            },
          },
        },
        select: {
          id: true,
          stockId: true,
          grading: true,
          serialNumber: true,
          heroImage: { id: true, publicId: true },
          model: { id: true, [`name__${lang}`]: true },
          saleItem: {
            id: true,
            sale: { id: true, saleNumber: true },
          },
          originalAccessories: {
            id: true,
            [`name__${lang}`]: true,
            displayInResults: true,
          },
          extraAttributes: {},
          [`displayColor__${lang}`]: true,
          variant: {
            id: true,
            attributeCombination: {
              id: true,
              choices: {
                id: true,
                option: {
                  id: true,
                  [`name__${lang}`]: true,
                },
              },
            },
          },
        },
      })
    ).reduce((acc, sku) => Object.assign(acc, { [sku.id]: sku }), {})

    const accessoryIds = order.orderAccessoryItems.map(({ accessoryProductId }) => accessoryProductId)
    const accessories: Record<string, AccessoryProductEntity> = (
      await AccessoryProductEntity.find({
        where: { id: In(accessoryIds) },
        relations: { heroImage: true },
      })
    ).reduce((acc, accessory) => Object.assign(acc, { [accessory.id]: accessory }), {})

    const locale = `${order.languageId}-${order.shippingAddress.countryId}`

    const emailData: IEmailBuyerNewOrderData = {
      skuItems: order.orderSkuItems.map((skuItem) => {
        const originalPrice = skus[skuItem.productSkuId].prices[0]?.originalPrice
        return {
          id: skuItem.productSkuId,
          stockId: skus[skuItem.productSkuId].stockId,
          price: formatMoney(skuItem.unitPrice, order.currencyId, locale),
          originalPrice:
            originalPrice && originalPrice === skuItem.unitPrice
              ? formatMoney(originalPrice, order.currencyId, locale)
              : null,
          thumbnail: cloudinaryGetResizedImageUrl({
            publicId: skus[skuItem.productSkuId].heroImage?.publicId,
            width: 100,
            height: 100,
          }),
          warrantyName: skuItem.warranty[`name__${lang}`],
          warrantyPrice: skuItem.warrantyPrice
            ? formatMoney(skuItem.warrantyPrice, order.currencyId, locale)
            : this.i18n.t('emails.free', { lang }),
          name: skus[skuItem.productSkuId].model?.[`name__${lang}`],
          attributes: [
            skus[skuItem.productSkuId][`displayColor__${lang}`],
            ...Object.values(skus[skuItem.productSkuId].extraAttributes).map((value) => value[`name__${lang}`]),
            ...skus[skuItem.productSkuId].variant.attributeCombination.choices.map(
              ({ option }) => option[`name__${lang}`]
            ),
          ].join(' | '),
          grading: this.i18n.t(`emails.grading.${skus[skuItem.productSkuId].grading}`, { lang }),
          serialNumber: skus[skuItem.productSkuId].serialNumber ?? '',
          saleNumber: skus[skuItem.productSkuId]?.saleItem?.sale?.saleNumber ?? '',
          accessories: skus[skuItem.productSkuId].originalAccessories
            .filter(({ displayInResults }) => displayInResults)
            .map((accessory) => accessory[`name__${lang}`]),
        }
      }),
      accessoryItems: order.orderAccessoryItems.map((accessoryItem) => ({
        id: accessoryItem.accessoryProductId,
        price: formatMoney(
          currency(accessoryItem.unitPrice).multiply(accessoryItem.quantity).value,
          order.currencyId,
          locale
        ),
        quantity: accessoryItem.quantity,
        name: accessories[accessoryItem.accessoryProductId][`name__${lang}`],
        description: accessories[accessoryItem.accessoryProductId][`desc__${lang}`],
        thumbnail: cloudinaryGetResizedImageUrl({
          publicId: accessories[accessoryItem.accessoryProductId].heroImage?.publicId,
          width: 100,
          height: 100,
        }),
      })),
      orderNumber: order.orderNumber,
      createdAt: formatDate(order.createdAt, locale),
      total: formatMoney(order.total, order.currencyId, locale),
      toPickupPoint: order.toPickupPoint,
      shippingFirstName: order.shippingAddress.firstName,
      shippingLastName: order.shippingAddress.lastName,
      shippingAddress1:
        order.shippingAddress.street +
        ' ' +
        houseNumberWithAddition(order.shippingAddress.houseNumber, order.shippingAddress.addition),
      shippingAddress2: order.shippingAddress.postalCode + ' ' + order.shippingAddress.city,
      shippingCountry: this.i18n.t(`emails.countries.${order.shippingAddress.countryId}`, {
        lang,
      }),
    }
    const subject = this.i18n.t('emails.buyerNewOrderSubject', { lang, args: { orderNumber: order.orderNumber } })
    const templatePrefix = await getEmailTemplatePrefix({
      isPartner: false,
      partnerSlug: undefined,
      type: EmailType.BUYER_NEW_ORDER,
      lang,
    })
    await sendmail(
      {
        type: EmailType.BUYER_NEW_ORDER,
        lang,
        to: order.user.email,
        subject,
        data: emailData,
        templatePrefix,
      },
      3
    )
    await EmailHistoryEntity.create({
      email: order.user.email,
      subject,
      userId: order.user.id,
      type: EmailType.BUYER_NEW_ORDER,
      relatedIds: [order.id, ...order.orderSkuItems.map(({ id }) => id)],
      lang,
      data: emailData,
      templatePrefix: undefined,
    }).save()

    if (sendLogisticsEmail && envConfig.LOGISTIC_EMAILS.length) {
      const emailInternalData: IEmailBuyerNewOrderInternalData = {
        ...emailData,
        trackingNumber: order.shipment?.trackingNumber,
        parcelId: order.shipment?.parcelId,
        isReturningCustomer: order.isReturningCustomer,
      }
      const templatePrefix = await getEmailTemplatePrefix({
        isPartner: false,
        partnerSlug: undefined,
        type: EmailType.BUYER_NEW_ORDER_INTERNAL,
        lang,
      })
      await sendmail(
        {
          type: EmailType.BUYER_NEW_ORDER_INTERNAL,
          lang: 'en',
          to: envConfig.LOGISTIC_EMAILS,
          subject: 'Order ' + order.orderNumber,
          data: emailInternalData,
          templatePrefix,
        },
        3
      )
    }
  }
}
