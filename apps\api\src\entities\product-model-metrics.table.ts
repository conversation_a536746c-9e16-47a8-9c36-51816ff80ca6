import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  Entity,
  Index,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { IProductSku, ProductModelEntity, ProductSkuEntity } from '~/entities'

@ObjectType('ProductModelMetrics')
@Entity('product_model_metrics')
export class ProductModelMetricsEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @OneToOne(() => ProductModelEntity, (model) => model.metrics, { nullable: false, onDelete: 'CASCADE' })
  model: Relation<ProductModelEntity>

  @OneToMany(() => ProductSkuEntity, (sku) => sku.modelMetrics, { nullable: false })
  skus: Relation<ProductSkuEntity>[]

  @Index()
  @Column({ default: 0 })
  recentOrdersCount: number

  @Index()
  @Column({ default: 0 })
  recentSalesCount: number

  @Index()
  @Column({ default: false })
  isBestseller: boolean

  @Index()
  @Column({ default: false })
  isMvp: boolean

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductModelMetrics = Omit<ProductModelMetricsEntity, keyof BaseEntity> & {
  model: IProductSku
  skus: IProductSku[]
}
