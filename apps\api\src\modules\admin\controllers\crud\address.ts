import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { AddressEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudAddressService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: AddressEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      user: {},
      country: {},
    },
  },
})
@Controller('admin/addresses')
export class CrudAddressController implements CrudController<AddressEntity> {
  constructor(public service: CrudAddressService) {}
}
