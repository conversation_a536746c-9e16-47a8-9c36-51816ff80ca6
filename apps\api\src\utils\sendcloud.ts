import {
  CarrierCode,
  findServicePoints,
  getParcelDocument,
  getParcelTracking,
  type IFindServicePointsParams,
  type IServicePoint,
  ParcelDocumentFormat,
  ParcelDocumentType,
} from '@valyuu/sendcloud'
import axios from 'axios'
import { isNil } from 'lodash'

import { AddressEntity } from '~/entities'

type SendcloudGetTrackingUrlParams = {
  parcelId: string
  type?: 'carrier' | 'sendcloud'
}

type SendcloudGetNearbyDropOffPointsParams = {
  address: AddressEntity
  carrier: CarrierCode
  radiusM?: number
}

export const sendcloudGetTrackingUrl = async ({
  parcelId,
  type = 'carrier',
}: SendcloudGetTrackingUrlParams): Promise<string | null> => {
  if (!parcelId) {
    null
  }
  const { carrier_tracking_url, sendcloud_tracking_url } = await getParcelTracking(parcelId)
  return type === 'carrier' ? carrier_tracking_url : sendcloud_tracking_url
}

export const sendcloudGetNearbyDropOffPoints = async ({
  address,
  carrier,
  radiusM: radius = 10_000,
}: SendcloudGetNearbyDropOffPointsParams): Promise<IServicePoint[]> => {
  if ((radius && radius < 100) || radius > 50_000) {
    throw new Error('Radius must be between 100 and 50,000 meters')
  }
  const houseNumber = address.houseNumber + (address.addition ? ' ' + address.addition : '')
  const servicePointParams: IFindServicePointsParams = {
    country: address.countryId,
    postal_code: address.postalCode,
    city: address.city,
    address: address.street + ' ' + houseNumber,
    house_number: houseNumber,
    carrier,
    ...(isNil(radius) ? {} : { radius }),
  }

  return findServicePoints(servicePointParams)
}

export const sendcloudGetShippingLabel = async (
  parcelId: string,
  format: ParcelDocumentFormat = ParcelDocumentFormat.PDF
): Promise<ArrayBuffer> => {
  return getParcelDocument(parcelId, ParcelDocumentType.LABEL, { format })
}

export const sendcloudGetPaperlessCode = async (
  parcelId: string,
  format: ParcelDocumentFormat = ParcelDocumentFormat.PNG
): Promise<ArrayBuffer> => {
  return getParcelDocument(parcelId, ParcelDocumentType.QR, { format })
}

export const sendcloudGetFinalTrackingUrl = async (url: string): Promise<string> => {
  try {
    const response = await axios.head(url, { maxRedirects: 10, validateStatus: (status) => status < 400 })
    return response.request.res?.responseUrl || response.request.responseURL || url
  } catch {
    return url
  }
}
