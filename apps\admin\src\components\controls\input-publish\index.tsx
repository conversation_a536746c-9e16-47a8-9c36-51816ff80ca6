import './index.css'

import { Flex, Radio, Space } from 'antd'
import type { FC, ReactNode } from 'react'
import { AiOutlineCheck, AiOutlineClose } from 'react-icons/ai'

export type InputPublishProps = {
  isPublished: boolean
  children?: ReactNode
  onChange: (value: boolean) => void
}

export const InputPublish: FC<InputPublishProps> = ({ isPublished, children, onChange }) => (
  <Space size={32}>
    <Radio.Group
      value={isPublished}
      buttonStyle="solid"
      className="input-content-published"
      onChange={({ target: { value } }) => {
        onChange(value)
      }}
    >
      <Radio.Button value={false}>
        <Flex justify="center" align="center" gap={5}>
          <AiOutlineClose />
          Unpublished
        </Flex>
      </Radio.Button>
      <Radio.Button value={true}>
        <Flex justify="center" align="center" gap={5}>
          <AiOutlineCheck />
          Published
        </Flex>
      </Radio.Button>
    </Radio.Group>
    {children}
  </Space>
)
