import { ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  Entity,
  Index,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
} from 'typeorm'

import {
  type IProductModelAttribute,
  type IProductModelAttributeCombination,
  type IProductModelAttributeOption,
  ProductModelAttributeCombinationEntity,
  ProductModelAttributeEntity,
  ProductModelAttributeOptionEntity,
} from '~/entities'

@ObjectType('ProductModelAttributeCombinationChoice')
@Entity('product_model_attribute_combination_choice')
export class ProductModelAttributeCombinationChoiceEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @ManyToOne(() => ProductModelAttributeEntity)
  attribute: Relation<ProductModelAttributeEntity>

  @PrimaryColumn()
  @RelationId((choice: ProductModelAttributeCombinationChoiceEntity) => choice.attribute)
  attributeId: string

  @ManyToOne(() => ProductModelAttributeOptionEntity, { nullable: false })
  option: Relation<ProductModelAttributeOptionEntity>

  @PrimaryColumn()
  @RelationId((choice: ProductModelAttributeCombinationChoiceEntity) => choice.option)
  optionId: string

  @ManyToOne(() => ProductModelAttributeCombinationEntity, (combination) => combination.choices, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  combination: Relation<ProductModelAttributeCombinationEntity>

  @PrimaryColumn()
  @RelationId((choice: ProductModelAttributeCombinationChoiceEntity) => choice.combination)
  combinationId: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number
}

export type IProductModelAttributeCombinationChoice = Omit<
  ProductModelAttributeCombinationChoiceEntity,
  keyof BaseEntity
> & {
  attribute: IProductModelAttribute
  option: IProductModelAttributeOption
  combination: IProductModelAttributeCombination
}
