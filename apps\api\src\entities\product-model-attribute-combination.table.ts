import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IProductModel,
  type IProductModelAttributeCombinationChoice,
  type IProductVariant,
  ProductModelAttributeCombinationChoiceEntity,
  ProductModelEntity,
  ProductVariantEntity,
} from '~/entities'

@Entity('product_model_attribute_combination')
export class ProductModelAttributeCombinationEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ default: true })
  allowVariant: boolean

  @OneToMany(() => ProductModelAttributeCombinationChoiceEntity, (choice) => choice.combination, { nullable: false })
  choices: Relation<ProductModelAttributeCombinationChoiceEntity>[]

  @OneToOne(() => ProductVariantEntity, (variant) => variant.attributeCombination, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  variant: Relation<ProductVariantEntity>

  @Column({ nullable: true })
  @RelationId((combination: ProductModelAttributeCombinationEntity) => combination.variant)
  variantId: string

  @ManyToOne(() => ProductModelEntity, (model) => model.attributeCombinations, { nullable: false, onDelete: 'CASCADE' })
  model: Relation<ProductModelEntity>

  @Column()
  @RelationId((combination: ProductModelAttributeCombinationEntity) => combination.model)
  modelId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductModelAttributeCombination = Omit<
  ProductModelAttributeCombinationEntity,
  keyof BaseEntity
> & {
  choices: IProductModelAttributeCombinationChoice[]
  variant: IProductVariant
  model: IProductModel
}
