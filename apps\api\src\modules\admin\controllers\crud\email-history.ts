import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { <PERSON>, Get, Param, UseGuards } from '@nestjs/common'

import { EmailHistoryEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudEmailHistoryService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: EmailHistoryEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      user: {},
      changedBy: {},
    },
  },
})
@Controller('admin/email-histories')
export class CrudEmailHistoryController implements CrudController<EmailHistoryEntity> {
  constructor(public service: CrudEmailHistoryService) {}

  @Get('preview/:id')
  previewEmail(@Param('id') id: string) {
    return this.service.previewEmail(id)
  }
}
