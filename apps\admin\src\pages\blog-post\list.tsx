import {
  DateF<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Button,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useSelect,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IBlogAuthor, IBlogPost, IBlogTag } from '@valyuu/api/entities'
import { Select, Space, Table } from 'antd'
import type { FC } from 'react'

import { PublishStatus } from '~/components'
import { handleTableRowClick } from '~/utils'

export const BlogPostList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IBlogPost>({
    syncWithLocation: true,
    meta: {
      fields: ['title__en', 'slug__en', 'tagId', 'authorId', 'sortOrder', 'createdAt', 'publishedAt'],
      join: [
        {
          field: 'tag',
        },
        {
          field: 'author',
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { selectProps: tagSelectProps } = useSelect<IBlogTag>({
    resource: 'admin/blog-tags',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: authorSelectProps } = useSelect<IBlogAuthor>({
    resource: 'admin/blog-authors',
    optionLabel: 'name',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/blog-posts', id!)))}
      >
        <Table.Column dataIndex="title__en" title="Title (English)" className="cursor-pointer" />
        <Table.Column
          dataIndex="tagId"
          title="Tag"
          className="cursor-pointer"
          render={(_, row: IBlogPost) => row.tag.name__en}
          defaultFilteredValue={getDefaultFilter('tagId', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} {...tagSelectProps} placeholder="Tag" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="authorId"
          title="Author"
          className="cursor-pointer"
          render={(_, row: IBlogPost) => row.author.name}
          defaultFilteredValue={getDefaultFilter('authorId', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} {...authorSelectProps} placeholder="Author" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="sortOrder"
          title="Order"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
          width="5rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IBlogPost>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
