{"name": "valyuu", "version": "0.0.0", "license": "MIT", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "prepare": "husky install", "prettier": "prettier --config .prettierrc --write \"{apps,packages}/**/*.{ts,tsx}\""}, "private": true, "devDependencies": {"@commitlint/cli": "^19.0.3", "@commitlint/config-conventional": "^19.0.3", "@eslint/js": "^9.13.0", "@eslint/json": "^0.6.0", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.17.5", "globals": "^15.11.0", "husky": "^9.1.6", "hygen": "^6.2.11", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "turbo": "^2.2.3", "typescript": "^5.3.3", "typescript-eslint": "^8.12.2"}, "workspaces": ["apps/*", "packages/*"], "engines": {"node": ">=18.0.0"}, "packageManager": "pnpm@9.12.3"}