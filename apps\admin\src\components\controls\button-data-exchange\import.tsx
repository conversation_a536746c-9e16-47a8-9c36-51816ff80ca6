import { useNotification } from '@refinedev/core'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Button, Dropdown, Space } from 'antd'
import { BaseButtonProps } from 'antd/lib/button/button'
import { capitalize } from 'lodash'
import { FC, useEffect, useRef, useState } from 'react'
import { AiOutlineDown } from 'react-icons/ai'

export type ButtonDataExchangeImportProps = {
  channels?: string[]
  type?: BaseButtonProps['type']
  disabled?: boolean
  content: string
  url: string
  duration?: string
  onChange?: (isImporting: boolean) => void
}

export const ButtonDataExchangeImport: FC<ButtonDataExchangeImportProps> = ({
  type,
  channels,
  disabled = false,
  content,
  url,
  duration = '2-10 minutes',
  onChange,
}) => {
  const [currentChannel, setCurrentChannel] = useState<string>()

  const buttonRef = useRef<HTMLButtonElement>(null)
  const dropdownOpenRef = useRef<boolean>(false)

  const [importFile, setImportFile] = useState<File>()
  const fileUploadRef = useRef<HTMLInputElement>(null)

  const [isImporting, setIsImporting] = useState(false)

  const { open } = useNotification()

  useEffect(() => {
    if (importFile && currentChannel) {
      setIsImporting(true)
      open?.({
        key: 'data-exchange-import',
        type: 'success',
        message: `Importing ${content}` + (currentChannel ? ` for channel ${currentChannel}` : ''),
        description: `Please wait, it may take a maximum of ${duration}...`,
      })
      const formData = new FormData()
      formData.append('file', importFile)
      axios
        .post(url + (channels ? `/${currentChannel}` : ''), formData)
        .then(({ data }) => {
          if (data?.success === false) {
            open?.({
              key: 'data-exchange-import',
              type: 'error',
              message: data?.error || `Error importing ${content}`,
            })
          } else {
            open?.({
              key: 'data-exchange-import',
              type: 'success',
              message: `${capitalize(content)} imported successfully`,
            })
          }
          setIsImporting(false)
          fileUploadRef.current!.value = ''
          setImportFile(undefined)
        })
        .catch((error) => {
          console.error(error)
          open?.({
            key: 'data-exchange-import',
            type: 'error',
            message: `Error importing ${content}`,
          })
          setIsImporting(false)
          fileUploadRef.current!.value = ''
          setImportFile(undefined)
        })
    }
  }, [currentChannel, importFile])

  useEffect(() => {
    onChange?.(isImporting)
  }, [onChange, isImporting])

  return (
    <>
      <input
        ref={fileUploadRef}
        type="file"
        className="hidden"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        onChange={(event) => setImportFile(event.target.files?.[0])}
      />
      <Space.Compact>
        <Button
          type={type}
          disabled={disabled || isImporting}
          loading={isImporting}
          onClick={() => {
            if (channels && !currentChannel) {
              if (currentChannel) {
                fileUploadRef.current?.click()
              } else if (!dropdownOpenRef.current) {
                buttonRef.current?.click()
              }
            } else {
              fileUploadRef.current?.click()
            }
          }}
        >
          Import {content}
          {currentChannel ? ` for ${currentChannel}` : ''}
        </Button>
        {channels ? (
          <Dropdown
            autoAdjustOverflow
            disabled={disabled || isImporting}
            menu={{
              items: channels.map((channel) => ({
                label: `Import prices for ${channel}`,
                key: channel,
                onClick: () => {
                  setCurrentChannel(channel)
                  fileUploadRef.current?.click()
                },
              })),
            }}
            onOpenChange={(open) => {
              setTimeout(() => {
                dropdownOpenRef.current = open
              }, 0)
            }}
            trigger={['click']}
            placement="bottomRight"
          >
            <Button
              ref={buttonRef}
              icon={<AiOutlineDown size={12} className="anticon" />}
              disabled={disabled || isImporting}
            />
          </Dropdown>
        ) : null}
      </Space.Compact>
    </>
  )
}
