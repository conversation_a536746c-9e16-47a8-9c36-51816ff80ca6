import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { ShipmentTrackingStatusCode, ShipmentType } from '@valyuu/api/constants'
import type { IShipment } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, RadioGroup, Select, TextArea } from 'antx'
import { capitalize } from 'lodash'
import type { FC } from 'react'

import { LabelWithDetails, LinkShipmentDocument } from '~/components'
import { formatSelectOptionsFromEnum, getSendCloudParcelUrl } from '~/utils'

const { Text } = Typography

export const ShipmentEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, queryResult } = useForm<IShipment, HttpError, IShipment>({
    meta: {
      join: [
        {
          field: 'sale',
          select: ['id', 'saleNumber'],
        },
        {
          field: 'order',
          select: ['id', 'orderNumber'],
        },
        {
          field: 'saleItems',
          select: ['id'],
        },
        {
          field: 'warehouse',
          select: ['id', 'name'],
        },
        {
          field: 'partner',
          select: ['id', 'name'],
        },
      ],
    },
  })

  const record = queryResult?.data?.data

  const { editUrl } = useNavigation()

  return (
    <Edit
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Text copyable>{id}</Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Select
          label="Type"
          name="type"
          rules={['required']}
          options={formatSelectOptionsFromEnum(ShipmentType)}
          disabled
        />
        {record?.sale?.id && record?.sale?.saleNumber ? (
          <Form.Item label="Sale">
            <a href={editUrl('admin/sales', record.sale.id)}>
              <Text copyable>{record.sale.saleNumber}</Text>
            </a>
          </Form.Item>
        ) : null}
        {record?.order?.id && record?.order?.orderNumber ? (
          <Form.Item label="Order">
            <a href={editUrl('admin/orders', record.order.id)}>
              <Text copyable>{record.order.orderNumber}</Text>
            </a>
          </Form.Item>
        ) : null}
        {record?.saleItem ? (
          <Form.Item label="Sale items">
            <div key={record.saleItem.id}>
              <a href={editUrl('admin/sale-items', record.saleItem.id)}>
                <Text copyable>{record.saleItem.id}</Text>
              </a>
            </div>
          </Form.Item>
        ) : null}
        <Select
          label="Status"
          name="statusCode"
          options={Object.entries(ShipmentTrackingStatusCode)
            .filter(([_, value]) => typeof value === 'number')
            .map(([key, value]) => ({ label: capitalize(key.replace(/_/g, ' ')), value: String(value) }))}
          disabled
        />
        {record?.parcelId ? (
          <Input
            label={<LabelWithDetails label="Parcel ID" link={getSendCloudParcelUrl(record.parcelId)} target="_blank" />}
            name="parcelId"
            disabled
          />
        ) : null}
        <Input
          label={<LabelWithDetails label="Tracking number" link={record?.trackingUrl} target="_blank" />}
          name="trackingNumber"
          rules={['required']}
          disabled
        />
        <RadioGroup
          label="Is a return parcel"
          name="isReturn"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
          disabled
        />
        <RadioGroup
          label="Is paperless"
          name="isPaperless"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
          disabled
        />
        <Form.Item label="Shipping label / Paperless code" name="shippingLabelUrl" className="flex flex-col gap-2">
          <LinkShipmentDocument shipmentId={id as string} trackingNumber={record?.trackingNumber ?? ''} />
        </Form.Item>
        {record?.warehouse?.id && record?.warehouse?.name ? (
          <Input
            label={
              <LabelWithDetails
                label="Warehouse"
                link={record.warehouse.id ? editUrl('admin/warehouses', record.warehouse.id) : undefined}
              />
            }
            value={record.warehouse.name}
            disabled
          />
        ) : null}

        {record?.warehouse?.id && record?.warehouse?.name ? (
          <Input label="Partner" value={record.partner.name} disabled />
        ) : null}
        <TextArea label="Note" name="note" rows={4} className="col-span-2" />
      </Form>
    </Edit>
  )
}
