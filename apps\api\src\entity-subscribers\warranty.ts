import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { WarrantyEntity } from '~/entities'
import { entityFixPublishedAt } from '~/utils/entity-subscriber'

@Injectable()
@EventSubscriber()
export class WarrantySubscriber implements EntitySubscriberInterface<WarrantyEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return WarrantyEntity
  }

  async beforeInsert(event: InsertEvent<WarrantyEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async beforeUpdate(event: UpdateEvent<WarrantyEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }
}
