import { Args, Query, Resolver } from '@nestjs/graphql'

import {
  GetProductSkuAccessoryProductOutput,
  GetProductSkuAccessoryProductsBySkuIdsInput,
  GetProductSkuInput,
  GetProductSkuOutput,
  GetProductSkuWarrantyOutput,
} from '~/dtos'
import { ProductSkuService } from '~/services'

@Resolver()
export class ProductSkuResolver {
  constructor(private readonly service: ProductSkuService) {}

  @Query(() => GetProductSkuOutput)
  async getProductSku(@Args() { channelId, lang, slug, slugNumber }: GetProductSkuInput): Promise<GetProductSkuOutput> {
    return this.service.getProductSku({ channelId, lang, slug, slugNumber })
  }

  @Query(() => [GetProductSkuWarrantyOutput])
  async getProductSkuWarranties(
    @Args() { channelId, lang, slug, slugNumber }: GetProductSkuInput
  ): Promise<GetProductSkuWarrantyOutput[]> {
    return this.service.getProductSkuWarranties({ channelId, lang, slug, slugNumber })
  }

  @Query(() => [GetProductSkuAccessoryProductOutput])
  async getProductSkuAccessoryProducts(
    @Args() { channelId, lang, slug, slugNumber }: GetProductSkuInput
  ): Promise<GetProductSkuAccessoryProductOutput[]> {
    return this.service.getProductSkuAccessoryProducts({ channelId, lang, slug, slugNumber })
  }

  @Query(() => [GetProductSkuAccessoryProductOutput])
  async getProductSkuAccessoryProductsBySkuIds(
    @Args() { channelId, skuIds }: GetProductSkuAccessoryProductsBySkuIdsInput
  ): Promise<GetProductSkuAccessoryProductOutput[]> {
    return this.service.getProductSkuAccessoryProductsBySkuIds({ channelId, skuIds })
  }
}
