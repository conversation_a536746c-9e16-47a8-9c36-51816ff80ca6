{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"plugins": ["@nestjs/graphql/plugin"], "assets": [{"include": "emails/templates/**/*", "outDir": "dist/src", "watchAssets": false}, {"include": "emails/attachments/**/*", "outDir": "dist/src", "watchAssets": false}, {"include": "i18n/**/*", "outDir": "dist/src", "watchAssets": true}, {"include": "files/**/*", "outDir": "dist/src", "watchAssets": false}], "watchAssets": true}, "watchOptions": {"ignored": ["data/**/*"]}}