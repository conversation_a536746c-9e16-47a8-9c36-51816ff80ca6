import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { PaymentListProblemItemEntity } from '~/entities'

export class CrudPaymentListProblemItemService extends TypeOrmCrudService<PaymentListProblemItemEntity> {
  constructor(@InjectRepository(PaymentListProblemItemEntity) repo: Repository<PaymentListProblemItemEntity>) {
    super(repo)
  }
}
