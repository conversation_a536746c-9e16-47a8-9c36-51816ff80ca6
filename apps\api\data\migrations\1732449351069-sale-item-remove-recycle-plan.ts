import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleItemRemoveRecyclePlan1732449351069 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if status column exists
    const hasStatusColumn = await queryRunner.hasColumn('sale_item', 'status')

    if (!hasStatusColumn) {
      await queryRunner.query(`
        ALTER TABLE sale_item 
        ADD COLUMN status varchar DEFAULT 'SUBMITTED' NOT NULL
      `)
    }

    // Update RECYCLE type records to C2B and set status to RECYCLED
    await queryRunner.query(`
      UPDATE sale_item 
      SET type = 'C2B', status = 'RECYCLED'
      WHERE type = 'RECYCLE'
    `)

    // Update UNKNOWN status to SUBMITTED
    await queryRunner.query(`
      UPDATE sale_item 
      SET status = 'SUBMITTED'
      WHERE status = 'UNKNOWN'
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
