import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  Unique,
  UpdateDateColumn,
} from 'typeorm'

import { ProductSkuGrading } from '~/constants'
import {
  CategoryEntity,
  CategoryWarrantyRuleWarrantyItemEntity,
  ChannelEntity,
  type ICategory,
  type ICategoryWarrantyRuleWarrantyItem,
} from '~/entities'

@Entity('category_warranty_rule')
@Unique(['channelId', 'categoryId', 'eligibleGrades', 'eligibleMinSkuPrice', 'eligibleMaxSkuPrice'])
export class CategoryWarrantyRuleEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((categoryWarranty: CategoryWarrantyRuleEntity) => categoryWarranty.channel)
  channelId: string

  @OneToMany(() => CategoryWarrantyRuleWarrantyItemEntity, (rule) => rule.rule, {
    nullable: false,
    cascade: true,
  })
  warrantyItems: Relation<CategoryWarrantyRuleWarrantyItemEntity>[]

  @ManyToOne(() => CategoryEntity, (category) => category.warrantyRules, { nullable: false })
  category: Relation<CategoryEntity>

  @Column()
  @RelationId((categoryWarranty: CategoryWarrantyRuleEntity) => categoryWarranty.category)
  categoryId: string

  @Column({ type: 'varchar', array: true })
  eligibleGrades: ProductSkuGrading[]

  @Column({ nullable: true })
  eligibleMinSkuPrice?: number | null

  @Column({ nullable: true })
  eligibleMaxSkuPrice?: number | null

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ICategoryWarrantyRule = Omit<CategoryWarrantyRuleEntity, keyof BaseEntity> & {
  warrantyItems: ICategoryWarrantyRuleWarrantyItem[]
  defaultWarrantyItem: ICategoryWarrantyRuleWarrantyItem
  category: ICategory
}
