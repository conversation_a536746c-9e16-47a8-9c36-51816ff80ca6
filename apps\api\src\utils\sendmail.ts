import 'mjml'

import { access, readFile } from 'node:fs/promises'
import { join } from 'node:path'

import type { MailDataRequired } from '@sendgrid/mail'
import * as sendGrid from '@sendgrid/mail'
import * as Sentry from '@sentry/nestjs'
import handlebars from 'handlebars'
import { convert } from 'html-to-text'
import mjml from 'mjml-core'
import ms from 'ms'
import * as pMap from 'p-map'
import * as pRetry from 'p-retry'

import { envConfig } from '~/configs'
import { EmailType } from '~/constants'
import { CustomerReviewEntity } from '~/entities'
import { IEmailCommonData, ISendmailInputType } from '~/interfaces'
import { cloudinaryGetUrl } from '~/utils'

sendGrid.setApiKey(envConfig.SENDGRID_API_KEY)

handlebars.registerHelper('eq', (a, b) => a == b)

const sendmailGetTrustpilotStars = async () => {
  const customerReview = await CustomerReviewEntity.findOne({ where: {}, cache: ms('1d') })

  const score = customerReview?.trustpilotScore ?? 0
  let stars: ('full' | 'half' | 'empty')[]

  const ratingRules: [number, typeof stars][] = [
    [1.2, ['full', 'empty', 'empty', 'empty', 'empty']],
    [1.7, ['full', 'half', 'empty', 'empty', 'empty']],
    [2.2, ['full', 'full', 'empty', 'empty', 'empty']],
    [2.7, ['full', 'full', 'half', 'empty', 'empty']],
    [3.2, ['full', 'full', 'full', 'empty', 'empty']],
    [3.7, ['full', 'full', 'full', 'half', 'empty']],
    [4.2, ['full', 'full', 'full', 'full', 'empty']],
    [4.7, ['full', 'full', 'full', 'full', 'half']],
    [5, ['full', 'full', 'full', 'full', 'full']],
  ]

  for (const ratingRule of ratingRules) {
    if (score <= ratingRule[0]) {
      stars = ratingRule[1]
      break
    }
  }

  return {
    score: customerReview?.trustpilotScore ?? 0,
    reviews: customerReview?.trustpilotCount ?? 0,
    stars,
  }
}

const getCommonData = async () => ({
  imagePrefix: cloudinaryGetUrl('backend/email'),
  year: new Date().getFullYear(),
  trustpilot: await sendmailGetTrustpilotStars(),
})

export const getEmailTemplatePrefix = async ({
  isPartner,
  partnerSlug,
  type,
  lang,
}: {
  isPartner: boolean
  partnerSlug?: string
  type: EmailType
  lang: string
}): Promise<string> => {
  let prefix = '_default'
  const baseDir = join(__dirname, '../emails/templates', type)

  // Try templatePrefix first if provided and isPartner is true
  if (isPartner && partnerSlug) {
    try {
      await access(join(baseDir, `${partnerSlug}.${lang}.mjml`))
      prefix = partnerSlug
    } catch {
      // File doesn't exist, continue to fallback
    }
  }

  // Try partner template if isPartner is true and no templatePrefix was found
  if (isPartner) {
    try {
      await access(join(baseDir, `_partner.${lang}.mjml`))
      prefix = '_partner'
    } catch (error) {
      Sentry.captureMessage('Sendmail get email template prefix: partner template not found', {
        tags: {
          type: 'utils:sendmailGetEmailTemplatePrefix',
        },
        extra: {
          isPartner,
          partnerSlug,
          type,
          lang,
        },
        level: 'info',
      })
    }
  }

  return prefix
}

export const renderEmail = async (
  input: Pick<ISendmailInputType, 'type' | 'lang' | 'data' | 'templatePrefix'>,
  commonData?: IEmailCommonData
) => {
  if (!commonData) {
    commonData = await getCommonData()
  }

  const baseDir = join(__dirname, '../emails/templates', input.type)
  const fileName = `${input.lang}.mjml`
  const templatePath = join(baseDir, `${input.templatePrefix ?? '_default'}.${fileName}`)

  return mjml(await readFile(templatePath, { encoding: 'utf-8' }), {
    preprocessors: [(rawMJML) => handlebars.compile(rawMJML)({ ...commonData, ...input.data })],
  }).html
}

export const sendmail = async (
  input: ISendmailInputType | ISendmailInputType[],
  retries = 0,
  retryInterval = 5_000
) => {
  const mails: MailDataRequired[] = []
  const inputArray = Array.isArray(input) ? input : [input]
  const commonData: IEmailCommonData = await getCommonData()
  await pMap(inputArray, async (input: ISendmailInputType) => {
    if (!input.from) {
      input.from = envConfig.EMAIL_GENERAL_FROM
    }
    const html = await renderEmail(input, commonData)

    const mailInput: MailDataRequired = {
      to: input.to,
      from: input.from ?? envConfig.EMAIL_GENERAL_FROM,
      subject: input.subject,
      html,
      text: convert(html), // TODO: remove extra html elements
    }
    switch (input.type) {
      case EmailType.SELLER_NEW_SALE:
      case EmailType.SELLER_REMINDER:
        if (Array.isArray(input.attachments)) {
          mailInput.attachments = input.attachments
        } else {
          Sentry.captureMessage('Sendmail: attachments is not an array', {
            tags: {
              type: 'utils:sendmail',
            },
            extra: {
              input,
            },
          })
        }
        break
    }
    mails.push(mailInput)
  })

  return retries
    ? pRetry(() => sendGrid.send(mails), {
        retries,
        minTimeout: retryInterval,
      })
    : sendGrid.send(mails)
}

export const sendmailGetAttachment = (filename: string) => readFile(join(__dirname, '../emails/attachments', filename))
