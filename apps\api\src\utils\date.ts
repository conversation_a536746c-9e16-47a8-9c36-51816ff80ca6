import { addDays, isWeekend } from 'date-fns'

/**
 * Adds business days (Monday-Friday) to a date
 * @param date The base date
 * @param days Number of business days to add
 * @returns Date with business days added
 */
export const dateAddBusinessDays = (date: Date, days: number): Date => {
  let result = new Date(date)
  let daysAdded = 0

  while (daysAdded < days) {
    result = addDays(result, 1)
    // Skip weekends (0 = Sunday, 6 = Saturday)
    if (!isWeekend(result)) {
      daysAdded++
    }
  }

  return result
}
