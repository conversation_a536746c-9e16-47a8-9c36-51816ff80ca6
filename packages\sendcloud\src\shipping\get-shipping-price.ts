import { sendcloudClientV2 } from '../client'
import { IShippingPrice } from '../interfaces'

export type IGetShippingCostParams = {
  /** ID of the contract */
  contract?: number
  /** Postal code of the sender (max 12 characters) */
  from_postal_code?: string
  /** Postal code of the recipient (max 12 characters) */
  to_postal_code?: string
  /** The sender country (ISO 3166-1 alpha-2) */
  from_country: string
  /** The receiver country (ISO 3166-1 alpha-2) */
  to_country: string
  /** The ID of the shipping method */
  shipping_method_id: string | number
  /** Weight of the shipment */
  weight: number
  /** Weight unit ('kilogram' or 'gram') */
  weight_unit: 'kilogram' | 'gram'
  /** The ID of the sender address */
  sender_address?: string
  /** The ID of the service point */
  service_point_id?: number
  /** If true, endpoint will return the shipping method only if it is a return shipping method */
  is_return?: boolean
}

/**
 * Get available shipping method based on provided parameters
 * @param params - Shipping method query parameters
 * @returns Promise with shipping method details
 */
export const getShippingPrice = async (params: IGetShippingCostParams): Promise<IShippingPrice[]> => {
  const { data } = await sendcloudClientV2.get('/shipping-price', {
    params,
  })

  return data
}
