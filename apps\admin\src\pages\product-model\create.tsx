import { ProCard } from '@ant-design/pro-components'
import ProForm, { ProFormList } from '@ant-design/pro-form'
import { Create, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNotification } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type { IAccessoryProduct, IProductModel, IProductSeries, IQuestionType } from '@valyuu/api/entities'
import { Button } from 'antd'
import { DatePicker, Input, InputNumber, RadioGroup, Select, TextArea, Watch } from 'antx'
import cx from 'clsx'
import dayjs from 'dayjs'
import { type FC, useCallback, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import {
  InputImage,
  InputLanguageTab,
  InputMultiEntitySelect,
  InputMultiLang,
  InputPublish,
  InputSlug,
  LanguageTabChoices,
} from '~/components'

export const ProductModelCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, onFinish, saveButtonProps } = useForm<IProductModel, HttpError, IProductModel>()
  const [autoFillValue, setAutoFillValue] = useState<string>('')
  const { open } = useNotification()
  const id = useMemo(() => uuid(), [])

  const publishedAt = ProForm.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const { selectProps: questionTypeSelectProps } = useSelect<IQuestionType>({
    resource: 'admin/question-types',
    optionLabel: 'internalName',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: seriesSelectProps } = useSelect<IProductSeries>({
    resource: 'admin/product-series',
    optionLabel: 'name__en',
    filters: [{ field: 'publishedAt', operator: 'nnull', value: true }],
    meta: {
      fields: ['id', 'name__en', 'name__pl', 'publishedAt'],
    },
    pagination: {
      mode: 'off',
    },
  })
  const { selectProps: accessoryProductSelectProps } = useSelect<IAccessoryProduct>({
    resource: 'admin/accessory-products',
    optionLabel: 'internalName',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const handleAutoFill = useCallback(() => {
    try {
      const fields = autoFillValue.split('\t')
      if (fields?.length != 8) {
        open?.({
          key: 'auto-fill-format-error',
          type: 'error',
          message: 'auto fill format error,please check the data or contact the developer',
        })
      }
      const name__en = fields[0]
      const name__nl = fields[1]
      const name__de = fields[2]
      const name__pl = fields[3]
      const seriesId = seriesSelectProps?.options?.find((option) => {
        return option.label === fields[3]
      })?.value
      const recycle = fields[4] === 'Yes' ? true : false
      const releaseDate = dayjs(new Date(fields[5]))
      const questionTypeId = questionTypeSelectProps?.options?.find((option) => {
        return option.label === fields[6]
      })
      const isMvp = fields[7] === 'Yes' ? true : false
      form.setFieldValue('name__en', name__en)
      form.setFieldValue('name__nl', name__nl)
      form.setFieldValue('name__de', name__de)
      form.setFieldValue('name__pl', name__pl)
      form.setFieldValue('seriesId', seriesId)
      form.setFieldValue('recycle', recycle)
      form.setFieldValue('releaseDate', releaseDate)
      form.setFieldValue('questionTypeId', questionTypeId)
      form.setFieldValue(['metrics', 'isMvp'], isMvp)
    } catch (e) {
      open?.({
        key: 'auto-fill-format-error',
        type: 'error',
        message: 'auto fill format error,please check the data or contact the developer',
      })
    }
  }, [
    autoFillValue,
    accessoryProductSelectProps?.options,
    questionTypeSelectProps?.options,
    seriesSelectProps?.options,
  ])

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      {/*auto fill*/}
      <section className={'flex items-center  gap-2'}>
        <TextArea
          placeholder={'Please paste a single row of data in excel for autofill'}
          className={'flex-1'}
          value={autoFillValue}
          onChange={(e) => setAutoFillValue(e.currentTarget.value)}
        />
        <Button onClick={handleAutoFill}>auto fill</Button>
      </section>
      <ProForm
        {...formProps}
        onFinish={async (values) => {
          onFinish?.(values)
        }}
        submitter={false}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
                autoFollow
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
                autoFollow
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
                autoFollow
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Polish)"
                name="slug__pl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__pl}
                className={clsHidePl}
                autoFollow
              />
              <InputImage
                label="Image"
                name="image"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.PRODUCT_MODEL}
                processName={ImageProcessNameType.COPY}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
            </>
          )}
        </Watch>
        <Select label="Product series" name="seriesId" rules={['required']} {...(seriesSelectProps as any)} />
        <RadioGroup
          label="Goes to recycle"
          name="recycle"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          initialValue={false}
          rules={['required']}
        />
        <DatePicker label="Release date" name="releaseDate" format="DD-MM-YYYY" rules={['required']} />
        <Select
          label="Question type"
          name="questionTypeId"
          rules={['required']}
          {...(questionTypeSelectProps as any)}
        />
        <RadioGroup
          label="Is buyer MVP"
          name={['metrics', 'isMvp']}
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
        />
        <ProFormList
          required
          actionGuard={{
            beforeAddRow: async (defaultValue) => {
              defaultValue.id = uuid()
              return true
            },
          }}
          name="attributes"
          label="Attributes"
          creatorButtonProps={{
            creatorButtonText: 'Add a new attribute',
          }}
          copyIconProps={{
            tooltipText: 'Duplicate this attribute',
          }}
          deleteIconProps={{
            tooltipText: 'Delete this attribute',
          }}
          itemRender={({ listDom, action }, { index }) => {
            return (
              <ProCard
                bordered
                extra={action}
                className="mb-3"
                title={<span className="list-item-title">Attribute {index + 1}</span>}
              >
                {listDom}
              </ProCard>
            )
          }}
        >
          {({ name: attributeName }, attributeIndex) => {
            return (
              <>
                <Input type="hidden" name={[attributeName, 'id']} hidden noStyle />
                <Watch
                  list={[
                    ['attributes', attributeName, 'name__en'],
                    ['attributes', attributeName, 'name__nl'],
                    ['attributes', attributeName, 'name__de'],
                    ['attributes', attributeName, 'name__pl'],
                  ]}
                >
                  {([name__en, name__nl, name__de, name__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="en"
                        label="Name (English)"
                        name={[attributeName, 'name__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="nl"
                        label="Name (Dutch)"
                        name={[attributeName, 'name__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="de"
                        label="Name (German)"
                        name={[attributeName, 'name__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="pl"
                        label="Name (Polish)"
                        name={[attributeName, 'name__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
                <Watch
                  list={[
                    ['attributes', attributeName, 'question__en'],
                    ['attributes', attributeName, 'question__nl'],
                    ['attributes', attributeName, 'question__de'],
                    ['attributes', attributeName, 'question__pl'],
                  ]}
                >
                  {([question__en, question__nl, question__de, question__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="en"
                        label="Question (English)"
                        name={[attributeName, 'question__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="nl"
                        label="Question (Dutch)"
                        name={[attributeName, 'question__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="de"
                        label="Question (German)"
                        name={[attributeName, 'question__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="pl"
                        label="Question (Polish)"
                        name={[attributeName, 'question__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
                <TextArea label="Description (English)" name={[attributeName, 'desc__en']} className={clsHideEn} />
                <TextArea label="Description (Dutch)" name={[attributeName, 'desc__nl']} className={clsHideNl} />
                <TextArea label="Description (German)" name={[attributeName, 'desc__de']} className={clsHideDe} />
                <TextArea label="Description (Polish)" name={[attributeName, 'desc__pl']} className={clsHidePl} />
                <InputNumber
                  label="Sort order"
                  precision={0}
                  name={[attributeName, 'sortOrder']}
                  rules={['required']}
                  initialValue={10}
                />
                <ProFormList
                  required
                  actionGuard={{
                    beforeAddRow: async (defaultValue) => {
                      defaultValue.id = uuid()
                      return true
                    },
                  }}
                  rules={[
                    {
                      validator: async (_, value) => {
                        if ((value || []).length === 0) {
                          return Promise.reject(new Error('At least one option is required'))
                        }
                      },
                    },
                  ]}
                  name="options"
                  label="Options"
                  creatorButtonProps={{
                    creatorButtonText: 'Add a new option',
                  }}
                  copyIconProps={{
                    tooltipText: 'Duplicate this option',
                  }}
                  deleteIconProps={{
                    tooltipText: 'Delete this option',
                  }}
                  itemRender={({ listDom, action }, { index }) => {
                    return (
                      <ProCard
                        bordered
                        extra={action}
                        className="mb-3"
                        title={
                          <span className="list-item-title">
                            Attribute {attributeIndex + 1} option {index + 1}
                          </span>
                        }
                      >
                        {listDom}
                      </ProCard>
                    )
                  }}
                >
                  {({ name: optionName }) => {
                    return (
                      <>
                        <Input type="hidden" name={[optionName, 'id']} hidden noStyle />
                        <Watch
                          list={[
                            ['attributes', attributeName, 'options', optionName, 'name__en'],
                            ['attributes', attributeName, 'options', optionName, 'name__nl'],
                            ['attributes', attributeName, 'options', optionName, 'name__de'],
                            ['attributes', attributeName, 'options', optionName, 'name__pl'],
                          ]}
                        >
                          {([name__en, name__nl, name__de, name__pl]) => (
                            <>
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="en"
                                label="Name (English)"
                                name={[optionName, 'name__en']}
                                rules={['required']}
                                className={clsHideEn}
                                fillType="duplicate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="nl"
                                label="Name (Dutch)"
                                name={[optionName, 'name__nl']}
                                rules={['required']}
                                className={clsHideNl}
                                fillType="duplicate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="de"
                                label="Name (German)"
                                name={[optionName, 'name__de']}
                                rules={['required']}
                                className={clsHideDe}
                                fillType="duplicate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="pl"
                                label="Name (Polish)"
                                name={[optionName, 'name__pl']}
                                rules={['required']}
                                className={clsHidePl}
                                fillType="duplicate"
                              />
                            </>
                          )}
                        </Watch>
                        <InputNumber
                          label="Sort order"
                          precision={0}
                          name={[optionName, 'sortOrder']}
                          rules={['required']}
                          initialValue={10}
                        />
                      </>
                    )
                  }}
                </ProFormList>
              </>
            )
          }}
        </ProFormList>
        <InputMultiEntitySelect
          label="Accessory products"
          name="accessoryProducts"
          {...(accessoryProductSelectProps as any)}
        />
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </ProForm>
    </Create>
  )
}
