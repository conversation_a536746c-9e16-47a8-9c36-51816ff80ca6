import { MigrationInterface, QueryRunner } from 'typeorm'

export class ModifySaleItemTable1729743880174 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop views
    await queryRunner.query(`
        DROP VIEW IF EXISTS order_summary;
        DROP VIEW IF EXISTS sale_summary;
        DROP VIEW IF EXISTS user_summary;
    `)

    // Add new column
    await queryRunner.query(`ALTER TABLE sale_item ADD COLUMN price DECIMAL(10,2) CHECK (price >= 0)`)

    // Copy data
    await queryRunner.query(`UPDATE sale_item SET price = seller_payment_price`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
