import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { ProductSkuPriceEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudProductSkuPriceService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ProductSkuPriceEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      channel: {},
      currency: {},
      sku: {},
    },
  },
  routes: {
    only: ['updateOneBase'],
  },
})
@Controller('admin/product-sku-prices')
export class CrudProductSkuPriceController implements CrudController<ProductSkuPriceEntity> {
  constructor(public service: CrudProductSkuPriceService) {}
}
