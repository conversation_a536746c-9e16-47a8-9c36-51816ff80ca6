import { useNotification } from '@refinedev/core'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Button, Dropdown, Space } from 'antd'
import { BaseButtonProps } from 'antd/lib/button/button'
import { FC, useCallback, useEffect, useRef, useState } from 'react'
import { AiOutlineDown } from 'react-icons/ai'

export type ButtonDataExchangeExportProps = {
  channels?: string[]
  type?: BaseButtonProps['type']
  disabled?: boolean
  content: string
  url: string
  duration?: string
  onChange?: (isImporting: boolean) => void
}

export const ButtonDataExchangeExport: FC<ButtonDataExchangeExportProps> = ({
  channels,
  type,
  disabled = false,
  content,
  url,
  duration = '3-5 minutes',
  onChange,
}) => {
  const [currentChannel, setCurrentChannel] = useState<string>()

  const buttonRef = useRef<HTMLButtonElement>(null)
  const dropdownOpenRef = useRef<boolean>(false)

  const [isExporting, setIsExporting] = useState(false)

  const { open } = useNotification()

  const doExport = useCallback(
    async (channelId?: string) => {
      channelId = channelId ?? currentChannel
      if (channels && !channelId) {
        return
      }
      setIsExporting(true)
      open?.({
        key: 'data-exchange-export',
        type: 'success',
        message: `Exporting ${content}` + (channelId ? ` for channel ${channelId}` : ''),
        description: `Please wait, it may take a maximum of ${duration}...`,
      })
      try {
        const { data, headers } = await axios.get(url + (channels ? `/${channelId}` : ''), {
          responseType: 'blob',
        })
        const contentDisposition = headers['content-disposition']
        let download = 'export.xlsx' // Default filename if none is specified

        // Parse Content-Disposition header to extract filename
        if (contentDisposition) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
          const matches = filenameRegex.exec(contentDisposition)
          if (matches != null && matches[1]) {
            download = matches[1].replace(/['"]/g, '') // Remove any surrounding quotes
          }
        }
        const href = window.URL.createObjectURL(new Blob([data]))
        Object.assign(document.createElement('a'), {
          href,
          download,
        }).click()
      } catch (error: any) {
        open?.({
          key: 'data-exchange-export',
          type: 'error',
          message: `Error exporting ${content}` + error?.message ? `: ${error.message}` : '',
        })
      }
      setIsExporting(false)
      open?.({
        key: 'data-exchange-export',
        type: 'success',
        message: 'Prices exported successfully',
      })
    },
    [channels, currentChannel, url, open]
  )

  useEffect(() => {
    onChange?.(isExporting)
  }, [onChange, isExporting])

  return (
    <Space.Compact>
      <Button
        type={type}
        disabled={disabled || isExporting}
        loading={isExporting}
        onClick={() => {
          if (channels) {
            if (currentChannel) {
              doExport(currentChannel)
            } else if (!dropdownOpenRef.current) {
              buttonRef.current?.click()
            }
          } else {
            doExport()
          }
        }}
      >
        Export {content}
        {currentChannel ? ` for channel ${currentChannel}` : ''}
      </Button>
      {channels ? (
        <Dropdown
          autoAdjustOverflow
          disabled={disabled || isExporting}
          menu={{
            items: channels.map((channel) => ({
              label: `Export ${content} for channel ${channel}`,
              key: channel,
              onClick: () => {
                setCurrentChannel(channel)
                doExport(channel)
              },
            })),
          }}
          onOpenChange={(open) => {
            setTimeout(() => {
              dropdownOpenRef.current = open
            }, 0)
          }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button
            ref={buttonRef}
            icon={<AiOutlineDown size={12} className="anticon" />}
            disabled={disabled || isExporting}
          />
        </Dropdown>
      ) : null}
    </Space.Compact>
  )
}
