import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { PriceThresholdEntity } from '~/entities'

export class CrudPriceThresholdService extends TypeOrmCrudService<PriceThresholdEntity> {
  constructor(@InjectRepository(PriceThresholdEntity) repo: Repository<PriceThresholdEntity>) {
    super(repo)
  }
}
