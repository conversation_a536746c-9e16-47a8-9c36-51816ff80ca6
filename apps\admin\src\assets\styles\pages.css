/* For edit page subTitle */
.ant-page-header-heading-sub-title {
  color: rgba(0, 0, 0, 0.45) !important;
  font-weight: normal !important;
  .ant-typography {
    font-weight: normal !important;
    color: inherit !important;
    svg {
      color: rgba(0, 0, 0, 0.45) !important;
    }
  }
}

.ant-form-item-margin-offset {
  margin: 0 !important;
}

form.ant-pro-form,
form.ant-form,
.ant-pro-form-list-container,
.ant-pro-card-body,
.input-modal-wrapper .ant-pro-card-body {
  @apply sm:grid sm:grid-cols-2 sm:gap-x-6;
  max-width: 1360px;
}

form.ant-pro-form {
  .ant-pro-form-list,
  .input-modal-wrapper {
    @apply col-span-2;
  }
  .list-item-title {
    @apply text-sm font-normal;
    color: #ccc;
  }
  .ant-pro-card:hover {
    border-color: #1890ff;
    > div > div > .list-item-title {
      color: #1890ff;
    }
  }
}

.ant-form-item-control-input-content .mdxeditor {
  @apply border rounded-md border-solid border-[#d9d9d9] overflow-hidden hover:border-[#4096ff];
  outline: 0;
  div[role='toolbar'] {
    border-radius: 0;
  }
}

.ant-pagination-total-text {
  margin-left: 1rem !important;
  margin-right: auto !important;
}

.ant-table-thead th.cursor-pointer {
  cursor: default;
}