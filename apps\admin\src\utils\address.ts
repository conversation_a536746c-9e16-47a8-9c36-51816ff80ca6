import type { IAddress, ILocaleCountry } from '@valyuu/api/entities'

export const getAddressText = (address: IAddress, countries: ILocaleCountry[] | undefined) => {
  if (address) {
    let country = address.countryId
    if (countries && countries.length) {
      country = countries.find(({ id }) => id === address.countryId)?.name__en ?? country
    }
    const { firstName, lastName, street, postalCode, houseNumber, addition, city } = address
    return `${firstName} ${lastName}\n${street} ${houseNumber}${addition ? `, ${addition}` : ''}\n${postalCode} ${city}\n${country}`
  }
  return ''
}

export const countryToEmoji = (countryCode: string) => {
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map((char) => 127397 + char.charCodeAt(0))

  return String.fromCodePoint(...codePoints)
}
