import { Injectable } from '@nestjs/common'
import { ClsService } from 'nestjs-cls'
import type { EntitySubscriberInterface, InsertEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { EmailHistoryEntity } from '~/entities'
import { ClsStoreType } from '~/interfaces'

@Injectable()
@EventSubscriber()
export class EmailHistorySubscriber implements EntitySubscriberInterface<EmailHistoryEntity> {
  constructor(
    private readonly cls: ClsService<ClsStoreType>,
    private readonly dataSource?: DataSource
  ) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return EmailHistoryEntity
  }

  async afterInsert(event: InsertEvent<EmailHistoryEntity>) {
    const { entity, manager } = event
    const adminUserId = this.cls.get('adminUserId')
    if (adminUserId) {
      await manager.update(EmailHistoryEntity, entity.id, { changedById: adminUserId })
    }
  }
}
