import { FilterDropdown, getDefaultSortOrder, List, ShowButton, useTable } from '@refinedev/antd'
import {
  getDefaultFilter,
  type IResourceComponentsProps,
  Link,
  useApiUrl,
  useCustomMutation,
  useInvalidate,
  useNavigation,
} from '@refinedev/core'
import { PaymentListProblemItemReason, PaymentListStatus, PaymentListType } from '@valyuu/api/constants'
import type { IPaymentList, IPaymentListProblemItem } from '@valyuu/api/entities'
import { Button } from 'antd'
import { Form, Modal, Popconfirm, Space, Table, Tabs, TabsProps, Tag, Typography } from 'antd'
import { Input, Select } from 'antx'
import { capitalize, omit } from 'lodash'
import { type FC, useState } from 'react'
import { AiOutlineCheck, AiOutlineDollar } from 'react-icons/ai'
import { BsBank } from 'react-icons/bs'

import { formatDate, formatMoney, formatSelectOptionsFromEnum } from '~/utils'

export type PaymentListUpcomingItem = {
  date: Date
  type: PaymentListType
  amount: number
  itemsCount: number
}

export const PaymentListList: FC<IResourceComponentsProps> = () => {
  const apiUrl = useApiUrl()

  const {
    tableProps: dueTableProps,
    tableQueryResult: dueQueryResult,
    sorters: dueSorters,
    filters: dueFilters,
  } = useTable<IPaymentList>({
    resource: 'admin/payment-lists',
    syncWithLocation: false,
    filters: {
      permanent: [
        {
          operator: 'or',
          value: [
            {
              field: 'status',
              operator: 'eq',
              value: PaymentListStatus.PENDING_REVIEW,
            },
            {
              field: 'status',
              operator: 'eq',
              value: PaymentListStatus.APPROVED,
            },
          ],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'dueDate',
          order: 'asc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const {
    tableProps: upcomingTableProps,
    tableQueryResult: upcomingQueryResult,
    sorters: upcomingSorters,
  } = useTable<PaymentListUpcomingItem>({
    resource: 'admin/payment-lists/upcoming',
    syncWithLocation: false,
  })

  const {
    tableProps: paidTableProps,
    sorters: paidSorters,
    filters: paidFilters,
  } = useTable<IPaymentList>({
    resource: 'admin/payment-lists',
    syncWithLocation: false,
    filters: {
      permanent: [
        {
          field: 'status',
          operator: 'eq',
          value: PaymentListStatus.PAID,
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'dueDate',
          order: 'asc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const {
    tableProps: problemItemsTableProps,
    tableQueryResult: problemItemsQueryResult,
    sorters: problemItemsSorters,
    filters: problemItemsFilters,
  } = useTable<IPaymentListProblemItem>({
    resource: 'admin/payment-list-problem-items',
    syncWithLocation: false,
    meta: {
      join: [
        {
          field: 'saleItem',
          select: ['id', 'stockId'],
        },
        {
          field: 'saleItem.sale',
          select: ['id', 'saleNumber'],
        },
      ],
    },
    filters: {
      permanent: [
        {
          field: 'isResolved',
          operator: 'eq',
          value: false,
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'dueDate',
          order: 'asc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const dueRecords = dueQueryResult.data?.data ?? []
  const upcomingRecords = upcomingQueryResult.data?.data ?? []
  const problemRecords = problemItemsQueryResult.data?.data ?? []
  const { showUrl, push } = useNavigation()

  const statusColorMap = {
    [PaymentListStatus.APPROVED]: 'processing',
    [PaymentListStatus.PENDING_REVIEW]: 'warning',
    [PaymentListStatus.PAID]: 'success',
    UPCOMING: 'default',
  }

  const statusLabels = {
    [PaymentListStatus.APPROVED]: 'Approved',
    [PaymentListStatus.PENDING_REVIEW]: 'Pending Review',
    [PaymentListStatus.PAID]: 'Paid',
    UPCOMING: 'Upcoming',
  }

  const paymentTypeMap = {
    [PaymentListType.D2C]: 'Direct to customer',
    [PaymentListType.BULK]: 'Bulk',
  } as const

  const paymentTypeColorMap = {
    [PaymentListType.D2C]: 'blue',
    [PaymentListType.BULK]: 'purple',
  } as const

  const [activeTab, setActiveTab] = useState('due')

  const PaymentListsTable = (props: any) => (
    <Table
      {...props.tableProps}
      rowKey="id"
      pagination={{
        ...props.tableProps.pagination,
        showSizeChanger: true,
        showTotal: (total) => `Total ${total} items`,
      }}
      onRow={({ id }) => ({ onClick: () => push(showUrl('admin/payment-lists', id!)) })}
    >
      <Table.Column
        dataIndex="id"
        title="List ID"
        className="max-w-20 cursor-pointer"
        defaultFilteredValue={getDefaultFilter('id', activeTab === 'due' ? dueFilters : paidFilters)}
        filterDropdown={(props) => (
          <FilterDropdown {...props}>
            <Input style={{ minWidth: 200 }} placeholder="Search List ID" allowClear />
          </FilterDropdown>
        )}
      />
      <Table.Column
        dataIndex="type"
        title="Type"
        className="cursor-pointer"
        defaultFilteredValue={getDefaultFilter('type', activeTab === 'due' ? dueFilters : paidFilters)}
        filterDropdown={(props) => (
          <FilterDropdown {...props}>
            <Select
              style={{ minWidth: 200 }}
              placeholder="Type"
              options={[
                { value: PaymentListType.D2C, label: paymentTypeMap[PaymentListType.D2C] },
                { value: PaymentListType.BULK, label: paymentTypeMap[PaymentListType.BULK] },
              ]}
              allowClear
            />
          </FilterDropdown>
        )}
        render={(value) => (
          <Tag color={paymentTypeColorMap[value as PaymentListType]}>{paymentTypeMap[value as PaymentListType]}</Tag>
        )}
      />
      <Table.Column
        dataIndex="amount"
        title="Amount"
        className="cursor-pointer"
        sorter
        defaultSortOrder={getDefaultSortOrder('amount', activeTab === 'due' ? dueSorters : paidSorters)}
        render={(value) => formatMoney(value)}
      />
      <Table.Column
        dataIndex="itemsCount"
        title="Records"
        defaultSortOrder={getDefaultSortOrder('itemsCount', activeTab === 'due' ? dueSorters : paidSorters)}
        sorter
        className="cursor-pointer"
        render={(value) => `${value} payments`}
      />
      <Table.Column
        dataIndex="dueDate"
        title="Due date"
        className="cursor-pointer"
        defaultSortOrder={getDefaultSortOrder('dueDate', activeTab === 'due' ? dueSorters : paidSorters)}
        sorter
        render={(value) => formatDate(value)}
      />
      <Table.Column
        dataIndex="status"
        title="Status"
        className="cursor-pointer"
        {...(activeTab === 'due' && {
          defaultFilteredValue: getDefaultFilter('status', dueFilters),
          filterDropdown: (props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                placeholder="Status"
                options={formatSelectOptionsFromEnum(omit(PaymentListStatus, PaymentListStatus.PAID))}
                allowClear
              />
            </FilterDropdown>
          ),
        })}
        render={(value) => {
          return (
            <Tag color={statusColorMap[value as PaymentListStatus]}>{statusLabels[value as PaymentListStatus]}</Tag>
          )
        }}
      />
      {activeTab === 'paid' && (
        <>
          <Table.Column
            dataIndex="referenceNumber"
            title="Reference number"
            render={(value) => value}
            defaultFilteredValue={getDefaultFilter('referenceNumber', paidFilters)}
            filterDropdown={(props) => (
              <FilterDropdown {...props}>
                <Input style={{ minWidth: 200 }} placeholder="Search Reference Number" allowClear />
              </FilterDropdown>
            )}
          />
          <Table.Column
            dataIndex="paidAt"
            title="Paid at"
            className="cursor-pointer"
            defaultSortOrder={getDefaultSortOrder('paidAt', paidSorters)}
            sorter
            render={(value) => formatDate(value)}
          />
        </>
      )}
      <Table.Column
        title="Actions"
        dataIndex="actions"
        render={(_, record: IPaymentList) => (
          <Space>
            <ShowButton size="small" recordItemId={record.id}>
              Review
            </ShowButton>
          </Space>
        )}
        width="10rem"
        fixed="right"
      />
    </Table>
  )

  const UpcomingTable = () => (
    <Table {...upcomingTableProps} rowKey={(record) => record.date + record.type}>
      <Table.Column
        dataIndex="date"
        title="Date"
        width="10rem"
        sorter
        defaultSortOrder={getDefaultSortOrder('date', upcomingSorters)}
        render={(value) => formatDate(value)}
      />
      <Table.Column
        dataIndex="type"
        title="Type"
        width="10rem"
        render={(value) => (
          <Tag color={paymentTypeColorMap[value as PaymentListType]}>{paymentTypeMap[value as PaymentListType]}</Tag>
        )}
      />
      <Table.Column
        dataIndex="amount"
        title="Amount"
        width="10rem"
        sorter
        defaultSortOrder={getDefaultSortOrder('amount', upcomingSorters)}
        render={(value) => formatMoney(value)}
      />
      <Table.Column
        dataIndex="itemsCount"
        title="Records"
        width="10rem"
        sorter
        defaultSortOrder={getDefaultSortOrder('itemsCount', upcomingSorters)}
        render={(value) => `${value} payments`}
      />
      <Table.Column
        title="Status"
        width="8rem"
        render={() => <Tag color={statusColorMap.UPCOMING}>{statusLabels.UPCOMING}</Tag>}
      />
    </Table>
  )

  const ProblemItemsTable = () => {
    const invalidate = useInvalidate()
    const [form] = Form.useForm()
    const [bankInfoModalVisible, setBankInfoModalVisible] = useState(false)
    const [selectedRecord, setSelectedRecord] = useState<IPaymentListProblemItem | null>(null)
    const { mutate: resolveItem, isLoading: isResolvingItem } = useCustomMutation()
    const { mutate: updateItemAmount, isLoading: isUpdatingItemAmount } = useCustomMutation()

    const handleBankInfoEdit = (record: IPaymentListProblemItem) => {
      setSelectedRecord(record)
      form.setFieldsValue({
        beneficiaryName: record.beneficiaryName,
        iban: record.iban,
      })
      setBankInfoModalVisible(true)
    }

    const { mutate: updateBankInfo, isLoading: isUpdatingBankInfo } = useCustomMutation()

    const handleBankInfoSave = () => {
      form.validateFields().then((values) => {
        if (!selectedRecord?.id) return

        updateBankInfo(
          {
            url: `${apiUrl}/admin/payment-lists/update-bank-info`,
            method: 'post',
            values: {
              id: selectedRecord.id,
              beneficiaryName: values.beneficiaryName,
              iban: values.iban,
            },
            successNotification: {
              message: 'Success',
              description: 'Bank information successfully updated',
              type: 'success',
            },
            errorNotification: {
              message: 'Error',
              description: 'Failed to update bank information',
              type: 'error',
            },
          },
          {
            onSuccess: () => {
              setBankInfoModalVisible(false)
              setSelectedRecord(null)
              form.resetFields()

              // Invalidate the resources to refresh the list
              invalidate({
                resource: 'admin/payment-list-problem-items',
                invalidates: ['list'],
              })

              invalidate({
                resource: 'admin/payment-lists',
                invalidates: ['detail'],
              })
            },
          }
        )
      })
    }

    const handleResolveItem = (id: string) => {
      resolveItem(
        {
          url: `${apiUrl}/admin/payment-lists/resolve-item`,
          method: 'post',
          values: { id },
          successNotification: {
            message: 'Success',
            description: 'Payment issue successfully resolved',
            type: 'success',
          },
          errorNotification: {
            message: 'Error',
            description: 'Failed to resolve payment issue',
            type: 'error',
          },
        },
        {
          onSuccess: () => {
            // Invalidate the resources
            invalidate({
              resource: 'admin/payment-list-problem-items',
              invalidates: ['all'],
            })
            invalidate({
              resource: 'admin/payment-lists/upcoming',
              invalidates: ['all'],
            })
          },
        }
      )
    }

    const handleUpdateItemAmount = (id: string) => {
      updateItemAmount(
        {
          url: `${apiUrl}/admin/payment-lists/update-item-amount`,
          method: 'post',
          values: { id },
          successNotification: {
            message: 'Success',
            description: 'Payment amount successfully updated',
            type: 'success',
          },
          errorNotification: {
            message: 'Error',
            description: 'Failed to update payment amount',
            type: 'error',
          },
        },
        {
          onSuccess: () => {
            // Invalidate the resources
            invalidate({
              resource: 'admin/payment-list-problem-items',
              invalidates: ['list'],
            })
          },
        }
      )
    }

    return (
      <>
        <Table
          {...problemItemsTableProps}
          rowKey="id"
          pagination={{
            ...problemItemsTableProps.pagination,
            showSizeChanger: true,
            showTotal: (total) => `Total ${total} items`,
          }}
        >
          <Table.Column dataIndex="beneficiaryName" title="Beneficiary name" />
          <Table.Column dataIndex="iban" title="IBAN" />
          <Table.Column
            dataIndex="amount"
            title="Amount"
            className="cursor-pointer"
            sorter
            defaultSortOrder={getDefaultSortOrder('amount', problemItemsSorters)}
            render={(value) => formatMoney(value)}
          />
          <Table.Column
            dataIndex="dueDate"
            title="Due date"
            defaultSortOrder={getDefaultSortOrder('dueDate', problemItemsSorters)}
            sorter
            render={(value) => formatDate(value)}
          />
          <Table.Column
            dataIndex={['saleItem', 'sale', 'saleNumber']}
            title="Sale number"
            render={(value, record: IPaymentListProblemItem) => (
              <Link to={`/sales/edit/${record.saleItem?.sale?.id}`} target="_blank">
                {value}
              </Link>
            )}
          />
          <Table.Column
            dataIndex={['saleItem', 'stockId']}
            title="Stock ID"
            render={(value, record: IPaymentListProblemItem) => (
              <Link to={`/sale-items/edit/${record.saleItem?.id}`} target="_blank">
                {value}
              </Link>
            )}
          />
          <Table.Column
            dataIndex="reason"
            title="Reason"
            defaultFilteredValue={getDefaultFilter('reason', problemItemsFilters)}
            filterDropdown={(props) => (
              <FilterDropdown {...props}>
                <Select
                  style={{ minWidth: 200 }}
                  placeholder="Reason"
                  options={formatSelectOptionsFromEnum(PaymentListProblemItemReason)}
                  allowClear
                />
              </FilterDropdown>
            )}
            render={(reason) => capitalize(reason?.replace(/_/g, ' '))}
          />
          <Table.Column dataIndex="note" title="Notes" />
          <Table.Column
            title="Action"
            dataIndex="actions"
            width="20rem"
            fixed="right"
            render={(_, record: IPaymentListProblemItem) => (
              <Space>
                <Popconfirm
                  title="Resolve this payment issue"
                  description={
                    <div className="max-w-64">
                      Make sure all the information is correct before resolving the payment issue. <br />
                      After clicking Confirm it will be added to the next payment list.
                    </div>
                  }
                  okText="Resolve"
                  onConfirm={() => {
                    if (record.id) {
                      handleResolveItem(record.id)
                    }
                  }}
                  onCancel={(e) => {
                    e?.stopPropagation()
                  }}
                >
                  <Button
                    type="primary"
                    size="small"
                    icon={<AiOutlineCheck />}
                    loading={isResolvingItem}
                    onClick={(e) => {
                      e.stopPropagation()
                    }}
                  >
                    Resolve
                  </Button>
                </Popconfirm>
                {record.paymentListType === PaymentListType.D2C && !record.isDonation && (
                  <Button
                    type="default"
                    size="small"
                    icon={<BsBank />}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleBankInfoEdit(record)
                    }}
                  >
                    Edit bank info
                  </Button>
                )}
                <Popconfirm
                  title="Update payment amount"
                  description={
                    <div className="max-w-64">
                      By clicking Update, it will be updated the amount from its related sale item.
                    </div>
                  }
                  onConfirm={() => {
                    if (record.id) {
                      handleUpdateItemAmount(record.id)
                    }
                  }}
                  onCancel={(e) => {
                    e?.stopPropagation()
                  }}
                  okText="Update"
                  cancelText="Cancel"
                >
                  <Button
                    type="default"
                    size="small"
                    icon={<AiOutlineDollar />}
                    loading={isUpdatingItemAmount}
                    onClick={(e) => {
                      e.stopPropagation()
                    }}
                  >
                    Update amount
                  </Button>
                </Popconfirm>
              </Space>
            )}
          />
        </Table>

        <Modal
          title="Edit Bank Information"
          open={bankInfoModalVisible}
          onCancel={() => {
            setBankInfoModalVisible(false)
            setSelectedRecord(null)
            form.resetFields()
          }}
          onOk={handleBankInfoSave}
          okText="Save"
          okButtonProps={{ loading: isUpdatingBankInfo }}
          cancelText="Cancel"
        >
          <Form form={form} layout="vertical" className="!grid-cols-1">
            <Input
              name="beneficiaryName"
              label="Beneficiary Name"
              rules={['required']}
              placeholder="Enter beneficiary name"
              normalize={(value: string) => value?.replace(/\d+/g, '')}
            />

            <Input
              name="iban"
              label="IBAN"
              rules={[
                'required',
                {
                  pattern: /^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$/,
                  message: 'Please enter a valid IBAN format',
                },
              ]}
              placeholder="Enter IBAN"
            />

            {selectedRecord && selectedRecord.saleItem && (
              <Typography.Paragraph>
                To update the amount, go to{' '}
                <Link to={`/sale-items/edit/${selectedRecord.saleItem.id}`} target="_blank">
                  sale item
                </Link>{' '}
                edit page, change the price and click Update amount button.
              </Typography.Paragraph>
            )}
          </Form>
        </Modal>
      </>
    )
  }

  const tabItems: TabsProps['items'] = [
    {
      key: 'due',
      label: `Due Today${dueRecords.length ? ` (${dueRecords.length})` : ''}`,
      children: <PaymentListsTable tableProps={dueTableProps} />,
    },
    {
      key: 'upcoming',
      label: `Upcoming${upcomingRecords.length ? ` (${upcomingRecords.length})` : ''}`,
      children: <UpcomingTable />,
    },
    {
      key: 'problem',
      label: `Problem Payment${problemRecords.length ? ` (${problemRecords.length})` : ''}`,
      children: <ProblemItemsTable />,
    },
    {
      key: 'paid',
      label: 'Paid',
      children: <PaymentListsTable tableProps={paidTableProps} />,
    },
  ]

  return (
    <List>
      <Tabs items={tabItems} activeKey={activeTab} onChange={setActiveTab} />
    </List>
  )
}
