import { MigrationInterface, QueryRunner } from 'typeorm'

export class LicenseplateEntitiesInitial1750848000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

      CREATE TYPE licenseplate_status_enum AS ENUM ('inbound', 'to_inventory', 'picked');

      CREATE TABLE licenseplate_device (
        guid VARCHAR PRIMARY KEY NOT NULL UNIQUE,
        imei VARCHAR,
        serial VARCHAR,
        grade VARCHAR,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW(),
        updated_by VARCHAR
      );

      CREATE TABLE licenseplate_product (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        guid VARCHAR NOT NULL,
        name VARCHAR,
        sku VARCHAR,
        category VARCHAR,
        brand VARCHAR,
        model VARCHAR,
        storage VARCHAR,
        CONSTRAINT fk_product_device FOREIGN KEY (guid) REFERENCES licenseplate_device(guid) ON DELETE CASCADE
      );

      CREATE TABLE licenseplate_sale (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        guid VARCHAR NOT NULL,
        base_sale_price DECIMAL,
        sale_price DECIMAL,
        sale_tax_percentage DECIMAL,
        sale_tax_amount DECIMAL,
        sale_grossmargin_type text NOT NULL,
        sale_currency VARCHAR,
        sale_country VARCHAR,
        sale_date TIMESTAMP,
        sale_channel VARCHAR,
        order_id VARCHAR,
        CONSTRAINT fk_sale_device FOREIGN KEY (guid) REFERENCES licenseplate_device(guid) ON DELETE CASCADE
      );

      CREATE TABLE licenseplate_purchase (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        guid VARCHAR NOT NULL,
        purchase_date TIMESTAMP,
        purchase_country VARCHAR,
        base_purchase_price DECIMAL,
        purchase_price DECIMAL,
        purchase_tax_percentage DECIMAL,
        purchase_tax_amount DECIMAL,
        purchase_grossmargin_type text NOT NULL,
        purchase_currency VARCHAR,
        purchase_channel VARCHAR,
        CONSTRAINT fk_purchase_device FOREIGN KEY (guid) REFERENCES licenseplate_device(guid) ON DELETE CASCADE
      );

      CREATE TABLE licenseplate_status (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        guid VARCHAR NOT NULL,
        status licenseplate_status_enum NOT NULL,
        note TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        CONSTRAINT fk_status_device FOREIGN KEY (guid) REFERENCES licenseplate_device(guid) ON DELETE CASCADE,
        CONSTRAINT unique_device_status UNIQUE (guid, status)
      );
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE IF EXISTS licenseplate_status;
            DROP TABLE IF EXISTS licenseplate_purchase;
            DROP TABLE IF EXISTS licenseplate_sale;
            DROP TABLE IF EXISTS licenseplate_product;
            DROP TABLE IF EXISTS licenseplate_device;
            DROP TYPE IF EXISTS licenseplate_status_enum;
        `)
  }
}
