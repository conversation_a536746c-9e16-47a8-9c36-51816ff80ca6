import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { ChannelEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudChannelService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ChannelEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
  query: {
    join: {
      countries: {},
      currency: {},
      priceThreshold: {},
      sellerPaymentPeriod: {},
    },
  },
})
@Controller('admin/channels')
export class CrudChannelController implements CrudController<ChannelEntity> {
  constructor(public service: CrudChannelService) {}
}
