import { Field, ObjectType } from '@nestjs/graphql'
import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn } from 'typeorm'

@ObjectType('LicensePlateUnmatched')
@Entity('licenseplate_unmatched')
export class LicensePlateUnmatchedEntity extends BaseEntity {
  @Field()
  @PrimaryColumn()
  guid: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  serial_number?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  sku?: string

  @Field({ nullable: true })
  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  base_sale_price?: number

  @Field({ nullable: true })
  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  sale_price?: number

  @Field({ nullable: true })
  @Column({ nullable: true })
  sale_tax_percentage?: number

  @Field({ nullable: true })
  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  sale_tax_amount?: number

  @Field({ nullable: true })
  @Column({ nullable: true })
  taxation?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  sales_currency?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  sales_country?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  sale_date?: Date

  @Field({ nullable: true })
  @Column({ nullable: true })
  sale_processed_data?: Date

  @Field({ nullable: true })
  @Column({ nullable: true })
  sale_channel?: string

  @Field()
  @CreateDateColumn()
  created_at: Date
}
