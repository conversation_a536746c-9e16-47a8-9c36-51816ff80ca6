import { Args, Query, Resolver } from '@nestjs/graphql'

import { GetBannersInput, GetBannersOutput, GetMarketingSkuCollectionInput, GetTagsInput, GetTagsOutput } from '~/dtos'
import { MarketingSkuCollectionEntity } from '~/entities'
import { MarketingService } from '~/services'

@Resolver()
export class MarketingCollectionResolver {
  constructor(private readonly service: MarketingService) {}

  @Query(() => [GetTagsOutput])
  async getBuyerTags(@Args() { channelId }: GetTagsInput) {
    return this.service.getBuyerTags(channelId)
  }

  @Query(() => [GetBannersOutput])
  async getBuyerBanners(@Args() { channelId }: GetBannersInput) {
    return this.service.getBuyerBanners(channelId)
  }

  @Query(() => MarketingSkuCollectionEntity)
  async getMarketingSkuCollection(@Args() input: GetMarketingSkuCollectionInput) {
    return this.service.getMarketingSkuCollection(input)
  }
}
