import { ArgsType, Field, InputType, ObjectType } from '@nestjs/graphql'
import { IsIn, IsNotEmpty } from 'class-validator'

import { LOCALE_ENABLED_LANGUAGES, SLUG_ALLOWED_TYPES } from '~/constants'

@ObjectType()
export class GetTranslatedSlugItems {
  @Field()
  slug__en: string

  @Field()
  slug__nl: string

  @Field()
  slug__de: string

  @Field()
  slug__pl: string
}

@InputType()
class TranslatedSlugInput {
  @Field()
  @IsNotEmpty()
  slug: string

  @Field(() => String, {
    description: 'Allowed type, should be one of ' + SLUG_ALLOWED_TYPES.map((value) => `"${value}"`).join(', '),
  })
  @IsIn(SLUG_ALLOWED_TYPES)
  @IsNotEmpty()
  type: (typeof SLUG_ALLOWED_TYPES)[number]
}

@ArgsType()
export class GetTranslatedSlugsInput {
  @Field(() => String, { description: 'Current language in frontend' })
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  @IsNotEmpty()
  lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @Field(() => [TranslatedSlugInput])
  slugs: TranslatedSlugInput[]
}

@ObjectType('GetTranslatedSlugs')
export class GetTranslatedSlugsOutput {
  @Field(() => GetTranslatedSlugItems, { nullable: true })
  category?: GetTranslatedSlugItems

  @Field(() => GetTranslatedSlugItems, { nullable: true })
  brand?: GetTranslatedSlugItems

  @Field(() => GetTranslatedSlugItems, { nullable: true })
  productSeries?: GetTranslatedSlugItems

  @Field(() => GetTranslatedSlugItems, { nullable: true })
  productModel?: GetTranslatedSlugItems
}
