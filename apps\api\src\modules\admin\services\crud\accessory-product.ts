import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { AccessoryProductEntity } from '~/entities'

export class CrudAccessoryProductService extends TypeOrmCrudService<AccessoryProductEntity> {
  constructor(
    @InjectRepository(AccessoryProductEntity)
    repo: Repository<AccessoryProductEntity>
  ) {
    super(repo)
  }
}
