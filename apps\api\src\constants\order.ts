export enum OrderStatus {
  PENDING_SHIPMENT = 'PENDING_SHIPMENT',
  SHIPPING = 'SHIPPING',
  COMPLETED = 'COMPLETED',
  PENDING_REFUND = 'PENDING_REFUND',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED',
  REFUNDED = 'REFUNDED',
  CANCELLED = 'CANCELLED',
  PROBLEM_EXCHANGED = 'PROBLEM_EXCHANGED',
  PROBLEM_REPAIRED = 'PROBLEM_REPAIRED',
  FRAUDULENT = 'FRAUDULENT',
}

export enum OrderSkuItemStatus {
  COMPLETED = 'COMPLETED',
  PROBLEM_EXCHANGED = 'PROBLEM_EXCHANGED',
  PROBLEM_REPAIRED = 'PROBLEM_REPAIRED',
  NORMAL = 'NORMAL',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
  FRAUDULENT = 'FRAUDULENT',
}

export enum PreOrderStatus {
  PENDING_PAYMENT = 'PENDING_PAYMENT',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
}

export const SUCCESSFUL_ORDER_STATUSES = [
  OrderStatus.PENDING_SHIPMENT,
  OrderStatus.SHIPPING,
  OrderStatus.COMPLETED,
  OrderStatus.PROBLEM_EXCHANGED,
  OrderStatus.PROBLEM_REPAIRED,
]
