import { Args, Info, Query, Resolver } from '@nestjs/graphql'
import { GraphQLResolveInfo } from 'graphql'
import { GraphRelationBuilder } from 'typeorm-relations-graphql'

import { GetBlogPostInput, GetBlogPostOutput, GetBlogPostsOutput } from '~/dtos'
import { BlogPostEntity } from '~/entities'
import { BlogService } from '~/services'

@Resolver()
export class BlogPostResolver {
  constructor(
    private readonly relationBuilder: GraphRelationBuilder,
    private readonly service: BlogService
  ) {}

  @Query(() => [GetBlogPostsOutput])
  async getBlogPosts(@Info() info: GraphQLResolveInfo) {
    const relations = this.relationBuilder.buildForQuery(BlogPostEntity, info).toFindOptionsRelations()

    return this.service.findAll(relations)
  }

  @Query(() => GetBlogPostOutput, { nullable: true })
  async getBlogPost(@Args() { lang, slug }: GetBlogPostInput, @Info() info: GraphQLResolveInfo) {
    const relations = this.relationBuilder.buildForQuery(BlogPostEntity, info).toFindOptionsRelations()

    return this.service.findOne({ lang, slug, relations })
  }
}
