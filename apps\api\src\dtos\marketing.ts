import { ArgsType, Field, ID, ObjectType, PickType } from '@nestjs/graphql'
import { IsIn, IsNotEmpty } from 'class-validator'

import { LOCALE_ENABLED_LANGUAGES, MarketingBannerType, MarketingTagType } from '~/constants'
import { MarketingBannerEntity, MarketingTagEntity } from '~/entities'

@ArgsType()
export class GetTagsInput {
  @Field()
  channelId: string
}

@ObjectType()
export class GetTagsOutput extends PickType(MarketingTagEntity, [
  'image',
  'name__en',
  'name__nl',
  'name__de',
  'name__pl',
  'modelId',
  'categoryId',
  'collectionId',
]) {
  @Field(() => ID, {
    description: 'The ID of the actual content, could be category id, model id or marketing sku collection id',
  })
  id: string

  @Field(() => MarketingTagType)
  type: MarketingTagType

  @Field()
  slug__en: string

  @Field()
  slug__nl: string

  @Field()
  slug__de: string

  @Field()
  slug__pl: string
}

@ArgsType()
export class GetBannersInput {
  @Field()
  channelId: string
}

@ObjectType()
export class GetBannersOutput extends PickType(MarketingBannerEntity, [
  'imageDesktop__en',
  'imageMobile__en',
  'imageDesktop__nl',
  'imageMobile__nl',
  'imageDesktop__de',
  'imageMobile__de',
  'imageDesktop__pl',
  'imageMobile__pl',
  'modelId',
  'categoryId',
  'collectionId',
]) {
  @Field(() => ID, {
    description: 'The ID of the actual content, could be category id, model id or marketing sku collection id',
  })
  id: string

  @Field(() => MarketingBannerType)
  type: MarketingBannerType

  @Field()
  slug__en: string

  @Field()
  slug__nl: string

  @Field()
  slug__de: string

  @Field()
  slug__pl: string
}

@ArgsType()
export class GetMarketingSkuCollectionInput {
  @Field(() => String, { description: 'Current language in frontend' })
  @IsNotEmpty()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @Field({ description: 'The slug from frontend URL' })
  @IsNotEmpty()
  slug: string
}
