import { BadRequestException, NotFoundException } from '@nestjs/common'

import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { ProductVariantEntity, ProductVariantProblemEntity } from '~/entities'
import { SellerConditionQuestionResponse, SellerProblemQuestionResponse } from '~admin/interfaces'

export class SellerQuestionsService {
  async getProblemQuestions({
    variantId,
    channelId,
    lang,
  }: {
    variantId: string
    lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
    channelId: string
  }): Promise<SellerProblemQuestionResponse> {
    const variant = await ProductVariantEntity.findOne({
      where: { id: variantId, problems: { prices: { channelId } }, basePrices: { channelId } },
      relations: {
        basePrices: true,
        problems: {
          problem: true,
          prices: true,
        },
      },
      select: {
        id: true,
        basePrices: {
          id: true,
          basePrice: true,
        },
        problems: {
          id: true,
          prices: {
            id: true,
            priceReduction: true,
          },
          problem: {
            id: true,
            [`name__${lang}`]: true,
            sortOrder: true,
          },
        },
      },
    })

    if (!variant) {
      throw new NotFoundException('Variant not found')
    }

    const basePrice = variant.basePrices?.[0]?.basePrice
    const problems = variant.problems
      ?.sort((a, b) => a.problem?.sortOrder - b.problem?.sortOrder)
      ?.map((variantProblem: Pick<ProductVariantProblemEntity, 'problem' | 'prices' | 'id'>) => {
        const id = variantProblem.problem.id
        const name = variantProblem.problem?.[`name__${lang}`]
        const price = variantProblem?.prices?.[0]
        const priceReduction = price?.priceReduction
        if (!name || !price || priceReduction === undefined) {
          throw new BadRequestException('Problem is missing name, price, or price reduction')
        }
        return {
          id,
          name,
          priceReduction,
        }
      })

    return {
      basePrice,
      problems,
    }
  }
  async getConditionQuestions({
    variantId,
    channelId,
    lang,
  }: {
    variantId: string
    lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
    channelId: string
  }): Promise<SellerConditionQuestionResponse> {
    const variant = await ProductVariantEntity.findOne({
      where: {
        id: variantId,
        conditionCombinations: {
          prices: { channelId },
        },
      },
      relations: {
        model: {
          questionType: {
            conditions: {
              options: true,
            },
          },
        },
        conditionCombinations: {
          choices: true,
          prices: true,
        },
      },
      select: {
        id: true,
        model: {
          id: true,
          questionType: {
            id: true,
            conditions: {
              id: true,
              [`name__${lang}`]: true,
              sortOrder: true,
              options: {
                id: true,
                [`name__${lang}`]: true,
                sortOrder: true,
              },
            },
          },
        },
        conditionCombinations: {
          id: true,
          prices: {
            id: true,
            c2cPrice: true,
            c2bPrice: true,
          },
          choices: {
            conditionId: true,
            optionId: true,
          },
        },
      },
      order: {
        model: {
          questionType: {
            conditions: {
              sortOrder: 'ASC',
              options: {
                sortOrder: 'ASC',
              },
            },
          },
        },
      },
    })

    if (!variant) {
      throw new NotFoundException('Variant not found')
    }

    const questions = variant.model.questionType.conditions.map((condition) => {
      const options = condition.options.map((option) => ({
        id: option.id,
        name: option[`name__${lang}`],
      }))

      const name = condition[`name__${lang}`]
      if (!options || !name) {
        throw new BadRequestException(`Condition is missing name or options: ${name}, ${options}`)
      }
      return {
        id: condition.id,
        name,
        options,
      }
    })

    const combinations = variant.conditionCombinations.map((combination) => {
      const choices = combination.choices.map((choice) => ({
        conditionId: choice.conditionId,
        optionId: choice.optionId,
      }))
      const price = combination.prices?.[0]
      const c2cPrice = price?.c2cPrice
      const c2bPrice = price?.c2bPrice
      if (!price || c2cPrice === undefined || c2bPrice === undefined) {
        throw new BadRequestException(`Combination is missing price or currencyId: ${price}, ${c2cPrice}, ${c2bPrice}`)
      }
      return {
        id: combination.id,
        choices,
        c2cPrice,
        c2bPrice,
      }
    })

    return {
      questions,
      combinations,
    }
  }
}
