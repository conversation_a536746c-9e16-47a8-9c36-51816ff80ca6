# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Blog<PERSON>uthor {
  avatar: Image!
  id: ID!
  name: String!
  posts: [BlogPost!]!

  """
  The title of the author in German, e.g. "CEO"
  """
  title__de: String!

  """
  The title of the author in English, e.g. "CEO"
  """
  title__en: String!

  """
  The title of the author in Dutch, e.g. "CEO"
  """
  title__nl: String!

  """
  The title of the author in Polish, e.g. "CEO"
  """
  title__pl: String!
}

type BlogPost {
  author: BlogAuthor!
  content__de: String!
  content__en: String!
  content__nl: String!
  content__pl: String!
  createdAt: DateTime!

  """Hero image for blog post, also used as thumbnail"""
  heroImage: Image!
  id: ID!

  """Estimated time to read in minutes"""
  minuteRead: Int!
  publishedAt: DateTime!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
  summary__de: String!
  summary__en: String!
  summary__nl: String!
  summary__pl: String!
  tag: BlogTag!
  title__de: String!
  title__en: String!
  title__nl: String!
  title__pl: String!
  updatedAt: DateTime!
}

type BlogTag {
  """The color of the tag"""
  color: BlogTagColor!
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  posts: [BlogPost!]!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

enum BlogTagColor {
  BLUE
  GREEN
  ORANGE
  RED
  YELLOW
}

type Brand {
  id: ID!
  image: Image!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type Category {
  """
  Description text for product with condition A in this category (German)
  """
  gradingADesc__de: String!

  """
  Description text for product with condition A in this category (English)
  """
  gradingADesc__en: String!

  """Description text for product with condition A in this category (Dutch)"""
  gradingADesc__nl: String!

  """
  Description text for product with condition A in this category (Polish)
  """
  gradingADesc__pl: String!

  """
  Description text for product with condition B in this category (German)
  """
  gradingBDesc__de: String!

  """
  Description text for product with condition B in this category (English)
  """
  gradingBDesc__en: String!

  """Description text for product with condition B in this category (Dutch)"""
  gradingBDesc__nl: String!

  """
  Description text for product with condition B in this category (Polish)
  """
  gradingBDesc__pl: String!

  """
  Description text for product with condition C in this category (German)
  """
  gradingCDesc__de: String!

  """
  Description text for product with condition C in this category (English)
  """
  gradingCDesc__en: String!

  """Description text for product with condition C in this category (Dutch)"""
  gradingCDesc__nl: String!

  """
  Description text for product with condition C in this category (Polish)
  """
  gradingCDesc__pl: String!

  """
  Description text for product with condition D in this category (German)
  """
  gradingDDesc__de: String!

  """
  Description text for product with condition D in this category (English)
  """
  gradingDDesc__en: String!

  """Description text for product with condition D in this category (Dutch)"""
  gradingDDesc__nl: String!

  """
  Description text for product with condition D in this category (Polish)
  """
  gradingDDesc__pl: String!
  icon: Image!
  id: ID!
  image: Image!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  productSeries: [ProductSeries!]!

  """Save how many grams of CO2 emission for each purchase"""
  savedCo2: Float!

  """Save how many grams of eWaste for each purchase"""
  savedEwaste: Float!
  seoContent: SeoContent
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type Channel {
  currency: LocaleCurrency!
  desc: String!
  id: ID!
  name: String!
}

type ConfirmSaleItemOffer {
  error: SaleItemOfferErrorType
  success: Boolean!
}

input CreateOrderAddressInput {
  addition: String
  city: String!
  country: String!
  firstName: String!
  houseNumber: String!
  lastName: String!
  phoneAreaCode: String!
  phoneNumber: String!
  postalCode: String!
  street: String!
}

type CreatePaymentIntentOutput {
  amount: Float!
  clientSecret: String!
  paymentIntentId: String!
  success: Boolean!
}

type CreatePreOrderOutput {
  accessoryItems: [OrderCartAccessoryItemOutput!]!
  hasPriceChange: Boolean!
  hasSoldItems: Boolean!
  id: ID
  renewPaymentIntent: Boolean!
  skuItems: [OrderCartSkuItemOutput!]!
  success: Boolean!
  totalAmount: Float!
}

input CreateSaleAddressInput {
  addition: String
  city: String!
  country: String!
  firstName: String!
  houseNumber: String!
  lastName: String!
  phoneAreaCode: String
  phoneNumber: String
  postalCode: String!
  street: String!
}

input CreateSaleBankAccountInput {
  accountNumber: String!
  holderName: String!
}

type CreateSaleOutput {
  createdAt: DateTime
  error: String
  saleId: String
  saleNumber: String
  success: Boolean!
}

type CustomerReview {
  googleScore: Float!
  id: ID!
  trustpilotCount: Int!
  trustpilotScore: Float!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type Faq {
  answer__de: String!
  answer__en: String!
  answer__nl: String!
  answer__pl: String!
  collections: [FaqCollectionType!]!
  id: ID!
  publishedAt: DateTime!
  question__de: String!
  question__en: String!
  question__nl: String!
  question__pl: String!
}

enum FaqCollectionType {
  BEN_ALL
  BEN_MAIN
  BEN_PLAN
  BEN_SUCCESS
  BUYER_MAIN
  SELLER_MAIN
  SELLER_PLAN
}

"""Amazon S3 file"""
type File {
  """File name in German when being downloaded"""
  downloadFilename__de: String

  """File name in English when being downloaded"""
  downloadFilename__en: String

  """File name in Dutch when being downloaded"""
  downloadFilename__nl: String

  """File name in Polish when being downloaded"""
  downloadFilename__pl: String
  id: ID!

  """File's mime type"""
  mimeType: String!

  """File path from AWS S3"""
  path: String!

  """Size of the file in bytes"""
  size: Int!
  url: String!
}

type FrontpageMisc {
  id: ID!
  totalDonation: Float!
  totalMembers: Float!
  totalSavedCo2: Float!
  totalSavedCo2Km: Float!
  totalSavedEwaste: Float!
  totalSavedEwasteElephant: Float!
}

type GetBannersOutput {
  categoryId: String
  collectionId: String

  """
  The ID of the actual content, could be category id, model id or marketing sku collection id
  """
  id: ID!
  imageDesktop__de: Image!
  imageDesktop__en: Image!
  imageDesktop__nl: Image!
  imageDesktop__pl: Image!
  imageMobile__de: Image!
  imageMobile__en: Image!
  imageMobile__nl: Image!
  imageMobile__pl: Image!
  modelId: String
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
  type: MarketingBannerType!
}

type GetBlogPost {
  author: GetBlogPostAuthor!
  content__de: String!
  content__en: String!
  content__nl: String!
  content__pl: String!
  createdAt: DateTime!

  """Hero image for blog post, also used as thumbnail"""
  heroImage: Image!
  id: ID!

  """Estimated time to read in minutes"""
  minuteRead: Int!
  publishedAt: DateTime!
  related: [GetBlogPostRelated!]!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
  summary__de: String!
  summary__en: String!
  summary__nl: String!
  summary__pl: String!
  tag: GetBlogPostTag!
  title__de: String!
  title__en: String!
  title__nl: String!
  title__pl: String!
  updatedAt: DateTime!
}

type GetBlogPostAuthor {
  avatar: Image!
  id: ID!
  name: String!

  """
  The title of the author in German, e.g. "CEO"
  """
  title__de: String!

  """
  The title of the author in English, e.g. "CEO"
  """
  title__en: String!

  """
  The title of the author in Dutch, e.g. "CEO"
  """
  title__nl: String!

  """
  The title of the author in Polish, e.g. "CEO"
  """
  title__pl: String!
}

type GetBlogPostRelated {
  """Hero image for blog post, also used as thumbnail"""
  heroImage: Image!
  id: ID!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
  summary__de: String!
  summary__en: String!
  summary__nl: String!
  summary__pl: String!
  title__de: String!
  title__en: String!
  title__nl: String!
  title__pl: String!
}

type GetBlogPostTag {
  """The color of the tag"""
  color: BlogTagColor!
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type GetBlogPosts {
  createdAt: DateTime!

  """Hero image for blog post, also used as thumbnail"""
  heroImage: Image!
  id: ID!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
  summary__de: String!
  summary__en: String!
  summary__nl: String!
  summary__pl: String!
  tag: GetBlogPostsTag!
  title__de: String!
  title__en: String!
  title__nl: String!
  title__pl: String!
}

type GetBlogPostsTag {
  """The color of the tag"""
  color: BlogTagColor!
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type GetBrands {
  id: ID!
  image: Image!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type GetCategories {
  icon: Image!
  id: ID!
  image: Image!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type GetOrderCartItemPricesOutput {
  accessoryItems: [OrderCartAccessoryItemOutput!]!
  hasPriceChange: Boolean!
  hasSoldItems: Boolean!
  skuItems: [OrderCartSkuItemOutput!]!
  totalAmount: Float!
}

type GetOrderResultAccessoryOutput {
  cartItemId: String!

  """If it's a sku then use sku id, If it's accessory, then accessory id"""
  id: String!
  price: Float!
  quantity: Int!
}

type GetOrderResultOutput {
  accessoryItems: [GetOrderResultAccessoryOutput!]!
  createdAt: DateTime!
  email: String!
  orderNumber: String
  savedCo2: Float!
  savedEwaste: Float!
  skuItems: [GetOrderResultSkuOutput!]!
  total: Float!
}

type GetOrderResultSkuOutput {
  cartItemId: String!

  """If it's a sku then use sku id, If it's accessory, then accessory id"""
  id: String!
  price: Float!
  warrantyItemId: String!
  warrantyPrice: Float!
}

input GetProductModelSellerPricesConditionInput {
  conditionId: String!
  optionId: String!
}

type GetProductModelSellerPricesItemOutput {
  disabled: Boolean!
  paymentPeriodFrom: Int
  paymentPeriodTo: Int!
  paymentPeriodUnit: SellerPaymentPeriodUnit!
  price: Float!
}

type GetProductModelSellerPricesOutput {
  c2b: GetProductModelSellerPricesItemOutput!
  c2c: GetProductModelSellerPricesItemOutput!
  currency: LocaleCurrency!
  recycle: Boolean!
  saved: GetProductModelSellerPricesSavedOutput!
}

type GetProductModelSellerPricesSavedOutput {
  co2: Int!
  ewaste: Int!
  totalC2o: Int!
  totalEwaste: Int!
}

type GetProductModelSellerQuestionsAttributeCombinationOutput {
  choices: JSONObject!
  variantId: String!
}

type GetProductModelSellerQuestionsOutput {
  attributeCombinations: [GetProductModelSellerQuestionsAttributeCombinationOutput!]!
  attributesQuestions: [ProductModelAttribute!]!
  brandSlug__de: String!
  brandSlug__en: String!
  brandSlug__nl: String!
  brandSlug__pl: String!
  categorySlug__de: String!
  categorySlug__en: String!
  categorySlug__nl: String!
  categorySlug__pl: String!
  conditionQuestions: [QuestionTypeCondition!]!
  id: ID!
  image: Image!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  problemImageTexts: [QuestionTypeImageText!]!
  problemQuestions: [QuestionTypeProblem!]!
  recycle: Boolean!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type GetProductSkuAccessoryProductOutput {
  desc__de: String!
  desc__en: String!
  desc__nl: String!
  desc__pl: String!
  heroImage: Image!
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  price: Float!
}

type GetProductSkuAttributeOutput {
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
}

type GetProductSkuOutput {
  attributes: [GetProductSkuAttributeOutput!]!
  brandName__de: String!
  brandName__en: String!
  brandName__nl: String!
  brandName__pl: String!
  brandNewPrice: Float!
  brandSlug__de: String!
  brandSlug__en: String!
  brandSlug__nl: String!
  brandSlug__pl: String!
  categorySlug__de: String!
  categorySlug__en: String!
  categorySlug__nl: String!
  categorySlug__pl: String!
  color: String!
  desc__de: String
  desc__en: String
  desc__nl: String
  desc__pl: String
  extraTestedItems: [ProductSkuExtraTestedItem!]!
  grading: String!

  """
  Description text for product with condition A in this category (German)
  """
  gradingADesc__de: String!

  """
  Description text for product with condition A in this category (English)
  """
  gradingADesc__en: String!

  """Description text for product with condition A in this category (Dutch)"""
  gradingADesc__nl: String!

  """
  Description text for product with condition A in this category (Polish)
  """
  gradingADesc__pl: String!

  """
  Description text for product with condition B in this category (German)
  """
  gradingBDesc__de: String!

  """
  Description text for product with condition B in this category (English)
  """
  gradingBDesc__en: String!

  """Description text for product with condition B in this category (Dutch)"""
  gradingBDesc__nl: String!

  """
  Description text for product with condition B in this category (Polish)
  """
  gradingBDesc__pl: String!

  """
  Description text for product with condition C in this category (German)
  """
  gradingCDesc__de: String!

  """
  Description text for product with condition C in this category (English)
  """
  gradingCDesc__en: String!

  """Description text for product with condition C in this category (Dutch)"""
  gradingCDesc__nl: String!

  """
  Description text for product with condition C in this category (Polish)
  """
  gradingCDesc__pl: String!

  """
  Description text for product with condition D in this category (German)
  """
  gradingDDesc__de: String!

  """
  Description text for product with condition D in this category (English)
  """
  gradingDDesc__en: String!

  """Description text for product with condition D in this category (Dutch)"""
  gradingDDesc__nl: String!

  """
  Description text for product with condition D in this category (Polish)
  """
  gradingDDesc__pl: String!
  heroImage: Image!
  id: ID!
  images: [Image!]!
  isDeal: Boolean!
  modelSlug__de: String!
  modelSlug__en: String!
  modelSlug__nl: String!
  modelSlug__pl: String!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  originalAccessories: [ProductSkuOriginalAccessory!]!
  originalPrice: Float!
  price: Float!
  savedCo2: Float!
  savedEwaste: Float!
  sellerJoinedYear: String!
  slugNumber__de: String!
  slugNumber__en: String!
  slugNumber__nl: String!
  slugNumber__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
  sold: Boolean!
  testReport: File
  testedItemList: TestedItemList!
}

type GetProductSkuWarrantyOutput {
  id: ID!
  isDefault: Boolean!
  label__de: String!
  label__en: String!
  label__nl: String!
  label__pl: String!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  periodNumber: Int!
  periodUnit: WarrantyPeriodUnitType!
  price: Float!
  warrantyId: String!
}

type GetSaleCartItemPricesItemOutput {
  cartItemId: String!
  hasPriceChange: Boolean!
  hasRecycled: Boolean!
  newPrice: Float!
  oldPrice: Float!
}

type GetSaleCartItemPricesOutput {
  hasPriceChange: Boolean!
  hasRecycled: Boolean!
  items: [GetSaleCartItemPricesItemOutput!]!
  totalAmount: Float!
}

type GetSaleItemOfferDetails {
  error: SaleItemOfferErrorType
  result: GetSaleItemOfferDetailsResult
  success: Boolean!
}

type GetSaleItemOfferDetailsResult {
  answers: [String!]!
  attributes: [String!]!
  bankTransferPaymentPeriodFrom: Int
  bankTransferPaymentPeriodTo: Int!
  bankTransferPaymentPeriodUnit: String!
  currency: String!
  donationPaymentPeriodFrom: Int
  donationPaymentPeriodTo: Int!
  donationPaymentPeriodUnit: String!
  image: String!
  isDonation: Boolean!
  name: String!
  offerExpiresAt: DateTime!
  offerStatus: SaleItemOfferStatus!
  plan: SaleItemPlanType!
  price: Float!
  saleNumber: String!
}

input GetSaleUpdatedCartItemPriceItemInput {
  cartItemId: String!

  """Selected conditions of the product, only used when type === CONDITION"""
  conditions: [GetProductModelSellerPricesConditionInput!]
  modelId: String!
  oldPrice: Float!
  plan: SaleItemPlanType!

  """Selected problems of the product, only used when type === PROBLEM"""
  problems: [String!]
  type: ProductModelSellerQuestionType!
  variantId: String!
}

type GetSeoContentOutput {
  footer__de: [SeoFooterContent!]
  footer__en: [SeoFooterContent!]
  footer__nl: [SeoFooterContent!]
  footer__pl: [SeoFooterContent!]
  header__de: String
  header__en: String
  header__nl: String
  header__pl: String
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
}

type GetTagsOutput {
  categoryId: String
  collectionId: String

  """
  The ID of the actual content, could be category id, model id or marketing sku collection id
  """
  id: ID!
  image: Image!
  modelId: String
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
  type: MarketingTagType!
}

type GetTranslatedSlugItems {
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

"""Cloudinary image"""
type Image {
  """Image format"""
  format: String!

  """Image original height in pixels"""
  height: Int!
  id: ID!

  """Image's alternative text in German, can be used as alt prop"""
  name__de: String

  """Image's alternative text in English, can be used as alt prop"""
  name__en: String

  """Image's alternative text in Dutch, can be used as alt prop"""
  name__nl: String

  """Image's alternative text in Polish, can be used as alt prop"""
  name__pl: String

  """Public ID from Cloudinary"""
  publicId: String!
  url: String!

  """Image original width in pixels"""
  width: Int!
}

"""
The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSONObject

type LocaleCountry {
  channels: [Channel!]!
  createdAt: DateTime!
  id: ID!
  languages: [LocaleLanguage!]!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  updatedAt: DateTime!
}

type LocaleCurrency {
  id: ID!
  name: String!
  symbol: String!
}

type LocaleLanguage {
  countries: [LocaleCountry!]!
  createdAt: DateTime!
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  updatedAt: DateTime!
}

enum MarketingBannerType {
  CATEGORY
  MARKETING_SKU_COLLECTION
  MODEL
}

type MarketingSkuCollection {
  headerBgImageDesktop__de: Image!
  headerBgImageDesktop__en: Image!
  headerBgImageDesktop__nl: Image!
  headerBgImageDesktop__pl: Image!
  headerBgImageMobile__de: Image!
  headerBgImageMobile__en: Image!
  headerBgImageMobile__nl: Image!
  headerBgImageMobile__pl: Image!
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

enum MarketingTagType {
  CATEGORY
  MARKETING_SKU_COLLECTION
  MODEL
}

type Mutation {
  confirmSaleItemOffer(authToken: String!, confirmationType: SaleItemOfferConfirmationType!, offerId: String!): ConfirmSaleItemOffer!
  createPaymentIntent(accessoryItems: [OrderCartAccessoryItemInput!]!, channelId: String!, language: String!, skuItems: [OrderCartSkuItemInput!]!): CreatePaymentIntentOutput!
  createPreOrder(
    accessoryItems: [OrderCartAccessoryItemInput!]!
    anonymousId: String!
    billingAddress: CreateOrderAddressInput!
    channelId: String!
    clientSecret: String!
    email: String!

    """
    Where the user comes from, default to UserFrom.VALYUU, it's only going to be changed when the user is from an affiliated link, like abnambro.valyuu.com
    """
    from: UserFrom!
    language: String
    shippingAddress: CreateOrderAddressInput!
    skuItems: [OrderCartSkuItemInput!]!
    toPickupPoint: Boolean!
  ): CreatePreOrderOutput!
  createProductIssueCategory(email: String!, issue: String!): ProductIssue!
  createProductIssueModelQuestion(email: String!, issue: String!, lang: String!, modelSlug: String!): ProductIssue!
  createProductIssueNoSearchResult(
    email: String!
    issue: String!
    keyword: String!

    """Only SELLER_SEARCH or BUYER_SEARCH allowed"""
    type: ProductIssueType!
  ): ProductIssue!
  createSale(
    address: CreateSaleAddressInput!
    anonymousId: String!
    bankAccount: CreateSaleBankAccountInput
    channelId: String!
    dateOfBirth: String
    email: String!

    """
    Where the user comes from, default to UserFrom.VALYUU, it's only going to be changed when the user is from an affiliated link, like abnambro.valyuu.com
    """
    from: UserFrom!
    items: [SaleCartItemInput!]!
    language: String!
    paymentType: SalePaymentType!
    shippingLabel: SaleShippingLabelTypeType!
  ): CreateSaleOutput!
}

input OrderCartAccessoryItemInput {
  cartItemId: String!

  """If it's a sku then use sku id, If it's accessory, then accessory id"""
  id: String!
  price: Float!
  quantity: Int!
}

type OrderCartAccessoryItemOutput {
  cartItemId: String!
  hasPriceChange: Boolean!
  newPrice: Float!
  oldPrice: Float!
  quantity: Float!
  sold: Boolean!
}

input OrderCartSkuItemInput {
  cartItemId: String!

  """If it's a sku then use sku id, If it's accessory, then accessory id"""
  id: String!
  price: Float!
  warrantyItemId: String!
  warrantyPrice: Float!
}

type OrderCartSkuItemOutput {
  cartItemId: String!
  hasPriceChange: Boolean!
  id: String!
  newPrice: Float!
  oldPrice: Float!
  sold: Boolean!
  warrantyHasPriceChange: Boolean!
  warrantyItemId: String!
  warrantyNewPrice: Float!
  warrantyOldPrice: Float!
}

type ProductIssue {
  email: String!
  id: ID!
  issue: String!
  type: ProductIssueType!
}

enum ProductIssueType {
  BUYER_SEARCH
  SELLER_CATEGORY
  SELLER_MODEL_QUESTION
  SELLER_SEARCH
}

type ProductModel {
  attributes: [ProductModelAttribute!]!
  id: ID!
  image: Image!
  nameIncludesBrandName: Boolean!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  recycle: Boolean!
  releaseDate: DateTime!
  seoContent: SeoContent
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type ProductModelAttribute {
  desc__de: String
  desc__en: String
  desc__nl: String
  desc__pl: String
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  options: [ProductModelAttributeOption!]!
  question__de: String!
  question__en: String!
  question__nl: String!
  question__pl: String!
}

type ProductModelAttributeOption {
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
}

enum ProductModelSellerQuestionType {
  CONDITION
  PROBLEM
}

type ProductSeries {
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type ProductSkuExtraAttribute {
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  type: ProductSkuExtraAttributeType!
}

enum ProductSkuExtraAttributeType {
  KEYBOARD
  OTHER
}

enum ProductSkuExtraFieldType {
  BATTERY
  DEFECTS
  OTHER
}

type ProductSkuExtraTestedItem {
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  type: ProductSkuExtraFieldType!
}

type ProductSkuOriginalAccessory {
  desc__de: String!
  desc__en: String!
  desc__nl: String!
  desc__pl: String!
  displayInResults: Boolean!
  highlight__de: String
  highlight__en: String
  highlight__nl: String
  highlight__pl: String
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  thumbnail: Image!
}

type ProductVariant {
  basePrices: [ProductVariantBasePrice!]!
  conditionCombinations: [ProductVariantConditionCombination!]!
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  problems: [ProductVariantConditionCombination!]!
  slug__de: String!
  slug__en: String!
  slug__nl: String!
  slug__pl: String!
}

type ProductVariantBasePrice {
  basePrice: Float!
  currency: LocaleCurrency!
  id: ID!
  variant: ProductVariant!
}

type ProductVariantConditionCombination {
  id: ID!
}

type Query {
  getBlogPost(
    """Current language in frontend"""
    lang: String!

    """The slug from frontend URL"""
    slug: String!
  ): GetBlogPost
  getBlogPosts: [GetBlogPosts!]!
  getBrands: [GetBrands!]!
  getBrandsByCategory(
    """Current language in frontend"""
    lang: String!

    """The slug from frontend URL"""
    slug: String!
  ): [GetBrands!]!
  getBuyerBanners(channelId: String!): [GetBannersOutput!]!
  getBuyerTags(channelId: String!): [GetTagsOutput!]!
  getCategories: [GetCategories!]!
  getChannelByCountry(countryCode: String!): Channel!
  getCustomerReview: CustomerReview!
  getFaqs(
    """FAQ collection type"""
    collection: FaqCollectionType!
  ): [Faq!]!
  getFrontpageMisc: FrontpageMisc!
  getMarketingSkuCollection(
    """Current language in frontend"""
    lang: String!

    """The slug from frontend URL"""
    slug: String!
  ): MarketingSkuCollection!
  getOrderCartItemPrices(accessoryItems: [OrderCartAccessoryItemInput!]!, channelId: String!, skuItems: [OrderCartSkuItemInput!]!): GetOrderCartItemPricesOutput!
  getOrderResult(paymentIntent: String!): GetOrderResultOutput!
  getProductModelSellerPrices(
    channelId: String!

    """Selected conditions of the product, only used when type === CONDITION"""
    conditions: [GetProductModelSellerPricesConditionInput!]
    modelId: String!

    """Selected problems of the product, only used when type === PROBLEM"""
    problems: [String!]
    type: ProductModelSellerQuestionType!
    variantId: String!
  ): GetProductModelSellerPricesOutput!
  getProductModelSellerQuestions(
    """Current language in frontend"""
    lang: String!

    """The slug from frontend URL"""
    slug: String!
  ): GetProductModelSellerQuestionsOutput!
  getProductSku(
    channelId: String!

    """Current language in frontend"""
    lang: String!

    """The slug from frontend URL"""
    slug: String!

    """The slug number from frontend URL"""
    slugNumber: String!
  ): GetProductSkuOutput!
  getProductSkuAccessoryProducts(
    channelId: String!

    """Current language in frontend"""
    lang: String!

    """The slug from frontend URL"""
    slug: String!

    """The slug number from frontend URL"""
    slugNumber: String!
  ): [GetProductSkuAccessoryProductOutput!]!
  getProductSkuAccessoryProductsBySkuIds(channelId: String!, skuIds: [String!]!): [GetProductSkuAccessoryProductOutput!]!
  getProductSkuWarranties(
    channelId: String!

    """Current language in frontend"""
    lang: String!

    """The slug from frontend URL"""
    slug: String!

    """The slug number from frontend URL"""
    slugNumber: String!
  ): [GetProductSkuWarrantyOutput!]!
  getSaleCartItemPrices(channelId: String!, items: [GetSaleUpdatedCartItemPriceItemInput!]!): GetSaleCartItemPricesOutput!
  getSaleItemOfferDetails(
    """Login key for offer access"""
    authToken: String!

    """Unique identifier of the offer"""
    offerId: String!
  ): GetSaleItemOfferDetails!
  getSeoContent(
    """Current language in frontend"""
    lang: String!

    """The slug from frontend URL"""
    slug: String!
    type: SeoContentType!
  ): GetSeoContentOutput
  getTestimonials: [Testimonial!]!
  validateSellerInfo(accountNumber: String!, addition: String, channelId: String!, city: String!, country: String!, dateOfBirth: String, email: String!, firstName: String!, holderName: String!, houseNumber: String!, lastName: String!, phoneAreaCode: String, phoneNumber: String, postalCode: String!, street: String!): ValidateSellerInfoOutput!
}

type QuestionTypeCondition {
  desc__de: String
  desc__en: String
  desc__nl: String
  desc__pl: String
  id: ID!
  key: String!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  options: [QuestionTypeConditionOption!]!
  question__de: String!
  question__en: String!
  question__nl: String!
  question__pl: String!
}

type QuestionTypeConditionOption {
  desc__de: String
  desc__en: String
  desc__nl: String
  desc__pl: String
  id: ID!
  key: String!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
  percentOfChoice: Float
}

type QuestionTypeImageText {
  id: ID!
  image: Image!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
}

type QuestionTypeProblem {
  desc__de: String
  desc__en: String
  desc__nl: String
  desc__pl: String
  id: ID!
  key: String!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
}

input SaleCartItemInput {
  """Selected conditions of the product, only used when type === CONDITION"""
  conditions: [GetProductModelSellerPricesConditionInput!]
  createdAt: DateTime!
  id: String!
  modelId: String!
  plan: SaleItemPlanType!
  price: Float!

  """Selected problems of the product, only used when type === PROBLEM"""
  problems: [String!]
  type: ProductModelSellerQuestionType!
  variantId: String!
}

enum SaleItemOfferConfirmationType {
  ACCEPT
  DECLINE_RECYCLE
  DECLINE_RETURN
}

enum SaleItemOfferErrorType {
  ALREADY_ACCEPTED
  ALREADY_REJECTED
  EXPIRED
  NOT_AUTHORIZED
  NOT_FOUND
}

enum SaleItemOfferStatus {
  ACCEPTED
  DECLINED_RECYCLE
  DECLINED_RETURN
  EXPIRED
  INITIAL
  PENDING
}

enum SaleItemPlanType {
  C2B
  C2C
}

enum SalePaymentType {
  BANK_TRANSFER
  BULK_SETTLEMENT
  DONATION
}

enum SaleShippingLabelTypeType {
  EMAIL
  POST
}

enum SellerPaymentPeriodUnit {
  DAYS
  HOURS
}

type SeoContent {
  footer__de: [SeoFooterContent!]
  footer__en: [SeoFooterContent!]
  footer__nl: [SeoFooterContent!]
  footer__pl: [SeoFooterContent!]
  header__de: String
  header__en: String
  header__nl: String
  header__pl: String
  id: ID!
  type: SeoContentType!
}

enum SeoContentType {
  CATEGORY
  MODEL
}

type SeoFooterContent {
  content: String!
  title: String!
}

type TestedItem {
  id: ID!
  name__de: String!
  name__en: String!
  name__nl: String!
  name__pl: String!
}

type TestedItemList {
  id: ID!
  items: [TestedItem!]!
  totalItemsCount: Float!
}

type Testimonial {
  date: DateTime!
  id: ID!
  name: String!
  review: String!
  role: TestimonialRoleType!
}

enum TestimonialRoleType {
  BUYER
  CHARITY
  PARTNER
  REFURBISHER
  SELLER
}

enum UserFrom {
  ABNAMRO
  BEN
  BUNDLES
  EWASTERACE
  PARTNER
  SURFSPOT
  VALYUU
}

type ValidateSellerInfoOutput {
  errorMessage: String
  field: String
  success: Boolean!
}

enum WarrantyPeriodUnitType {
  DAY
  LIFETIME
  MONTH
  YEAR
}