import { Create, useForm } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import { TestimonialRole } from '@valyuu/api/constants'
import type { ITestimonial } from '@valyuu/api/entities'
import { Form } from 'antd'
import { DatePicker, Input, InputNumber, Select, TextArea } from 'antx'
import dayjs from 'dayjs'
import { FC, useMemo } from 'react'
import { v4 as uuid } from 'uuid'

import { InputPublish } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const TestimonialCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<ITestimonial, HttpError, ITestimonial>()
  const id = useMemo(() => uuid(), [])

  const publishedAt = Form.useWatch('publishedAt', form)

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form {...formProps} layout="vertical">
        <Input noStyle hidden name="id" initialValue={id} />
        <Input label="Customer name" name="name" rules={['required']} />
        <Select
          label="Role"
          name="role"
          style={{ minWidth: 200 }}
          options={formatSelectOptionsFromEnum(TestimonialRole)}
          placeholder="Role"
          rules={['required']}
        />
        <TextArea label="Review" name="review" rules={['required']} />
        <DatePicker
          label="Date of review"
          name="date"
          format="DD-MM-YYYY HH:mm"
          maxDate={dayjs(new Date())}
          showTime
          rules={['required']}
        />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
