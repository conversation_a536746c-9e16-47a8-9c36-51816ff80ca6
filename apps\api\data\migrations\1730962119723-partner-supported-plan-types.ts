import { MigrationInterface, QueryRunner } from 'typeorm'

export class PartnerSupportedPlanTypes1730962119723 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if table exists
    const hasTable = await queryRunner.hasTable('partner')

    if (!hasTable) {
      await queryRunner.query(`
        CREATE TABLE "public"."partner" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
          "channel_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
          "created_at" timestamp(6) NOT NULL DEFAULT now(),
          "updated_at" timestamp(6) NOT NULL DEFAULT now(),
          "secret" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'::text),
          "manage_customer_emails" bool NOT NULL DEFAULT true,
          "api_endpoint" varchar COLLATE "pg_catalog"."default",
          "contact_email" varchar COLLATE "pg_catalog"."default",
          "supported_plan_types" varchar[] COLLATE "pg_catalog"."default" NOT NULL DEFAULT '{}'::character varying[]
        )
      `)
    } else {
      // Check if column exists
      const hasColumn = await queryRunner.hasColumn('partner', 'supported_plan_types')

      if (!hasColumn) {
        // Add the column
        await queryRunner.query(`
          ALTER TABLE "partner" 
          ADD COLUMN "supported_plan_types" varchar[] 
          COLLATE "pg_catalog"."default" 
          NOT NULL 
          DEFAULT '{}'::character varying[]
        `)
      }
    }

    // Update existing records to have ['C2B']
    await queryRunner.query(`
      UPDATE "partner"
      SET "supported_plan_types" = '{C2B}'
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add rollback logic if needed
    await queryRunner.query(`
      ALTER TABLE "partner"
      DROP COLUMN IF EXISTS "supported_plan_types"
    `)
  }
}
