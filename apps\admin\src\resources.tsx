import { ResourceProps } from '@refinedev/core'
import { AiOutlineException, AiOutlineGlobal, AiOutlineTag, AiOutlineUser } from 'react-icons/ai'
import { BsCashStack } from 'react-icons/bs'
import { GiReceiveMoney } from 'react-icons/gi'
import { MdOutlineLocalShipping } from 'react-icons/md'
import { PiArticleLight } from 'react-icons/pi'
import { RiAdminLine, RiAdvertisementLine, RiTeamLine } from 'react-icons/ri'

import * as pages from '~/pages'

export const pageResources: ResourceProps[] = [
  {
    name: 'product',
    options: {
      label: 'Products',
    },
    icon: <AiOutlineTag />,
  },
  {
    name: 'order',
    options: {
      label: 'Orders',
    },
    icon: <GiReceiveMoney />,
  },
  {
    name: 'customer',
    options: {
      label: 'Customers',
    },
    icon: <AiOutlineUser />,
  },
  {
    name: 'logistics',
    options: {
      label: 'Logistics',
    },
    icon: <MdOutlineLocalShipping />,
  },
  {
    name: 'channel',
    options: {
      label: 'Channels & Partners',
    },
    icon: <RiTeamLine />,
  },
  {
    name: 'finance',
    options: {
      label: 'Finance',
    },
    icon: <BsCashStack />,
  },
  {
    name: 'content',
    options: {
      label: 'Content',
    },
    icon: <PiArticleLight />,
  },
  {
    name: 'marketing',
    options: {
      label: 'Marketing',
    },
    icon: <RiAdvertisementLine />,
  },
  {
    name: 'admin',
    options: {
      label: 'Admin',
    },
    icon: <RiAdminLine />,
  },
  {
    name: 'log',
    options: {
      label: 'Logs',
    },
    icon: <AiOutlineException />,
  },
  {
    name: 'localization',
    options: {
      label: 'Localization',
    },
    icon: <AiOutlineGlobal />,
  },
]

Object.entries(pages)
  .filter(([key]) => key.endsWith('Page'))
  .forEach(([, page]) => {
    if (!page.withLayout) {
      return
    }
    pageResources.push({
      name: page.resource ?? 'admin/' + page.path,
      options: {
        label: page.label,
      },
      ...(page.index ? { list: `/${page.path}` } : {}),
      ...(page.create ? { create: `/${page.path}/create` } : {}),
      ...(page.edit ? { edit: `/${page.path}/edit/:id` } : {}),
      ...(page.show ? { show: `/${page.path}/:id` } : {}),
      meta: {
        label: page.label,
        ...(page.parent ? { parent: page.parent } : {}),
      },
    })
  })
