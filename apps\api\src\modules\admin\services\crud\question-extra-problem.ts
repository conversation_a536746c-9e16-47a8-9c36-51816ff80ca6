import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { QuestionExtraProblemEntity } from '~/entities'

export class CrudQuestionExtraProblemService extends TypeOrmCrudService<QuestionExtraProblemEntity> {
  constructor(
    @InjectRepository(QuestionExtraProblemEntity)
    repo: Repository<QuestionExtraProblemEntity>
  ) {
    super(repo)
  }
}
