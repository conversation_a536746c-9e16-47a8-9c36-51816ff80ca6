import { Injectable } from '@nestjs/common'
import { Cron } from '@nestjs/schedule'
import * as currency from 'currency.js'
import { endOfDay, format, isWeekend } from 'date-fns'
import { groupBy } from 'lodash'
import * as pMap from 'p-map'
import { In, IsNull, LessThanOrEqual } from 'typeorm'

import {
  PaymentListProblemItemReason,
  PaymentListStatus,
  PaymentListType,
  SaleItemStatus,
  SalePaymentType,
} from '~/constants'
import {
  CharityEntity,
  PaymentListBulkItemEntity,
  PaymentListEntity,
  PaymentListIndividualItemEntity,
  PaymentListProblemItemEntity,
  SaleItemEntity,
} from '~/entities'

const PAYMENT_LIST_BATCH_SIZE = 1000

@Injectable()
export class PaymentListService {
  /**
   * Cron job that runs at 0:10 (10 minutes past midnight) each day
   */
  @Cron('0 10 0 * * *')
  async generatePaymentList(runDate = new Date()) {
    if (isWeekend(runDate)) {
      return
    }
    const endOfToday = endOfDay(runDate)

    const charity = await CharityEntity.findOneOrFail({
      where: {},
      relations: {
        bankAccount: true,
      },
    })

    await PaymentListEntity.getRepository().manager.transaction(async (manager) => {
      // Individual payment list

      const individualItems = await manager.find(SaleItemEntity, {
        where: {
          payoutDueDate: LessThanOrEqual(endOfToday),
          status: SaleItemStatus.PENDING_PAYMENT,
          paymentListIndividualItemId: IsNull(),
          paymentListBulkItemId: IsNull(),
          sale: {
            paymentType: In([SalePaymentType.BANK_TRANSFER, SalePaymentType.DONATION]),
          },
        },
        relations: {
          sale: {
            bankAccount: true,
          },
        },
      })

      // filter out items with empty bankAccount
      for (let i = individualItems.length - 1; i >= 0; i--) {
        if (individualItems[i].sale?.paymentType === SalePaymentType.DONATION) {
          continue
        }
        if (!individualItems[i].sale?.bankAccount?.holderName || !individualItems[i].sale?.bankAccount?.accountNumber) {
          const [removedItem] = individualItems.splice(i, 1)
          await manager.save(
            PaymentListProblemItemEntity,
            manager.create(PaymentListProblemItemEntity, {
              beneficiaryName: removedItem?.sale?.bankAccount?.holderName,
              iban: removedItem?.sale?.bankAccount?.accountNumber,
              reason: PaymentListProblemItemReason.MISSING_BANK_INFO,
              isDonation: false,
              amount: removedItem.price,
              dueDate: removedItem.payoutDueDate,
              paymentListType: PaymentListType.D2C,
              saleItemId: removedItem.id,
            })
          )
        }
      }

      // If there are no items, skip
      if (individualItems.length) {
        const batchCount = Math.ceil(individualItems.length / PAYMENT_LIST_BATCH_SIZE)

        for (let i = 1; i <= batchCount; i++) {
          const batchItems = individualItems.slice((i - 1) * PAYMENT_LIST_BATCH_SIZE, i * PAYMENT_LIST_BATCH_SIZE)

          // Create payment list
          const idPrefix = 'D2C-' + format(runDate, 'yyyyMMdd') + '-'
          let seqNum = i
          let idExists = true
          let paymentListId = ''

          // Keep incrementing until we find an unused ID
          while (idExists) {
            paymentListId = `${idPrefix}${seqNum}`

            // Check if this ID exists
            const existedPaymentList = await manager.findOne(PaymentListEntity, {
              where: { id: paymentListId },
              select: { id: true },
            })

            if (!existedPaymentList) {
              idExists = false // Found an available ID
            } else {
              seqNum++ // Try next number
            }
          }
          const paymentList = manager.create(PaymentListEntity, {
            id: paymentListId,
            type: PaymentListType.D2C,
            dueDate: endOfToday,
            amount: batchItems.reduce((acc, item) => currency(acc).add(item.price).value, 0),
            itemsCount: batchItems.length,
            status: PaymentListStatus.PENDING_REVIEW,
          })
          await manager.save(paymentList)

          // Create payment list items
          for (const item of batchItems) {
            const isDonation = item.sale.paymentType === SalePaymentType.DONATION
            const paymentListIndividualItem = await manager.save(
              PaymentListIndividualItemEntity,
              manager.create(PaymentListIndividualItemEntity, {
                dueDate: item.payoutDueDate,
                partnerId: item.sale.partnerId,
                beneficiaryName: isDonation ? charity.bankAccount.holderName : item.sale.bankAccount.holderName,
                iban: isDonation ? charity.bankAccount.accountNumber : item.sale.bankAccount.accountNumber,
                amount: item.price,
                isDonation,
                saleItem: item,
                paymentList,
              })
            )

            item.status = SaleItemStatus.PAYMENT_IN_PROCESS
            item.paymentListIndividualItem = paymentListIndividualItem
            await manager.save(item)
          }
        }
      }

      // Bulk payment list

      const bulkItems = await manager.find(SaleItemEntity, {
        where: {
          payoutDueDate: LessThanOrEqual(endOfToday),
          status: SaleItemStatus.PENDING_PAYMENT,
          paymentListIndividualItemId: IsNull(),
          paymentListBulkItemId: IsNull(),
          sale: {
            paymentType: SalePaymentType.BULK_SETTLEMENT,
          },
        },
        relations: {
          sale: true,
          partner: {
            bankAccount: true,
          },
        },
      })

      // If there are no items, skip
      if (bulkItems.length) {
        // group items by partner
        const partnerItemsArray = Object.entries(groupBy(bulkItems, 'partnerId'))
        // Batch bulk items for payment lists
        const batchCount = Math.ceil(partnerItemsArray.length / PAYMENT_LIST_BATCH_SIZE)

        for (let i = 1; i <= batchCount; i++) {
          const partners = partnerItemsArray.slice((i - 1) * PAYMENT_LIST_BATCH_SIZE, i * PAYMENT_LIST_BATCH_SIZE)
          const idPrefix = 'BULK-' + format(runDate, 'yyyyMMdd') + '-'
          let seqNum = i
          let idExists = true
          let paymentListId = ''

          // Keep incrementing until we find an unused ID
          while (idExists) {
            paymentListId = `${idPrefix}${seqNum}`

            // Check if this ID exists
            const existedPaymentList = await manager.findOne(PaymentListEntity, {
              where: { id: paymentListId },
              select: { id: true },
            })

            if (!existedPaymentList) {
              idExists = false // Found an available ID
            } else {
              seqNum++ // Try next number
            }
          }
          // Create payment list
          const amount = partners.reduce(
            (accPartner, [, saleItems]) =>
              currency(accPartner).add(
                saleItems.reduce((accItem, saleItem) => currency(accItem).add(saleItem.price).value, 0)
              ).value,
            0
          )
          const paymentList = manager.create(PaymentListEntity, {
            id: paymentListId,
            type: PaymentListType.BULK,
            dueDate: endOfToday,
            amount,
            itemsCount: partners.reduce((acc, [, items]) => acc + items.length, 0),
            status: PaymentListStatus.PENDING_REVIEW,
          })
          await manager.save(paymentList)

          // Create bulk items for each partner
          for (const [partnerId, saleItems] of partners) {
            const paymentListBulkItem = await manager.save(
              PaymentListBulkItemEntity,
              manager.create(PaymentListBulkItemEntity, {
                dueDate: endOfToday,
                beneficiaryName: saleItems[0].partner.bankAccount.holderName,
                iban: saleItems[0].partner.bankAccount.accountNumber,
                amount: saleItems.reduce((acc, item) => currency(acc).add(item.price).value, 0),
                itemsCount: saleItems.length,
                partnerId,
                saleItems,
                paymentList,
              })
            )

            // Update sale items status
            await pMap(
              saleItems,
              async (item) => {
                item.status = SaleItemStatus.PAYMENT_IN_PROCESS
                item.paymentListBulkItem = paymentListBulkItem
                await manager.save(item)
              },
              { concurrency: 5 }
            )
          }
        }
      }
    })
  }
}
