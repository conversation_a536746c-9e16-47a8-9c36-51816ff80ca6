import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PaymentListProblemItemEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudPaymentListProblemItemService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: PaymentListProblemItemEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      saleItem: {},
      'saleItem.sale': {},
    },
  },
})
@Controller('admin/payment-list-problem-items')
export class CrudPaymentListProblemItemsController implements CrudController<PaymentListProblemItemEntity> {
  constructor(public service: CrudPaymentListProblemItemService) {}
}
