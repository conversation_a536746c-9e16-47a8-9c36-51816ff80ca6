import { ArgsType, Field } from '@nestjs/graphql'
import { Transform } from 'class-transformer'
import { IsEmail, IsNotEmpty, MaxLength, MinLength } from 'class-validator'

@ArgsType()
export class UserCreateInput {
  @IsNotEmpty()
  @IsEmail()
  @Transform(({ value }) => value?.trim(), { toClassOnly: true })
  @Field({ description: 'User email' })
  email: string

  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(255)
  @Field({ description: 'User password, minimum 8 characters' })
  password: string
}
