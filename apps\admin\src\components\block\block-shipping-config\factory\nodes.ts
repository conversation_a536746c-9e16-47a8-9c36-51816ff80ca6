import { ILocaleCountry, IWarehouse } from '@valyuu/api/entities'
import { Node, Position } from '@xyflow/react'

import { countryToEmoji } from '~/utils'

import { ExtendedNode } from '../interfaces'

export const createFlowNodes = (
  buyerCountries: ILocaleCountry[],
  sellerCountries: ILocaleCountry[],
  warehouses: IWarehouse[]
) => {
  const groupNodes: Node[] = [
    ...(sellerCountries.length > 0
      ? [
          {
            id: 'sellers-group',
            type: 'groupWithLabel',
            data: { label: 'Sellers from' },
            position: { x: 0, y: 0 },
            width: 260,
            height: Math.max(sellerCountries.length * 80 + 100, 300),
            zIndex: -1,
            className: 'bg-[#f6ffed99] border-2 border-solid border-[#b7eb8f] rounded-md',
            draggable: false,
            selectable: false,
          },
        ]
      : []),
    {
      id: 'warehouse-group',
      type: 'groupWithLabel',
      data: { label: 'Valyuu warehouses' },
      position: { x: 400, y: 0 },
      width: 260,
      height: Math.max(warehouses.length * 80 + 100, 300),
      zIndex: -1,
      className: 'bg-[#bfdbfe33] border-2 border-solid border-[#bfdbfe] rounded-md',
      draggable: false,
      selectable: false,
    },
    ...(buyerCountries.length > 0
      ? [
          {
            id: 'buyers-group',
            type: 'groupWithLabel',
            data: { label: 'Buyers from' },
            position: { x: 800, y: 0 },
            width: 260,
            height: Math.max(buyerCountries.length * 80 + 100, 300),
            zIndex: -1,
            className: 'bg-[#fffbeb99] border-2 border-solid border-[#ffd666] rounded-md',
            draggable: false,
            selectable: false,
          },
        ]
      : []),
  ]

  const warehouseNodes: ExtendedNode[] = warehouses
    .filter(
      (warehouse) =>
        warehouse.receivingFromCountries.some(({ id }) => sellerCountries.find((country) => country.id === id)) ||
        warehouse.shippingToCountries.some(({ id }) => buyerCountries.find((country) => country.id === id))
    )
    .map((warehouse, index) => ({
      id: `warehouse-${warehouse.id}`,
      type: 'warehouse',
      data: {
        label: `${countryToEmoji(warehouse.countryId)} ${warehouse.companyName}`,
        hasBuyerCountries: warehouse.shippingToCountries.length > 0 && buyerCountries.length > 0,
        hasSellerCountries: warehouse.receivingFromCountries.length > 0 && sellerCountries.length > 0,
        nodeType: 'warehouse',
        warehouseId: warehouse.id,
        countryId: warehouse.countryId,
      },
      position: { x: 40, y: 95 + index * 120 },
      width: 180,
      extent: 'parent',
      parentId: 'warehouse-group',
      sourcePosition: Position.Right,
      targetPosition: Position.Left,
      draggable: false,
      selectable: false,
      className: 'bg-white rounded-md p-4 border-2 border-gray-200',
    }))

  const buyersNodes: ExtendedNode[] = buyerCountries.map((country, index) => ({
    id: `buyers-${country.id}`,
    type: 'output',
    data: {
      label: `${countryToEmoji(country.id)} ${country.name__en}`,
      nodeType: 'customer',
      countryId: country.id,
    },
    position: { x: 40, y: 95 + index * 80 },
    width: 180,
    extent: 'parent',
    parentId: 'buyers-group',
    targetPosition: Position.Left,
    draggable: false,
    selectable: false,
    className: 'bg-white rounded-md p-4 border-2 border-gray-200',
  }))

  const sellersNodes: ExtendedNode[] = sellerCountries.map((country, index) => ({
    id: `sellers-${country.id}`,
    type: 'input',
    data: {
      label: `${countryToEmoji(country.id)} ${country.name__en}`,
      nodeType: 'customer',
      countryId: country.id,
    },
    position: { x: 40, y: 95 + index * 80 },
    width: 180,
    extent: 'parent',
    parentId: 'sellers-group',
    sourcePosition: Position.Right,
    draggable: false,
    selectable: false,
    className: 'bg-white rounded-md p-4 border-2 border-gray-200',
  }))

  return [...groupNodes, ...warehouseNodes, ...buyersNodes, ...sellersNodes]
}
