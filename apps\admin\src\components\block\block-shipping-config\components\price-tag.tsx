import { useApiUrl, useCustom } from '@refinedev/core'
import { Spin, Tag } from 'antd'

import { formatMoney } from '~/utils'

type IGetShippingMethodPriceResult = {
  price: string | null
  currency: string | null
  to_country: string
  breakdown: {
    type: string
    label: string
    value: number
  }[]
}

type PriceTagProps = {
  methodId: string
  fromCountry: string
  toCountry: string
}

export const PriceTag = ({ methodId, fromCountry, toCountry }: PriceTagProps) => {
  const apiUrl = useApiUrl()
  const { data, isLoading } = useCustom<IGetShippingMethodPriceResult>({
    url: `${apiUrl}/admin/shipping-methods/price`,
    method: 'get',
    config: {
      query: {
        method_id: methodId,
        from_country: fromCountry,
        to_country: toCountry,
      },
    },
    queryOptions: {
      enabled: !!methodId && !!fromCountry && !!toCountry,
    },
  })

  const priceData = data?.data ?? null

  if (isLoading) {
    return (
      <Tag>
        <Spin size="small" className="scale-50" />
      </Tag>
    )
  }

  if (!priceData?.price || !priceData?.currency) {
    return null
  }

  const formattedPrice = formatMoney(Number(priceData.price), priceData.currency)

  const breakdownContent = priceData.breakdown
    .map((item) => `${item.label}: ${formatMoney(item.value, priceData.currency!)}`)
    .join('\n')

  return (
    <Tag color="purple" title={breakdownContent}>
      {formattedPrice}
    </Tag>
  )
}
