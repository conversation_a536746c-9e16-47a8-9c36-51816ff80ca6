import { ProCard } from '@ant-design/pro-components'
import ProForm, { ProFormList } from '@ant-design/pro-form'
import { Edit, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useApiUrl, useList, useUpdate } from '@refinedev/core'
import {
  FileUsedInType,
  ImageProcessNameType,
  ImageUsedInType,
  ProductSkuBtwType,
  ProductSkuColor,
  ProductSkuExtraAttributeType,
  ProductSkuExtraTestedItemType,
  ProductSkuGrading,
  ProductSkuSourceType,
} from '@valyuu/api/constants'
import type { IChannel, IProductSku, IProductSkuOriginalAccessory, IProductVariant } from '@valyuu/api/entities'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Button, Space, Typography } from 'antd'
import { Input, InputNumber, RadioGroup, Select, Switch, TextArea, Watch } from 'antx'
import cx from 'clsx'
import { cloneDeep } from 'lodash'
import { type FC, Fragment, useCallback, useEffect, useLayoutEffect, useState } from 'react'
import { AiOutlineGlobal } from 'react-icons/ai'
import { v4 as uuid } from 'uuid'

import {
  InputFile,
  InputImage,
  InputLanguageTab,
  InputMpn,
  InputMultiEntitySelect,
  InputMultiLang,
  InputPublish,
  InputSlug,
  LanguageTabChoices,
} from '~/components'
import { formatCurrencySymbol, formatSelectOptionsFromEnum } from '~/utils'

export const ProductSkuEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, onFinish, id, saveButtonProps, query, formLoading } = useForm<
    IProductSku,
    HttpError,
    IProductSku
  >({
    meta: {
      join: [
        {
          field: 'prices',
        },
        {
          field: 'heroImage',
        },
        {
          field: 'images',
        },
        {
          field: 'testReport',
        },
        {
          field: 'variant',
          select: ['name__en'],
        },
        {
          field: 'originalAccessories',
          select: ['id'],
        },
      ],
    },
  })
  const record = query?.data?.data

  const { selectProps: variantSelectProps } = useSelect<IProductVariant>({
    resource: 'admin/product-variants',
    optionLabel: 'name__en',
    optionValue: 'id',
    filters: [{ field: 'publishedAt', operator: 'nnull', value: true }],
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'server',
    },
    debounce: 500,
    onSearch: (value) => [
      {
        field: 'name__en',
        operator: 'contains',
        value,
      },
    ],
  })

  useLayoutEffect(() => {
    if (record && variantSelectProps) {
      variantSelectProps.onSearch?.(record.variant.name__en)
    }
  }, [record, variantSelectProps])

  const { selectProps: originalAccessorySelectProps } = useSelect<IProductSkuOriginalAccessory>({
    resource: 'admin/original-accessories',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  if (formProps.initialValues?.publishedAt) {
    formProps.initialValues.publishedAt = new Date(formProps.initialValues.publishedAt)
  }
  const { mutate } = useUpdate<IProductSku, HttpError, Partial<IProductSku>>()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const apiUrl = useApiUrl()

  const { data: { data: channels = [] } = {}, isLoading: channelLoading } = useList<IChannel, HttpError, IChannel>({
    resource: 'admin/channels',
    filters: [{ field: 'disableBuyer', operator: 'eq', value: false }],
    pagination: { mode: 'off' },
  })

  const [priceItems, setPriceItems] = useState<IProductSku['prices']>([])

  useEffect(() => {
    if (channels.length) {
      const prices = cloneDeep((form.getFieldValue('prices') as IProductSku['prices']) ?? [])
      setPriceItems(
        channels.map((channel) => {
          const price = prices.find((price) => price.channelId === channel.id)
          if (!price) {
            prices.push({ id: uuid(), channelId: channel.id, currencyId: channel.currencyId })
          }
          return {
            id: price?.id ?? uuid(),
            desc: channel.desc,
            channelId: channel.id,
            currencyId: channel.currencyId,
            currencySymbol: formatCurrencySymbol(channel.currencyId),
          }
        })
      )
      form.setFieldValue('prices', prices)
    }
  }, [form, formProps?.initialValues, channels])

  const handleVariantChange = useCallback(
    async (variantId: string) => {
      if (variantId) {
        const { data: variant } = await axios.get<IProductVariant>(`admin/product-variants/${variantId}`, {
          baseURL: apiUrl,
          params: {
            fields: 'id,name__en,name__nl,name__de,name__pl,model',
            'join[0]': 'model||id,categoryId',
          },
        })
        if (variant) {
          form.setFieldsValue({
            name__en: variant.name__en,
            name__nl: variant.name__nl,
            name__de: variant.name__de,
          })
          const categoryId = variant.model?.categoryId
          if (categoryId) {
            const { data: category } = await axios.get(`admin/categories/${categoryId}`, {
              baseURL: apiUrl,
              params: {
                fields: 'id,defaultOriginalAccessories',
                'join[0]': 'defaultOriginalAccessories||id',
              },
            })
            if (category?.defaultOriginalAccessories) {
              form.setFieldValue('originalAccessories', category.defaultOriginalAccessories)
            }
          }
        }
      }
    },
    [form, apiUrl]
  )

  return (
    <Edit
      isLoading={formLoading || channelLoading}
      saveButtonProps={saveButtonProps}
      headerButtons={({ defaultButtons }) => (
        <Space>
          {defaultButtons}
          {record ? (
            <Button
              icon={
                <span className="anticon">
                  <AiOutlineGlobal />
                </span>
              }
              onClick={() => {
                window.open(
                  `${import.meta.env.VITE_WEB_URL as string}/en/buy/${record.slug__en}/${record.slugNumber__en}`,
                  '_blank'
                )
              }}
            >
              Go to website
            </Button>
          ) : null}
        </Space>
      )}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!record?.publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
            mutate({
              resource: 'admin/product-skus',
              id: id! as string,
              values: {
                publishedAt: value ? new Date() : null,
              },
              successNotification: (data) => {
                return {
                  type: 'success',
                  message: `Product series has been ${value ? 'published' : 'unpublished'} successfully`,
                }
              },
              errorNotification: () => {
                return {
                  type: 'error',
                  message: `Failed to ${value ? 'publish' : 'unpublish'} product series`,
                }
              },
            })
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <ProForm
        {...formProps}
        layout="vertical"
        onFinish={async (values) => {
          onFinish?.(values)
        }}
        submitter={false}
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        {priceItems.map((channel, index) => {
          return (
            <Fragment key={channel.channelId}>
              <Input type="hidden" name={['prices', index, 'id']} hidden noStyle />
              <Input type="hidden" name={['prices', index, 'channelId']} hidden noStyle />
              <Input type="hidden" name={['prices', index, 'currencyId']} hidden noStyle />
              <InputNumber
                label={
                  <span>
                    Price for channel{' '}
                    <span className="underline decoration-dashed" title={channel.desc}>
                      {channel.channelId}
                    </span>
                  </span>
                }
                name={['prices', index, 'price']}
                prefix={channel.currencySymbol}
                precision={2}
                min={0}
                controls={false}
                rules={['required']}
                required
              />
              <InputNumber
                label={
                  <span>
                    Original price for channel{' '}
                    <span className="underline decoration-dashed" title={channel.desc}>
                      {channel.channelId}
                    </span>
                  </span>
                }
                name={['prices', index, 'originalPrice']}
                prefix={channel.currencySymbol}
                precision={2}
                min={0}
                controls={false}
                rules={['required']}
                required
              />
              <InputNumber
                label={
                  <span>
                    Brand new price for channel{' '}
                    <span className="underline decoration-dashed" title={channel.desc}>
                      {channel.channelId}
                    </span>
                  </span>
                }
                name={['prices', index, 'brandNewPrice']}
                prefix={channel.currencySymbol}
                precision={2}
                min={0}
                controls={false}
                rules={['required']}
                required
              />
              <RadioGroup
                label={
                  <span>
                    Is deal for channel{' '}
                    <span className="underline decoration-dashed" title={channel.desc}>
                      {channel.channelId}
                    </span>
                  </span>
                }
                name={['prices', index, 'isDeal']}
                options={[
                  { label: 'Yes', value: true },
                  { label: 'No', value: false },
                ]}
                rules={['required']}
              />
              <InputNumber
                label={
                  <span>
                    Margin for channel{' '}
                    <span className="underline decoration-dashed" title={channel.desc}>
                      {channel.channelId}
                    </span>
                  </span>
                }
                name={['prices', index, 'margin']}
                prefix={channel.currencySymbol}
                precision={2}
                min={0}
                controls={false}
                rules={['required']}
                required
              />
            </Fragment>
          )
        })}
        <TextArea label="Note" name="note" className="col-span-2" />
        <Select
          {...(variantSelectProps as any)}
          label="Product variant"
          name="variantId"
          rules={['required']}
          onChange={handleVariantChange}
        />
        <Input label="Stock ID" name="stockId" disabled />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputImage
                label="Hero image"
                name="heroImage"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.PRODUCT_SKU}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputFile
                label="Test report"
                name="testReport"
                limit={1}
                entityId={id! as string}
                entityType={FileUsedInType.PRODUCT_SKU}
                allowedFileTypes={['application/pdf']}
                defaultNames={{
                  downloadFilename__en: name__en + ' test report.pdf',
                  downloadFilename__nl: name__nl + ' testrapport.pdf',
                  downloadFilename__de: name__de + ' Testbericht.pdf',
                  downloadFilename__pl: name__pl + ' raport testowy.pdf',
                }}
              />
              <InputImage
                label="Product photos"
                name="images"
                rules={[{ required: true, message: 'Please upload an image' }]}
                className="col-span-2"
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.PRODUCT_SKU}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />

              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Polish)"
                name="slug__pl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__pl}
                className={clsHidePl}
              />
            </>
          )}
        </Watch>
        <Watch list={['desc__en', 'desc__nl', 'desc__de', 'desc__pl']}>
          {([desc__en, desc__nl, desc__de, desc__pl]) => (
            <>
              <InputMultiLang
                element="textarea"
                sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                targetLang="en"
                label="Description (English)"
                name="desc__en"
                className={cx(clsHideEn, 'col-span-2')}
                fillType="translate"
              />
              <InputMultiLang
                element="textarea"
                sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                targetLang="nl"
                label="Description (Dutch)"
                name="desc__nl"
                className={cx(clsHideNl, 'col-span-2')}
                fillType="translate"
              />
              <InputMultiLang
                element="textarea"
                sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                targetLang="de"
                label="Description (German)"
                name="desc__de"
                className={cx(clsHideDe, 'col-span-2')}
                fillType="translate"
              />
              <InputMultiLang
                element="textarea"
                sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                targetLang="pl"
                label="Description (Polish)"
                name="desc__pl"
                className={cx(clsHidePl, 'col-span-2')}
                fillType="translate"
              />
            </>
          )}
        </Watch>
        <Select
          label="Color"
          name="color"
          options={formatSelectOptionsFromEnum(ProductSkuColor)}
          rules={['required']}
        />
        <Watch list={['displayColor__en', 'displayColor__nl', 'displayColor__de', 'displayColor__pl']}>
          {([displayColor__en, displayColor__nl, displayColor__de, displayColor__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: displayColor__en, nl: displayColor__nl, de: displayColor__de, pl: displayColor__pl }}
                targetLang="en"
                label="Display color (English)"
                name="displayColor__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: displayColor__en, nl: displayColor__nl, de: displayColor__de, pl: displayColor__pl }}
                targetLang="nl"
                label="Display color (Dutch)"
                name="displayColor__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: displayColor__en, nl: displayColor__nl, de: displayColor__de, pl: displayColor__pl }}
                targetLang="de"
                label="Display color (German)"
                name="displayColor__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: displayColor__en, nl: displayColor__nl, de: displayColor__de, pl: displayColor__pl }}
                targetLang="pl"
                label="Display color (Polish)"
                name="displayColor__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
            </>
          )}
        </Watch>
        <Select
          label="Grading"
          name="grading"
          options={formatSelectOptionsFromEnum(ProductSkuGrading)}
          rules={['required']}
        />
        <RadioGroup
          label="Is sold"
          name="sold"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
          disabled
        />
        <RadioGroup
          label="Is internal"
          name="isInternal"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
          help="If it is set to Yes, when this item is sold, no money will be transferred to the C2C seller."
        />
        <Select
          label="Source"
          name="source"
          options={Object.values(ProductSkuSourceType).map((value) => ({
            label: value.replace(/_/g, '-').toLowerCase(),
            value,
          }))}
          rules={['required']}
          disabled
        />
        <Switch label="Exclude from bestseller" name="excludeFromBestseller" />
        <Switch label="Exclude from MVP" name="excludeFromMvp" />
        <Input label="Serial number" name="serialNumber" rules={['required']} />
        <Watch name="variantId">
          {(variantId) => (
            <InputMpn
              label="Model Part Number (MPN)"
              name="mpn"
              variantName={variantSelectProps?.options?.find(({ value }) => value === variantId)?.label as string}
              rules={['required']}
            />
          )}
        </Watch>
        <Select label="BTW" name="btw" options={formatSelectOptionsFromEnum(ProductSkuBtwType)} rules={['required']} />
        <InputMultiEntitySelect
          label="Original accessories / packing"
          name="originalAccessories"
          rules={['required']}
          {...(originalAccessorySelectProps as any)}
        />
        <ProFormList
          actionGuard={{
            beforeAddRow: async (defaultValue) => {
              defaultValue.id = uuid()
              return true
            },
          }}
          name="extraAttributes"
          label="Extra attributes"
          creatorButtonProps={{
            creatorButtonText: 'Add a new extra attribute',
          }}
          copyIconProps={{
            tooltipText: 'Duplicate this extra attribute',
          }}
          deleteIconProps={{
            tooltipText: 'Delete this extra attribute',
          }}
          itemRender={({ listDom, action }, { index }) => {
            return (
              <ProCard
                bordered
                extra={action}
                className="mb-3"
                title={<span className="list-item-title">Extra attribute {index + 1}</span>}
              >
                {listDom}
              </ProCard>
            )
          }}
        >
          {({ name: extraAttributeName }) => {
            return (
              <>
                <Select
                  label="Type"
                  name={[extraAttributeName, 'type']}
                  options={formatSelectOptionsFromEnum(ProductSkuExtraAttributeType)}
                  rules={['required']}
                />
                <Watch
                  list={[
                    ['extraAttributes', extraAttributeName, 'name__en'],
                    ['extraAttributes', extraAttributeName, 'name__nl'],
                    ['extraAttributes', extraAttributeName, 'name__de'],
                    ['extraAttributes', extraAttributeName, 'name__pl'],
                  ]}
                >
                  {([name__en, name__nl, name__de, name__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="en"
                        label="Name (English)"
                        name={[extraAttributeName, 'name__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="nl"
                        label="Name (Dutch)"
                        name={[extraAttributeName, 'name__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="de"
                        label="Name (German)"
                        name={[extraAttributeName, 'name__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="pl"
                        label="Name (Polish)"
                        name={[extraAttributeName, 'name__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
              </>
            )
          }}
        </ProFormList>
        <ProFormList
          actionGuard={{
            beforeAddRow: async (defaultValue) => {
              defaultValue.id = uuid()
              return true
            },
          }}
          name="extraTestedItems"
          label="Extra tested items"
          creatorButtonProps={{
            creatorButtonText: 'Add a new extra tested item',
          }}
          copyIconProps={{
            tooltipText: 'Duplicate this extra tested item',
          }}
          deleteIconProps={{
            tooltipText: 'Delete this extra tested item',
          }}
          itemRender={({ listDom, action }, { index }) => {
            return (
              <ProCard
                bordered
                extra={action}
                className="mb-3"
                title={<span className="list-item-title">Extra tested item {index + 1}</span>}
              >
                {listDom}
              </ProCard>
            )
          }}
        >
          {({ name: extraTestedItemName }) => {
            return (
              <>
                <Select
                  label="Type"
                  name={[extraTestedItemName, 'type']}
                  options={formatSelectOptionsFromEnum(ProductSkuExtraTestedItemType)}
                  rules={['required']}
                />
                <Watch
                  list={[
                    ['extraTestedItems', extraTestedItemName, 'name__en'],
                    ['extraTestedItems', extraTestedItemName, 'name__nl'],
                    ['extraTestedItems', extraTestedItemName, 'name__de'],
                    ['extraTestedItems', extraTestedItemName, 'name__pl'],
                  ]}
                >
                  {([name__en, name__nl, name__de, name__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="en"
                        label="Name (English)"
                        name={[extraTestedItemName, 'name__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="nl"
                        label="Name (Dutch)"
                        name={[extraTestedItemName, 'name__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="de"
                        label="Name (German)"
                        name={[extraTestedItemName, 'name__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="pl"
                        label="Name (Polish)"
                        name={[extraTestedItemName, 'name__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
              </>
            )
          }}
        </ProFormList>

        <Input noStyle type="datetime" name="publishedAt" hidden />
      </ProForm>
    </Edit>
  )
}
