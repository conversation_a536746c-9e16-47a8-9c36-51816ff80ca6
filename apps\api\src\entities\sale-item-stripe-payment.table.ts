import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { SaleItemPaymentStatus } from '~/constants'
import {
  BankAccountEntity,
  ChannelEntity,
  type IBankAccount,
  type ILocaleCurrency,
  type IOrder,
  type ISaleItem,
  type IUser,
  LocaleCurrencyEntity,
  OrderEntity,
  SaleItemEntity,
  UserEntity,
} from '~/entities'

@Entity('sale_item_stripe_payment')
export class SaleItemStripePaymentEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Column({ type: 'varchar' })
  status: SaleItemPaymentStatus

  @Column({
    type: 'decimal',
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  amount: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((payment: SaleItemStripePaymentEntity) => payment.channel)
  channelId: string

  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((threshold: SaleItemStripePaymentEntity) => threshold.currency)
  currencyId: string

  @Column({ nullable: false })
  availableOn: Date

  @Column({ nullable: false })
  stripeTransfer: string

  @Column({ default: 0, unsigned: true })
  retries: number

  @ManyToOne(() => SaleItemEntity, (saleItem) => saleItem.stripePayments, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  saleItem: Relation<SaleItemEntity>

  @Column()
  @RelationId((payment: SaleItemStripePaymentEntity) => payment.saleItem)
  saleItemId: string

  @ManyToOne(() => BankAccountEntity, (bankAccount) => bankAccount.payments, {
    nullable: false,
  })
  toBankAccount: Relation<BankAccountEntity>

  @Column()
  @RelationId((payment: SaleItemStripePaymentEntity) => payment.toBankAccount)
  toBankAccountId: string

  @ManyToOne(() => UserEntity, {
    nullable: false,
  })
  toUser: Relation<UserEntity>

  @Column()
  @RelationId((payment: SaleItemStripePaymentEntity) => payment.toUser)
  toUserId: string

  @ManyToOne(() => OrderEntity, {
    nullable: false,
  })
  order: Relation<OrderEntity>

  @Column()
  @RelationId((payment: SaleItemStripePaymentEntity) => payment.order)
  orderId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ISaleItemStripePayment = Omit<SaleItemStripePaymentEntity, keyof BaseEntity> & {
  currency: ILocaleCurrency
  bankAccount: IBankAccount
  saleItem: ISaleItem
  user: IUser
  order: IOrder
}
