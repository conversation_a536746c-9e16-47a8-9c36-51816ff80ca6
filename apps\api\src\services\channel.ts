import { Injectable } from '@nestjs/common'
import ms from 'ms'
import { IsNull, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { ChannelType } from '~/constants'
import { ChannelEntity } from '~/entities'

@Injectable()
export class ChannelService {
  async findOneByCountry(countryCode: string) {
    return ChannelEntity.findOne({
      where: { countries: { id: countryCode }, publishedAt: Not(IsNull()), type: ChannelType.REGION },
      relations: { currency: true },
      cache: envConfig.isProd ? ms('30 minutes') : false,
    })
  }

  async findCurrencyByChannel(channelId: string) {
    const channel = await ChannelEntity.findOne({
      where: { id: channelId, publishedAt: Not(IsNull()) },
      relations: { currency: true },
      cache: envConfig.isProd ? ms('30 minutes') : false,
    })
    return channel?.currency ?? null
  }
}
