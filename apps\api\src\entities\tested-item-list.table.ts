import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { type IProductSeries, type ITestedItem, ProductSeriesEntity, TestedItemEntity } from '~/entities'

@ObjectType('TestedItemList')
@Entity('tested_item_list')
export class TestedItemListEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  internalName: string

  @OneToMany(() => ProductSeriesEntity, (series) => series.testComponentList)
  productSeries: Relation<ProductSeriesEntity>[]

  @Field(() => [TestedItemEntity])
  @ManyToMany(() => TestedItemEntity, (item) => item.lists, { nullable: false, cascade: true, onDelete: 'CASCADE' })
  @JoinTable({ name: 'tested_item_list_items' })
  items: Relation<TestedItemEntity>[]

  @Field()
  @Column('int2')
  totalItemsCount: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ITestedItemList = Omit<TestedItemListEntity, keyof BaseEntity> & {
  productSeries: IProductSeries[]
  items: ITestedItem[]
}
