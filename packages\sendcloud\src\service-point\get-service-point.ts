import { sendcloudClientV2 } from '../client'
import { type IServicePoint } from '../interfaces'

type IGetServicePointParams = {
  servicePointId: number
}

/**
 * Retrieves a specific service point by ID from Sendcloud
 * @param params - Parameters for getting a service point
 * @returns Promise containing the service point data
 */
export const getServicePoint = async ({ servicePointId }: IGetServicePointParams): Promise<IServicePoint> => {
  const { data } = await sendcloudClientV2.get(`/service-points/${servicePointId}`)
  return data.service_point as IServicePoint
}
