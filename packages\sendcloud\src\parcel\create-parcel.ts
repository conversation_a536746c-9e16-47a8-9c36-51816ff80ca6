import { sendcloudClientV2 } from '../client'
import { IParcelCreation, IParcelObject } from '../interfaces'

export const createParcel = async (parcel: Omit<IParcelCreation, 'id'>): Promise<IParcelObject> => {
  // Validate required fields for all parcels
  const requiredFields = ['name', 'address', 'house_number', 'city', 'postal_code', 'email', 'country', 'shipment']

  const missingFields = requiredFields.filter((field) => !parcel[field as keyof typeof parcel])

  if (missingFields.length > 0) {
    throw new Error(`Missing required fields: ${missingFields.join(', ')}`)
  }

  // Validate return fields if is_return is true
  if (parcel?.is_return) {
    const requiredFromFields = [
      'from_name',
      'from_address_1',
      'from_house_number',
      'from_city',
      'from_postal_code',
      'from_email',
      'from_country',
    ]

    const missingFields = requiredFromFields.filter((field) => !parcel[field as keyof typeof parcel])

    if (missingFields.length > 0) {
      throw new Error(`Missing required return fields: ${missingFields.join(', ')}`)
    }
  }

  const response = await sendcloudClientV2.post('/parcels', { parcel })
  return response.data.parcel
}
