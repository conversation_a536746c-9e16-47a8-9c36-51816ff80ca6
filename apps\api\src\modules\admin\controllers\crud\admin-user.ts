import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { AdminUserEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudAdminUserService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: AdminUserEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
})
@Controller('admin/admin-users')
export class CrudAdminUserController implements CrudController<AdminUserEntity> {
  constructor(public service: CrudAdminUserService) {}
}
