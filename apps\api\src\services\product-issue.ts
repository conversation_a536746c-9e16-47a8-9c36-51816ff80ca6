import { Injectable, NotFoundException } from '@nestjs/common'

import { LOCALE_ENABLED_LANGUAGES, ProductIssueType } from '~/constants'
import { ProductIssueEntity, ProductModelEntity } from '~/entities'

@Injectable()
export class ProductIssueService {
  async create({
    email,
    type,
    productIssue,
    keyword,
    lang,
    slug,
  }: {
    email: string
    type: ProductIssueType
    productIssue: string
    keyword?: string
    lang?: string
    slug?: string
  }) {
    let issue = productIssue
    switch (type) {
      case ProductIssueType.SELLER_SEARCH:
        issue = `${productIssue}\r\n\r\nKeyword: ${keyword}`
        break
      case ProductIssueType.SELLER_MODEL_QUESTION: {
        const { name__en } = await ProductModelEntity.findOne({
          where: Array.from(LOCALE_ENABLED_LANGUAGES)
            .sort((l) => (l === lang ? -1 : 1))
            .map((l) => ({ [`slug__${l}`]: slug })),
          select: ['name__en', 'name__pl'],
        })
        if (!name__en) {
          throw new NotFoundException('Model not found')
        }
        issue = `${productIssue}\r\n\r\nModel: ${name__en}`
        break
      }
      case ProductIssueType.BUYER_SEARCH:
        issue = `${productIssue}\r\n\r\nKeyword: ${keyword}`
        break
      default:
        issue = `${productIssue}`
        break
    }
    return await ProductIssueEntity.create({
      email,
      type,
      issue,
    }).save()
  }
}
