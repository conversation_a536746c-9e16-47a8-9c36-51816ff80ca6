import { Field, ID, ObjectType } from '@nestjs/graphql'
import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  Unique,
  UpdateDateColumn,
} from 'typeorm'

import {
  ChannelEntity,
  type IChannel,
  type ILocaleCurrency,
  type IProductVariant,
  LocaleCurrencyEntity,
  ProductVariantEntity,
} from '~/entities'

@ObjectType('ProductVariantBasePrice')
@Entity('product_variant_base_price')
@Unique(['channelId', 'variantId'])
export class ProductVariantBasePriceEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  basePrice: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Index()
  @Column()
  @RelationId((problem: ProductVariantBasePriceEntity) => problem.channel)
  channelId: string

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: ProductVariantBasePriceEntity) => item.currency)
  currencyId: string

  @Field(() => ProductVariantEntity)
  @ManyToOne(() => ProductVariantEntity, { nullable: false, onDelete: 'CASCADE' })
  variant: Relation<ProductVariantEntity>

  @Column()
  @RelationId((problem: ProductVariantBasePriceEntity) => problem.variant)
  variantId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductVariantBasePrice = Omit<ProductVariantBasePriceEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
  variant: IProductVariant
}
