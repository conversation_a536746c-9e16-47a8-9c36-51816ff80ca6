import { Field, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm'

import { LicensePlateGrossMarginEnum } from '~/constants/licenseplate-grossmargin'

import { LicensePlateDeviceEntity } from './licenseplate-device.table'

@ObjectType('LicensePlatePurchase')
@Entity('licenseplate_purchase')
export class LicensePlatePurchaseEntity extends BaseEntity {
  @Field()
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  guid: string

  @Column({ type: 'timestamp', nullable: true })
  purchase_date?: Date

  @Column({ nullable: true })
  purchase_country?: string

  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  base_purchase_price?: number

  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  purchase_price?: number

  @Column('decimal', { nullable: true })
  purchase_tax_percentage?: number

  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  purchase_tax_amount?: number

  @Field(() => LicensePlateGrossMarginEnum)
  @Column({ type: 'text', nullable: true })
  purchase_grossmargin_type: LicensePlateGrossMarginEnum

  @Column({ nullable: true })
  purchase_currency?: string

  @Column({ nullable: true })
  purchase_channel?: string

  @OneToOne(() => LicensePlateDeviceEntity, (device) => device.product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'guid', referencedColumnName: 'guid' })
  device: LicensePlateDeviceEntity
}

export type ILicensePlatePurchase = Omit<LicensePlatePurchaseEntity, keyof BaseEntity> & {
  device: LicensePlateDeviceEntity
}
