import { IParcelCarrierErrors } from './parcel-carrier-errors'
import { IParcelDocumentObject } from './parcel-document-object'
import { IParcelItem } from './parcel-item'
import { IParcelShipment } from './parcel-shipment'
import { IParcelStatus } from './parcel-status'

/**
 * Represents a parcel object model for SendCloud
 */
export type IParcelObject = {
  /** Identifier of the parcel (ignore if creating a new parcel) */
  id: number
  /** Main address line */
  address: string
  /** Apartment or floor number extracted from general address input */
  address_2: string
  /** Address divided object for a parcel */
  address_divided: {
    house_number: string
    street: string
  }
  /** Carrier information */
  carrier: {
    /** A carrier represented by a Sendcloud code */
    code: string
  }
  /** City name */
  city: string
  /** Company name */
  company_name: string
  /** ID of the contract that was used to create the parcel (>= 1) */
  contract: number | null
  /** Country object for a parcel */
  country: {
    iso_2: string
    iso_3: string
    name: string
  }
  /** Customs invoice number */
  customs_invoice_nr: string
  /** Additional data object */
  data: Record<string, unknown>
  /** Creation date */
  date_created: string
  /** Last update date */
  date_updated: string
  /** Announcement date */
  date_announced: string
  /** Email address of the person this parcel is supposed to be delivered to */
  email: string
  /** Insurance value */
  insured_value: number
  /** Label object for a parcel */
  label: {
    label_printer: string
    normal_printer: string[]
  }
  /** Name */
  name: string
  /** Order number */
  order_number: string
  /** Shipment UUID */
  shipment_uuid: string
  /** List of items the order contains. Check the structure of a parcel_item in the “Parcel_items” section (remember, it’s a list of them!). */
  parcel_items: IParcelItem[]
  /** Postal code */
  postal_code: string
  /** A reference to be assigned to this parcel. Must be unique across parcels. This field is used to create idempotence. */
  external_reference?: string
  /** A reference to be assigned to this parcel. Multiple parcels can have the same reference. */
  reference?: string
  /** Shipment object */
  shipment: IParcelShipment
  /** Status object */
  status: IParcelStatus
  /** Documents object */
  documents: IParcelDocumentObject[]
  /** Phone number of the recipient */
  telephone: string
  /** Service point ID */
  to_service_point?: string
  /** Represents the state or province of a country. Countries that need a state are USA, Canada, Italy and Australia. e.g. America’s city of Los Angeles belongs to the California state: CA. Shipments to Italy need a province. e.g. Italy’s city of Rome belongs to Roma province: RM. */
  to_state?: string
  /** Total insured value */
  total_insured_value?: number
  /** The currency of the total order value. Validated against a format of “XYZ” (ISO 4217). */
  total_order_value_currency?: string
  /** The value paid by the buyer (via various payment methods supported by the shop(cash on delivery, pre-paid or post-paid), it will also be used for the cash on delivery amount for example “99.99”. */
  total_order_value?: string
  /** Tracking number */
  tracking_number: string
  /** Tracking URL */
  tracking_url: string
  /** Weight of the parcel in kilograms */
  weight: string
  /** Colli UUID */
  colli_uuid: string
  /** Collo number */
  collo_nr: string
  /** Collo count */
  collo_count: number
  /** Set to true if this parcel is a return */
  is_return: boolean
  /** Deutsche Post Global Mail only. This indicates the air waybill number of this box of Global Mail parcels. This will be only set once Finalize box has been called.  */
  awb_tracking_number?: string
  /** Deutsche Post Global Mail only. This indicates the box to which this parcel belongs. This will be only set for Global Mail parcels. */
  box_number?: string
  /** Parcel length in centimeters (will be used for volumetric weight calculation). Examples: “48” or “52.3” */
  length?: string
  /** Parcel width in centimeters (will be used for volumetric weight calculation). Examples: “38” or “42.3” */
  width?: string
  /** Parcel height in centimeters (will be used for volumetric weight calculation). Examples: “28” or “32.3” */
  height?: string
  /** Shipping method name from checkout */
  shipping_method_checkout_name: string
  /** Errors object */
  errors?: IParcelCarrierErrors
}
