import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddPolishLanguageSupport1747728423136 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add Polish language fields to all entities with __en fields
    // Copy values from __en fields to avoid null constraint issues

    // accessory_product table
    await queryRunner.query(`
      ALTER TABLE "accessory_product" 
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "desc__pl" text
    `)
    await queryRunner.query(`
      UPDATE "accessory_product" 
      SET "name__pl" = "name__en", "desc__pl" = "desc__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "accessory_product" 
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "desc__pl" SET NOT NULL
    `)

    // blog_author table
    await queryRunner.query(`
      ALTER TABLE "blog_author" 
      ADD COLUMN "title__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "blog_author" 
      SET "title__pl" = "title__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "blog_author" 
      ALTER COLUMN "title__pl" SET NOT NULL
    `)

    // blog_post table
    await queryRunner.query(`
      ALTER TABLE "blog_post" 
      ADD COLUMN "title__pl" character varying,
      ADD COLUMN "slug__pl" character varying,
      ADD COLUMN "content__pl" text,
      ADD COLUMN "summary__pl" text
    `)
    await queryRunner.query(`
      UPDATE "blog_post" 
      SET "title__pl" = "title__en", 
          "slug__pl" = "slug__en", 
          "content__pl" = "content__en", 
          "summary__pl" = "summary__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "blog_post" 
      ALTER COLUMN "title__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL,
      ALTER COLUMN "content__pl" SET NOT NULL,
      ALTER COLUMN "summary__pl" SET NOT NULL
    `)
    await queryRunner.query(`
      ALTER TABLE "blog_post" 
      ADD CONSTRAINT "UQ_blog_post_slug__pl" UNIQUE ("slug__pl")
    `)

    // blog_tag table
    await queryRunner.query(`
      ALTER TABLE "blog_tag" 
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "slug__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "blog_tag" 
      SET "name__pl" = "name__en", "slug__pl" = "slug__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "blog_tag" 
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL
    `)
    await queryRunner.query(`
      ALTER TABLE "blog_tag" 
      ADD CONSTRAINT "UQ_blog_tag_slug__pl" UNIQUE ("slug__pl")
    `)

    // brand table
    await queryRunner.query(`
      ALTER TABLE "brand" 
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "slug__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "brand" 
      SET "name__pl" = "name__en", "slug__pl" = "slug__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "brand" 
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL
    `)
    await queryRunner.query(`
      ALTER TABLE "brand" 
      ADD CONSTRAINT "UQ_brand_slug__pl" UNIQUE ("slug__pl")
    `)

    // category table
    await queryRunner.query(`
      ALTER TABLE "category" 
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "slug__pl" character varying,
      ADD COLUMN "grading_a_desc__pl" text,
      ADD COLUMN "grading_b_desc__pl" text,
      ADD COLUMN "grading_c_desc__pl" text,
      ADD COLUMN "grading_d_desc__pl" text
    `)
    await queryRunner.query(`
      UPDATE "category" 
      SET "name__pl" = "name__en", 
          "slug__pl" = "slug__en",
          "grading_a_desc__pl" = "grading_a_desc__en",
          "grading_b_desc__pl" = "grading_b_desc__en",
          "grading_c_desc__pl" = "grading_c_desc__en",
          "grading_d_desc__pl" = "grading_d_desc__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "category" 
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL,
      ALTER COLUMN "grading_a_desc__pl" SET NOT NULL,
      ALTER COLUMN "grading_b_desc__pl" SET NOT NULL,
      ALTER COLUMN "grading_c_desc__pl" SET NOT NULL,
      ALTER COLUMN "grading_d_desc__pl" SET NOT NULL
    `)
    await queryRunner.query(`
      ALTER TABLE "category" 
      ADD CONSTRAINT "UQ_category_slug__pl" UNIQUE ("slug__pl")
    `)

    // charity table
    await queryRunner.query(`
      ALTER TABLE "charity" 
      ADD COLUMN "name__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "charity" 
      SET "name__pl" = "name__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "charity" 
      ALTER COLUMN "name__pl" SET NOT NULL
    `)

    // faq table
    await queryRunner.query(`
      ALTER TABLE "faq" 
      ADD COLUMN "question__pl" character varying,
      ADD COLUMN "answer__pl" text
    `)
    await queryRunner.query(`
      UPDATE "faq" 
      SET "question__pl" = "question__en", "answer__pl" = "answer__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "faq" 
      ALTER COLUMN "question__pl" SET NOT NULL,
      ALTER COLUMN "answer__pl" SET NOT NULL
    `)

    // file table
    await queryRunner.query(`
      ALTER TABLE "file" 
      ADD COLUMN "download_filename__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "file" 
      SET "download_filename__pl" = "download_filename__en"
    `)

    // image table
    await queryRunner.query(`
      ALTER TABLE "image" 
      ADD COLUMN "name__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "image" 
      SET "name__pl" = "name__en"
    `)

    // locale_country table
    await queryRunner.query(`
      ALTER TABLE "locale_country" 
      ADD COLUMN "name__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "locale_country" 
      SET "name__pl" = "name__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "locale_country" 
      ALTER COLUMN "name__pl" SET NOT NULL
    `)

    // locale_language table
    await queryRunner.query(`
      ALTER TABLE "locale_language"
      ADD COLUMN "name__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "locale_language"
      SET "name__pl" = "name__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "locale_language"
      ALTER COLUMN "name__pl" SET NOT NULL
    `)

    // marketing_banner table
    await queryRunner.query(`
      ALTER TABLE "marketing_banner"
      ADD COLUMN "image_desktop_id__pl" uuid,
      ADD COLUMN "image_mobile_id__pl" uuid
    `)
    await queryRunner.query(`
      UPDATE "marketing_banner"
      SET "image_desktop_id__pl" = "image_desktop_id__en",
          "image_mobile_id__pl" = "image_mobile_id__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "marketing_banner"
      ALTER COLUMN "image_desktop_id__pl" SET NOT NULL,
      ALTER COLUMN "image_mobile_id__pl" SET NOT NULL
    `)

    // marketing_sku_collection table
    await queryRunner.query(`
      ALTER TABLE "marketing_sku_collection"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "slug__pl" character varying,
      ADD COLUMN "header_bg_image_desktop_id__pl" uuid,
      ADD COLUMN "header_bg_image_mobile_id__pl" uuid
    `)
    await queryRunner.query(`
      UPDATE "marketing_sku_collection"
      SET "name__pl" = "name__en",
          "slug__pl" = "slug__en",
          "header_bg_image_desktop_id__pl" = "header_bg_image_desktop_id__en",
          "header_bg_image_mobile_id__pl" = "header_bg_image_mobile_id__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "marketing_sku_collection"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL,
      ALTER COLUMN "header_bg_image_desktop_id__pl" SET NOT NULL,
      ALTER COLUMN "header_bg_image_mobile_id__pl" SET NOT NULL
    `)
    await queryRunner.query(`
      ALTER TABLE "marketing_sku_collection"
      ADD CONSTRAINT "UQ_marketing_sku_collection_slug__pl" UNIQUE ("slug__pl")
    `)

    // marketing_tag table
    await queryRunner.query(`
      ALTER TABLE "marketing_tag"
      ADD COLUMN "name__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "marketing_tag"
      SET "name__pl" = "name__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "marketing_tag"
      ALTER COLUMN "name__pl" SET NOT NULL
    `)

    // product_model table
    await queryRunner.query(`
      ALTER TABLE "product_model"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "slug__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "product_model"
      SET "name__pl" = "name__en", "slug__pl" = "slug__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "product_model"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL
    `)
    await queryRunner.query(`
      ALTER TABLE "product_model"
      ADD CONSTRAINT "UQ_product_model_slug__pl" UNIQUE ("slug__pl")
    `)

    // product_model_attribute table
    await queryRunner.query(`
      ALTER TABLE "product_model_attribute"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "question__pl" character varying,
      ADD COLUMN "desc__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "product_model_attribute"
      SET "name__pl" = "name__en",
          "question__pl" = "question__en",
          "desc__pl" = "desc__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "product_model_attribute"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "question__pl" SET NOT NULL
    `)

    // product_model_attribute_option table
    await queryRunner.query(`
      ALTER TABLE "product_model_attribute_option"
      ADD COLUMN "name__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "product_model_attribute_option"
      SET "name__pl" = "name__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "product_model_attribute_option"
      ALTER COLUMN "name__pl" SET NOT NULL
    `)

    // product_series table
    await queryRunner.query(`
      ALTER TABLE "product_series"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "slug__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "product_series"
      SET "name__pl" = "name__en", "slug__pl" = "slug__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "product_series"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL
    `)
    await queryRunner.query(`
      ALTER TABLE "product_series"
      ADD CONSTRAINT "UQ_product_series_slug__pl" UNIQUE ("slug__pl")
    `)

    // question_extra_problem table
    await queryRunner.query(`
      ALTER TABLE "question_extra_problem"
      ADD COLUMN "template__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "question_extra_problem"
      SET "template__pl" = "template__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "question_extra_problem"
      ALTER COLUMN "template__pl" SET NOT NULL
    `)

    // question_type_problem table
    await queryRunner.query(`
      ALTER TABLE "question_type_problem"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "desc__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "question_type_problem"
      SET "name__pl" = "name__en", "desc__pl" = "desc__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "question_type_problem"
      ALTER COLUMN "name__pl" SET NOT NULL
    `)

    // seo_content table
    await queryRunner.query(`
      ALTER TABLE "seo_content"
      ADD COLUMN "header__pl" text,
      ADD COLUMN "footer__pl" jsonb
    `)
    await queryRunner.query(`
      UPDATE "seo_content"
      SET "header__pl" = "header__en", "footer__pl" = "footer__en"
    `)

    // Add remaining entities that need Polish fields
    // product_sku table (partial - main fields)
    await queryRunner.query(`
      ALTER TABLE "product_sku"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "slug__pl" character varying,
      ADD COLUMN "slug_number__pl" int4,
      ADD COLUMN "desc__pl" text,
      ADD COLUMN "display_color__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "product_sku"
      SET "name__pl" = "name__en",
          "slug__pl" = "slug__en",
          "slug_number__pl" = "slug_number__en",
          "desc__pl" = "desc__en",
          "display_color__pl" = "display_color__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "product_sku"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL,
      ALTER COLUMN "slug_number__pl" SET NOT NULL,
      ALTER COLUMN "display_color__pl" SET NOT NULL
    `)

    // Add unique constraint for product_sku Polish fields
    await queryRunner.query(`
      ALTER TABLE "product_sku"
      ADD CONSTRAINT "UQ_product_sku_slug_slug_number__pl" UNIQUE ("slug__pl", "slug_number__pl")
    `)

    // Add remaining question type entities
    await queryRunner.query(`
      ALTER TABLE "question_type_condition"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "question__pl" character varying,
      ADD COLUMN "desc__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "question_type_condition"
      SET "name__pl" = "name__en", 
          "question__pl" = "question__en",
          "desc__pl" = "desc__en"
      WHERE "name__en" IS NOT NULL
    `)
    await queryRunner.query(`
      ALTER TABLE "question_type_condition"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "question__pl" SET NOT NULL
    `)

    await queryRunner.query(`
      ALTER TABLE "question_type_condition_option"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "desc__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "question_type_condition_option"
      SET "name__pl" = "name__en", "desc__pl" = "desc__en"
      WHERE "name__en" IS NOT NULL
    `)

    await queryRunner.query(`
      ALTER TABLE "question_type_image_text"
      ADD COLUMN "name__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "question_type_image_text"
      SET "name__pl" = "name__en"
      WHERE "name__en" IS NOT NULL
    `)

    // product_variant table
    await queryRunner.query(`
      ALTER TABLE "product_variant"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "slug__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "product_variant"
      SET "name__pl" = "name__en", "slug__pl" = "slug__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "product_variant"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "slug__pl" SET NOT NULL
    `)

    // tested_item table
    await queryRunner.query(`
      ALTER TABLE "tested_item"
      ADD COLUMN "name__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "tested_item"
      SET "name__pl" = "name__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "tested_item"
      ALTER COLUMN "name__pl" SET NOT NULL
    `)

    // warranty table
    await queryRunner.query(`
      ALTER TABLE "warranty"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "label_with_price__pl" character varying,
      ADD COLUMN "label_free__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "warranty"
      SET "name__pl" = "name__en",
        "label_with_price__pl" = "label_with_price__en",
        "label_free__pl" = "label_free__en"
  `)
    await queryRunner.query(`
      ALTER TABLE "warranty"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "label_with_price__pl" SET NOT NULL,
      ALTER COLUMN "label_free__pl" SET NOT NULL
    `)

    // original_accessory table
    await queryRunner.query(`
      ALTER TABLE "original_accessory"
      ADD COLUMN "name__pl" character varying,
      ADD COLUMN "desc__pl" character varying,
      ADD COLUMN "highlight__pl" character varying
    `)
    await queryRunner.query(`
      UPDATE "original_accessory"
      SET "name__pl" = "name__en",
        "desc__pl" = "desc__en",
        "highlight__pl" = "highlight__en"
    `)
    await queryRunner.query(`
      ALTER TABLE "original_accessory"
      ALTER COLUMN "name__pl" SET NOT NULL,
      ALTER COLUMN "desc__pl" SET NOT NULL
    `)

    await queryRunner.query(`
      INSERT INTO "locale_language" ("id", "name__en", "name__nl", "name__de", "name__pl", "created_at", "updated_at")
      VALUES ('pl', 'Polish', 'Pools', 'Polnisch', 'Polski', NOW(), NOW())
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
