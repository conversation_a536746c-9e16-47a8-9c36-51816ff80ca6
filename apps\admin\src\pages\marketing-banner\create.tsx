import { Create, useForm, useSelect } from '@refinedev/antd'
import type { HttpError, IResourceComponentsProps } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType, MarketingBannerType } from '@valyuu/api/constants'
import type { ICategory, IMarketingBanner, IMarketingSkuCollection, IProductModel } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber, Select, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputImage, InputLanguageTab, InputPublish, LanguageTabChoices } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const MarketingBannerCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, onFinish, saveButtonProps } = useForm<IMarketingBanner, HttpError, IMarketingBanner>()
  const id = useMemo(() => uuid(), [])
  const publishedAt = Form.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const type = Form.useWatch('type', form)

  const { selectProps: categorySelectProps } = useSelect<ICategory>({
    resource: 'admin/categories',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: modelSelectProps } = useSelect<IProductModel>({
    resource: 'admin/product-models',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: collectionSelectProps } = useSelect<IMarketingSkuCollection>({
    resource: 'admin/marketing-sku-collections',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinish={(values) => {
          switch (type) {
            case MarketingBannerType.CATEGORY:
              values.modelId = null
              values.collectionId = null
              break
            case MarketingBannerType.MODEL:
              values.categoryId = null
              values.collectionId = null
              break
            case MarketingBannerType.MARKETING_SKU_COLLECTION:
              values.categoryId = null
              values.modelId = null
              break
          }
          onFinish?.(values)
        }}
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Select
          label="Type"
          name="type"
          rules={['required']}
          options={formatSelectOptionsFromEnum(MarketingBannerType)}
        />
        <Select
          label="Category"
          name="categoryId"
          className={type === MarketingBannerType.CATEGORY ? '' : 'hidden'}
          rules={[...(type === MarketingBannerType.CATEGORY ? (['required'] as const) : [])]}
          {...(categorySelectProps as any)}
        />
        <Select
          label="Product model"
          name="modelId"
          className={type === MarketingBannerType.MODEL ? '' : 'hidden'}
          rules={[...(type === MarketingBannerType.MODEL ? (['required'] as const) : [])]}
          {...(modelSelectProps as any)}
        />
        <Select
          label="SKU collection"
          name="collectionId"
          className={type === MarketingBannerType.MARKETING_SKU_COLLECTION ? '' : 'hidden'}
          rules={[...(type === MarketingBannerType.MARKETING_SKU_COLLECTION ? (['required'] as const) : [])]}
          {...(collectionSelectProps as any)}
        />
        <div className="col-span-2" />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputImage
                label="Desktop image (English)"
                name="imageDesktop__en"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideEn}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Mobile image (English)"
                name="imageMobile__en"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideEn}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Desktop image (Dutch)"
                name="imageDesktop__nl"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideNl}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Mobile image (Dutch)"
                name="imageMobile__nl"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideNl}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Desktop image (German)"
                name="imageDesktop__de"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideDe}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Mobile image (German)"
                name="imageMobile__de"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideDe}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Desktop image (Polish)"
                name="imageDesktop__pl"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHidePl}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Mobile image (Polish)"
                name="imageMobile__pl"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHidePl}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
            </>
          )}
        </Watch>
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
