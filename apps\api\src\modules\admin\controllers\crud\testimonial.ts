import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { TestimonialEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudTestimonialService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: TestimonialEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
})
@Controller('admin/testimonials')
export class CrudTestimonialController implements CrudController<TestimonialEntity> {
  constructor(public service: CrudTestimonialService) {}
}
