export type IWebhookPayload = {
  /**
   * Describes the webhook action
   * Allowed value: parcel_status_changed
   * @example parcel_status_changed
   */
  action: string

  /**
   * A unix timestamp indicating the time that the status changed
   */
  timestamp: number

  /**
   * A unix timestamp indicating the time that the status changed in the carrier's system
   */
  carrier_status_change_timestamp: number | null

  parcel: {
    /**
     * Sendcloud unique identifier of the parcel
     * @example 1
     */
    id: number

    /**
     * Sender name
     * @example Mr Bob
     */
    name: string

    /**
     * Company name of the sender
     * @example Sendcloud
     */
    company_name: string

    /**
     * Address of the sender
     * @example Stadhuisplein 10
     */
    address: string

    address_divided: {
      /**
       * Street name
       * @example Stadhuisplein
       */
      street: string

      /**
       * House number
       * @example 10
       */
      house_number: number
    }

    /**
     * City name
     * @example Eindhoven
     */
    city: string

    /**
     * Postal code
     * @example 5611 EM
     */
    postal_code: string

    /**
     * Telephone number of the contact person
     * @example *********
     */
    telephone: string

    /**
     * An email address of the person this parcel is supposed to be delivered to
     * @example <EMAIL>
     */
    email: string

    /**
     * Date and time of when parcel created
     * @example 2019-02-03T06:48:07
     */
    date_created: string

    /**
     * Tracking number of the shipment
     * @example 3SYZXG132912330
     */
    tracking_number: string

    /**
     * Weight of the parcel
     * @example 2.000
     */
    weight: string

    label: {
      /**
       * Labels array: More information in Labels
       */
      normal_printer: string[]

      /**
       * @example https://panel.sendcloud.sc/api/v2/label/label_printer/3172?hash=bbfd669ce9ebb19408b85b33d181a50040fd9bc4
       */
      label_printer: string
    }

    status: {
      /**
       * The Sendcloud unique identifier of the status
       * @example 1
       */
      id: number

      /**
       * The description of the status
       * @example Ready to send
       */
      message: string
    }

    country: {
      /**
       * Country code in ISO-3 format
       * @example NLD
       */
      iso_3: string

      /**
       * Country code in ISO-2 format
       * @example NL
       */
      iso_2: string

      /**
       * Country name
       * @example Netherlands
       */
      name: string
    }

    shipment: {
      /**
       * The unique identifier of the shipping method
       * @example 1
       */
      id: number

      /**
       * The Sendcloud shipping method name
       * @example PostNL Standard
       */
      name: string
    }

    /**
     * Order number of your order
     * @example ORD12334
     */
    order_number: string

    /**
     * Unique identifier that we assign to your shipment within the Sendcloud system.
     * @example 87e18823-016b-479b-b9e0-c5c0c4065452
     */
    shipment_uuid: string

    /**
     * Our system will ensure uniqueness of shipments with the combination of external_order_id and external_shipment_id
     * @example AMZ23311
     */
    external_order_id: string

    /**
     * Our system will ensure uniqueness of shipments with the combination of external_order_id and external_shipment_id
     * @example AMZ231231
     */
    external_shipment_id: string
  }
}
