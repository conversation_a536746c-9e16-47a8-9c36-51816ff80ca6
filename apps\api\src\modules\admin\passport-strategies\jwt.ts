import { Injectable, UnauthorizedException } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import ms from 'ms'
import { ClsService } from 'nestjs-cls'
import { ExtractJwt, Strategy } from 'passport-jwt'

import { envConfig } from '~/configs'
import { AdminUserEntity } from '~/entities'
import { ClsStoreType } from '~/interfaces'
import { AdminUserJtwPayloadType } from '~admin/interfaces'

@Injectable()
export class AdminJwtStrategy extends PassportStrategy(Strategy, 'admin-jwt') {
  constructor(private readonly cls: ClsService<ClsStoreType>) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: envConfig.JWT_SECRET,
    })
  }

  async validate({ sub: id }: AdminUserJtwPayloadType) {
    // TODO: ugly hack, remove it
    if (id === '<EMAIL>') {
      id = '18373595-3a47-4aea-860e-e08e0ed9d8e4'
    }

    const user = await AdminUserEntity.findOne({
      where: { id },
      cache: envConfig.isProd ? ms('5 minutes') : false,
    })
    if (!user || !user.enabled) {
      throw new UnauthorizedException()
    }
    this.cls.set('adminUserId', user.id)
    return user
  }
}
