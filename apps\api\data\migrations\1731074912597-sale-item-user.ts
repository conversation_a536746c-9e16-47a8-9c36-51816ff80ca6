import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleItemUser1731074912597 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add user_id column as nullable first
    await queryRunner.query(`
      DO $$ 
      BEGIN 
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_name = 'sale_item' AND column_name = 'user_id'
        ) THEN
          ALTER TABLE "sale_item" ADD COLUMN "user_id" uuid NULL;
        END IF;
      END $$;
    `)

    // Update user_id from related sale table
    await queryRunner.query(`
      UPDATE sale_item si 
      SET user_id = s.user_id 
      FROM sale s 
      WHERE si.sale_id = s.id
    `)

    // Now make the column non-null
    await queryRunner.query(`
      ALTER TABLE "sale_item" 
      ALTER COLUMN "user_id" SET NOT NULL
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
