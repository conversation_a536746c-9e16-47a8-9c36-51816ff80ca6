import { Injectable } from '@nestjs/common'
import { IWebhookPayload } from '@valyuu/sendcloud'
import { ClsService } from 'nestjs-cls'

import { ShipmentTrackingStatusCode, ShipmentTrackingStatusMessage } from '~/constants'
import { ShipmentEntity } from '~/entities'

@Injectable()
export class WebhookService {
  constructor(private readonly cls: ClsService) {}

  async handleSendcloudWebhook(payload: IWebhookPayload) {
    const { tracking_number, status } = payload.parcel
    if (!tracking_number || !status) {
      return
    }
    if (!Object.values(ShipmentTrackingStatusCode).includes(status.id)) {
      console.error(`Sendcloud webhook status not found:`, status.id)
    }
    const shipment = await ShipmentEntity.findOne({ where: { trackingNumber: tracking_number } })
    if (shipment) {
      shipment.statusCode = status.id
      shipment.statusMessage = status.message as ShipmentTrackingStatusMessage
      await shipment.save()
    } else {
      return
    }
  }
}
