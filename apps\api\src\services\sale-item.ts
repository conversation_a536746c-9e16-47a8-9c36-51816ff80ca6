import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { escapeRegExp } from 'lodash'
import ms from 'ms'
import { I18nService } from 'nestjs-i18n'
import { In } from 'typeorm'

import { envConfig } from '~/configs'
import {
  LOCALE_ENABLED_LANGUAGES,
  ProductModelSellerQuestionType,
  SaleItemPlanType,
  SalePaymentType,
} from '~/constants'
import {
  ProductModelEntity,
  ProductVariantEntity,
  QuestionExtraProblemEntity,
  QuestionTypeConditionOptionEntity,
  QuestionTypeProblemEntity,
  SaleEntity,
  SaleItemEntity,
  SaleItemOfferEntity,
  SellerPaymentPeriodEntity,
} from '~/entities'

const CACHE_TIME = '3 hours'

@Injectable()
export class SaleItemService {
  constructor(private readonly i18n: I18nService) {}

  async getDisplay(saleItem: SaleItemEntity) {
    if (!saleItem) {
      throw new BadRequestException('Missing saleItem')
    }
    if (!saleItem.saleId) {
      throw new BadRequestException('Missing saleId')
    }
    if (!saleItem.productVariantId) {
      throw new BadRequestException('Missing productVariantId')
    }
    if (!saleItem.productModelId) {
      throw new BadRequestException('Missing productModelId')
    }
    if (!saleItem.answers) {
      throw new BadRequestException('Missing answers')
    }

    const sale = await SaleEntity.findOne({
      where: { id: saleItem.saleId },
      select: { id: true, languageId: true, saleNumber: true },
    })
    if (!sale) {
      throw new NotFoundException('Sale not found')
    }
    const offerId = saleItem.offerId ?? saleItem.offer?.id
    const offer = saleItem.offer ?? (offerId ? await SaleItemOfferEntity.findOne({ where: { id: offerId } }) : null)

    const lang = sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]

    const productVariant = await ProductVariantEntity.findOne({
      where: { id: saleItem.productVariantId },
      relations: {
        attributeCombination: {
          choices: {
            option: true,
          },
        },
      },
      select: {
        id: true,
        attributeCombination: {
          id: true,
          choices: {
            id: true,
            option: {
              id: true,
              [`name__${lang}`]: true,
            },
          },
        },
      },
      cache: envConfig.isProd ? ms(CACHE_TIME) : false,
    })

    const attributes = productVariant.attributeCombination.choices.map((choice) => choice.option[`name__${lang}`])

    const model = await ProductModelEntity.findOne({
      where: { id: saleItem.productModelId },
      relations: {
        image: true,
      },
      select: {
        id: true,
        [`name__${lang}`]: true,
        image: {
          id: true,
          url: true,
        },
      },
      cache: envConfig.isProd ? ms(CACHE_TIME) : false,
    })

    const answers: string[] = []
    if (saleItem.answers.type === ProductModelSellerQuestionType.CONDITION) {
      const conditionOptions = await QuestionTypeConditionOptionEntity.find({
        where: { id: In(saleItem.answers.CONDITION.map(({ optionId }) => optionId)) },
        relations: { condition: true },
        select: {
          id: true,
          [`name__${lang}`]: true,
          condition: {
            id: true,
            [`name__${lang}`]: true,
          },
        },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      conditionOptions.forEach((option) =>
        answers.push(`${option.condition[`name__${lang}`]}: ${option[`name__${lang}`]}`)
      )
    } else if (saleItem.answers.type === ProductModelSellerQuestionType.PROBLEM && saleItem.answers.PROBLEM) {
      const problemOptions = await QuestionTypeProblemEntity.find({
        where: { id: In(saleItem.answers.PROBLEM) },
        select: { id: true, [`name__${lang}`]: true },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })
      problemOptions.forEach((problem) =>
        answers.push(this.i18n.t('sale.problem', { lang }) + ': ' + problem[`name__${lang}`])
      )
    }

    const sellerPaymentPeriod = await SellerPaymentPeriodEntity.findOne({
      where: { channelId: saleItem.channelId },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })

    const plan = saleItem.type === SaleItemPlanType.C2B ? 'c2b' : 'c2c'

    return {
      saleNumber: sale.saleNumber,
      name: model[`name__${lang}`],
      image: model.image.url,
      attributes,
      answers,
      lang,
      isFunctional: saleItem.answers.type === ProductModelSellerQuestionType.CONDITION,
      isDonation: sale.paymentType === SalePaymentType.DONATION,
      plan: saleItem.type,
      price: saleItem.price,
      initialPrice: saleItem.initialPrice,
      currency: saleItem.currencyId,
      offerStatus: saleItem.offerStatus,
      offerExpiresAt: offer?.expiresAt,
      donationPaymentPeriodUnit: sellerPaymentPeriod?.donationUnit,
      donationPaymentPeriodFrom: sellerPaymentPeriod?.donationFrom,
      donationPaymentPeriodTo: sellerPaymentPeriod?.donationTo,
      bankTransferPaymentPeriodUnit: sellerPaymentPeriod?.[`${plan}Unit`],
      bankTransferPaymentPeriodFrom: sellerPaymentPeriod?.[`${plan}From`],
      bankTransferPaymentPeriodTo: sellerPaymentPeriod?.[`${plan}To`],
    }
  }

  async getCustomerNotes(saleItem: SaleItemEntity) {
    if (!saleItem.saleId) {
      throw new BadRequestException('Missing saleId')
    }
    if (!saleItem?.answers?.type) {
      throw new BadRequestException('Missing answers type')
    }

    const sale = await SaleEntity.findOne({ where: { id: saleItem.saleId }, select: { id: true, languageId: true } })
    if (!sale) {
      throw new NotFoundException('Sale not found')
    }
    const lang = sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]
    const extraProblems = saleItem.answers.EXTRA_PROBLEM ?? []
    let notes = ''
    if (extraProblems?.length) {
      const templates = (
        await QuestionExtraProblemEntity.find({
          where: { id: In(extraProblems.map(({ id }) => id)) },
          select: { id: true, [`template__${lang}`]: true },
        })
      ).reduce(
        (acc, ep) => {
          acc[ep.id] = ep[`template__${lang}`]
          return acc
        },
        {} as Record<string, string>
      )

      extraProblems.forEach((ep) => {
        let template = templates[ep.id]
        Object.entries(ep.variables).forEach(([key, value]) => {
          template = template.replace(new RegExp(`{{${escapeRegExp(key)}}}`, 'g'), value)
        })
        notes += '\n' + template
      })
    }
    notes += '\n' + (saleItem.customerNote ?? '')
    return notes.trim()
  }
}
