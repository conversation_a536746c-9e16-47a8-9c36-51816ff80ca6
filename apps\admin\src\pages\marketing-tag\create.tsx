import { Create, useForm, useSelect } from '@refinedev/antd'
import type { HttpError, IResourceComponentsProps } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType, MarketingTagType } from '@valyuu/api/constants'
import type { ICategory, IMarketingSkuCollection, IMarketingTag, IProductModel } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber, Select, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputImage, InputLanguageTab, InputMultiLang, InputPublish, LanguageTabChoices } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const MarketingTagCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, onFinish, saveButtonProps } = useForm<IMarketingTag, HttpError, IMarketingTag>()
  const id = useMemo(() => uuid(), [])
  const publishedAt = Form.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const type = Form.useWatch('type', form)

  const { selectProps: categorySelectProps } = useSelect<ICategory>({
    resource: 'admin/categories',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: modelSelectProps } = useSelect<IProductModel>({
    resource: 'admin/product-models',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: collectionSelectProps } = useSelect<IMarketingSkuCollection>({
    resource: 'admin/marketing-sku-collections',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinish={(values) => {
          switch (values.type) {
            case MarketingTagType.CATEGORY:
              values.modelId = null
              values.collectionId = null
              break
            case MarketingTagType.MODEL:
              values.categoryId = null
              values.collectionId = null
              break
            case MarketingTagType.MARKETING_SKU_COLLECTION:
              values.categoryId = null
              values.modelId = null
              break
          }
          onFinish?.(values)
        }}
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Select label="Type" name="type" rules={['required']} options={formatSelectOptionsFromEnum(MarketingTagType)} />
        <Select
          label="Category"
          name="categoryId"
          className={type === MarketingTagType.CATEGORY ? '' : 'hidden'}
          rules={[...(type === MarketingTagType.CATEGORY ? (['required'] as const) : [])]}
          {...(categorySelectProps as any)}
        />
        <Select
          label="Product model"
          name="modelId"
          className={type === MarketingTagType.MODEL ? '' : 'hidden'}
          rules={[...(type === MarketingTagType.MODEL ? (['required'] as const) : [])]}
          {...(modelSelectProps as any)}
        />
        <Select
          label="SKU collection"
          name="collectionId"
          className={type === MarketingTagType.MARKETING_SKU_COLLECTION ? '' : 'hidden'}
          rules={[...(type === MarketingTagType.MARKETING_SKU_COLLECTION ? (['required'] as const) : [])]}
          {...(collectionSelectProps as any)}
        />
        <div className="col-span-2" />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
              <InputImage
                label="Image"
                name="image"
                className="col-span-2"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
            </>
          )}
        </Watch>
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
