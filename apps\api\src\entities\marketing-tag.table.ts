import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { MarketingTagType } from '~/constants'
import {
  CategoryEntity,
  ChannelEntity,
  type ICategory,
  type IChannel,
  type IImage,
  ImageEntity,
  type IMarketingSkuCollection,
  type IProductModel,
  MarketingSkuCollectionEntity,
  ProductModelEntity,
} from '~/entities'

registerEnumType(MarketingTagType, { name: 'MarketingTagType' })

@ObjectType('MarketingTag')
@Entity('marketing_tag')
export class MarketingTagEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field(() => ChannelEntity)
  @ManyToMany(() => ChannelEntity, { nullable: false })
  @JoinTable({ name: 'marketing_tag_channels' })
  channels: Relation<ChannelEntity>[]

  @Field(() => MarketingTagType)
  @Column({ type: 'varchar', nullable: false })
  type: MarketingTagType

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @ManyToOne(() => CategoryEntity, { nullable: true })
  category?: Relation<CategoryEntity> | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  @RelationId((banner: MarketingTagEntity) => banner.category)
  categoryId?: string | null

  @ManyToOne(() => ProductModelEntity, { nullable: true })
  model?: Relation<ProductModelEntity> | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  @RelationId((banner: MarketingTagEntity) => banner.model)
  modelId?: string | null

  @ManyToOne(() => MarketingSkuCollectionEntity, { nullable: false })
  collection?: Relation<MarketingSkuCollectionEntity> | null

  @Field(() => String, { nullable: true })
  @Column({ nullable: true })
  @RelationId((banner: MarketingTagEntity) => banner.collection)
  collectionId?: string | null

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingTagImage, { nullable: false, cascade: true })
  @JoinColumn()
  image: Relation<ImageEntity>

  @Column()
  @RelationId((banner: MarketingTagEntity) => banner.image)
  imageId: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IMarketingTag = Omit<MarketingTagEntity, keyof BaseEntity> & {
  channels: IChannel[]
  category?: ICategory
  model?: IProductModel
  collection?: IMarketingSkuCollection
  image: IImage
  type: MarketingTagType
}
