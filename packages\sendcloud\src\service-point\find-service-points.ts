import { sendcloudClientV2 } from '../client'
import { CarrierCode } from '../constants'
import { IServicePoint } from '../interfaces'

export type IFindServicePointsParams = {
  country: string
  access_token?: string
  address?: string
  carrier?: CarrierCode | CarrierCode[]
  city?: string
  house_number?: string
  latitude?: string
  longitude?: string
  ne_latitude?: string
  ne_longitude?: string
  postal_code?: string
  pudo_id?: string
  radius?: number
  shop_type?: string
  sw_latitude?: string
  sw_longitude?: string
  weight?: number
}

/**
 * Find service points based on provided parameters
 * @param params Search parameters for finding service points
 * @returns Promise with array of service points
 */
export const findServicePoints = async (params: IFindServicePointsParams): Promise<IServicePoint[]> => {
  const { data } = await sendcloudClientV2.get('/service-points', {
    params: {
      ...params,
      // Convert carrier to comma-separated string if it's an array
      carrier: Array.isArray(params.carrier) ? params.carrier.join(',') : params.carrier,
    },
  })

  return data
}
