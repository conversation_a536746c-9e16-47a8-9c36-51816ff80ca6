export enum PaymentListStatus {
  PENDING_REVIEW = 'PENDING_REVIEW',
  APPROVED = 'APPROVED',
  PAID = 'PAID',
}

export enum PaymentListType {
  D2C = 'D2C',
  BULK = 'BULK',
}

export enum PaymentListProblemItemReason {
  MISSING_BANK_INFO = 'MISSING_BANK_INFO',
  CUSTOMER_REQUEST_CANCELLATION = 'CUSTOMER_REQUEST_CANCELLATION',
  FAILED_PAYMENT = 'FAILED_PAYMENT',
  OTHER = 'OTHER',
}

export const PAYMENT_LIST_MAX_ITEMS = 1000
