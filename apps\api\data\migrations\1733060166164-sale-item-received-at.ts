import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleItemReceivedAt1733060166164 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First add the column if it doesn't exist
    await queryRunner.query(`
      ALTER TABLE sale_item
      ADD COLUMN IF NOT EXISTS received_at TIMESTAMP
    `)

    // Then update the values
    await queryRunner.query(`
      UPDATE sale_item si
      SET received_at = s.updated_at
      FROM sale s
      WHERE si.sale_id = s.id
      AND s.status IN ('RECEIVED', 'COMPLETED')
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
