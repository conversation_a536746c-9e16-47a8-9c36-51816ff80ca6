import type { SearchIndex } from 'algoliasearch'
import algolia from 'algoliasearch'
import { plainToClass } from 'class-transformer'
import { omit } from 'lodash'
import type { EntityManager } from 'typeorm'

import { envConfig } from '~/configs'

const client = algolia(envConfig.ALGOLIA_APP_ID, envConfig.ALGOLIA_ADMIN_API_KEY)

export class AlgoliaIndex {
  static indexName: string
  protected static algoliaIndex: SearchIndex
  protected static initialized: boolean = false
  protected static async init() {
    if (this.initialized) {
      return this.algoliaIndex
    }
    this.algoliaIndex = await client.initIndex(this.indexName)
    if (await this.algoliaIndex.exists()) {
      this.initialized = true
    }
    return this.algoliaIndex
  }
  static fromEntity({
    id: _id,
    manager: _manager,
  }: {
    id: string
    manager?: EntityManager
  }): Promise<Partial<AlgoliaIndex>> {
    throw new Error('Method not implemented.')
  }
  static async getObject(objectID: string): Promise<any> {
    await this.init()
    const object = await this.algoliaIndex.getObject(objectID)
    if (!object) {
      return null
    }
    return plainToClass(this, object)
  }
  static async getObjects(objectIDs: string[]): Promise<any[]> {
    await this.init()
    const objects = await this.algoliaIndex.getObjects(objectIDs)
    return objects.results.map((object) => plainToClass(this, object))
  }
  static async partialUpdateObject(object: Record<string, any>) {
    await this.init()
    const { taskID, objectID } = await this.algoliaIndex.partialUpdateObject(object, {})
    return { taskID, objectID }
  }
  static async partialUpdateObjects(objects: Record<string, any>[]) {
    await this.init()
    const { taskIDs, objectIDs } = await this.algoliaIndex.partialUpdateObjects(objects)
    return { taskIDs, objectIDs }
  }
  static async deleteObject(objectID: string) {
    await this.init()
    await this.algoliaIndex.deleteObject(objectID)
  }
  static async deleteObjects(objectIDs: string[]) {
    await this.init()
    await this.algoliaIndex.deleteObjects(objectIDs)
  }
  static async clearObjects() {
    await this.init()
    await this.algoliaIndex.clearObjects()
  }

  objectID: string
  get _model() {
    return omit(this, ['model', 'save', 'delete'])
  }
  async save() {
    if (!(this.constructor as typeof AlgoliaIndex).initialized) {
      await (this.constructor as typeof AlgoliaIndex).init()
    }
    await (this.constructor as typeof AlgoliaIndex).algoliaIndex.saveObject(this._model)
  }
  async delete() {
    if (!(this.constructor as typeof AlgoliaIndex).initialized) {
      await (this.constructor as typeof AlgoliaIndex).init()
    }
    await (this.constructor as typeof AlgoliaIndex).algoliaIndex.deleteObject(this.objectID)
  }
}
