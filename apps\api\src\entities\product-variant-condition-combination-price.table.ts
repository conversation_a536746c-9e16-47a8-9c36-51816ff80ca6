import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  Unique,
  UpdateDateColumn,
} from 'typeorm'

import {
  ChannelEntity,
  type IChannel,
  type ILocaleCurrency,
  type IProductVariantConditionCombination,
  LocaleCurrencyEntity,
  ProductVariantConditionCombinationEntity,
} from '~/entities'

@ObjectType('ProductVariantConditionCombinationPrice')
@Entity('product_variant_condition_combination_price')
@Unique(['channelId', 'conditionCombinationId'])
export class ProductVariantConditionCombinationPriceEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Index()
  @Column()
  @RelationId((problem: ProductVariantConditionCombinationPriceEntity) => problem.channel)
  channelId: string

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: ProductVariantConditionCombinationPriceEntity) => item.currency)
  currencyId: string

  @ManyToOne(() => ProductVariantConditionCombinationEntity, (combination) => combination.prices, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  conditionCombination: Relation<ProductVariantConditionCombinationEntity>

  @Column()
  @RelationId((price: ProductVariantConditionCombinationPriceEntity) => price.conditionCombination)
  conditionCombinationId: string

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  c2bPrice: number

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  c2cPrice: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductVariantConditionCombinationPrice = Omit<
  ProductVariantConditionCombinationPriceEntity,
  keyof BaseEntity
> & {
  chanel: IChannel
  currency: ILocaleCurrency
  conditionCombination: IProductVariantConditionCombination
}
