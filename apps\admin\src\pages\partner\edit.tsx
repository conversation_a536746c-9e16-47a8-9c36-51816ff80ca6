import { Edit, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { SaleItemPlanType, SalePaymentType } from '@valyuu/api/constants'
import type { IChannel, IPartner } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import form from 'antd/es/form'
import { Input, RadioGroup, Select, Watch } from 'antx'
import type { FC } from 'react'

import { InputSlug, LabelWithDetails } from '~/components'

export const PartnerEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, id, saveButtonProps, formLoading, query } = useForm<IPartner, HttpError, IPartner>({
    meta: {
      join: [
        {
          field: 'channel',
        },
        {
          field: 'bankAccount',
          select: ['id', 'holderName'],
        },
      ],
    },
  })

  const record = query?.data?.data

  const { selectProps: channelSelectProps } = useSelect<IChannel>({
    resource: 'admin/channels',
    optionLabel: 'name',
    meta: {
      fields: ['id', 'name'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl } = useNavigation()

  const supportedPaymentTypes = Form.useWatch('supportedPaymentTypes', form)

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Watch name="name">
          {(name) => (
            <>
              <Input label="Name" name="name" rules={['required']} value={name} />
              <InputSlug label="Slug" name="slug" rules={['required']} sourceString={name} allowEdit={false} />
            </>
          )}
        </Watch>
        <Watch name="channelId">
          {(channelId) => (
            <Select
              label={
                <LabelWithDetails label="Channel" link={channelId ? editUrl('admin/channels', channelId) : undefined} />
              }
              name="channelId"
              rules={['required']}
              {...(channelSelectProps as any)}
            />
          )}
        </Watch>
        <Input label="Api key" name="id" disabled />
        <Input label="Api secret" name="secret" disabled />
        <RadioGroup
          label="Manage customer Emails"
          name="manageCustomerEmails"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
        />
        <Select
          label="Supported plan types"
          name="supportedPlanTypes"
          mode="multiple"
          options={Object.values(SaleItemPlanType).map((type) => ({
            label: type,
            value: type,
          }))}
          rules={['required']}
        />
        <Select
          label="Supported payment types"
          name="supportedPaymentTypes"
          mode="multiple"
          options={Object.values(SalePaymentType).map((type) => ({
            label: type,
            value: type,
          }))}
          rules={['required']}
        />
        {supportedPaymentTypes?.includes(SalePaymentType.BULK_SETTLEMENT) ? (
          <Select
            label={
              <LabelWithDetails
                link={
                  record?.bankAccountId ? editUrl('admin/organization-bank-accounts', record.bankAccountId) : undefined
                }
                label={'Bank account'}
              />
            }
            name="bankAccountId"
            rules={['required']}
            options={[{ label: record?.bankAccount?.holderName, value: record?.bankAccount?.id }]}
            disabled
          />
        ) : null}
        <Input
          label="API endpoint URL"
          name="apiEndpointUrl"
          type="url"
          rules={[
            {
              type: 'url',
              message: 'Please enter a valid URL',
            },
          ]}
        />
        <Input
          label="Standalone site URL"
          name="standaloneSiteUrl"
          type="url"
          rules={[
            {
              type: 'url',
              message: 'Please enter a valid URL',
            },
          ]}
        />
      </Form>
    </Edit>
  )
}
