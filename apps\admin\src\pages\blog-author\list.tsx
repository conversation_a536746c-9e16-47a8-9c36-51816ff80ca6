import { fit } from '@cloudinary/url-gen/actions/resize'
import { <PERSON><PERSON>ield, DeleteButton, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IBlogAuthor } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import type { FC } from 'react'

import { cloudinary, handleTableRowClick } from '~/utils'

export const BlogAuthorList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<IBlogAuthor>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'name', 'title__en', 'createdAt'],
      join: [
        {
          field: 'avatar',
          select: ['publicId'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/blog-authors', id!)))}
      >
        <Table.Column dataIndex="id" title="ID" className="max-w-10 cursor-pointer" ellipsis={true} />
        <Table.Column
          dataIndex="avatar"
          title="Avatar"
          className="cursor-pointer"
          render={(value) => (
            <img
              src={cloudinary.image(value.publicId).resize(fit('crop').width(32).height(32)).toURL()}
              alt=""
              className="size-8 rounded-full"
            />
          )}
          align="center"
          width="4rem"
        />
        <Table.Column dataIndex="name" title="Name" className="cursor-pointer" />
        <Table.Column dataIndex="title__en" title="Title (English)" className="cursor-pointer" />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IBlogAuthor>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
