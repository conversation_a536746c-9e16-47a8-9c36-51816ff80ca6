import { Injectable, NotFoundException } from '@nestjs/common'
import { FeedBuilder } from 'google-merchant-feed'
import { pick } from 'lodash'
import ms from 'ms'
import { IsNull, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { LOCALE_ENABLED_LANGUAGES, MarketingBannerType, MarketingTagType } from '~/constants'
import { GetMarketingSkuCollectionInput } from '~/dtos'
import { MarketingBannerEntity, MarketingSkuCollectionEntity, MarketingTagEntity, ProductSkuEntity } from '~/entities'

@Injectable()
export class MarketingService {
  async productSkuFeed(lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]) {
    const feedBuilder = new FeedBuilder()

    feedBuilder.withTitle('Prioont')
    feedBuilder.withLink('https://prioont.com')
    feedBuilder.withDescription('Your one-stop shop for used electronics')

    const skus = await ProductSkuEntity.find({
      where: { sold: false, prices: { channelId: 'EU-EUR-1' }, publishedAt: Not(IsNull()) },
      relations: {
        brand: true,
        heroImage: true,
        images: true,
        prices: true,
      },
      select: {
        id: true,
        [`slug__${lang}`]: true,
        [`slugNumber__${lang}`]: true,
        [`name__${lang}`]: true,
        [`desc__${lang}`]: true,
        heroImage: {
          url: true,
        },
        images: {
          url: true,
        },
        [`displayColor__${lang}`]: true,
        prices: true,
        mpn: true,
        brand: {
          id: true,
          [`name__${lang}`]: true,
        },
      },
    })

    for (const sku of skus) {
      if (
        !sku.prices?.[0]?.price ||
        !sku.prices?.[0]?.originalPrice ||
        !sku.prices?.[0]?.currencyId ||
        !sku.heroImage?.url ||
        !sku[`name__${lang}`] ||
        !sku[`desc__${lang}`] ||
        !sku.brand[`name__${lang}`]
      ) {
        continue
      }
      feedBuilder.withProduct({
        id: sku.id,
        title: sku[`name__${lang}`],
        description: sku[`desc__${lang}`],
        link: envConfig.SITE_URL + '/en/buy/' + sku[`slug__${lang}`] + '/' + sku[`slugNumber__${lang}`],
        imageLink: sku.heroImage?.url,
        additionalImageLinks: sku.images?.map((image) => image.url),
        availability: 'in_stock',
        price: {
          currency: sku.prices?.[0]?.currencyId,
          value: sku.prices?.[0]?.originalPrice,
        },
        salePrice: {
          currency: sku.prices?.[0]?.currencyId,
          value: sku.prices?.[0]?.price,
        },
        brand: sku.brand[`name__${lang}`],
        condition: 'used',
        shipsFromCountry: 'NL',
        mpn: sku.mpn,
        color: sku[`displayColor__${lang}`],
      })
    }
    return feedBuilder.buildXml()
  }
  async getBuyerTags(channelId: string) {
    const tags = await MarketingTagEntity.find({
      where: [
        {
          channels: { id: channelId },
          publishedAt: Not(IsNull()),
          type: MarketingTagType.CATEGORY,
          category: { publishedAt: Not(IsNull()) },
        },
        {
          channels: { id: channelId },
          publishedAt: Not(IsNull()),
          type: MarketingTagType.MODEL,
          model: { publishedAt: Not(IsNull()) },
        },
        {
          channels: { id: channelId },
          publishedAt: Not(IsNull()),
          type: MarketingTagType.MARKETING_SKU_COLLECTION,
          collection: { publishedAt: Not(IsNull()) },
        },
      ],
      order: { sortOrder: 'ASC' },
      relations: { collection: true, category: true, model: true, image: true },
      cache: envConfig.isProd ? ms('1 hour') : false,
    })
    return tags.map((tag) => {
      const pickedTag = pick(tag, [
        'name__en',
        'name__nl',
        'name__de',
        'name__pl',
        'image',
        'type',
        ...(tag.type === MarketingTagType.CATEGORY ? ['categoryId'] : []),
        ...(tag.type === MarketingTagType.MARKETING_SKU_COLLECTION ? ['collectionId'] : []),
        ...(tag.type === MarketingTagType.MODEL ? ['modelId'] : []),
      ])
      switch (tag.type) {
        case MarketingTagType.CATEGORY:
          return {
            ...pickedTag,
            ...pick(tag.category, ['id', 'slug__en', 'slug__nl', 'slug__de', 'slug__pl']),
          }
        case MarketingTagType.MODEL:
          return {
            ...pickedTag,
            ...pick(tag.model, ['id', 'slug__en', 'slug__nl', 'slug__de', 'slug__pl']),
          }
        case MarketingTagType.MARKETING_SKU_COLLECTION:
          return {
            ...pickedTag,
            ...pick(tag.collection, ['id', 'slug__en', 'slug__nl', 'slug__de', 'slug__pl']),
          }
      }
    })
  }
  async getBuyerBanners(channelId: string) {
    const banners = await MarketingBannerEntity.find({
      where: [
        {
          channels: { id: channelId },
          publishedAt: Not(IsNull()),
          type: MarketingBannerType.CATEGORY,
          category: { publishedAt: Not(IsNull()) },
        },
        {
          channels: { id: channelId },
          publishedAt: Not(IsNull()),
          type: MarketingBannerType.MODEL,
          model: { publishedAt: Not(IsNull()) },
        },
        {
          channels: { id: channelId },
          publishedAt: Not(IsNull()),
          type: MarketingBannerType.MARKETING_SKU_COLLECTION,
          collection: { publishedAt: Not(IsNull()) },
        },
      ],
      order: { sortOrder: 'ASC' },
      relations: {
        collection: true,
        category: true,
        model: true,
        imageDesktop__en: true,
        imageDesktop__nl: true,
        imageDesktop__de: true,
        imageDesktop__pl: true,
        imageMobile__en: true,
        imageMobile__nl: true,
        imageMobile__de: true,
        imageMobile__pl: true,
      },
      cache: envConfig.isProd ? ms('1 hour') : false,
    })
    return banners.map((banner) => {
      const pickedBanner = pick(banner, [
        'imageDesktop__en',
        'imageDesktop__nl',
        'imageDesktop__de',
        'imageDesktop__pl',
        'imageMobile__en',
        'imageMobile__nl',
        'imageMobile__de',
        'imageMobile__pl',
        'type',
        ...(banner.type === MarketingBannerType.CATEGORY ? ['categoryId'] : []),
        ...(banner.type === MarketingBannerType.MARKETING_SKU_COLLECTION ? ['collectionId'] : []),
        ...(banner.type === MarketingBannerType.MODEL ? ['modelId'] : []),
      ])
      switch (banner.type) {
        case MarketingBannerType.CATEGORY:
          return {
            ...pickedBanner,
            ...pick(banner.category, ['id', 'slug__en', 'slug__nl', 'slug__de', 'slug__pl']),
          }
        case MarketingBannerType.MODEL:
          return {
            ...pickedBanner,
            ...pick(banner.model, ['id', 'slug__en', 'slug__nl', 'slug__de', 'slug__pl']),
          }
        case MarketingBannerType.MARKETING_SKU_COLLECTION:
          return {
            ...pickedBanner,
            ...pick(banner.collection, ['id', 'slug__en', 'slug__nl', 'slug__de', 'slug__pl']),
          }
      }
    })
  }
  async getMarketingSkuCollection({ lang, slug }: GetMarketingSkuCollectionInput) {
    const relations = {
      headerBgImageDesktop__en: true,
      headerBgImageDesktop__nl: true,
      headerBgImageDesktop__de: true,
      headerBgImageDesktop__pl: true,
      headerBgImageMobile__en: true,
      headerBgImageMobile__nl: true,
      headerBgImageMobile__de: true,
      headerBgImageMobile__pl: true,
    }
    let collection = await MarketingSkuCollectionEntity.findOne({
      where: { [`slug__${lang}`]: slug, publishedAt: Not(IsNull()) },
      relations,
      cache: envConfig.isProd ? ms('1 hour') : false,
    })

    // Check other languages, make sure language switching has no issue
    if (!collection) {
      collection = await MarketingSkuCollectionEntity.findOne({
        where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({
          [`slug__${l}`]: slug,
          publishedAt: Not(IsNull()),
        })),
        relations,
      })
    }

    if (!collection) {
      throw new NotFoundException('Collection not found')
    }

    return collection
  }
}
