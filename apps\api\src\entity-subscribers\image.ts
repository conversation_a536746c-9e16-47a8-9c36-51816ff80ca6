import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, RemoveEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageEntity } from '~/entities'
import { cloudinaryDeleteResource } from '~/utils'

@Injectable()
@EventSubscriber()
export class ImageSubscriber implements EntitySubscriberInterface<ImageEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ImageEntity
  }

  async afterRemove(event: RemoveEvent<ImageEntity>) {
    const { databaseEntity } = event

    if (databaseEntity?.publicId) {
      await cloudinaryDeleteResource(event.entity.publicId)
    }
  }
}
