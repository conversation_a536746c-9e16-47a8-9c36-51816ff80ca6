{"compilerOptions": {"module": "CommonJS", "moduleResolution": "Node10", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "noImplicitAny": true, "strict": true, "strictNullChecks": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"~/*": ["src/*"], "~admin/*": ["src/modules/admin/*"], "~partner/*": ["src/modules/partner/*"]}}, "ts-node": {"require": ["tsconfig-paths/register"], "files": true}}