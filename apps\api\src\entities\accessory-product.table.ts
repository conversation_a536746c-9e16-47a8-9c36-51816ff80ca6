import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  AccessoryProductPriceEntity,
  type IAccessoryProductPrice,
  type IImage,
  ImageEntity,
  type IProductModel,
  ProductModelEntity,
} from '~/entities'

@Entity('accessory_product')
export class AccessoryProductEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Column()
  internalName: string

  @Column()
  name__en: string

  @Column()
  name__nl: string

  @Column()
  name__de: string

  @Column()
  name__pl: string

  @Column('text')
  desc__en: string

  @Column('text')
  desc__nl: string

  @Column('text')
  desc__de: string

  @Column('text')
  desc__pl: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @OneToMany(() => AccessoryProductPriceEntity, (price) => price.accessoryProduct, { nullable: false, cascade: true })
  prices: Relation<AccessoryProductPriceEntity>[]

  @OneToOne(() => ImageEntity, (image) => image.accessoryProductHeroImage, { nullable: false, cascade: true })
  @JoinColumn()
  heroImage: Relation<ImageEntity>

  @Column()
  @RelationId((accessory: AccessoryProductEntity) => accessory.heroImage)
  heroImageId: string

  @ManyToMany(() => ProductModelEntity, (productModel) => productModel.accessoryProducts, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  relatedProductModels: Relation<ProductModelEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IAccessoryProduct = Omit<AccessoryProductEntity, keyof BaseEntity> & {
  prices: IAccessoryProductPrice[]
  image: IImage
  relatedProductModels: IProductModel[]
}
