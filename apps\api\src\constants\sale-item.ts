export enum SaleItemPlanType {
  C2C = 'C2C',
  C2B = 'C2B',
}

export enum SaleItemPaymentStatus {
  TRANSFERRED = 'TRANSFERRED',
  PAID_OUT = 'PAID_OUT',
  FAILED = 'FAILED',
}

export enum SaleItemStatus {
  SUBMITTED = 'SUBMITTED',
  RECEIVED = 'RECEIVED',
  CANCELLED = 'CANCELLED',
  PENDING_OFFER = 'PENDING_OFFER',
  PENDING_PAYMENT = 'PENDING_PAYMENT',
  PENDING_RETURN = 'PENDING_RETURN',
  PENDING_RECYCLE = 'PENDING_RECYCLE',
  ON_HOLD = 'ON_HOLD',
  CUSTOMER_ABANDONED = 'CUSTOMER_ABANDONED',
  RETURNED = 'RETURNED',
  PAYMENT_IN_PROCESS = 'PAYMENT_IN_PROCESS',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  NOT_RECEIVED = 'NOT_RECEIVED',
  PAID = 'PAID',
  RECYCLED = 'RECYCLED',
}
