import { ArgsType, Field, ID, InputType, Int, ObjectType } from '@nestjs/graphql'
import { Transform } from 'class-transformer'
import {
  IsEmail,
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsNumberString,
  IsPositive,
  IsUUID,
  Matches,
  Max,
  Min,
  ValidateIf,
} from 'class-validator'

import { LOCALE_ENABLED_LANGUAGES, UserFrom } from '~/constants'

@ObjectType()
export class OrderCartSkuItemOutput {
  @Field()
  id: string

  @Field()
  cartItemId: string

  @Field()
  hasPriceChange: boolean

  @Field()
  sold: boolean

  @Field()
  oldPrice: number

  @Field()
  newPrice: number

  @Field()
  warrantyItemId: string

  @Field()
  warrantyHasPriceChange: boolean

  @Field()
  warrantyOldPrice: number

  @Field()
  warrantyNewPrice: number
}

@ObjectType()
export class OrderCartAccessoryItemOutput {
  id: string

  @Field()
  cartItemId: string

  @Field()
  hasPriceChange: boolean

  @Field()
  sold: boolean

  @Field()
  quantity: number

  @Field()
  oldPrice: number

  @Field()
  newPrice: number
}

@ObjectType()
export class GetOrderCartItemPricesOutput {
  @Field()
  hasPriceChange: boolean

  @Field()
  hasSoldItems: boolean

  @Field(() => [OrderCartSkuItemOutput])
  skuItems: OrderCartSkuItemOutput[]

  @Field(() => [OrderCartAccessoryItemOutput])
  accessoryItems: OrderCartAccessoryItemOutput[]

  @Field()
  totalAmount: number
}

@InputType()
@ObjectType('GetOrderResultSkuOutput')
export class OrderCartSkuItemInput {
  @Field({
    description: "If it's a sku then use sku id, If it's accessory, then accessory id",
  })
  @IsUUID('4')
  id: string

  @Field()
  @IsUUID('4')
  cartItemId: string

  @Field()
  @IsUUID('4')
  warrantyItemId: string

  @Field()
  @IsNumber()
  warrantyPrice: number

  @Field()
  @IsNumber()
  @IsPositive()
  price: number
}

@InputType()
@ObjectType('GetOrderResultAccessoryOutput')
export class OrderCartAccessoryItemInput {
  @Field({
    description: "If it's a sku then use sku id, If it's accessory, then accessory id",
  })
  @IsUUID('4')
  id: string

  @Field()
  @IsUUID('4')
  cartItemId: string

  @Field(() => Int)
  @IsInt()
  @IsPositive()
  quantity: number

  @Field()
  @IsNumber()
  price: number
}

@ArgsType()
export class GetOrderCartItemPricesInput {
  @Field()
  channelId: string

  @Field(() => [OrderCartSkuItemInput])
  skuItems: OrderCartSkuItemInput[]

  @Field(() => [OrderCartAccessoryItemInput])
  accessoryItems: OrderCartAccessoryItemInput[]
}

@InputType()
export class CreateOrderAddressInput {
  @Field()
  @Min(1)
  @Max(30)
  @Transform(({ value }) => value?.trim(), { toClassOnly: true })
  firstName: string

  @Field()
  @Min(1)
  @Max(30)
  @Transform(({ value }) => value?.trim(), { toClassOnly: true })
  lastName: string

  @Field()
  @Matches(/^\+\d{1,4}$/, { message: 'Value must be a valid European country code' })
  phoneAreaCode: string

  @Field()
  @IsNumberString({ no_symbols: true }, { message: 'Value must be a valid phone number' })
  @Min(4)
  @Max(15)
  phoneNumber: string

  @Field()
  @IsNotEmpty()
  country: string

  @Field()
  @IsNotEmpty()
  postalCode: string

  @Field()
  @Max(8)
  houseNumber: string

  @Field({ nullable: true })
  @ValidateIf((o) => o.addition?.trim())
  @Max(6)
  addition?: string

  @Field()
  @Min(3)
  street: string

  @Field()
  @IsNotEmpty()
  city: string
}

@ArgsType()
export class CreatePreOrderInput {
  @Field()
  channelId: string

  @Field()
  @IsEmail()
  email: string

  @Field(() => CreateOrderAddressInput)
  shippingAddress: CreateOrderAddressInput

  @Field()
  toPickupPoint: boolean

  @Field()
  @Field(() => CreateOrderAddressInput)
  billingAddress: CreateOrderAddressInput

  @Field(() => [OrderCartSkuItemInput])
  skuItems: OrderCartSkuItemInput[]

  @Field(() => [OrderCartAccessoryItemInput])
  accessoryItems: OrderCartAccessoryItemInput[]

  @Field()
  anonymousId: string

  @Field()
  clientSecret: string

  @Field({ nullable: true })
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  language: string

  @Field(() => UserFrom, {
    description:
      "Where the user comes from, default to UserFrom.VALYUU, it's only going to be changed when the user is from an affiliated link, like abnambro.valyuu.com",
  })
  @IsEnum(UserFrom)
  from: UserFrom
}

@ObjectType()
export class CreatePreOrderOutput {
  @Field(() => ID, { nullable: true })
  id?: string

  @Field()
  hasPriceChange: boolean

  @Field()
  hasSoldItems: boolean

  @Field()
  renewPaymentIntent: boolean

  @Field(() => [OrderCartSkuItemOutput])
  skuItems: OrderCartSkuItemOutput[]

  @Field(() => [OrderCartAccessoryItemOutput])
  accessoryItems: OrderCartAccessoryItemOutput[]

  @Field()
  totalAmount: number

  @Field()
  success: boolean

  // TODO: error code
}

@ArgsType()
export class createPaymentIntentInput {
  @Field()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  language: string

  @Field()
  channelId: string

  @Field(() => [OrderCartSkuItemInput])
  skuItems: OrderCartSkuItemInput[]

  @Field(() => [OrderCartAccessoryItemInput])
  accessoryItems: OrderCartAccessoryItemInput[]
}

@ObjectType()
export class CreatePaymentIntentOutput {
  @Field()
  success: boolean

  @Field()
  clientSecret?: string

  @Field()
  paymentIntentId?: string

  @Field()
  amount: number
}

@ObjectType()
export class GetOrderResultOutput {
  @Field({ nullable: true })
  orderNumber?: string

  @Field(() => [OrderCartSkuItemInput])
  skuItems: OrderCartSkuItemInput[]

  @Field(() => [OrderCartAccessoryItemInput])
  accessoryItems: OrderCartAccessoryItemInput[]

  @Field()
  total: number

  @Field()
  savedCo2: number

  @Field()
  savedEwaste: number

  @Field()
  createdAt: Date

  @Field()
  email: string
}
