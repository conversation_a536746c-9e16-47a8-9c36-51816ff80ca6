import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import { IsEmail } from 'class-validator'
import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

import { ProductIssueType } from '~/constants'

registerEnumType(ProductIssueType, { name: 'ProductIssueType' })

@ObjectType('ProductIssue')
@Entity('product_issue')
export class ProductIssueEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column({ nullable: false })
  @IsEmail()
  email: string

  @Field(() => ProductIssueType)
  @Column({ type: 'varchar', nullable: false })
  type: ProductIssueType

  @Field()
  @Column({ type: 'text' })
  issue: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductIssue = Omit<ProductIssueEntity, keyof BaseEntity>
