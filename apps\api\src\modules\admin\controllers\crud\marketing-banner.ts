import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { MarketingBannerEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudMarketingBannerService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: MarketingBannerEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      channels: {},
      category: {},
      model: {},
      collection: {},
      imageDesktop__en: {},
      imageMobile__en: {},
      imageDesktop__nl: {},
      imageMobile__nl: {},
      imageDesktop__de: {},
      imageMobile__de: {},
    },
  },
})
@Controller('admin/marketing-banners')
export class CrudMarketingBannerController implements CrudController<MarketingBannerEntity> {
  constructor(public service: CrudMarketingBannerService) {}
}
