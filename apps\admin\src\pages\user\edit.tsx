import { Edit, useForm, useSelect } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { ILocaleLanguage, IUser } from '@valyuu/api/entities'
import { Form, Tabs, Typography } from 'antd'
import { DatePicker, Input, Select } from 'antx'
import dayjs from 'dayjs'
import { FC, useState } from 'react'
import { AiOutlineEdit } from 'react-icons/ai'

import { BlockEmailHistory, LabelWithDetails } from '~/components'

enum PageTab {
  USER = 'user',
  EMAIL_HISTORY = 'email-history',
}

export const UserEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, query, formLoading } = useForm<IUser, HttpError, IUser>()

  if (formProps.initialValues?.dateOfBirth) {
    formProps.initialValues.dateOfBirth = dayjs(new Date(formProps.initialValues.dateOfBirth))
  }
  const { selectProps: languageSelectProps } = useSelect<ILocaleLanguage>({
    resource: 'admin/languages',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const record = query?.data?.data

  const [activeTab, setActiveTab] = useState<PageTab>(PageTab.USER)

  const [emailEditable, setEmailEditable] = useState(false)

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Tabs
        activeKey={activeTab}
        className="select-none"
        onChange={(key) => setActiveTab(key as PageTab)}
        items={[
          {
            key: PageTab.USER,
            label: 'User',
            children: (
              <Form {...formProps} layout="vertical">
                <Input
                  className="[&.ant-form-item_.ant-input-group-addon>span]:flex [&.ant-form-item_.ant-input-group-addon>span]:h-[30px] [&.ant-form-item_.ant-input-group-addon>span]:w-9 [&.ant-form-item_.ant-input-group-addon>span]:cursor-pointer [&.ant-form-item_.ant-input-group-addon>span]:items-center [&.ant-form-item_.ant-input-group-addon>span]:justify-center [&.ant-form-item_.ant-input-group-addon]:bg-black/[0.02] [&.ant-form-item_.ant-input-group-addon]:p-0 [&.ant-form-item_.ant-input-group-addon]:text-black/[0.88]"
                  label="Email"
                  name="email"
                  rules={['required']}
                  disabled={!emailEditable}
                  addonAfter={
                    emailEditable ? null : (
                      <span onClick={() => setEmailEditable(true)} title="Edit email">
                        <AiOutlineEdit />
                      </span>
                    )
                  }
                />
                <Input label="From" name="from" rules={['required']} disabled />
                <Input
                  label={
                    <LabelWithDetails
                      label="Stripe account"
                      link={
                        record?.stripeAccount
                          ? `https://dashboard.stripe.com/connect/accounts/${record.stripeAccount}`
                          : undefined
                      }
                      target="_blank"
                    />
                  }
                  name="stripeAccount"
                  disabled
                />
                <Input
                  label={
                    <LabelWithDetails
                      label="Stripe customer"
                      link={
                        record?.stripeCustomer
                          ? `https://dashboard.stripe.com/customers/${record.stripeCustomer}`
                          : undefined
                      }
                      target="_blank"
                    />
                  }
                  name="stripeCustomer"
                  disabled
                />
                <Select label="Language" name="languageId" rules={['required']} {...(languageSelectProps as any)} />
                <DatePicker label="Date of birth" name="dateOfBirth" format="DD-MM-YYYY" maxDate={dayjs(new Date())} />
              </Form>
            ),
          },
          {
            key: PageTab.EMAIL_HISTORY,
            label: 'Email History',
            children: <BlockEmailHistory userId={id as string} />,
          },
        ]}
      />
    </Edit>
  )
}
