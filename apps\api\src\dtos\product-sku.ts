import { ArgsType, Field, ID, Int, ObjectType, PickType, registerEnumType } from '@nestjs/graphql'
import { IsIn, IsNotEmpty, IsUUID, Matches } from 'class-validator'

import { LOCALE_ENABLED_LANGUAGES, WarrantyPeriodUnit } from '~/constants'
import { ImageEntity, ProductSkuEntity, TestedItemListEntity } from '~/entities'

@ArgsType()
export class GetProductSkuInput {
  @Field(() => String, { description: 'Current language in frontend' })
  @IsNotEmpty()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @Field({ description: 'The slug from frontend URL' })
  @IsNotEmpty()
  slug: string

  @Field({ description: 'The slug number from frontend URL' })
  @IsNotEmpty()
  @Matches(/^[1-9][0-9]*?$/, { message: 'Slug number must contain only numbers' })
  slugNumber: string

  @Field()
  channelId: string
}

@ObjectType()
class GetProductSkuAttributeOutput {
  @Field()
  name__en: string

  @Field()
  name__nl: string

  @Field()
  name__de: string

  @Field()
  name__pl: string
}

@ObjectType()
export class GetProductSkuOutput extends PickType(ProductSkuEntity, [
  'id',
  'slug__en',
  'slug__nl',
  'slug__de',
  'slug__pl',
  'slugNumber__en',
  'slugNumber__nl',
  'slugNumber__de',
  'slugNumber__pl',
  'name__en',
  'name__nl',
  'name__de',
  'name__pl',
  'desc__en',
  'desc__nl',
  'desc__de',
  'desc__pl',
  'grading',
  'testReport',
  'color',
  'sold',
  'heroImage',
  'images',
  'originalAccessories',
  'sellerJoinedYear',
  'extraTestedItems',
]) {
  @Field(() => [GetProductSkuAttributeOutput])
  attributes: GetProductSkuAttributeOutput[]

  @Field({ description: 'Description text for product with condition A in this category (English)' })
  gradingADesc__en: string

  @Field({ description: 'Description text for product with condition A in this category (Dutch)' })
  gradingADesc__nl: string

  @Field({ description: 'Description text for product with condition A in this category (German)' })
  gradingADesc__de: string

  @Field({ description: 'Description text for product with condition B in this category (English)' })
  gradingBDesc__en: string

  @Field({ description: 'Description text for product with condition B in this category (Dutch)' })
  gradingBDesc__nl: string

  @Field({ description: 'Description text for product with condition B in this category (German)' })
  gradingBDesc__de: string

  @Field({ description: 'Description text for product with condition C in this category (English)' })
  gradingCDesc__en: string

  @Field({ description: 'Description text for product with condition C in this category (Dutch)' })
  gradingCDesc__nl: string

  @Field({ description: 'Description text for product with condition C in this category (German)' })
  gradingCDesc__de: string

  @Field({ description: 'Description text for product with condition D in this category (English)' })
  gradingDDesc__en: string

  @Field({ description: 'Description text for product with condition D in this category (Dutch)' })
  gradingDDesc__nl: string

  @Field({ description: 'Description text for product with condition D in this category (German)' })
  gradingDDesc__de: string

  @Field({ description: 'Description text for product with condition A in this category (Polish)' })
  gradingADesc__pl: string

  @Field({ description: 'Description text for product with condition B in this category (Polish)' })
  gradingBDesc__pl: string

  @Field({ description: 'Description text for product with condition C in this category (Polish)' })
  gradingCDesc__pl: string

  @Field({ description: 'Description text for product with condition D in this category (Polish)' })
  gradingDDesc__pl: string

  @Field(() => TestedItemListEntity)
  testedItemList: TestedItemListEntity

  @Field()
  savedCo2: number

  @Field()
  savedEwaste: number

  @Field()
  price: number

  @Field()
  originalPrice: number

  @Field()
  brandNewPrice: number

  @Field()
  categorySlug__en: string

  @Field()
  categorySlug__nl: string

  @Field()
  categorySlug__de: string

  @Field()
  categorySlug__pl: string

  @Field()
  brandSlug__en: string

  @Field()
  brandSlug__nl: string

  @Field()
  brandSlug__de: string

  @Field()
  brandSlug__pl: string

  @Field()
  brandName__en: string

  @Field()
  brandName__nl: string

  @Field()
  brandName__de: string

  @Field()
  brandName__pl: string

  @Field()
  modelSlug__en: string

  @Field()
  modelSlug__nl: string

  @Field()
  modelSlug__de: string

  @Field()
  modelSlug__pl: string

  @Field()
  isDeal: boolean
}

registerEnumType(WarrantyPeriodUnit, {
  name: 'WarrantyPeriodUnitType',
})

@ObjectType()
export class GetProductSkuWarrantyOutput {
  @Field(() => ID)
  @IsUUID(4)
  id: string

  @Field()
  price: number

  @Field()
  @IsUUID(4)
  warrantyId: string

  @Field()
  isDefault: boolean

  @Field(() => Int)
  periodNumber: number

  @Field(() => WarrantyPeriodUnit)
  periodUnit: WarrantyPeriodUnit

  @Field()
  name__en: string

  @Field()
  name__nl: string

  @Field()
  name__de: string

  @Field()
  name__pl: string

  @Field()
  label__en: string

  @Field()
  label__nl: string

  @Field()
  label__de: string

  @Field()
  label__pl: string
}

@ObjectType()
export class GetProductSkuAccessoryProductOutput {
  @Field(() => ID)
  id: string

  @Field()
  name__en: string

  @Field()
  name__nl: string

  @Field()
  name__de: string

  @Field()
  name__pl: string

  @Field()
  desc__en: string

  @Field()
  desc__nl: string

  @Field()
  desc__de: string

  @Field()
  desc__pl: string

  @Field()
  price: number

  @Field(() => ImageEntity)
  heroImage: ImageEntity
}

@ArgsType()
export class GetProductSkuAccessoryProductsBySkuIdsInput {
  @Field()
  channelId: string

  @Field(() => [String])
  @IsUUID(4, { each: true })
  skuIds: string[]
}
