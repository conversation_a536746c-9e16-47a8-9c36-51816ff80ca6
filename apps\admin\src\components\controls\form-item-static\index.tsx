import { Form, FormInstance } from 'antd'
import { CSSProperties, FC } from 'react'

export type FormItemStaticProps = {
  label: JSX.Element | string
  name: string | (string | number)[]
  required?: boolean
  render?: (value: any, form?: FormInstance) => JSX.Element | string | null
  className?: string
  style?: CSSProperties
}

export const FormItemStatic: FC<FormItemStaticProps> = ({ label, required, className, style, name, render }) => {
  return (
    <Form.Item label={label} required={required} className={className} style={style} shouldUpdate>
      {(form) => (render ? render(form.getFieldValue(name), form) : form.getFieldValue(name))}
    </Form.Item>
  )
}
