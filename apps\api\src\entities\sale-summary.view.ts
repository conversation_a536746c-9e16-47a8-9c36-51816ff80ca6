import { ViewColumn, ViewEntity } from 'typeorm'

import { SUCCESSFUL_SALE_STATUSES } from '~/constants'

@ViewEntity({
  name: 'sale_summary',
  expression: `
    SELECT
      s.id,
      COUNT(DISTINCT si.id) AS items_count,
      SUM(si.price) AS total,
      s.is_returning_customer,
      a.country_id AS country,
      INITCAP(s.from::text) AS source,
      s.user_id AS user_id,
      CASE
        WHEN s.status IN ('${SUCCESSFUL_SALE_STATUSES.join("', '")}') THEN true
        ELSE false
      END AS is_successful,
      s.created_at,
      TO_CHAR(s.created_at, 'YYYY-MM-DD') AS day,
      TO_CHAR(s.created_at, 'YYYY-WW') AS week,
      TO_CHAR(s.created_at, 'YYYY-MM') AS month
    FROM "sale" s
    LEFT JOIN "sale_item" si ON si.sale_id = s.id
    LEFT JOIN "address" a ON s.address_id = a.id
    GROUP BY
      s.id, s.is_returning_customer, a.country_id, s.from, s.created_at, s.status
  `,
})
export class SaleSummaryEntity {
  @ViewColumn()
  id: number

  @ViewColumn()
  itemsCount: number

  @ViewColumn()
  total: number

  @ViewColumn()
  isReturningCustomer: boolean

  @ViewColumn()
  country: string

  @ViewColumn()
  source: string

  @ViewColumn()
  userId: number

  @ViewColumn()
  isSuccessful: boolean

  @ViewColumn()
  createdAt: Date

  @ViewColumn()
  day: string

  @ViewColumn()
  week: string

  @ViewColumn()
  month: string
}
