import { useNavigation } from '@refinedev/core'
import { Space } from 'antd'
import { FC } from 'react'
import { Link } from 'react-router-dom'

export type LabelWithDetailsProps = {
  link?: string
  label: JSX.Element | string
  target?: string
}

export const LabelWithDetails: FC<LabelWithDetailsProps> = ({ link, label, target }) => {
  const { push } = useNavigation()
  return link ? (
    <Space>
      {label}
      <Link
        to={link}
        target={target}
        rel={target ? 'noopener noreferrer' : undefined}
        onClick={(event) => {
          if (!target) {
            event.preventDefault()
            push(link)
          }
        }}
        title="Click to see more details"
      >
        (Details)
      </Link>
    </Space>
  ) : (
    label
  )
}
