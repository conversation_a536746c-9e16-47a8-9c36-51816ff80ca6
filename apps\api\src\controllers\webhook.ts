import { <PERSON>, <PERSON><PERSON>, Post, RawBody, UnauthorizedException } from '@nestjs/common'
import * as crypto from 'crypto'

import { envConfig } from '~/configs'
import { WebhookService } from '~/services'

@Controller('webhook')
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) {}

  @Post('sendcloud')
  async sendcloud(@RawBody() rawBody: Buffer, @Headers('sendcloud-signature') signature: string) {
    const body = rawBody.toString()
    const payload = JSON.parse(body)
    if (payload?.action !== 'parcel_status_changed') {
      return
    }

    const generatedSignature = crypto.createHmac('sha256', envConfig.SENDCLOUD_SK).update(body).digest('hex')

    if (!signature || signature !== generatedSignature) {
      throw new UnauthorizedException('Invalid signature')
    }

    return this.webhookService.handleSendcloudWebhook(payload)
  }
}
