import { EntityManager } from 'typeorm'

import {
  ProductModelAttributeCombinationChoiceEntity,
  ProductModelAttributeCombinationEntity,
  ProductModelEntity,
} from '~/entities'

const cartesianProduct = (arrays: any[][]): any[][] => {
  return arrays.reduce(
    (acc, curr) => {
      return acc.flatMap((a) => curr.map((b) => [...a, b]))
    },
    [[]]
  )
}

const getAllPossibleCombinations = (
  model: ProductModelEntity
): { choices: { attributeId: string; optionId: string }[] }[] => {
  const allChoices = model.attributes.map((attribute) =>
    attribute.options.map((option) => ({
      attributeId: attribute.id,
      optionId: option.id,
    }))
  )

  const combinations = cartesianProduct(allChoices)
  return combinations.map((combination) => ({ choices: combination }))
}

const areChoicesEqual = (
  choices1: { attributeId: string; optionId: string }[],
  choices2: { attributeId: string; optionId: string }[]
): boolean => {
  if (choices1.length !== choices2.length) return false
  return choices1.every((choice1) =>
    choices2.some((choice2) => choice1.attributeId === choice2.attributeId && choice1.optionId === choice2.optionId)
  )
}

const checkMissingCombinations = (
  productModel: ProductModelEntity
): { choices: { attributeId: string; optionId: string }[] }[] => {
  const allPossibleCombinations = getAllPossibleCombinations(productModel)

  const existingCombinations = productModel.attributeCombinations.map((combination) => combination.choices)

  const missingCombinations = allPossibleCombinations.filter((possibleCombination) => {
    return !existingCombinations.some((existingCombination) =>
      areChoicesEqual(possibleCombination.choices, existingCombination)
    )
  })

  return missingCombinations
}

export const productModelGenerateAttributeCombinations = async (
  modelId: string,
  manager: EntityManager = ProductModelEntity.getRepository().manager
) => {
  const model = await manager.findOne(ProductModelEntity, {
    where: { id: modelId },
    relations: { attributes: { options: true }, attributeCombinations: { choices: true } },
    select: {
      id: true,
      attributes: {
        id: true,
        sortOrder: true,
        options: {
          id: true,
          sortOrder: true,
        },
      },
      attributeCombinations: {
        id: true,
        choices: {
          attributeId: true,
          optionId: true,
        },
      },
    },
    order: {
      attributes: {
        sortOrder: 'ASC',
        options: {
          sortOrder: 'ASC',
        },
      },
    },
  })
  const missingCombinations = checkMissingCombinations(model)
  if (missingCombinations?.length) {
    for (const newCombination of missingCombinations) {
      const combination = await manager.save(manager.create(ProductModelAttributeCombinationEntity, { model }))
      for (const choice of newCombination.choices) {
        await manager.save(
          manager.create(ProductModelAttributeCombinationChoiceEntity, {
            attributeId: choice.attributeId,
            optionId: choice.optionId,
            combination,
          })
        )
      }
    }
  }
}
