<mjml lang="en">
  <mj-head>
    <mj-title>New order</mj-title>
    <mj-preview>Order number: {{orderNumber}}</mj-preview>
  </mj-head>
  <mj-body background-color="white">
    <mj-section>
      <mj-column>
        <mj-table>
          <tr>
            <td>
              <strong>Order Number: {{orderNumber}}</strong>
            </td>
            <td align="right">
              <strong>{{createdAt}}</strong>
            </td>
          </tr>
        </mj-table>
        <mj-text><h3>Products</h3></mj-text>
        {{#each skuItems}}
        <mj-table padding-top="16px">
          <tr style="border-bottom: 1px solid #000000">
            <td>
              <strong>{{grading}} | {{name}}<br />{{attributes}}</strong>
              {{#each accessories}}
              <br />{{this}} {{/each}}
            </td>
            <td style="vertical-align: top" align="right">
              {{#if originalPrice}}<del>{{originalPrice}}</del>{{/if}} <strong>{{price}}</strong>
            </td>
          </tr>
          <tr style="border-bottom: 1px solid #000000">
            <td>SKU</td>
            <td align="right">{{id}}</td>
          </tr>
          <tr style="border-bottom: 1px solid #000000">
            <td>Stock ID</td>
            <td align="right">{{stockId}}</td>
          </tr>
          {{#if saleNumber}}
          <tr style="border-bottom: 1px solid #000000">
            <td>Sale Number</td>
            <td align="right">{{saleNumber}}</td>
          </tr>
          {{/if}} {{#if serialNumber}}
          <tr style="border-bottom: 1px solid #000000">
            <td>Serial Number</td>
            <td align="right">{{serialNumber}}</td>
          </tr>
          {{/if}}
          <tr style="border-bottom: 1px solid #000000">
            <td>
              <strong>{{warrantyName}}</strong>
            </td>
            <td align="right">{{warrantyPrice}}</td>
          </tr>
        </mj-table>
        {{/each}}
        <mj-text><h3>Accessories</h3></mj-text>
        {{#each accessoryItems }}
        <mj-table padding-top="16px" padding-bottom="0">
          <tr style="border-bottom: 1px solid #000000">
            <td>
              <strong>{{name}}</strong><br />
              {{description}}
            </td>
            <td align="right" style="vertical-align: top"><strong>{{price}}</strong></td>
          </tr>
          <tr style="border-bottom: 1px solid #000000">
            <td>Quantity</td>
            <td align="right">{{quantity}}</td>
          </tr>
        </mj-table>
        {{/each}}
        <mj-table padding-top="16px" padding-bottom="0">
          <tr style="border-bottom: 1px solid #000000">
            <td>
              <h3>Total</h3>
            </td>
            <td align="right"><strong>{{total}}</strong></td>
          </tr>
        </mj-table>
        <mj-text><h3>Delivery address</h3></mj-text>
        <mj-text line-height="1.5">
          {{shippingFirstName}} {{shippingLastName}}<br />{{shippingAddress1}}<br />{{shippingAddress2}}<br />{{shippingCountry}}
        </mj-text>
        {{#if toPickupPoint}}
        <mj-text font-size="16px"><strong>Deliver the package to the nearest pickup point</strong></mj-text>
        {{/if}} {{#if trackingNumber}}
        <mj-text>
          <strong>Tracking number</strong>
          {{trackingNumber}}
        </mj-text>
        {{/if}} {{#if parcelId}}
        <mj-text>
          <strong>Parcel ID</strong>
          {{parcelId}}
        </mj-text>
        {{/if}} {{#if isReturningCustomer}}
        <mj-text><strong>This is a returning customer</strong></mj-text>
        {{/if}}
      </mj-column>
    </mj-section>
  </mj-body>
</mjml>
