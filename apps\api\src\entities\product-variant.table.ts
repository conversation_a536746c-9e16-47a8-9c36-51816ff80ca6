import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  CategoryEntity,
  type ICategory,
  type IProductModel,
  type IProductModelAttributeCombination,
  type IProductSku,
  type IProductVariantConditionCombination,
  type IProductVariantProblem,
  type ISaleItem,
  ProductModelAttributeCombinationEntity,
  ProductModelEntity,
  ProductSkuEntity,
  ProductVariantConditionCombinationEntity,
  ProductVariantProblemEntity,
  QuestionTypeEntity,
  SaleItemEntity,
} from '~/entities'

import { IProductVariantBasePrice, ProductVariantBasePriceEntity } from './product-variant-base-price.table'

@ObjectType('ProductVariant')
@Entity('product_variant')
export class ProductVariantEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column()
  slug__en: string

  @Field()
  @Column()
  slug__nl: string

  @Field()
  @Column()
  slug__de: string

  @Field()
  @Column()
  slug__pl: string

  @Field(() => [ProductVariantBasePriceEntity])
  @OneToMany(() => ProductVariantBasePriceEntity, (price) => price.variant, { nullable: false, cascade: true })
  basePrices: Relation<ProductVariantBasePriceEntity>[]

  @OneToOne(() => ProductModelAttributeCombinationEntity, (combination) => combination.variant, {
    nullable: false,
  })
  attributeCombination: Relation<ProductModelAttributeCombinationEntity>

  @Field(() => [ProductVariantConditionCombinationEntity])
  @OneToMany(() => ProductVariantConditionCombinationEntity, (conditionPrice) => conditionPrice.variant, {
    nullable: false,
  })
  conditionCombinations: Relation<ProductVariantConditionCombinationEntity>[]

  @Field(() => [ProductVariantConditionCombinationEntity])
  @OneToMany(() => ProductVariantProblemEntity, (problemPrice) => problemPrice.variant, { nullable: false })
  problems: Relation<ProductVariantProblemEntity>[]

  @ManyToOne(() => ProductModelEntity, (productModel) => productModel.variants, {
    nullable: false,
  })
  model: Relation<ProductModelEntity>

  @Column()
  @RelationId((variant: ProductVariantEntity) => variant.model)
  modelId: string

  @ManyToOne(() => CategoryEntity, (category) => category.productVariants, {
    nullable: false,
  })
  category: Relation<CategoryEntity>

  @Column()
  @RelationId((variant: ProductVariantEntity) => variant.category)
  categoryId: string

  @ManyToOne(() => QuestionTypeEntity, (questionType) => questionType.productVariants, {
    nullable: false,
  })
  questionType: Relation<QuestionTypeEntity>

  // TODO: this shouldn't be nullable
  @Column({ nullable: true })
  @RelationId((productVariant: ProductModelEntity) => productVariant.questionType)
  questionTypeId: string

  @OneToMany(() => ProductSkuEntity, (sku) => sku.variant, { nullable: false })
  productSkus: Relation<ProductSkuEntity>[]

  @OneToMany(() => SaleItemEntity, (saleItem) => saleItem.productVariant, { nullable: false })
  saleItems: Relation<SaleItemEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IProductVariant = Omit<ProductVariantEntity, keyof BaseEntity> & {
  basePrices: IProductVariantBasePrice
  attributeCombination: IProductModelAttributeCombination
  conditionCombinations: IProductVariantConditionCombination[]
  problems: IProductVariantProblem[]
  model: IProductModel
  category: ICategory
  productSkus: IProductSku[]
  saleItems: ISaleItem[]
}
