import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps } from '@refinedev/core'
import type { ILocaleCurrency } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input } from 'antx'
import { type FC } from 'react'

export const LocaleCurrencyEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, formLoading } = useForm<ILocaleCurrency, HttpError, ILocaleCurrency>()

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Input label="ID" name="id" rules={['required']} disabled />
        <Input label="Name" name="name" rules={['required']} />
        <Input
          label="Symbol"
          name="symbol"
          rules={['required']}
          normalize={(value) =>
            value
              .replace(/[a-zA-Z]/g, '')
              .trim()
              .substring(0, 1)
          }
        />
      </Form>
    </Edit>
  )
}
