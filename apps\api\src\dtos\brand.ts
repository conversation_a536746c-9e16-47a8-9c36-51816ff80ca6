import { ArgsType, Field, ObjectType, PickType } from '@nestjs/graphql'
import { IsIn, IsNotEmpty } from 'class-validator'

import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { BrandEntity } from '~/entities'

@ArgsType()
export class GetBrandsByCategoryInput {
  @Field(() => String, { description: 'Current language in frontend' })
  @IsNotEmpty()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @Field({ description: 'The slug from frontend URL' })
  @IsNotEmpty()
  slug: string
}

@ObjectType('GetBrands')
export class GetBrandsOutput extends PickType(BrandEntity, [
  'id',
  'name__en',
  'name__nl',
  'name__de',
  'name__pl',
  'slug__en',
  'slug__nl',
  'slug__de',
  'slug__pl',
  'image',
] as const) {}
