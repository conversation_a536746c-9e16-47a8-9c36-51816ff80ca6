/**
 * Represents a Sendcloud service point object
 */
export type IServicePoint = {
  id: number
  code: string
  is_active: boolean
  shop_type?: string
  extra_data: Record<string, unknown> // Can contain carrier specific data
  name: string
  street: string
  house_number: string
  postal_code: string
  city: string
  latitude: string
  longitude: string
  email: string
  phone: string
  homepage: string
  carrier: string // Example: postnl
  country: string // Example: NL
  formatted_opening_times: {
    0: string[]
    1: string[]
    2: string[]
    3: string[]
    4: string[]
    5: string[]
    6: string[]
  }
  open_tomorrow: boolean
  open_upcoming_week: boolean
  distance: number // Distance between the reference point and the service point in meters
}
