import { Injectable } from '@nestjs/common'
import { uniq } from 'lodash'
import ms from 'ms'
import { FindOptionsRelations, In, IsNull, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { BrandEntity, CategoryEntity } from '~/entities'

@Injectable()
export class BrandService {
  async findAll(relations: FindOptionsRelations<BrandEntity>) {
    return BrandEntity.find({
      where: { publishedAt: Not(IsNull()) },
      relations,
      order: { sortOrder: 'ASC' },
      cache: envConfig.isProd ? ms('1 day') : false,
    })
  }

  async findAllByCategory({
    lang,
    slug,
    relations,
  }: {
    lang: string
    slug: string
    relations: FindOptionsRelations<BrandEntity>
  }) {
    // The reason of not using findOne is because findOne with deep select of OneToMany / ManyToMany relations only return 1 result
    const resetOptions = {
      relations: { productSeries: true },
      select: { id: true, productSeries: { brandId: true } },
    } as const
    let category = (
      await CategoryEntity.find({
        where: { publishedAt: Not(IsNull()), [`slug__${lang}`]: slug },
        ...resetOptions,
        cache: envConfig.isProd ? ms('1 hour') : false,
      })
    )?.[0]

    // Check other languages, make sure language switching has no issue
    if (!category) {
      category = (
        await CategoryEntity.find({
          where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({
            publishedAt: Not(IsNull()),
            [`slug__${l}`]: slug,
          })),
          ...resetOptions,
        })
      )?.[0]
    }

    if (!category) {
      return []
    }

    const brandIds = uniq(category.productSeries.map(({ brandId }) => brandId))

    return BrandEntity.find({
      where: { publishedAt: Not(IsNull()), id: In(brandIds) },
      relations,
      order: { sortOrder: 'ASC' },
      cache: envConfig.isProd ? ms('1 hour') : false,
    })
  }
}
