import { Injectable } from '@nestjs/common'
import ms from 'ms'
import { FindOptionsRelations, IsNull, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { CategoryEntity } from '~/entities'

@Injectable()
export class CategoryService {
  async findAll(relations: FindOptionsRelations<CategoryEntity>) {
    return await CategoryEntity.find({
      where: { publishedAt: Not(IsNull()) },
      order: { sortOrder: 'ASC' },
      relations,
      cache: envConfig.isProd ? ms('1 day') : false,
    })
  }
}
