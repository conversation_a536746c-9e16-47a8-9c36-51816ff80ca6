import { MigrationInterface, QueryRunner } from 'typeorm'

export class OrderSaleStatusChange1730777897719 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE "order"
      SET status = 'COMPLETED'
      WHERE status = 'COMPLETE'
    `)

    await queryRunner.query(`
      UPDATE "order_sku_item"
      SET status = 'COMPLETED'
      WHERE status = 'COMPLETE'
    `)

    await queryRunner.query(`
      UPDATE "pre_order"
      SET status = 'COMPLETED'
      WHERE status = 'COMPLETE'
    `)

    await queryRunner.query(`
      UPDATE "sale"
      SET status = 'COMPLETED'
      WHERE status = 'COMPLETE'
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      UPDATE "order"
      SET status = 'COMPLETE'
      WHERE status = 'COMPLETED'
    `)

    await queryRunner.query(`
      UPDATE "order_sku_item"
      SET status = 'COMPLETE'
      WHERE status = 'COMPLETED'
    `)

    await queryRunner.query(`
      UPDATE "pre_order"
      SET status = 'COMPLETE'
      WHERE status = 'COMPLETED'
    `)

    await queryRunner.query(`
      UPDATE "sale"
      SET status = 'COMPLETE'
      WHERE status = 'COMPLETED'
    `)
  }
}
