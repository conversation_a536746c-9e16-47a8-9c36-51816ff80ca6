export enum SaleItemOfferStatus {
  INITIAL = 'INITIAL',
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  DECLINED_RECYCLE = 'DECLINED_RECYCLE',
  DECLINED_RETURN = 'DECLINED_RETURN',
  EXPIRED = 'EXPIRED',
}

export enum SaleItemOfferErrorType {
  EXPIRED = 'EXPIRED',
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_ACCEPTED = 'ALREADY_ACCEPTED',
  ALREADY_REJECTED = 'ALREADY_REJECTED',
  NOT_AUTHORIZED = 'NOT_AUTHORIZED',
}

export enum SaleItemOfferConfirmationType {
  ACCEPT = 'ACCEPT',
  DECLINE_RECYCLE = 'DECLINE_RECYCLE',
  DECLINE_RETURN = 'DECLINE_RETURN',
}

export const OFFER_EXPIRATION_DAYS = 14
export const OFFER_ABANDONMENT_MONTHS = 6
