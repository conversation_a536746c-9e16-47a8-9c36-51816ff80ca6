import { ProCard } from '@ant-design/pro-components'
import { Button, Form, FormInstance, Tooltip } from 'antd'
import { Input, Watch } from 'antx'
import cx from 'clsx'
import { FC, useEffect, useMemo, useState } from 'react'
import { AiOutlineDelete, AiOutlinePlus } from 'react-icons/ai'
import { v4 as uuid } from 'uuid'

import { InputMDXEditor, InputMultiLang, LanguageTabChoices } from '~/components'
export type InputModalProps = {
  name: string | number | (string | number)[]
  parentName?: string | number | (string | number)[]
  label?: string
  required?: boolean
  currentLanguage: LanguageTabChoices
  form: FormInstance
}

export type InputModalItemProps = {
  name: string | number | (string | number)[]
  parentName?: string | number | (string | number)[]
  required?: boolean
  currentLanguage: LanguageTabChoices
  form: FormInstance
}

const InputModalItem: FC<InputModalItemProps> = ({ required, name, parentName, currentLanguage, form }) => {
  if (!Array.isArray(name)) {
    name = [name]
  }
  if (parentName) {
    if (!Array.isArray(parentName)) {
      parentName = [parentName]
    }
  } else {
    parentName = []
  }

  const [showFields, setShowFields] = useState(Boolean(required))

  useEffect(() => {
    if (form && !required) {
      const values = form.getFieldValue([...(parentName as string[]), ...(name as string[])])
      if (values) {
        setShowFields(true)
      }
    }
  }, [form, required, parentName, name])

  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })

  // TODO: id in edit form will prompt an error
  const id = useMemo(() => {
    const savedId = form.getFieldValue([...(parentName as string[]), ...(name as string[]), 'id'])
    return savedId ?? uuid()
  }, [form, parentName, name])

  return (
    <ProCard
      bordered
      extra={
        showFields && !required ? (
          <Tooltip title="Delete modal" key="delete">
            <AiOutlineDelete
              className="size-4 cursor-pointer transition-[color] duration-[0.3s] ease-[ease-in-out] hover:text-[#4096ff]"
              onClick={() => {
                form.setFieldValue([...(parentName as string[]), ...(name as string[])], null)
                setShowFields(false)
              }}
            />
          </Tooltip>
        ) : undefined
      }
    >
      {showFields ? (
        <>
          <Input name={[...name, 'id']} hidden noStyle initialValue={id} />
          <Watch
            list={[
              [...(parentName as string[]), ...(name as string[]), 'title__en'],
              [...(parentName as string[]), ...(name as string[]), 'title__nl'],
              [...(parentName as string[]), ...(name as string[]), 'title__de'],
            ]}
          >
            {([title__en, title__nl, title__de]) => (
              <>
                <InputMultiLang
                  sources={{ en: title__en, nl: title__nl, de: title__de }}
                  targetLang="en"
                  label="Question (English)"
                  name={[...(Array.isArray(name) ? name : [name]), 'title__en']}
                  rules={['required']}
                  className={clsHideEn}
                  fillType="translate"
                />
                <InputMultiLang
                  sources={{ en: title__en, nl: title__nl, de: title__de }}
                  targetLang="nl"
                  label="Question (Dutch)"
                  name={[...(Array.isArray(name) ? name : [name]), 'title__nl']}
                  rules={['required']}
                  className={clsHideNl}
                  fillType="translate"
                />
                <InputMultiLang
                  sources={{ en: title__en, nl: title__nl, de: title__de }}
                  targetLang="de"
                  label="Question (German)"
                  name={[...(Array.isArray(name) ? name : [name]), 'title__de']}
                  rules={['required']}
                  className={clsHideDe}
                  fillType="translate"
                />
              </>
            )}
          </Watch>

          <InputMDXEditor
            name={[...name, 'content__en']}
            label="Content (English)"
            className={cx('col-span-2', clsHideEn)}
            rules={['required']}
          />
          <InputMDXEditor
            name={[...name, 'content__nl']}
            label="Content (Dutch)"
            className={cx('col-span-2', clsHideNl)}
            rules={['required']}
          />
          <InputMDXEditor
            name={[...name, 'content__de']}
            label="Content (German)"
            className={cx('col-span-2', clsHideDe)}
            rules={['required']}
          />
        </>
      ) : (
        <>
          <Input type="hidden" name={name} hidden noStyle value={undefined} />
          <Button
            type="dashed"
            onClick={() => setShowFields(true)}
            block
            icon={<AiOutlinePlus />}
            className="col-span-2 flex items-center justify-center"
          >
            Add modal
          </Button>
        </>
      )}
    </ProCard>
  )
}

export const InputModal = ({ name, parentName, label, required, currentLanguage, form }: InputModalProps) => {
  return (
    <Form.Item label={label} required={required} className="input-modal-wrapper" shouldUpdate>
      <InputModalItem name={name} parentName={parentName} currentLanguage={currentLanguage} form={form} />
    </Form.Item>
  )
}
