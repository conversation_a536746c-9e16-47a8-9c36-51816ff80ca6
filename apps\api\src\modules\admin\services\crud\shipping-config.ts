import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { IsNull, Repository } from 'typeorm'

import { ShippingConfigEntity } from '~/entities'

export class CrudShippingConfigService extends TypeOrmCrudService<ShippingConfigEntity> {
  constructor(@InjectRepository(ShippingConfigEntity) repo: Repository<ShippingConfigEntity>) {
    super(repo)
  }

  async saveForChannel(channelId: string, configs: Partial<ShippingConfigEntity>[]) {
    // Delete old configs for the channel
    if (channelId) {
      const configsToDelete = await this.repo.find({ where: { channelId } })
      await this.repo.remove(configsToDelete)
    } else {
      const configsToDelete = await this.repo.find({ where: { channelId: IsNull() } })
      await this.repo.remove(configsToDelete)
    }
    // Save new configs
    await this.repo.save(configs.map((config) => ({ ...config, channelId })))
  }
}
