import { Create, useForm, useSelect } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { ITestedItem, ITestedItemList } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber } from 'antx'
import { FC, useMemo } from 'react'
import { v4 as uuid } from 'uuid'

import { InputMultiEntitySelect } from '~/components'

export const TestedItemListCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps } = useForm<ITestedItemList, HttpError, ITestedItemList>()
  const id = useMemo(() => uuid(), [])

  const { selectProps: listSelectProps } = useSelect<ITestedItem>({
    resource: 'admin/tested-items',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Input noStyle hidden name="id" initialValue={id} />
        <Input label="Internal name" name="internalName" rules={['required']} />
        <InputMultiEntitySelect label="Tested items" name="items" {...(listSelectProps as any)} rules={['required']} />
        <InputNumber label="Total items count" precision={0} min={0} name="totalItemsCount" rules={['required']} />
      </Form>
    </Create>
  )
}
