import { MigrationInterface, QueryRunner } from 'typeorm'

export class QuestionTypeConditionOptionFix1731730348270 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update name__de field
    await queryRunner.query(`
      UPDATE question_type_condition_option 
      SET name__de = regexp_replace(name__de, '([0-9]+)%', '\\1 %', 'g')
      WHERE name__de ~ '[0-9]+%'
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
