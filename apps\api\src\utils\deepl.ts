import * as deepl from 'deepl-node'

import { envConfig } from '~/configs'
import { LOCALE_ENABLED_LANGUAGES } from '~/constants'

const translator = new deepl.Translator(envConfig.DEEPL_KEY)

export type TranslateOptions = {
  text: string
  target: (typeof LOCALE_ENABLED_LANGUAGES)[number]
  source?: (typeof LOCALE_ENABLED_LANGUAGES)[number] | 'auto'
}

export const deeplTranslate = async ({ text, target, source }: TranslateOptions) => {
  if (source && source !== target) {
    const { text: translatedText } = await translator.translateText(
      text,
      source === 'auto' ? null : source,
      target === 'en' ? 'en-GB' : target,
      {
        preserveFormatting: true,
        tagHandling: 'xml',
        ignoreTags: ['keep'],
      }
    )
    return translatedText
  }
  return text
}
