import { createReadStream } from 'node:fs'
import { Readable } from 'node:stream'

import { google } from 'googleapis'

import { envConfig } from '~/configs'

const SCOPES = ['https://www.googleapis.com/auth/spreadsheets', 'https://www.googleapis.com/auth/drive.file']

interface PaymentData {
  name: string
  iban: string
  amount: number
  description: string
  stockId: string
}

// Initialize Google services
const getGoogleAuth = () => {
  const credentials = JSON.parse(envConfig.GOOGLE_SERVICE_ACCOUNT_KEY)
  return new google.auth.GoogleAuth({
    credentials,
    scopes: SCOPES,
  })
}

const auth = getGoogleAuth()
const googleSheets = google.sheets({ version: 'v4', auth })
const googleDrive = google.drive({ version: 'v3', auth })

export const googleApiUploadExcelToGoogleSheets = async (excelFilePath: string): Promise<string> => {
  const fileMetadata = {
    name: 'Payment list',
    mimeType: 'application/vnd.google-apps.spreadsheet',
  }

  const media = {
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    body: createReadStream(excelFilePath),
  }

  const driveResponse = await googleDrive.files.create({
    requestBody: fileMetadata,
    media: media,
    fields: 'id',
  })

  return driveResponse.data.id!
}

export const googleApiWritePaymentData = async (spreadsheetId: string, payments: PaymentData[]): Promise<void> => {
  if (payments.length > 1000) {
    throw new Error('Maximum allowed payments is 1000')
  }

  const values = payments.map((payment) => [
    payment.name,
    payment.iban,
    payment.amount,
    payment.description,
    payment.stockId,
  ])

  await googleSheets.spreadsheets.values.update({
    spreadsheetId,
    range: `B12:F${11 + payments.length}`,
    valueInputOption: 'RAW',
    requestBody: {
      values,
    },
  })
}

export const googleApiWriteValues = async (
  spreadsheetId: string,
  range: string,
  values: (string | number)[][]
): Promise<void> => {
  await googleSheets.spreadsheets.values.update({
    spreadsheetId,
    range,
    valueInputOption: 'RAW',
    requestBody: {
      values,
    },
  })
}

export const googleApiWriteToSpreadsheet = async (
  spreadsheetId: string,
  range: string,
  values: unknown[][]
): Promise<void> => {
  if (values.length > 1000) {
    throw new Error('Maximum allowed rows is 1000')
  }

  await googleSheets.spreadsheets.values.update({
    spreadsheetId,
    range,
    valueInputOption: 'RAW',
    requestBody: {
      values,
    },
  })
}

export const googleApiGetSpreadsheetStream = async (
  spreadsheetId: string
): Promise<{ stream: Readable; mimeType: string }> => {
  const response = await googleDrive.files.export(
    {
      fileId: spreadsheetId,
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
    {
      responseType: 'stream',
    }
  )

  return {
    stream: response.data as Readable,
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  }
}

export const googleApiDeleteFile = async (fileId: string): Promise<void> => {
  await googleDrive.files.delete({
    fileId,
  })
}
