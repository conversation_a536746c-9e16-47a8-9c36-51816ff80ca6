import { Args, Context, Mutation, Query, Resolver } from '@nestjs/graphql'
import { isIP } from 'class-validator'
import type { Request } from 'express'

import {
  CreateSaleInput,
  CreateSaleOutput,
  GetSaleCartItemPricesInput,
  GetSaleCartItemPricesOutput,
  ValidateSellerInfoInput,
  ValidateSellerInfoOutput,
} from '~/dtos'
import { ProductModelService, SaleService } from '~/services'

@Resolver()
export class SaleResolver {
  constructor(
    protected readonly modelService: ProductModelService,
    protected readonly saleService: SaleService
  ) {}

  @Query(() => GetSaleCartItemPricesOutput)
  async getSaleCartItemPrices(@Args() { channelId, items }: GetSaleCartItemPricesInput) {
    return this.saleService.getSaleCartItemPrices({ channelId, items })
  }

  @Mutation(() => CreateSaleOutput)
  createSale(@Args() input: CreateSaleInput): Promise<CreateSaleOutput> {
    return this.saleService.createSale({
      input,
    })
  }

  @Query(() => ValidateSellerInfoOutput)
  async validateSellerInfo(
    @Args() input: ValidateSellerInfoInput,
    @Context() { req }: { req: Request }
  ): Promise<ValidateSellerInfoOutput> {
    return this.saleService.validateSellerInfo(input, isIP(req.ip, 4) ? req.ip : '***********')
  }
}
