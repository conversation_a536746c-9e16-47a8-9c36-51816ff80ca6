import { <PERSON><PERSON><PERSON>, EditButton, FilterDropdown, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { ShipmentTrackingStatusCode, ShipmentTrackingStatusMessage, ShipmentType } from '@valyuu/api/constants'
import type { IShipment } from '@valyuu/api/entities'
import { Input, Popover, Select, Space, Table, Tag } from 'antd'
import { capitalize } from 'lodash'
import type { FC } from 'react'

import { formatSelectOptionsFromEnum, getSendCloudParcelUrl, handleTableRowClick } from '~/utils'

const typeColorMap = {
  [ShipmentType.SALE]: 'green',
  [ShipmentType.SALE_ITEM_RETURN]: 'gold',
  [ShipmentType.ORDER]: 'purple',
}

const statusColorMap = {
  // Successful states
  [ShipmentTrackingStatusCode.DELIVERED]: 'success',
  [ShipmentTrackingStatusCode.SHIPMENT_COLLECTED_BY_CUSTOMER]: 'success',

  // In-progress states
  [ShipmentTrackingStatusCode.ANNOUNCED]: 'processing',
  [ShipmentTrackingStatusCode.BEING_ANNOUNCED]: 'processing',
  [ShipmentTrackingStatusCode.EN_ROUTE_TO_SORTING_CENTER]: 'processing',
  [ShipmentTrackingStatusCode.PARCEL_EN_ROUTE]: 'processing',
  [ShipmentTrackingStatusCode.DRIVER_EN_ROUTE]: 'processing',
  [ShipmentTrackingStatusCode.SHIPMENT_PICKED_UP_BY_DRIVER]: 'processing',

  // Warning/Attention needed states
  [ShipmentTrackingStatusCode.BEING_SORTED]: 'warning',
  [ShipmentTrackingStatusCode.NOT_SORTED]: 'warning',
  [ShipmentTrackingStatusCode.DELIVERY_DELAYED]: 'warning',
  [ShipmentTrackingStatusCode.AT_CUSTOMS]: 'warning',
  [ShipmentTrackingStatusCode.AWAITING_CUSTOMER_PICKUP]: 'warning',
  [ShipmentTrackingStatusCode.AT_SORTING_CENTRE]: 'warning',

  // Error states
  [ShipmentTrackingStatusCode.DELIVERY_ATTEMPT_FAILED]: 'error',
  [ShipmentTrackingStatusCode.ERROR_COLLECTING]: 'error',
  [ShipmentTrackingStatusCode.UNABLE_TO_DELIVER]: 'error',
  [ShipmentTrackingStatusCode.ADDRESS_INVALID]: 'error',
  [ShipmentTrackingStatusCode.ANNOUNCEMENT_FAILED]: 'error',
  [ShipmentTrackingStatusCode.PARCEL_CANCELLATION_FAILED]: 'error',
  [ShipmentTrackingStatusCode.EXCEPTION]: 'error',

  // Cancelled/Returned states
  [ShipmentTrackingStatusCode.CANCELLED]: 'default',
  [ShipmentTrackingStatusCode.CANCELLED_UPSTREAM]: 'default',
  [ShipmentTrackingStatusCode.CANCELLATION_REQUEST]: 'default',
  [ShipmentTrackingStatusCode.SUBMITTING_CANCELLATION_REQUEST]: 'default',
  [ShipmentTrackingStatusCode.RETURNED_TO_SENDER]: 'orange',
  [ShipmentTrackingStatusCode.REFUSED_BY_RECIPIENT]: 'orange',

  // Changed/Updated states
  [ShipmentTrackingStatusCode.DELIVERY_METHOD_CHANGED]: 'cyan',
  [ShipmentTrackingStatusCode.DELIVERY_ADDRESS_CHANGED]: 'cyan',
  [ShipmentTrackingStatusCode.DELIVERY_DATE_CHANGED]: 'cyan',

  // Initial/Neutral states
  [ShipmentTrackingStatusCode.READY_TO_SEND]: 'blue',
  [ShipmentTrackingStatusCode.SORTED]: 'blue',
  [ShipmentTrackingStatusCode.ANNOUNCED_NOT_COLLECTED]: 'blue',

  // Unknown/Default states
  [ShipmentTrackingStatusCode.UNKNOWN_STATUS]: 'default',
  [ShipmentTrackingStatusCode.NO_LABEL]: 'default',
}

export const ShipmentList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IShipment>({
    syncWithLocation: true,
    meta: {
      join: [
        {
          field: 'shippingMethod',
          select: ['name'],
        },
        {
          field: 'warehouse',
          select: ['countryId', 'name'],
        },
        {
          field: 'partner',
          select: ['name'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/shipments', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="type"
          title="Type"
          className="cursor-pointer"
          width="7rem"
          render={(value: ShipmentType) => (
            <Tag color={typeColorMap[value]}>{capitalize(value.replace(/_/g, ' '))}</Tag>
          )}
          defaultFilteredValue={getDefaultFilter('type', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} options={formatSelectOptionsFromEnum(ShipmentType)} />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="statusCode"
          title="Status"
          className="cursor-pointer"
          width="8rem"
          render={(value, record: IShipment) => (
            <Tag color={statusColorMap[value as keyof typeof statusColorMap]}>{record.statusMessage ?? 'Unknown'}</Tag>
          )}
          defaultFilteredValue={getDefaultFilter('type', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                showSearch
                optionFilterProp="label"
                style={{ minWidth: 200 }}
                options={Object.entries(ShipmentTrackingStatusCode)
                  .filter(([key]) => /^\d+$/.test(key))
                  .map(([key, value]) => ({
                    label: ShipmentTrackingStatusMessage[value as keyof typeof ShipmentTrackingStatusMessage],
                    value: key,
                  }))}
              />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="parcelId"
          title="Parcel ID"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('parcelId', filters)}
          render={(value) => (
            <a
              title="View parcel on SendCloud"
              href={getSendCloudParcelUrl(value)}
              onClick={(e) => e.stopPropagation()}
              target="_blank"
            >
              {value}
            </a>
          )}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input placeholder="Search parcel ID" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="trackingNumber"
          title="Tracking Number"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('trackingNumber', filters)}
          render={(value, row: IShipment) =>
            row.trackingUrl ? (
              <a title="View tracking page" href={row.trackingUrl} target="_blank" onClick={(e) => e.stopPropagation()}>
                {value}
              </a>
            ) : (
              value
            )
          }
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input placeholder="Search tracking number" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="isPaperless"
          title="Label Type"
          className="cursor-pointer"
          width="6rem"
          render={(value) => (
            <Tag color={value ? 'geekblue' : 'purple'}>{value ? 'Paperless Code' : 'Shipping Label'}</Tag>
          )}
        />
        <Table.Column
          dataIndex={['shippingMethod', 'name']}
          title="Shipping Method"
          className="cursor-pointer"
          width="9rem"
          render={(value) => (
            <div title={value} className="w-full max-w-24 overflow-hidden text-ellipsis text-nowrap">
              {value}
            </div>
          )}
        />
        <Table.Column dataIndex={['warehouse', 'name']} title="Warehouse" className="cursor-pointer" />
        <Table.Column dataIndex={['partner', 'name']} title="Partner" className="cursor-pointer" />
        <Table.Column
          dataIndex="note"
          title="Note"
          className="cursor-pointer overflow-hidden"
          ellipsis={true}
          render={(value) => (
            <Popover content={value} placement="topLeft">
              <div className="max-w-16 truncate">{value}</div>
            </Popover>
          )}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IShipment>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
