import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Body, Controller, Get, Param, Post, Query, Res, UseGuards } from '@nestjs/common'
import { Response } from 'express'

import { PaymentListProblemItemReason, PaymentListType } from '~/constants'
import { PaymentListEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudPaymentListService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: PaymentListEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
  query: {
    join: {
      individualItems: {},
      bulkItems: {},
    },
  },
})
@Controller('admin/payment-lists')
export class CrudPaymentListsController implements CrudController<PaymentListEntity> {
  constructor(public service: CrudPaymentListService) {}

  @Get('upcoming')
  async upcoming(@Query('sort') sort: string[] = []) {
    const [orderByField, order] = sort?.[0]?.split(',') ?? []
    return this.service.upcoming(orderByField, order as 'ASC' | 'DESC')
  }

  @Get(':id/details')
  async details(@Param('id') id: string) {
    return this.service.details(id)
  }

  @Post('remove-item')
  async removeItem(
    @Body() body: { type: PaymentListType; itemId: string; reason: PaymentListProblemItemReason; note?: string }
  ) {
    return this.service.removeItem(body)
  }

  @Post('resolve-item')
  async resolveItem(@Body('id') id: string) {
    return this.service.resolveItem(id)
  }

  @Post('update-item-amount')
  async updateItemAmount(@Body('id') id: string) {
    return this.service.updateItemAmount(id)
  }

  @Post('update-bank-info')
  async updateBankInfo(@Body() body: { id: string; beneficiaryName: string; iban: string }) {
    return this.service.updateBankInfo(body)
  }

  @Post(':id/approve')
  async approve(@Param('id') id: string) {
    return this.service.approve(id)
  }

  @Get(':id/check-is-updated')
  async checkIsUpdated(@Param('id') id: string) {
    return this.service.checkIsUpdated(id)
  }

  @Get(':id/download-excel')
  async downloadExcel(@Param('id') id: string, @Res() res: Response) {
    return this.service.downloadExcel(id, res)
  }

  @Post(':id/mark-as-paid')
  async markAsPaid(@Param('id') id: string, @Body('referenceNumber') referenceNumber: string) {
    return this.service.markAsPaid(id, referenceNumber)
  }
}
