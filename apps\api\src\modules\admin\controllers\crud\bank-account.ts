import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { BankAccountEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudBankAccountService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: BankAccountEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      user: {},
      payments: {},
    },
  },
})
@Controller('admin/bank-accounts')
export class CrudBankAccountController implements CrudController<BankAccountEntity> {
  constructor(public service: CrudBankAccountService) {}
}
