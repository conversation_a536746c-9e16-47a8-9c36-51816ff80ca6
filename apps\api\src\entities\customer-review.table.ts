import { Field, ID, Int, ObjectType } from '@nestjs/graphql'
import { BaseEntity, Column, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm'

@ObjectType('CustomerReview')
@Entity('customer_review')
export class CustomerReviewEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryColumn({ type: 'char', length: 2 })
  id: string

  @Field()
  @Column({
    type: 'decimal',
    precision: 2,
    scale: 1,
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  googleScore: number

  @Field()
  @Column({
    type: 'decimal',
    precision: 2,
    scale: 1,
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  trustpilotScore: number

  @Field(() => Int)
  @Column({ type: 'int', unsigned: true })
  trustpilotCount: number

  @UpdateDateColumn()
  updatedAt: Date
}

export type ICustomerReview = Omit<CustomerReviewEntity, keyof BaseEntity>
