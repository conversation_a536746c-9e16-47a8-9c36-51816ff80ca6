import { DateField, EditButton, FilterDropdown, getDefaultSortOrder, List, useSelect, useTable } from '@refinedev/antd'
import { getDefaultFilter, HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { SalePaymentType, SaleShippingLabelType, SaleStatus } from '@valyuu/api/constants'
import type { ISale, ISaleItem } from '@valyuu/api/entities'
import type { IPartner } from '@valyuu/api/entities'
import { Badge, Popover, Radio, Space, Table, Tag } from 'antd'
import { Input, Select } from 'antx'
import { capitalize } from 'lodash'
import { type FC, useState } from 'react'

import { FormTableSearch } from '~/components'
import { formatSelectOptionsFromEnum, handleTableRowClick, isUUID } from '~/utils'

const statusColorMap = {
  [SaleStatus.SUBMITTED]: 'default',
  [SaleStatus.IN_TRANSIT]: 'warning',
  [SaleStatus.RECEIVED]: 'processing',
  [SaleStatus.CANCELLED]: 'grey',
  [SaleStatus.COMPLETED]: 'success',
}

const paymentTypeMap = {
  [SalePaymentType.BANK_TRANSFER]: 'gold',
  [SalePaymentType.DONATION]: 'magenta',
  [SalePaymentType.BULK_SETTLEMENT]: 'purple',
}

export const SaleList: FC<IResourceComponentsProps> = () => {
  const [searchedStockId, setSearchedStockId] = useState<string>()
  const { tableProps, sorters, filters, searchFormProps, tableQueryResult } = useTable<
    ISale,
    HttpError,
    { search: string }
  >({
    syncWithLocation: true,
    meta: {
      fields: [
        'id',
        'saleNumber',
        'status',
        'paymentType',
        'shippingLabel',
        'isLabelSent',
        'isReturningCustomer',
        'partnerPlatform',
        'note',
        'isLegacy',
        'receivedAt',
        'createdAt',
      ],
      join: [
        {
          field: 'user',
          select: ['email'],
        },
        {
          field: 'saleItems',
          select: ['id', 'stockId'],
        },
        {
          field: 'saleItems.productVariant',
          select: ['name__en'],
        },
        {
          field: 'shipment',
          select: ['id', 'trackingNumber'],
        },
        {
          field: 'partner',
          select: ['id', 'name'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    onSearch: ({ search }) => {
      search = (search ?? '').trim()
      switch (true) {
        case !search:
          setSearchedStockId(undefined)
          return [
            {
              operator: 'or',
              value: [],
            },
          ]
        case /^(S|E)\d+$/.test(search):
          setSearchedStockId(search)
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'saleItems.stockId',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        case isUUID(search):
          setSearchedStockId(undefined)
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'id',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        case /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$/.test(search): // email
          setSearchedStockId(undefined)
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'user.email',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        default:
          setSearchedStockId(undefined)
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'saleNumber',
                  operator: 'eq',
                  value: search,
                },
                {
                  field: 'shipment.trackingNumber',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
      }
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { selectProps: partnerSelectProps } = useSelect<IPartner>({
    resource: 'admin/partners',
    optionLabel: 'name',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List
      headerButtons={({ defaultButtons }) => (
        <Space>
          <FormTableSearch
            searchFormProps={searchFormProps}
            title="Search for a sale by its ID, sale number, stock ID, tracking number or user's email address"
          />
          {defaultButtons}
        </Space>
      )}
    >
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) =>
          handleTableRowClick(() => {
            let redirect = editUrl('admin/sales', id!)
            if (searchedStockId) {
              const currentRow = tableQueryResult?.data?.data.find((sale) => sale.id === id)
              const saleItemId = currentRow?.saleItems?.find((saleItem) => saleItem.stockId === searchedStockId)?.id
              if (saleItemId) {
                redirect = editUrl('admin/sale-items', saleItemId)
              }
            }
            push(redirect)
          })
        }
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value, row: ISale) => (
            <>
              {row.isLegacy && (
                <Badge.Ribbon
                  color="gray"
                  text="legacy"
                  placement="start"
                  className="absolute -top-5 left-0 -ml-2 origin-top-left scale-75"
                />
              )}
              <span title={value}>{value}</span>
            </>
          )}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="saleNumber"
          title="Sale number"
          className="cursor-pointer"
          width="9rem"
          defaultFilteredValue={getDefaultFilter('saleNumber', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Sale number" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex={['user', 'email']}
          title="User"
          className="cursor-pointer"
          align="center"
          defaultFilteredValue={getDefaultFilter('user.email', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 280 }} placeholder="User email" normalize={(value) => value.trim()} />
            </FilterDropdown>
          )}
          width="7rem"
        />
        <Table.Column
          dataIndex="saleItems"
          title="Sale items"
          className="cursor-pointer"
          render={(value) => (
            <div
              title={value
                ?.map(
                  (saleItem: ISaleItem) =>
                    saleItem.productVariant?.name__en + (saleItem.stockId ? ' | ' + saleItem.stockId : '')
                )
                .join('\n')}
            >
              <span className="underline">
                {value.length} {value.length === 1 ? 'item' : 'items'}
              </span>
            </div>
          )}
          width="4.5rem"
        />
        <Table.Column
          dataIndex="paymentType"
          title="Payment type"
          className="cursor-pointer"
          width="7rem"
          render={(value) => (
            <Tag color={paymentTypeMap[value as keyof typeof paymentTypeMap]}>
              {capitalize(value.replace(/_/g, ' '))}
            </Tag>
          )}
          defaultFilteredValue={getDefaultFilter('paymentType', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                placeholder="Status"
                options={formatSelectOptionsFromEnum(SalePaymentType)}
              />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="status"
          title="Status"
          className="cursor-pointer"
          width="7rem"
          render={(value) => (
            <Tag color={statusColorMap[value as keyof typeof statusColorMap]}>
              {capitalize(value.replace(/_/g, ' '))}
            </Tag>
          )}
          defaultFilteredValue={getDefaultFilter('status', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                placeholder="Status"
                options={formatSelectOptionsFromEnum(SaleStatus)}
              />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="isLabelSent"
          title="Shipping label"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('isLabelSent', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Radio.Group
                options={[
                  {
                    label: 'Sent',
                    value: 1,
                  },
                  {
                    label: 'Not sent',
                    value: 0,
                  },
                ]}
              />
            </FilterDropdown>
          )}
          render={(value, row: ISale) => {
            const tagText: string = {
              [SaleShippingLabelType.EMAIL]: 'Email',
              [SaleShippingLabelType.POST]: 'Post',
            }[row.shippingLabel as keyof typeof SaleShippingLabelType]
            const sentEmoji = value ? ' √' : ' ×'
            const sentText = value ? ' sent' : ' not sent'
            return (
              <Tag color={value ? 'success' : 'error'} title={tagText + sentText}>
                {tagText + sentEmoji}
              </Tag>
            )
          }}
          width="6rem"
        />
        <Table.Column
          dataIndex="isReturningCustomer"
          title="Is returning customer"
          className="cursor-pointer"
          render={(value) => (value ? 'Yes' : 'No')}
          width="6rem"
        />
        <Table.Column
          dataIndex="note"
          title="Note"
          className="cursor-pointer overflow-hidden"
          ellipsis={true}
          render={(value) => (
            <Popover content={value} placement="topLeft">
              <div className="max-w-16 truncate">{value}</div>
            </Popover>
          )}
        />
        <Table.Column
          dataIndex={['partner', 'id']}
          title="Source"
          className="cursor-pointer"
          width="6rem"
          render={(_, row: ISale) =>
            row?.partner?.name
              ? capitalize(row.partner.name) +
                (row?.partnerPlatform ? ' (' + capitalize(row.partnerPlatform) + ')' : '')
              : 'Prioont'
          }
          defaultFilteredValue={getDefaultFilter('partner.id', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} placeholder="Partner" {...(partnerSelectProps as any)} />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="receivedAt"
          title="Received at"
          render={(value) => (value ? <DateField value={value} format="DD-MM-YYYY HH:mm" /> : '-')}
          defaultSortOrder={getDefaultSortOrder('receivedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<ISale>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
