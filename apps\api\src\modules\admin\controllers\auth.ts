import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common'
import { Request } from 'express'

import { AuthService } from '~admin/services'

import { AdminJwtAuthGuard } from '../guards'

@Controller('admin/auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('google')
  async googleLogin(@Body() body: { accessToken: string }) {
    return this.authService.googleLogin(body.accessToken)
  }

  @UseGuards(AdminJwtAuthGuard)
  @Get('me')
  async me(@Req() req: Request) {
    return this.authService.me(req.headers.authorization.split(' ')[1])
  }
}
