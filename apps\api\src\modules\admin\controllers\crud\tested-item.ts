import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { TestedItemEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudTestedItemService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: TestedItemEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      lists: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/tested-items')
export class CrudTestedItemController implements CrudController<TestedItemEntity> {
  constructor(public service: CrudTestedItemService) {}
}
