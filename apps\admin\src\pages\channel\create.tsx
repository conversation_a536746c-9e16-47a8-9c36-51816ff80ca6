import { Create, useForm, useSelect } from '@refinedev/antd'
import type { HttpError, IResourceComponentsProps } from '@refinedev/core'
import { ChannelType } from '@valyuu/api/constants'
import type { IChannel, ILocaleCountry, ILocaleCurrency } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, RadioGroup, Select } from 'antx'
import { FC } from 'react'

import { InputMultiEntitySelect, InputPublish } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const ChannelCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<IChannel, HttpError, IChannel>()
  const publishedAt = Form.useWatch('publishedAt', form)

  const { selectProps: currencySelectProps } = useSelect<ILocaleCurrency>({
    resource: 'admin/currencies',
    optionLabel: 'id',
    meta: {
      fields: ['id'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: countrySelectProps } = useSelect<ILocaleCountry>({
    resource: 'admin/countries',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form {...formProps} layout="vertical">
        <Input
          label="ID"
          name="id"
          rules={['required']}
          normalize={(value) => value.replace(/[^a-zA-Z0-9-]/g, '').toUpperCase()}
        />
        <Input label="Name" name="name" rules={['required']} />
        <Input label="Description" name="desc" rules={['required']} />
        <Select label="Currency" name="currencyId" rules={['required']} {...(currencySelectProps as any)} />
        <InputMultiEntitySelect
          label="Countries"
          name="countries"
          rules={['required']}
          {...(countrySelectProps as any)}
        />
        <Select label="Type" name="type" rules={['required']} options={formatSelectOptionsFromEnum(ChannelType)} />
        <RadioGroup
          label="Disable buyer"
          name="disableBuyer"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
        />
        <RadioGroup
          label="Disable seller"
          name="disableSeller"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
        />
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
