import { IShippingProductMethodFunctionalities } from './shipping-product-method-functionalities'

export type IShippingProductMethod = {
  /** The shipping method unique identifier */
  id: number

  /** A human friendly name for the shipping method */
  name: string

  /** A unique identifier for the shipping product which the shipping method belongs to */
  shipping_product_code: string

  /** Expected lead time for this shipping method for the filtered from_country -> to_country pair */
  lead_time_hours: Record<string, number>

  /** Shipping method functionalities and features */
  functionalities: IShippingProductMethodFunctionalities

  /** Properties of this shipping method */
  properties: {
    /** The minimal weight of the shipping method, in grams (bounds are inclusive) */
    min_weight: number
    /** The maximal weight of the shipping method, in grams (bounds are inclusive) */
    max_weight: number
    /** Maximum dimensions that can be used when shipping a parcel */
    max_dimensions: {
      /** Maximum length the parcel can have */
      length: number
      /** Maximum width the parcel can have */
      width: number
      /** Maximum height the parcel can have */
      height: number
      /** Unit of the length, width and height measurements */
      unit: 'millimeter' | 'centimeter' | 'meter'
    }
  }
}
