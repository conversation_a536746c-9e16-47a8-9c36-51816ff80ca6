import { StripeWebhook<PERSON>andler } from '@golevelup/nestjs-stripe'
import { Injectable } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import type Stripe from 'stripe'

import { OrderService } from '~/services'

@Injectable()
export class StripeService {
  constructor(private readonly orderService: OrderService) {}

  @StripeWebhookHandler('charge.succeeded')
  async handleChargeSucceeded(event: Stripe.ChargeSucceededEvent) {
    const data = event?.data?.object
    // filter out those events that charges are not made by a payment intent
    if (!data?.payment_intent) {
      Sentry.captureMessage('Charge succeeded but no payment intent', {
        tags: {
          type: 'stripe:chargeSucceeded',
        },
        extra: {
          data,
        },
        level: 'warning',
      })
      return
    }
    if (data?.metadata?.pre_order_id) {
      await this.orderService.createOrder({
        preOrderId: data.metadata.pre_order_id,
        paymentMethod: data.payment_method_details?.type,
        stripeCharge: data.id,
        amount: data.amount,
      })
    } else {
      Sentry.captureMessage('No pre_order_id in metadata', {
        tags: {
          type: 'stripe:chargeSucceeded',
        },
        extra: {
          data,
        },
        level: 'warning',
      })
    }
  }

  @StripeWebhookHandler('charge.refunded')
  async handleChargeRefunded(event: Stripe.ChargeRefundedEvent) {
    Sentry.captureMessage('Charge refunded', {
      tags: {
        type: 'stripe:chargeRefunded',
      },
      extra: {
        event,
      },
      level: 'info',
    })
  }

  @StripeWebhookHandler('charge.refund.updated')
  async handleChargeRefundUpdated(event: Stripe.ChargeRefundUpdatedEvent) {
    Sentry.captureMessage('Charge refund updated', {
      tags: {
        type: 'stripe:chargeRefundUpdated',
      },
      extra: {
        event,
      },
      level: 'info',
    })
  }

  @StripeWebhookHandler('payment_intent.canceled')
  async handlePaymentIntentCanceled(event: Stripe.PaymentIntentCanceledEvent) {
    const data = event?.data?.object
    if (data?.metadata?.pre_order_id && data?.payment_method) {
      const preOrderId = data.metadata.pre_order_id
      await this.orderService.cancelPreOrder({
        preOrderId,
        checkStripeStatus: true,
        paymentMethod: data?.last_payment_error?.payment_method?.type,
        reason: 'Canceled by user',
      })
      return ''
    } else {
      Sentry.captureMessage('No pre_order_id or payment_method in metadata', {
        tags: {
          type: 'stripe:paymentIntentCanceled',
        },
        extra: {
          data,
        },
      })
    }
  }

  @StripeWebhookHandler('payment_intent.payment_failed')
  async handlePaymentIntentPaymentFailed(event: Stripe.PaymentIntentPaymentFailedEvent) {
    const data = event?.data?.object
    if (data?.metadata?.pre_order_id && data?.payment_method) {
      const preOrderId = data.metadata.pre_order_id
      this.orderService.cancelPreOrder({
        preOrderId,
        checkStripeStatus: true,
        paymentMethod: data?.last_payment_error?.payment_method?.type,
        reason: 'Payment failed',
      })
      return ''
    } else {
      Sentry.captureMessage('No pre_order_id or payment_method in metadata', {
        tags: {
          type: 'stripe:paymentIntentPaymentFailed',
        },
        extra: {
          data,
        },
      })
    }
  }
}
