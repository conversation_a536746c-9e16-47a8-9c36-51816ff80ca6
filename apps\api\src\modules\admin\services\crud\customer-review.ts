import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { CustomerReviewEntity } from '~/entities'

export class CrudCustomerReviewService extends TypeOrmCrudService<CustomerReviewEntity> {
  constructor(
    @InjectRepository(CustomerReviewEntity)
    repo: Repository<CustomerReviewEntity>
  ) {
    super(repo)
  }
}
