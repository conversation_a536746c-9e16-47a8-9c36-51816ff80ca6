import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageUsedInType } from '~/constants'
import { ProductSkuOriginalAccessoryEntity } from '~/entities'
import { entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'
import { entityFixPublishedAt } from '~/utils'

@Injectable()
@EventSubscriber()
export class ProductSkuOriginalAccessorySubscriber
  implements EntitySubscriberInterface<ProductSkuOriginalAccessoryEntity>
{
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ProductSkuOriginalAccessoryEntity
  }

  async beforeInsert(event: InsertEvent<ProductSkuOriginalAccessoryEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async afterInsert(event: InsertEvent<ProductSkuOriginalAccessoryEntity>) {
    const { entity, manager } = event

    // process thumbnail
    await entityImageAutoProcess({
      image: entity.thumbnail,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.PRODUCT_SKU_ORIGINAL_ACCESSORY,
      manager,
    })
  }

  async beforeUpdate(event: UpdateEvent<ProductSkuOriginalAccessoryEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }

  async afterUpdate(event: UpdateEvent<ProductSkuOriginalAccessoryEntity>) {
    const { entity, manager } = event

    // process thumbnail
    await entityImageAutoProcess({
      image: entity.thumbnail ?? { id: entity.thumbnailId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.PRODUCT_SKU_ORIGINAL_ACCESSORY,
      manager,
    })
  }

  async afterRemove(event: RemoveEvent<ProductSkuOriginalAccessoryEntity>) {
    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.PRODUCT_SKU_ORIGINAL_ACCESSORY,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
