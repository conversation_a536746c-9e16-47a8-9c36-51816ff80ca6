import { Field, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm'

import { LicensePlateDeviceEntity } from './licenseplate-device.table'

@ObjectType('product')
@Entity('licenseplate_product')
export class LicensePlateProductEntity extends BaseEntity {
  @Field()
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  guid: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  name?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  sku?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  category?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  brand?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  model?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  storage?: string

  @OneToOne(() => LicensePlateDeviceEntity, (device) => device.product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'guid', referencedColumnName: 'guid' })
  device: LicensePlateDeviceEntity
}

export type ILicensePlateProduct = Omit<LicensePlateProductEntity, keyof BaseEntity> & {
  device: LicensePlateDeviceEntity
}
