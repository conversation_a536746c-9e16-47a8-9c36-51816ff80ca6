import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, UseGuards } from '@nestjs/common'

import { SaleItemEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudSaleItemService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: SaleItemEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      sale: {},
      'sale.shipment': {},
      user: {},
      productVariant: {},
      productSku: {},
      payments: {},
      channel: {},
      partner: {},
      returnAddress: {},
      returnShipment: {},
      'returnShipment.shippingMethod': {},
    },
  },
})
@Controller('admin/sale-items')
export class CrudSaleItemController implements CrudController<SaleItemEntity> {
  constructor(public service: CrudSaleItemService) {}

  @Get(':id/histories')
  async getHistories(@Param('id', ParseUUIDPipe) id: string) {
    return this.service.getHistories(id)
  }

  @Post(':id/create-return-shipment')
  async createReturnShipment(@Param('id', ParseUUIDPipe) id: string, @Body('override') override?: boolean) {
    return this.service.createReturnShipment(id, override)
  }

  @Post(':id/reuse-return-shipment')
  async reuseReturnShipment(@Param('id', ParseUUIDPipe) id: string, @Body('shipmentId') shipmentId: string) {
    return this.service.reuseReturnShipment(id, shipmentId)
  }
}
