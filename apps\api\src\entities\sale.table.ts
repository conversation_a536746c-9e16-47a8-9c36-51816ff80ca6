import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { SalePaymentType, SaleShippingLabelType, SaleStatus, UserFrom } from '~/constants'
import {
  AddressEntity,
  BankAccountEntity,
  ChannelEntity,
  type IAddress,
  type IBankAccount,
  type IChannel,
  type ILocaleLanguage,
  type IPartner,
  type ISaleHistory,
  type ISaleItem,
  type IShipment,
  type IUser,
  LocaleCurrencyEntity,
  LocaleLanguageEntity,
  PartnerEntity,
  SaleHistoryEntity,
  SaleItemEntity,
  ShipmentEntity,
  UserEntity,
} from '~/entities'
import { PartnerPlatform } from '~partner/constants'

registerEnumType(SaleStatus, { name: 'SaleStatusType' })
registerEnumType(SaleShippingLabelType, { name: 'SaleShippingLabelTypeType' })

@ObjectType('Sale')
@Entity('sale')
export class SaleEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'smallint', default: 1 })
  version: number

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Column({ type: 'boolean', default: false })
  isLegacy: boolean

  @Field()
  @Column({ unique: true })
  saleNumber: string

  @Field(() => SaleStatus)
  @Index()
  @Column({ type: 'varchar', nullable: false })
  status: SaleStatus

  @Field(() => SaleShippingLabelType)
  @Column({ type: 'varchar', nullable: false })
  shippingLabel: SaleShippingLabelType

  @Field({ defaultValue: false })
  @Column({ default: false })
  isLabelSent: boolean

  @Column({ nullable: true })
  trackingNumber?: string

  @Column({ nullable: true })
  parcelId?: string

  @OneToOne(() => ShipmentEntity, { nullable: true })
  @JoinColumn()
  shipment?: Relation<ShipmentEntity>

  @Column({ nullable: true })
  @RelationId((sale: SaleEntity) => sale.shipment)
  shipmentId?: string

  @OneToMany(() => SaleItemEntity, (saleItem) => saleItem.sale)
  saleItems: Relation<SaleItemEntity>[]

  @Column({ type: 'varchar' })
  paymentType: SalePaymentType

  @ManyToOne(() => BankAccountEntity, { nullable: true })
  bankAccount?: Relation<BankAccountEntity>

  @Column({ nullable: true })
  @RelationId((problem: SaleEntity) => problem.bankAccount)
  bankAccountId?: string

  @ManyToOne(() => AddressEntity, { nullable: false })
  address: Relation<AddressEntity>

  @Column()
  @RelationId((problem: SaleEntity) => problem.address)
  addressId: string

  @Field()
  @Column({ nullable: false })
  isReturningCustomer: boolean

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((problem: SaleEntity) => problem.channel)
  channelId: string

  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((order: SaleEntity) => order.currency)
  currencyId: string

  @ManyToOne(() => UserEntity, (user) => user.orders, { nullable: false })
  user: Relation<UserEntity>

  @Column()
  @RelationId((order: SaleEntity) => order.user)
  userId: string

  @Field(() => LocaleLanguageEntity)
  @ManyToOne(() => LocaleLanguageEntity)
  language: Relation<LocaleLanguageEntity>

  @Column()
  @RelationId((sale: SaleEntity) => sale.language)
  languageId: string

  @Column({ type: 'varchar', default: 'VALYUU' })
  from: UserFrom

  @Column({ type: 'int2', unsigned: true, default: 0 })
  reminderSentCount: number

  @Column({ default: false })
  surveySent: boolean

  @Column({ nullable: true })
  note?: string

  @ManyToOne(() => PartnerEntity, { nullable: true })
  partner?: Relation<PartnerEntity>

  @Column({ nullable: true })
  @RelationId((sale: SaleEntity) => sale.partner)
  partnerId?: string

  @Index()
  @Column({ type: 'text', nullable: true })
  partnerPlatform?: PartnerPlatform

  @OneToMany(() => SaleHistoryEntity, (history) => history.sale)
  histories: Relation<SaleHistoryEntity>[]

  @Column({ type: 'timestamp', nullable: true })
  receivedAt?: Date | null

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ISale = Omit<SaleEntity, keyof BaseEntity> & {
  saleItems: ISaleItem[]
  shipment?: IShipment
  bankAccount?: IBankAccount
  address: IAddress
  channel: IChannel
  user: IUser
  shippingLabel: typeof SaleShippingLabelType
  partner?: IPartner
  language: ILocaleLanguage
  histories: ISaleHistory[]
}
