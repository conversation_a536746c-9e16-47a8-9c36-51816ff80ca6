import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { ProductSeriesEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudProductSeriesService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ProductSeriesEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      brand: {},
      category: {},
      models: {},
      testComponentList: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/product-series')
export class CrudProductSeriesController implements CrudController<ProductSeriesEntity> {
  constructor(public service: CrudProductSeriesService) {}
}
