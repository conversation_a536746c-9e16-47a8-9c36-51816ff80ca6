import { Injectable } from '@nestjs/common'
import { Cron } from '@nestjs/schedule'
import * as Sentry from '@sentry/nestjs'
import { cancelParcel, createParcel, IParcelCreation, ParcelDocumentType } from '@valyuu/sendcloud'
import { subDays } from 'date-fns'
import { isNil } from 'lodash'
import ms from 'ms'
import * as pMap from 'p-map'
import { Between, FindOptionsWhere, In, IsNull } from 'typeorm'

import { envConfig } from '~/configs'
import {
  SHIPMENT_BEFORE_CUSTOMER_DROP_OFF_STATUSES,
  SHIPMENT_RETURN_LABEL_EXPIRATION_DAYS,
  ShipmentTrackingStatusCode,
  ShipmentTrackingStatusMessage,
  ShipmentType,
} from '~/constants'
import { AddressEntity, ShipmentEntity, ShippingConfigEntity, ShippingMethodEntity } from '~/entities'
import { WarehouseEntity } from '~/entities'
import { houseNumberWithAddition, sendcloudGetFinalTrackingUrl } from '~/utils'

type GetShippingMethodsParams = {
  customerCountryId: string
  isReturn: boolean
  warehouseId?: string
  channelId?: string
  isPaperless?: boolean
}

type CreateShipmentParams = {
  type: ShipmentType
  orderNumber?: string
  customerEmail: string
  customerAddress: AddressEntity
  isReturn: boolean
  shippingMethodId?: string
  channelId?: string
  partnerId?: string
  saleId?: string
  orderId?: string
  saleItemId?: string
}

@Injectable()
export class ShipmentService {
  @Cron('30 2 * * *') // Runs at 2:30 AM every day
  async cleanupOldShipments() {
    try {
      // If a shipment is ready to send for 42 days, it will be charged
      const fortyThreeDaysAgo = subDays(new Date(), 43)
      const thirtySixDaysAgo = subDays(new Date(), SHIPMENT_RETURN_LABEL_EXPIRATION_DAYS)

      const oldShipments = await ShipmentEntity.find({
        where: {
          statusCode: In(SHIPMENT_BEFORE_CUSTOMER_DROP_OFF_STATUSES),
          createdAt: Between(fortyThreeDaysAgo, thirtySixDaysAgo),
        },
      })

      await pMap(oldShipments, async ({ parcelId }) => cancelParcel(parcelId).catch(() => {}), { concurrency: 5 })
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          type: 'shipment:cleanupOldShipments',
        },
      })
    }
  }
  async getShippingMethods({
    customerCountryId,
    isReturn,
    warehouseId,
    channelId,
    isPaperless,
  }: GetShippingMethodsParams): Promise<ShippingMethodEntity[]> {
    try {
      if (!customerCountryId) {
        throw new Error('Country is required')
      }

      if (isNil(isReturn)) {
        throw new Error('isReturn is required')
      }

      const shippingMethodWhere: FindOptionsWhere<ShippingMethodEntity> = {}
      if (!isNil(isPaperless)) {
        shippingMethodWhere.isPaperless = isPaperless
      }

      const shippingConfigs = await ShippingConfigEntity.find({
        where: {
          channelId: channelId ? In([channelId, null]) : IsNull(),
          customerCountryId,
          isReturn,
          ...(warehouseId ? { warehouseId } : {}),
          ...shippingMethodWhere,
        },
        relations: {
          warehouse: true,
          shippingMethod: true,
        },
        order: {
          warehouse: {
            sortOrder: 'ASC',
          },
          sortOrder: 'ASC',
        },
        cache: envConfig.isProduction ? ms('1 hour') : false,
      })

      const channelShippingMethods = shippingConfigs.filter(({ channelId }) => channelId)
      if (channelShippingMethods.length) {
        return channelShippingMethods.map(({ shippingMethod }) => shippingMethod)
      }
      return shippingConfigs.filter(({ channelId }) => !channelId).map(({ shippingMethod }) => shippingMethod)
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          type: 'shipment:getShippingMethods',
        },
      })
      throw error
    }
  }

  async create({
    type,
    orderNumber,
    customerEmail,
    customerAddress,
    isReturn,
    shippingMethodId,
    channelId,
    partnerId,
    saleId,
    orderId,
    saleItemId,
  }: CreateShipmentParams): Promise<ShipmentEntity> {
    try {
      if (!customerEmail) {
        throw new Error('Customer email is required')
      }

      if (!customerAddress) {
        throw new Error('Customer address is required')
      }

      if (!(customerAddress.countryId || customerAddress?.country?.id)) {
        throw new Error('Customer address country is required')
      }

      if (!customerAddress.firstName || !customerAddress.lastName) {
        throw new Error('Customer first and last name are required')
      }

      if (!customerAddress.street || !customerAddress.houseNumber) {
        throw new Error('Customer street and house number are required')
      }

      if (!customerAddress.city || !customerAddress.postalCode) {
        throw new Error('Customer city and postal code are required')
      }

      const customerCountryId = customerAddress.countryId || customerAddress.country?.id

      let warehouse: WarehouseEntity

      const configs = await ShippingConfigEntity.find({
        where: [
          { customerCountryId, isReturn, channelId: IsNull() },
          {
            customerCountryId,
            isReturn,
            channelId,
          },
        ],
        relations: {
          warehouse: true,
        },
        order: {
          warehouse: {
            sortOrder: 'ASC',
          },
          sortOrder: 'ASC',
        },
        cache: envConfig.isProduction ? ms('1 hour') : false,
      })

      if (!configs.length) {
        throw new Error('No shipping methods found')
      }

      const fullySupportedConfigs = configs.filter(
        (shippingConfig) =>
          // Shipping method id match
          (shippingMethodId ? shippingConfig.shippingMethodId === shippingMethodId : true) &&
          // Channel id match
          (shippingConfig.channelId ?? null) === (channelId ?? null)
      )
      if (fullySupportedConfigs.length) {
        shippingMethodId = fullySupportedConfigs[0].shippingMethodId
        warehouse = fullySupportedConfigs[0].warehouse
      } else {
        Sentry.captureMessage('No fully supported shipping methods found', {
          tags: {
            type: 'shipment:create',
          },
        })
        // No fully supported shipping methods found
        const channelSupportedConfigs = configs.filter(
          (shippingConfig) => (shippingConfig.channelId ?? null) === (channelId ?? null)
        )
        if (channelSupportedConfigs.length) {
          shippingMethodId = channelSupportedConfigs[0].shippingMethodId
          warehouse = channelSupportedConfigs[0].warehouse
        } else {
          // No channel supported shipping methods found
          shippingMethodId = configs[0].shippingMethodId
          warehouse = configs[0].warehouse
        }
      }

      if (!warehouse) {
        throw new Error('No warehouse found')
      }

      let houseNumberFull = houseNumberWithAddition(customerAddress.houseNumber, customerAddress.addition)
      // Fix sendcloud's bug that it cannot handle house number with addition longer than 8 characters
      let address2 = ''
      if (houseNumberFull.length > 8) {
        houseNumberFull = customerAddress.houseNumber
        address2 = customerAddress.addition
      }

      const createParcelParams: IParcelCreation = isReturn
        ? {
            is_return: true,
            request_label: true,
            ...(warehouse.contactName ? { company_name: warehouse.companyName } : {}),
            name: warehouse.contactName ? warehouse.contactName : warehouse.companyName,
            address: warehouse.street,
            house_number: warehouse.houseNumber,
            city: warehouse.city,
            postal_code: warehouse.postalCode,
            telephone: warehouse.phone,
            email: warehouse.email,
            country: warehouse.countryId,
            from_name: customerAddress.firstName + ' ' + customerAddress.lastName,
            from_address_1: customerAddress.street,
            ...(address2 ? { from_address_2: address2 } : {}),
            from_house_number: houseNumberFull,
            from_city: customerAddress.city,
            from_postal_code: customerAddress.postalCode,
            ...(customerAddress.phoneAreaCode && customerAddress.phoneNumber
              ? { from_telephone: customerAddress.phoneAreaCode + ' ' + customerAddress.phoneNumber }
              : {}),
            from_email: customerEmail,
            from_country: customerAddress.countryId,
            weight: '0.5',
            shipment: {
              id: Number(shippingMethodId),
            },
            order_number: orderNumber,
            quantity: 1,
          }
        : {
            request_label: true,
            name: customerAddress.firstName + ' ' + customerAddress.lastName,
            address: customerAddress.street + (address2 ? ' ' + address2 : ''),
            house_number: houseNumberFull,
            city: customerAddress.city,
            postal_code: customerAddress.postalCode,
            ...(customerAddress.phoneAreaCode && customerAddress.phoneNumber
              ? { telephone: customerAddress.phoneAreaCode + ' ' + customerAddress.phoneNumber }
              : {}),
            email: customerEmail,
            country: customerAddress.countryId,
            weight: '0.5',
            shipment: {
              id: Number(shippingMethodId),
            },
            order_number: orderNumber,
            quantity: 1,
          }

      const parcel = await createParcel(createParcelParams)

      if (parcel.tracking_number) {
        const shipment = await ShipmentEntity.create({
          type,
          parcelId: String(parcel.id),
          trackingNumber: parcel.tracking_number,
          trackingUrl: parcel.tracking_url,
          statusCode: parcel.status.id as ShipmentTrackingStatusCode,
          statusMessage: parcel.status.message as ShipmentTrackingStatusMessage,
          isReturn,
          isPaperless: parcel.documents.some((document) => document.type === ParcelDocumentType.QR),
          shippingMethodId,
          warehouseId: warehouse.id,
          customerAddressId: customerAddress.id,
          partnerId,
          ...(saleId ? { sale: { id: saleId } } : {}),
          ...(orderId ? { order: { id: orderId } } : {}),
          ...(saleItemId ? { saleItem: { id: saleItemId } } : {}),
        }).save()

        setImmediate(async () => {
          try {
            const trackingUrl = await sendcloudGetFinalTrackingUrl(parcel.tracking_url)
            if (trackingUrl && trackingUrl !== shipment.trackingUrl) {
              await ShipmentEntity.update({ id: shipment.id }, { trackingUrl })
            }
          } catch (error) {
            Sentry.captureException(error, {
              tags: {
                type: 'shipment:create',
              },
            })
          }
        })

        return shipment
      } else {
        await cancelParcel(parcel.id).catch(() => {})
        throw new Error('Failed to create parcel')
      }
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          type: 'shipment:create',
        },
      })
      throw error
    }
  }
}
