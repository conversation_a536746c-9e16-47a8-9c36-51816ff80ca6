import { Create, useForm } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import { WarrantyPeriodUnit } from '@valyuu/api/constants'
import type { IWarranty } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, Select, Watch } from 'antx'
import cx from 'clsx'
import { FC, useState } from 'react'

import { InputLanguageTab, InputMultiLang, LanguageTabChoices } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const WarrantyCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps } = useForm<IWarranty, HttpError, IWarranty>()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
            </>
          )}
        </Watch>
        <Watch list={['labelWithPrice__en', 'labelWithPrice__nl', 'labelWithPrice__de', 'labelWithPrice__pl']}>
          {([labelWithPrice__en, labelWithPrice__nl, labelWithPrice__de, labelWithPrice__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: labelWithPrice__en, nl: labelWithPrice__nl, de: labelWithPrice__de, pl: labelWithPrice__pl }}
                targetLang="en"
                label="Priced warranty label (English)"
                name="labelWithPrice__en"
                rules={['required', { pattern: /\{price\}/, message: 'This field must contain {price}' }]}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: labelWithPrice__en, nl: labelWithPrice__nl, de: labelWithPrice__de, pl: labelWithPrice__pl }}
                targetLang="nl"
                label="Priced warranty label (Dutch)"
                name="labelWithPrice__nl"
                rules={['required', { pattern: /\{price\}/, message: 'This field must contain {price}' }]}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: labelWithPrice__en, nl: labelWithPrice__nl, de: labelWithPrice__de, pl: labelWithPrice__pl }}
                targetLang="de"
                label="Priced warranty label (German)"
                name="labelWithPrice__de"
                rules={['required', { pattern: /\{price\}/, message: 'This field must contain {price}' }]}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: labelWithPrice__en, nl: labelWithPrice__nl, de: labelWithPrice__de, pl: labelWithPrice__pl }}
                targetLang="pl"
                label="Priced warranty label (Polish)"
                name="labelWithPrice__pl"
                rules={['required', { pattern: /\{price\}/, message: 'This field must contain {price}' }]}
                className={clsHidePl}
                fillType="translate"
              />
            </>
          )}
        </Watch>
        <Watch list={['labelFree__en', 'labelFree__nl', 'labelFree__de', 'labelFree__pl']}>
          {([labelFree__en, labelFree__nl, labelFree__de, labelFree__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: labelFree__en, nl: labelFree__nl, de: labelFree__de, pl: labelFree__pl }}
                targetLang="en"
                label="Free warranty label (English)"
                name="labelFree__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: labelFree__en, nl: labelFree__nl, de: labelFree__de, pl: labelFree__pl }}
                targetLang="nl"
                label="Free warranty label (Dutch)"
                name="labelFree__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: labelFree__en, nl: labelFree__nl, de: labelFree__de, pl: labelFree__pl }}
                targetLang="de"
                label="Free warranty label (German)"
                name="labelFree__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: labelFree__en, nl: labelFree__nl, de: labelFree__de, pl: labelFree__pl }}
                targetLang="pl"
                label="Free warranty label (Polish)"
                name="labelFree__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
            </>
          )}
        </Watch>
        <Input label="Period number" name="periodNumber" rules={['required']} />
        <Select
          label="Period unit"
          name="periodUnit"
          rules={['required']}
          options={formatSelectOptionsFromEnum(WarrantyPeriodUnit)}
        />
      </Form>
    </Create>
  )
}
