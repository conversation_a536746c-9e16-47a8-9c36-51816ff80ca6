import { resolve } from 'node:path'

import react from '@vitejs/plugin-react-swc'
import { config } from 'dotenv'
import { defineConfig } from 'vite'
import svgr from 'vite-plugin-svgr'

config()

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), svgr()],
  resolve: {
    alias: {
      '~': resolve(__dirname, './src'),
    },
  },
  server: {
    host: process.env.HOST,
    port: parseInt(process.env.PORT || '4000'),
  },
  optimizeDeps: {
    entries: ['react', 'react-dom', 'react-router-dom'],
  },
})
