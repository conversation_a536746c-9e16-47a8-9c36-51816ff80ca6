import { <PERSON><PERSON><PERSON>, EditButton, FilterDropdown, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { getDefaultFilter, HttpError, type IResourceComponentsProps, useApiUrl, useNavigation } from '@refinedev/core'
import { OrderStatus } from '@valyuu/api/constants'
import type { IOrder, IOrderSkuItem } from '@valyuu/api/entities'
import { Popover, Space, Table, Tag } from 'antd'
import { Input, Select } from 'antx'
import { capitalize } from 'lodash'
import type { FC } from 'react'

import { ButtonDataExchangeExport, FormTableSearch } from '~/components'
import { formatMoney, formatSelectOptionsFromEnum, handleTableRowClick, isUUID } from '~/utils'

const statusColorMap = {
  [OrderStatus.PENDING_SHIPMENT]: 'processing',
  [OrderStatus.SHIPPING]: 'orange',
  [OrderStatus.COMPLETED]: 'success',
  [OrderStatus.PENDING_REFUND]: 'warning',
  [OrderStatus.PARTIALLY_REFUNDED]: 'warning',
  [OrderStatus.REFUNDED]: 'error',
  [OrderStatus.CANCELLED]: 'default',
  [OrderStatus.PROBLEM_EXCHANGED]: 'error',
  [OrderStatus.PROBLEM_REPAIRED]: 'error',
  [OrderStatus.FRAUDULENT]: 'error',
}

export const OrderList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters, searchFormProps } = useTable<IOrder, HttpError, { search: string }>({
    syncWithLocation: true,
    meta: {
      fields: [
        'id',
        'orderNumber',
        'total',
        'currencyId',
        'status',
        'toPickupPoint',
        'isReturningCustomer',
        'note',
        'from',
        'createdAt',
      ],
      join: [
        {
          field: 'user',
          select: ['email'],
        },
        {
          field: 'orderSkuItems',
          select: ['id'],
        },
        {
          field: 'orderSkuItems.productSku',
          select: ['name__en'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    onSearch: ({ search }) => {
      search = (search ?? '').trim()
      switch (true) {
        case !search:
          return [
            {
              operator: 'or',
              value: [],
            },
          ]
        case isUUID(search):
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'id',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        case /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$/.test(search): // email
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'user.email',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        default:
          return [
            {
              field: 'orderNumber',
              operator: 'eq',
              value: search,
            },
          ]
      }
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  const apiUrl = useApiUrl()

  return (
    <List
      headerButtons={({ defaultButtons }) => (
        <Space>
          <ButtonDataExchangeExport content="order items" url={`${apiUrl}/admin/data-exchange/order-export`} />
          <FormTableSearch
            searchFormProps={searchFormProps}
            title="Search for an order by its ID, order number or user's email address"
          />
          {defaultButtons}
        </Space>
      )}
    >
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/orders', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="orderNumber"
          title="Order number"
          className="cursor-pointer"
          width="9rem"
          defaultFilteredValue={getDefaultFilter('orderNumber', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Order number" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex={['user', 'email']}
          title="User"
          className="cursor-pointer"
          align="center"
          defaultFilteredValue={getDefaultFilter('user.email', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 280 }} placeholder="User email" normalize={(value) => value.trim()} />
            </FilterDropdown>
          )}
          width="7rem"
        />
        <Table.Column
          dataIndex="total"
          title="Total"
          className="cursor-pointer"
          width="4rem"
          render={(_, row: IOrder) => formatMoney(row.total, row.currencyId)}
          defaultSortOrder={getDefaultSortOrder('total', sorters)}
          sorter
        />
        <Table.Column
          dataIndex="orderSkuItems"
          title="Order SKU items"
          className="cursor-pointer"
          render={(value) => (
            <div title={value?.map((orderItem: IOrderSkuItem) => orderItem.productSku?.name__en).join('\n')}>
              <span className="underline">
                {value.length} {value.length === 1 ? 'item' : 'items'}
              </span>
            </div>
          )}
        />
        <Table.Column
          dataIndex="status"
          title="Status"
          className="cursor-pointer"
          width="7rem"
          render={(value) => (
            <Tag color={statusColorMap[value as keyof typeof statusColorMap]}>
              {capitalize(value.replace(/_/g, ' '))}
            </Tag>
          )}
          defaultFilteredValue={getDefaultFilter('status', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                placeholder="Status"
                options={formatSelectOptionsFromEnum(OrderStatus)}
              />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="toPickupPoint"
          title="To pickup point"
          className="cursor-pointer"
          render={(value) => (value ? 'Yes' : 'No')}
          width="6rem"
        />
        <Table.Column
          dataIndex="isReturningCustomer"
          title="Is returning customer"
          className="cursor-pointer"
          render={(value) => (value ? 'Yes' : 'No')}
          width="6rem"
        />
        <Table.Column
          dataIndex="note"
          title="Note"
          className="cursor-pointer overflow-hidden"
          ellipsis={true}
          render={(value) => (
            <Popover content={value} placement="topLeft">
              <div className="max-w-16 truncate">{value}</div>
            </Popover>
          )}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IOrder>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
