import '~/configs'

import * as pMap from 'p-map'
import { DataSource } from 'typeorm'

import { envConfig, typeOrmConfig } from '~/configs'
import { ImageEntity, UserEntity } from '~/entities'

const dummyEmailSuffix = '@dropjar.com'

console.info('Dummifying sensitive user data and updating image URLs...')

let userCount = 0

new DataSource({
  ...typeOrmConfig,
  dropSchema: false,
  synchronize: false,
  logging: false,
})
  .initialize()
  .then(async () => {
    if (!envConfig.isDev) {
      console.error('This script can only be run in development mode.')
      process.exit(1)
    }

    // Dummify users
    console.time('Dummify users')
    const users = await UserEntity.find({ select: { id: true } })
    await pMap(
      users,
      async ({ id }) => {
        userCount++
        return UserEntity.update(id, { email: `dummy${userCount}${dummyEmailSuffix}` })
      },
      { concurrency: 10 }
    )
    console.timeEnd('Dummify users')
    console.info(`${userCount} users have been dummified.`)

    // Update Cloudinary image URLs using query builder
    console.time('Update image URLs')
    const result = await ImageEntity.createQueryBuilder()
      .update()
      .set({
        url: () => "REPLACE(url, 'deqs8styw', 'drdngwlot')",
      })
      .where('url LIKE :pattern', { pattern: '%deqs8styw%' })
      .execute()

    console.timeEnd('Update image URLs')
    console.info(`${result.affected || 0} image URLs have been updated.`)

    process.exit(0)
  })
  .catch((error) => {
    console.error('Error during dummification:', error)
    process.exit(1)
  })
