import { useNavigation } from '@refinedev/core'
import { Table, Tag, Typography } from 'antd'
import { isEqual } from 'lodash'
import { type FC, useMemo } from 'react'

interface StandardHistoryChange {
  field: string
  from: unknown
  to: unknown
}

interface TransformedHistoryChange {
  product?: {
    from?: string
    to?: string
  }
  conditions?: {
    from?: string[]
    to?: string[]
  }
  problems?: {
    from?: string[]
    to?: string[]
  }
  extraProblems?: {
    from?: string[]
    to?: string[]
  }
}

export interface HistoryRecord {
  version: number
  changes?: StandardHistoryChange[]
  product?: { from?: string; to?: string }
  conditions?: { from?: string[]; to?: string[] }
  problems?: { from?: string[]; to?: string[] }
  extraProblems?: { from?: string[]; to?: string[] }
  changedAt: string | Date
  changedBy?: {
    id: string
    name: string
    type: string
  }
}

type HistoryProps = {
  histories?: HistoryRecord[]
  currentValue?: any
}

export const BlockChangeHistory: FC<HistoryProps> = ({ histories }) => {
  const { editUrl } = useNavigation()

  const historyData = useMemo(() => {
    if (!histories) return []

    // Handle different response formats
    if (Array.isArray(histories)) {
      return histories
    }
    return []
  }, [histories])

  const formatValue = (value: unknown): string => {
    if (value === undefined || value === null) return ''
    if (typeof value === 'boolean') return value ? 'Yes' : 'No'
    if (typeof value === 'string') return value
    if (typeof value === 'number') return value.toString()
    if (Array.isArray(value)) return value.join(', ')
    return JSON.stringify(value)
  }

  const renderChanges = (record: HistoryRecord) => {
    const changes: JSX.Element[] = []

    // Handle standard field changes
    if (record.changes?.length) {
      record.changes.forEach((change, index) => {
        changes.push(
          <div key={`standard-${index}`}>
            <Typography.Text strong>Changed</Typography.Text>{' '}
            <Tag className="ml-1 mr-0.5" color="blue">
              {change.field.replace(/([A-Z])/g, ' $1').toLowerCase()}
            </Tag>{' '}
            <Typography.Text strong>from</Typography.Text>{' '}
            <Typography.Text code>{formatValue(change.from)}</Typography.Text>{' '}
            <Typography.Text strong>to</Typography.Text>{' '}
            <Typography.Text code>{formatValue(change.to)}</Typography.Text>
          </div>
        )
      })
    }

    // Handle transformed changes (product)
    if (record.product) {
      changes.push(
        <div key="product">
          <Typography.Text strong>Changed</Typography.Text>{' '}
          <Tag className="ml-1 mr-0.5" color="blue">
            product
          </Tag>{' '}
          <Typography.Text strong>from</Typography.Text>{' '}
          <Typography.Text code>{formatValue(record.product.from)}</Typography.Text>{' '}
          <Typography.Text strong>to</Typography.Text>{' '}
          <Typography.Text code>{formatValue(record.product.to)}</Typography.Text>
        </div>
      )
    }

    // Handle transformed changes (conditions)
    if (record.conditions) {
      changes.push(
        <div key="conditions">
          <Typography.Text strong>Changed</Typography.Text>{' '}
          <Tag className="ml-1 mr-0.5" color="blue">
            conditions
          </Tag>{' '}
          <Typography.Text strong>from</Typography.Text>{' '}
          <Typography.Text code>{formatValue(record.conditions.from)}</Typography.Text>{' '}
          <Typography.Text strong>to</Typography.Text>{' '}
          <Typography.Text code>{formatValue(record.conditions.to)}</Typography.Text>
        </div>
      )
    }

    // Handle transformed changes (problems)
    if (record.problems) {
      changes.push(
        <div key="problems">
          <Typography.Text strong>Changed</Typography.Text>{' '}
          <Tag className="ml-1 mr-0.5" color="blue">
            problems
          </Tag>{' '}
          <Typography.Text strong>from</Typography.Text>{' '}
          <Typography.Text code>{formatValue(record.problems.from)}</Typography.Text>{' '}
          <Typography.Text strong>to</Typography.Text>{' '}
          <Typography.Text code>{formatValue(record.problems.to)}</Typography.Text>
        </div>
      )
    }

    // Handle transformed changes (extra problems)
    if (record.extraProblems) {
      changes.push(
        <div key="extra-problems">
          <Typography.Text strong>Changed</Typography.Text>{' '}
          <Tag className="ml-1 mr-0.5" color="blue">
            extra problems
          </Tag>{' '}
          <Typography.Text strong>from</Typography.Text>{' '}
          <Typography.Text code>{formatValue(record.extraProblems.from)}</Typography.Text>{' '}
          <Typography.Text strong>to</Typography.Text>{' '}
          <Typography.Text code>{formatValue(record.extraProblems.to)}</Typography.Text>
        </div>
      )
    }

    if (changes.length === 0) {
      return <Typography.Text type="secondary">No changes</Typography.Text>
    }

    return <div className="space-y-2">{changes}</div>
  }

  if (!historyData.length) {
    return <Typography.Text>No history records found</Typography.Text>
  }

  return (
    <Table dataSource={historyData} pagination={false} rowKey="version">
      <Table.Column title="Version" dataIndex="version" key="version" width={100} />

      <Table.Column
        title="Changed at"
        dataIndex="changedAt"
        key="changedAt"
        render={(value) => new Date(value).toLocaleString()}
      />

      <Table.Column
        title="Changed by"
        key="changedBy"
        render={(_, record: HistoryRecord) => {
          if (!record.changedBy) {
            return <Tag color="default">System</Tag>
          }

          const { type, id, name } = record.changedBy
          const resource = type === 'ADMIN' ? 'admin/admin-users' : 'admin/partners'

          return <Typography.Link href={editUrl(resource, id)}>{name}</Typography.Link>
        }}
      />

      <Table.Column title="Changes" key="changes" render={(_, record: HistoryRecord) => renderChanges(record)} />
    </Table>
  )
}
