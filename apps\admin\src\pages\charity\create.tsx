import { Create, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { OrganizationBankAccountType } from '@valyuu/api/constants'
import type { ICharity, IOrganizationBankAccount } from '@valyuu/api/entities'
import { Button, Form } from 'antd'
import { Input, Select } from 'antx'
import cx from 'clsx'
import { type FC, useState } from 'react'
import { FiPlus, FiRefreshCw } from 'react-icons/fi'

import { InputLanguageTab, LanguageTabChoices } from '~/components'

export const CharityCreate: FC<IResourceComponentsProps> = () => {
  const { createUrl } = useNavigation()
  const { formProps, saveButtonProps, formLoading } = useForm<ICharity, HttpError, ICharity>()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const [createBankAccountOpen, setCreateBankAccountOpen] = useState(false)

  const { selectProps: bankAccountSelectProps, queryResult: bankAccountQueryResult } =
    useSelect<IOrganizationBankAccount>({
      resource: 'admin/organization-bank-accounts',
      optionLabel: 'holderName',
      optionValue: 'id',
      meta: {
        fields: ['id', 'holderName'],
        join: [
          {
            field: 'charity',
            select: ['id'],
          },
          {
            field: 'partner',
            select: ['id'],
          },
        ],
      },
      filters: [
        {
          field: 'type',
          operator: 'eq',
          value: OrganizationBankAccountType.CHARITY,
        },
      ],
    })

  if (bankAccountQueryResult.data?.data?.length) {
    bankAccountSelectProps.options = bankAccountSelectProps.options?.filter((option) =>
      bankAccountQueryResult.data?.data.some(
        (bankAccount) => bankAccount.id === option.value && !bankAccount.partner && !bankAccount.charity
      )
    )
  }

  const handleCreateBankAccount = () => {
    if (createBankAccountOpen) {
      bankAccountQueryResult.refetch()
    } else {
      const bankAccountCreateUrl = createUrl('admin/organization-bank-accounts')
      window.open(bankAccountCreateUrl, 'create-bank-account')?.focus()
    }
    setCreateBankAccountOpen(!createBankAccountOpen)
  }

  const noOptionsContent = (
    <Button
      type="primary"
      icon={createBankAccountOpen ? <FiRefreshCw /> : <FiPlus />}
      onClick={handleCreateBankAccount}
      block
    >
      {createBankAccountOpen ? 'Refresh' : 'Create Bank Account'}
    </Button>
  )

  return (
    <Create isLoading={formLoading} saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input label="Name (EN)" name="name__en" rules={['required']} className={clsHideEn} />
        <Input label="Name (NL)" name="name__nl" rules={['required']} className={clsHideNl} />
        <Input label="Name (DE)" name="name__de" rules={['required']} className={clsHideDe} />
        <Input label="Name (PL)" name="name__pl" rules={['required']} className={clsHidePl} />
        <Select
          label="Bank account"
          name="bankAccountId"
          rules={['required']}
          {...(bankAccountSelectProps as any)}
          notFoundContent={!bankAccountSelectProps.options?.length ? noOptionsContent : undefined}
        />
      </Form>
    </Create>
  )
}
