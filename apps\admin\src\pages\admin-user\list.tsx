import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  EditButton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { AdminRoleType } from '@valyuu/api/admin-constants'
import type { IAdminUser } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { Input } from 'antx'
import { capitalize } from 'lodash'
import type { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const AdminUserList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IAdminUser>({
    syncWithLocation: true,
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/admin-users', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="email"
          title="Email"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('email', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Email" />
            </FilterDropdown>
          )}
        />
        <Table.Column dataIndex="name" title="Name" className="cursor-pointer" />
        <Table.Column
          dataIndex="roles"
          title="Roles"
          className="cursor-pointer"
          render={(values: AdminRoleType[]) => values.map((value) => capitalize(value.replace(/_/g, ' '))).join(', ')}
        />
        <Table.Column
          dataIndex="enabled"
          title="Enabled"
          className="cursor-pointer"
          render={(value) => (value ? 'Yes' : 'No')}
        />
        <Table.Column
          dataIndex="lastLoginAt"
          title="Last login at"
          render={(value) => (value ? <DateField value={value} format="DD-MM-YYYY HH:mm" /> : 'Not logged in yet')}
          defaultSortOrder={getDefaultSortOrder('lastLoginAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IAdminUser>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
