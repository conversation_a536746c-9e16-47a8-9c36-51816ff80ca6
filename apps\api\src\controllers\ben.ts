import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common'
import { isUUID } from 'class-validator'
import type { Request } from 'express'
import { cloneDeep, pick } from 'lodash'
import { v4 as uuid } from 'uuid'

import {
  ProductModelSellerQuestionType,
  SaleItemPlanType,
  SalePaymentType,
  SaleShippingLabelType,
  UserFrom,
} from '~/constants'
import { CreateSaleInput } from '~/dtos'
import { ApiLogEntity } from '~/entities'
import { BenAuthGuard } from '~/guards'
import { PartnerPlatform } from '~/modules/partner/constants'
import { SaleService } from '~/services'

export type SaleData = {
  email: string
  dateOfBirth: string
  anonymousId?: string
  shippingAddress: {
    firstName: string
    lastName: string
    country: string
    postalCode: string
    houseNumber: string
    addition?: string
    street: string
    city: string
    phoneNumber: string
  }
  bankAccount?: {
    holderName?: string
    accountNumber: string
  }
  items: {
    id?: string
    variantId: string
    modelId: string
    price: number
    type: ProductModelSellerQuestionType
    problems?: string[]
    conditions?: {
      optionId: string
      conditionId: string
    }[]
  }[]
  isDonation?: boolean
  signature: string
}

@Controller('partners/ben')
export class BenSaleController {
  constructor(private readonly saleService: SaleService) {}

  @UseGuards(BenAuthGuard)
  @Post('sale')
  async createSale(@Body() data: SaleData, @Req() req: Request) {
    await ApiLogEntity.create({ data: { type: 'ben-sale', data } }).save()
    let bankAccount = cloneDeep(data.bankAccount) as { holderName: string; accountNumber: string } | undefined

    if (data.isDonation) {
      bankAccount = undefined
    } else {
      if (!bankAccount?.accountNumber) {
        return {
          success: false,
          error: 'No bank account number provided',
        }
      }
      if (!bankAccount?.holderName) {
        bankAccount.holderName = `${data.shippingAddress.firstName} ${data.shippingAddress.lastName}`
      }
    }

    const createdAt = new Date()
    const input: CreateSaleInput = {
      channelId: 'NL-EUR-BEN',
      ...pick(data, ['email', 'dateOfBirth']),
      bankAccount,
      anonymousId: data.anonymousId ?? uuid(),
      shippingLabel: SaleShippingLabelType.EMAIL,
      paymentType: data.isDonation ? SalePaymentType.DONATION : SalePaymentType.BANK_TRANSFER,
      address: { ...data.shippingAddress, phoneAreaCode: '+31' },
      language: 'nl',
      from: UserFrom.BEN,
      items: data.items.map((item) => ({
        id: item.id && isUUID(item.id) ? item.id : uuid(),
        plan: SaleItemPlanType.C2B,
        createdAt,
        ...item,
      })),
    }
    const { success, error, saleNumber } = await this.saleService.createSale({
      input,
      partnerId: '55aef518-e9da-4084-a32e-1fdb80f39979',
      partnerPlatform: PartnerPlatform.EMBEDDED,
    })
    return {
      success,
      error,
      ...(success ? { saleNumber } : {}),
    }
  }
}
