/** Shipping price response from Sendcloud API */
export type IShippingPrice = {
  /** Shipping price @example '6.50' */
  price: string | null

  /** 3 letter currency code as defined by ISO-4217 @example 'EUR' */
  currency: string | null

  /** The receiver country of the shipment, as an ISO 3166-1 alpha-2 @example 'NL' */
  to_country: string

  /** A Sendcloud shipping price breakdown */
  breakdown: {
    /** Type of the price. It is an identifier of category of the price @example 'price_without_insurance' */
    type: string

    /** This label is a friendly name for type of the price type and can be used to represent it @example 'Initial price per parcel' */
    label: string

    /** Price amount of the breakdown item @example 6.4 */
    value: number
  }[]
}
