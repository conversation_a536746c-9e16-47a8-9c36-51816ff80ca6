import { <PERSON><PERSON>, <PERSON>rud<PERSON>ontroller } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { ProductSkuEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudProductSkuService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ProductSkuEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      stock: {},
      prices: {},
      'prices.channel': {},
      'prices.currency': {},
      heroImage: {},
      images: {},
      variant: {},
      model: {},
      'model.category': {},
      category: {},
      brand: {},
      testReport: {},
      originalAccessories: {},
      warrantyRules: {},
      orderSkuItems: {},
      preOrders: {},
      saleItem: {},
      marketingSkuCollections: {},
      modelMetrics: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/product-skus')
export class CrudProductSkuController implements CrudController<ProductSkuEntity> {
  constructor(public service: CrudProductSkuService) {}
}
