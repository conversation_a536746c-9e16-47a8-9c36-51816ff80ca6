// TODO: move to question-type-condition-combination
import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IProductVariant,
  type IProductVariantConditionCombinationChoice,
  type IProductVariantConditionCombinationPrice,
  ProductVariantConditionCombinationChoiceEntity,
  ProductVariantConditionCombinationPriceEntity,
  ProductVariantEntity,
} from '~/entities'

@ObjectType('ProductVariantConditionCombination')
@Entity('product_variant_condition_combination')
export class ProductVariantConditionCombinationEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @OneToMany(() => ProductVariantConditionCombinationChoiceEntity, (choice) => choice.combination, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  choices: Relation<ProductVariantConditionCombinationChoiceEntity>[]

  @OneToMany(() => ProductVariantConditionCombinationPriceEntity, (price) => price.conditionCombination, {
    nullable: false,
    cascade: true,
  })
  prices: Relation<ProductVariantConditionCombinationPriceEntity>[]

  @ManyToOne(() => ProductVariantEntity, (productVariant) => productVariant.conditionCombinations, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  variant: Relation<ProductVariantEntity>

  @Column()
  @RelationId((combination: ProductVariantConditionCombinationEntity) => combination.variant)
  variantId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductVariantConditionCombination = Omit<ProductVariantConditionCombinationEntity, keyof BaseEntity> & {
  prices: IProductVariantConditionCombinationPrice[]
  choices: IProductVariantConditionCombinationChoice[]
  vVariant: IProductVariant
}
