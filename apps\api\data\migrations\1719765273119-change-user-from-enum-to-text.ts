import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeUserFromEnumToText1719765273119 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP VIEW IF EXISTS order_summary')
    await queryRunner.query('DROP VIEW IF EXISTS sale_summary')
    await queryRunner.query('DROP VIEW IF EXISTS user_summary')
    await queryRunner.query('ALTER TABLE "user" ALTER COLUMN "from" TYPE TEXT USING "from"::TEXT')
    await queryRunner.query('ALTER TABLE "order" ALTER COLUMN "from" TYPE TEXT USING "from"::TEXT')
    await queryRunner.query('ALTER TABLE "sale" ALTER COLUMN "from" TYPE TEXT USING "from"::TEXT')
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
