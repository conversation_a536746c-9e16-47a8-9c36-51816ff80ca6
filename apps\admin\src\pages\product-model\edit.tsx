import { ProCard } from '@ant-design/pro-components'
import ProForm, { ProFormList } from '@ant-design/pro-form'
import { Edit, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation, useUpdate } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type {
  IAccessoryProduct,
  IProductModel,
  IProductModelAttribute,
  IProductSeries,
  IQuestionType,
} from '@valyuu/api/entities'
import { Typography } from 'antd'
import { DatePicker, Input, InputNumber, RadioGroup, Select, TextArea, Watch } from 'antx'
import cx from 'clsx'
import dayjs from 'dayjs'
import { type FC, useState } from 'react'
import { v4 as uuid } from 'uuid'

import {
  InputImage,
  InputLanguageTab,
  InputMultiEntitySelect,
  InputMultiLang,
  InputPublish,
  InputSlug,
  LabelWithDetails,
  LanguageTabChoices,
} from '~/components'

export const ProductModelEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, id, onFinish, saveButtonProps, query, formLoading } = useForm<
    IProductModel,
    HttpError,
    IProductModel
  >({
    meta: {
      join: [
        { field: 'image' },
        { field: 'attributes' },
        { field: 'attributes.options' },
        { field: 'metrics' },
        { field: 'accessoryProducts' },
      ],
    },
  })

  const record = query?.data?.data

  if (formProps.initialValues?.attributes) {
    formProps.initialValues.attributes.sort(
      (a: IProductModelAttribute, b: IProductModelAttribute) => a.sortOrder - b.sortOrder
    )
    formProps.initialValues.attributes.forEach((attribute: IProductModelAttribute) => {
      if (attribute.options) {
        attribute.options.sort((a, b) => a.sortOrder - b.sortOrder)
      }
    })
  }

  if (formProps.initialValues?.publishedAt) {
    formProps.initialValues.publishedAt = new Date(formProps.initialValues.publishedAt)
  }
  if (formProps.initialValues?.releaseDate) {
    formProps.initialValues.releaseDate = dayjs(formProps.initialValues.releaseDate)
  }
  const { mutate } = useUpdate<IProductModel, HttpError, Partial<IProductModel>>()
  const { editUrl } = useNavigation()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const { selectProps: questionTypeSelectProps } = useSelect<IQuestionType>({
    resource: 'admin/question-types',
    optionLabel: 'internalName',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: seriesSelectProps } = useSelect<IProductSeries>({
    resource: 'admin/product-series',
    optionLabel: 'name__en',
    filters: [{ field: 'publishedAt', operator: 'nnull', value: true }],
    meta: {
      fields: ['id', 'name__en', 'publishedAt'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: accessoryProductSelectProps } = useSelect<IAccessoryProduct>({
    resource: 'admin/accessory-products',
    optionLabel: 'internalName',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!record?.publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
            mutate({
              resource: 'admin/product-models',
              id: id! as string,
              values: {
                publishedAt: value ? new Date() : null,
              },
              successNotification: (data) => {
                return {
                  type: 'success',
                  message: `ProductModel has been ${value ? 'published' : 'unpublished'} successfully`,
                }
              },
              errorNotification: () => {
                return {
                  type: 'error',
                  message: `Failed to ${value ? 'publish' : 'unpublish'} product model`,
                }
              },
            })
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <ProForm
        {...formProps}
        onFinish={async (values) => {
          onFinish?.(values)
        }}
        submitter={false}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Polish)"
                name="slug__pl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__pl}
                className={clsHidePl}
              />
              <InputImage
                label="Image"
                name="image"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.PRODUCT_MODEL}
                processName={ImageProcessNameType.COPY}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
            </>
          )}
        </Watch>

        <Select
          label={
            <LabelWithDetails
              link={record?.seriesId ? editUrl('admin/product-series', record.seriesId) : undefined}
              label="Product series"
            />
          }
          name="seriesId"
          rules={['required']}
          {...(seriesSelectProps as any)}
          disabled
        />
        <RadioGroup
          label="Goes to recycle"
          name="recycle"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
        />
        <DatePicker label="Release date" name="releaseDate" format="DD-MM-YYYY" rules={['required']} />
        <Select
          label="Question type"
          name="questionTypeId"
          rules={['required']}
          {...(questionTypeSelectProps as any)}
        />
        <RadioGroup
          label="Is buyer MVP"
          name={['metrics', 'isMvp']}
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
        />
        <RadioGroup
          label="Is buyer bestseller"
          name={['metrics', 'isBestseller']}
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          disabled
        />
        <ProFormList
          required
          actionGuard={{
            beforeAddRow: async (defaultValue) => {
              defaultValue.id = uuid()
              return true
            },
          }}
          name="attributes"
          label="Attributes"
          creatorButtonProps={false}
          copyIconProps={false}
          deleteIconProps={false}
          itemRender={({ listDom, action }, { index }) => {
            return (
              <ProCard
                bordered
                extra={action}
                className="mb-3"
                title={<span className="list-item-title">Attribute {index + 1}</span>}
              >
                {listDom}
              </ProCard>
            )
          }}
        >
          {({ name: attributeName }, attributeIndex) => {
            return (
              <>
                <Input type="hidden" name={[attributeName, 'id']} hidden noStyle />
                <Watch
                  list={[
                    ['attributes', attributeName, 'name__en'],
                    ['attributes', attributeName, 'name__nl'],
                    ['attributes', attributeName, 'name__de'],
                    ['attributes', attributeName, 'name__pl'],
                  ]}
                >
                  {([name__en, name__nl, name__de, name__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="en"
                        label="Name (English)"
                        name={[attributeName, 'name__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="nl"
                        label="Name (Dutch)"
                        name={[attributeName, 'name__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="de"
                        label="Name (German)"
                        name={[attributeName, 'name__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="pl"
                        label="Name (Polish)"
                        name={[attributeName, 'name__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
                <Watch
                  list={[
                    ['attributes', attributeName, 'question__en'],
                    ['attributes', attributeName, 'question__nl'],
                    ['attributes', attributeName, 'question__de'],
                    ['attributes', attributeName, 'question__pl'],
                  ]}
                >
                  {([question__en, question__nl, question__de, question__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="en"
                        label="Question (English)"
                        name={[attributeName, 'question__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="nl"
                        label="Question (Dutch)"
                        name={[attributeName, 'question__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="de"
                        label="Question (German)"
                        name={[attributeName, 'question__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="pl"
                        label="Question (Polish)"
                        name={[attributeName, 'question__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
                <TextArea label="Description (English)" name={[attributeName, 'desc__en']} className={clsHideEn} />
                <TextArea label="Description (Dutch)" name={[attributeName, 'desc__nl']} className={clsHideNl} />
                <TextArea label="Description (German)" name={[attributeName, 'desc__de']} className={clsHideDe} />
                <TextArea label="Description (Polish)" name={[attributeName, 'desc__pl']} className={clsHidePl} />
                <InputNumber
                  label="Sort order"
                  precision={0}
                  name={[attributeName, 'sortOrder']}
                  rules={['required']}
                />
                <ProFormList
                  required
                  actionGuard={{
                    beforeAddRow: async (defaultValue) => {
                      defaultValue.id = uuid()
                      return true
                    },
                  }}
                  rules={[
                    {
                      validator: async (_, value) => {
                        if ((value || []).length === 0) {
                          return Promise.reject(new Error('At least one option is required'))
                        }
                      },
                    },
                  ]}
                  name="options"
                  label="Options"
                  creatorButtonProps={{
                    creatorButtonText: 'Add a new option',
                  }}
                  copyIconProps={{
                    tooltipText: 'Duplicate this option',
                  }}
                  deleteIconProps={{
                    tooltipText: 'Delete this option',
                  }}
                  itemRender={({ listDom, action }, { index }) => {
                    return (
                      <ProCard
                        bordered
                        extra={action}
                        className="mb-3"
                        title={
                          <span className="list-item-title">
                            Attribute {attributeIndex + 1} option {index + 1}
                          </span>
                        }
                      >
                        {listDom}
                      </ProCard>
                    )
                  }}
                >
                  {({ name: optionName }) => {
                    return (
                      <>
                        <Input type="hidden" name={[optionName, 'id']} hidden noStyle />
                        <Watch
                          list={[
                            ['attributes', attributeName, 'options', optionName, 'name__en'],
                            ['attributes', attributeName, 'options', optionName, 'name__nl'],
                            ['attributes', attributeName, 'options', optionName, 'name__de'],
                            ['attributes', attributeName, 'options', optionName, 'name__pl'],
                          ]}
                        >
                          {([name__en, name__nl, name__de, name__pl]) => (
                            <>
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="en"
                                label="Name (English)"
                                name={[optionName, 'name__en']}
                                rules={['required']}
                                className={clsHideEn}
                                fillType="duplicate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="nl"
                                label="Name (Dutch)"
                                name={[optionName, 'name__nl']}
                                rules={['required']}
                                className={clsHideNl}
                                fillType="duplicate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="de"
                                label="Name (German)"
                                name={[optionName, 'name__de']}
                                rules={['required']}
                                className={clsHideDe}
                                fillType="duplicate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="pl"
                                label="Name (Polish)"
                                name={[optionName, 'name__pl']}
                                rules={['required']}
                                className={clsHidePl}
                                fillType="duplicate"
                              />
                            </>
                          )}
                        </Watch>
                        <InputNumber
                          label="Sort order"
                          precision={0}
                          name={[optionName, 'sortOrder']}
                          rules={['required']}
                        />
                      </>
                    )
                  }}
                </ProFormList>
              </>
            )
          }}
        </ProFormList>
        <InputMultiEntitySelect
          label="Accessory products"
          name="accessoryProducts"
          {...(accessoryProductSelectProps as any)}
        />
        <Input noStyle type="datetime" name="publishedAt" hidden />
      </ProForm>
    </Edit>
  )
}
