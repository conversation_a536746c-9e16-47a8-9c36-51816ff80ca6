import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddAllegroChannelAndPartner1747728423135 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add Poland country
    await queryRunner.query(`
      INSERT INTO "public"."locale_country" (
        "id",
        "name__en",
        "name__nl",
        "name__de",
        "country_area_code",
        "created_at",
        "updated_at"
      )
      SELECT
        'PL',
        'Poland',
        'Polen',
        'Polen',
        '+48',
        '2025-05-20 08:48:06.711725',
        '2025-05-20 08:48:06.711725'
      WHERE NOT EXISTS (
        SELECT 1 FROM "public"."locale_country" WHERE "id" = 'PL'
      );
    `)

    // Add Allegro channel
    await queryRunner.query(`
      INSERT INTO "public"."channel" (
        "id", "name", "desc", "currency_id", 
        "created_at", "updated_at", "published_at", 
        "disable_buyer", "disable_seller", "type"
      ) 
      SELECT
        'PL-EUR-ALLEGRO',
        'Allegro',
        'Channel exclusively for partner Allegro',
        'EUR',
        '2025-05-20 08:48:06.711725',
        '2025-05-20 08:48:06.711725',
        '2025-05-20 08:48:06.711725',
        true,
        false,
        'PARTNER'
      WHERE NOT EXISTS (
        SELECT 1 FROM "public"."channel" 
        WHERE "id" = 'PL-EUR-ALLEGRO'
      );
    `)

    // Add Poland to Allegro channel
    await queryRunner.query(`
      INSERT INTO "public"."channel_countries" (
        "channel_id",
        "locale_country_id"
      )
      SELECT
        'PL-EUR-ALLEGRO',
        'PL'
      WHERE NOT EXISTS (
        SELECT 1 FROM "public"."channel_countries"
        WHERE "channel_id" = 'PL-EUR-ALLEGRO' AND "locale_country_id" = 'PL'
      );
    `)

    // Add channel seller payment period
    await queryRunner.query(`
      INSERT INTO "public"."seller_payment_period" (
        "channel_id",
        "c2b_from",
        "c2b_to",
        "c2b_unit",
        "c2c_from",
        "c2c_to",
        "c2c_unit",
        "donation_from",
        "donation_to",
        "donation_unit",
        "partner_id"
      )
      SELECT
        'PL-EUR-ALLEGRO',
        3,
        5,
        'DAY',
        NULL,
        14,
        'DAY',
        NULL,
        14,
        'DAY',
        NULL
      WHERE NOT EXISTS (
        SELECT 1 FROM "public"."seller_payment_period"
        WHERE "channel_id" = 'PL-EUR-ALLEGRO'
      );
    `)

    await queryRunner.query(`
      INSERT INTO "public"."partner" (
        "id", 
        "name", 
        "channel_id", 
        "created_at", 
        "updated_at", 
        "secret",
        "manage_customer_emails",
        "supported_plan_types",
        "supported_payment_types",
        "slug",
        "api_endpoint_url",
        "standalone_site_url",
        "bank_account_id"
      ) 
      SELECT
        '00a599a9-1d0f-40d6-aff3-fc717a68fb28',
        'Allegro',
        'PL-EUR-ALLEGRO',
        '2025-05-20 08:48:06.711725',
        '2025-05-20 16:27:56.725956',
        '24f7795e565884ce8260bb8050c4c166eb5becc4c27af0f3e5756e3ad5accfc9',
        false,
        '{C2B}'::character varying[],
        '{BANK_TRANSFER}'::character varying[],
        'allegro',
        NULL,
        NULL,
        NULL
      WHERE NOT EXISTS (
        SELECT 1 FROM "public"."partner" 
        WHERE "slug" = 'allegro' OR "id" = '00a599a9-1d0f-40d6-aff3-fc717a68fb28'
      );
    `)

    // Copy product_model_selling_promise records
    await queryRunner.query(`
      INSERT INTO product_model_selling_promise (
        c2c_days, c2b_number_from, c2b_number_to, channel_id, model_id, 
        created_at, updated_at, c2b_unit
      )
      SELECT 
        c2c_days, c2b_number_from, c2b_number_to, 'PL-EUR-ALLEGRO', model_id,
        created_at, updated_at, c2b_unit
      FROM product_model_selling_promise
      WHERE channel_id = 'NL-EUR-BEN';
    `)

    // Copy product_variant_base_price records
    await queryRunner.query(`
      INSERT INTO product_variant_base_price (
        base_price, channel_id, currency_id, variant_id, 
        created_at, updated_at
      )
      SELECT 
        base_price, 'PL-EUR-ALLEGRO', currency_id, variant_id,
        created_at, updated_at
      FROM product_variant_base_price
      WHERE channel_id = 'NL-EUR-BEN';
    `)

    // Copy product_variant_condition_combination_price records
    await queryRunner.query(`
      INSERT INTO product_variant_condition_combination_price (
        channel_id, currency_id, c2b_price, c2c_price,
        created_at, updated_at, condition_combination_id
      )
      SELECT 
        'PL-EUR-ALLEGRO', currency_id, c2b_price, c2c_price,
        created_at, updated_at, condition_combination_id
      FROM product_variant_condition_combination_price
      WHERE channel_id = 'NL-EUR-BEN';
    `)

    // Copy product_variant_problem_price records
    await queryRunner.query(`
      INSERT INTO product_variant_problem_price (
        price_reduction, channel_id, currency_id, variant_problem_id,
        created_at, updated_at
      )
      SELECT 
        price_reduction, 'PL-EUR-ALLEGRO', currency_id, variant_problem_id,
        created_at, updated_at
      FROM product_variant_problem_price
      WHERE channel_id = 'NL-EUR-BEN';
    `)

    // Copy recycle_threshold records
    await queryRunner.query(`
      INSERT INTO recycle_threshold (
        channel_id, currency_id, price
      )
      SELECT 
        'PL-EUR-ALLEGRO', currency_id, price
      FROM recycle_threshold
      WHERE channel_id = 'NL-EUR-BEN';
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Delete recycle_threshold records
    await queryRunner.query(`
      DELETE FROM "public"."recycle_threshold"
      WHERE "channel_id" = 'PL-EUR-ALLEGRO';
    `)

    // Delete product_variant_problem_price records
    await queryRunner.query(`
      DELETE FROM "public"."product_variant_problem_price"
      WHERE "channel_id" = 'PL-EUR-ALLEGRO';
    `)

    // Delete product_variant_condition_combination_price records
    await queryRunner.query(`
      DELETE FROM "public"."product_variant_condition_combination_price"
      WHERE "channel_id" = 'PL-EUR-ALLEGRO';
    `)

    // Delete product_variant_base_price records
    await queryRunner.query(`
      DELETE FROM "public"."product_variant_base_price"
      WHERE "channel_id" = 'PL-EUR-ALLEGRO';
    `)

    // Delete product_model_selling_promise records
    await queryRunner.query(`
      DELETE FROM "public"."product_model_selling_promise"
      WHERE "channel_id" = 'PL-EUR-ALLEGRO';
    `)

    // Delete channel_seller_payment_period records
    await queryRunner.query(`
      DELETE FROM "public"."seller_payment_period"
      WHERE "channel_id" = 'PL-EUR-ALLEGRO';
    `)

    // Delete channel_countries records
    await queryRunner.query(`
      DELETE FROM "public"."channel_countries"
      WHERE "channel_id" = 'PL-EUR-ALLEGRO' AND "locale_country_id" = 'PL';
    `)

    // Delete partner records
    await queryRunner.query(`
      DELETE FROM "public"."partner"
      WHERE "id" = '00a599a9-1d0f-40d6-aff3-fc717a68fb28';
    `)

    // Delete channel records
    await queryRunner.query(`
      DELETE FROM "public"."channel"
      WHERE "id" = 'PL-EUR-ALLEGRO';
    `)

    // Delete country records
    await queryRunner.query(`
      DELETE FROM "public"."locale_country"
      WHERE "id" = 'PL';
    `)
  }
}
