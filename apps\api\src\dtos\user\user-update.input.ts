import { ArgsType, Field } from '@nestjs/graphql'
import { Transform } from 'class-transformer'
import { IsEmail, IsNotEmpty, IsUUID, MaxLength, MinLength, ValidateIf } from 'class-validator'

@ArgsType()
export class UserUpdateInput {
  @IsNotEmpty()
  @IsUUID('4')
  @Field({ description: 'User ID' })
  userId: string

  @ValidateIf((obj) => obj.hasOwnProperty('password'))
  @IsNotEmpty()
  @MinLength(6)
  @MaxLength(255)
  @Field({ description: 'User password, minimum 8 characters', nullable: true })
  password: string

  @ValidateIf((obj) => obj.hasOwnProperty('email'))
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }) => (value ? value.trim() : value), {
    toClassOnly: true,
  })
  @Field({ description: 'User email', nullable: true })
  email: string
}
