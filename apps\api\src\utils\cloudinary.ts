import { UploadApiResponse, v2 as cloudinary } from 'cloudinary'

import { envConfig } from '~/configs'
import { ImageBasePathMapMap, ImageUsedInType } from '~/constants'

export const cloudinaryUploadResrouce = async (filePath: string, publicId: string, overwrite: boolean = true) => {
  return cloudinary.uploader.upload(filePath, {
    public_id: publicId,
    overwrite,
  })
}

export const cloudinaryUploadResourceFromStream = (
  stream: NodeJS.ReadableStream,
  publicId: string,
  overwrite: boolean = true
): Promise<UploadApiResponse> => {
  return new Promise((resolve, reject) => {
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        public_id: publicId,
        overwrite,
      },
      (err, res) => {
        if (err) {
          reject(err)
        }
        resolve(res)
      }
    )
    stream.pipe(uploadStream)
  })
}

export const cloudinaryDeleteResourcesByPrefix = async (prefix: string) => {
  return cloudinary.api.delete_resources_by_prefix(prefix, { max_results: 9999 })
}

export const cloudinaryDeleteFolder = async (folderPath: string) => {
  // TODO: fix this to delete folder
  // https://community.cloudinary.com/discussion/350/how-to-correctly-delete-folders-with-mixed-contents-by-prefix
  await cloudinary.api.delete_folder(folderPath, { invalidate: true })
}

export const cloudinaryDeleteResource = async (publicId: string) => {
  return cloudinary.uploader.destroy(publicId, { invalidate: true })
}

export const cloudinaryRenameResource = async (publicId: string, newPublicId: string, overwrite = false) => {
  return cloudinary.uploader.rename(publicId, newPublicId, { overwrite })
}

export const cloudinaryGetResizedImageUrl = ({
  publicId,
  width,
  height,
  crop = 'pad',
  gravity,
}: {
  publicId: string
  width: number
  height: number
  crop?: string
  gravity?: string
}) => {
  return cloudinary.url(publicId, {
    width,
    height,
    crop,
    gravity,
  })
}

export const cloudinaryGetUrl = (publicId: string) => {
  const { origin, pathname } = new URL(cloudinary.url(publicId, { secure: true }))
  return origin + pathname
}

export const cloudinaryGetEntityFolderPath = ({
  imageUsedIn,
  entityId,
}: {
  imageUsedIn: ImageUsedInType
  entityId: string
}) => {
  return `${envConfig.CLOUDINARY_PREFIX}${ImageBasePathMapMap[imageUsedIn]}/${entityId}/`
}

export const cloudinaryGetEntityResourcePath = ({
  imageUsedIn,
  entityId,
  imageId,
}: {
  imageUsedIn: ImageUsedInType
  entityId: string
  imageId: string
}) => {
  return `${cloudinaryGetEntityFolderPath({ imageUsedIn, entityId })}${imageId}`
}

export const cloudinaryGetResourceInfo = async (publicId: string) => {
  return cloudinary.api.resource(publicId)
}

export const cloudinaryListResources = async (prefix: string) => {
  return cloudinary.api.resources({ prefix })
}
