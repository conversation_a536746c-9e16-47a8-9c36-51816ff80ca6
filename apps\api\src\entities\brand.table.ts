import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IImage,
  ImageEntity,
  type IProductModel,
  type IProductSeries,
  ProductModelEntity,
  ProductSeriesEntity,
} from '~/entities'

@ObjectType('Brand')
@Entity('brand')
export class BrandEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column({ unique: true })
  slug__en: string

  @Field()
  @Column({ unique: true })
  slug__nl: string

  @Field()
  @Column({ unique: true })
  slug__de: string

  @Field()
  @Column({ unique: true })
  slug__pl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.brandImage, { nullable: false, cascade: true })
  @JoinColumn()
  image: Relation<ImageEntity>

  @Column()
  @RelationId((brand: BrandEntity) => brand.image)
  imageId: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @OneToMany(() => ProductSeriesEntity, (productSeries) => productSeries.brand, { nullable: false })
  productSeries: Relation<ProductSeriesEntity>[]

  @OneToMany(() => ProductModelEntity, (productModel) => productModel.brand, { nullable: false })
  productModels: Relation<ProductModelEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IBrand = Omit<BrandEntity, keyof BaseEntity> & {
  image: IImage
  productSeries: IProductSeries[]
  productModels: IProductModel[]
}
