import '@xyflow/react/dist/style.css'

import { List } from '@refinedev/antd'
import {
  IResourceComponentsProps,
  useApiUrl,
  useCustomMutation,
  useInvalidate,
  useList,
  useNotification,
} from '@refinedev/core'
import type { IChannel, ILocaleCountry, IShippingConfig, IWarehouse } from '@valyuu/api/entities'
import { Button, Card, Popconfirm, Space, Spin, Tabs } from 'antd'
import clsx from 'clsx'
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { AiOutlineEdit, AiOutlineRollback, AiOutlineSave } from 'react-icons/ai'

import { BlockShippingConfig } from '~/components'

export const ShippingConfigList: FC<IResourceComponentsProps> = () => {
  const { data: warehouses, isLoading: isWarehousesLoading } = useList<IWarehouse>({
    resource: 'admin/warehouses',
    meta: {
      join: [
        { field: 'shippingToCountries', select: ['id', 'name__en'] },
        { field: 'receivingFromCountries', select: ['id', 'name__en'] },
        { field: 'country', select: ['id', 'name__en'] },
      ],
    },
    pagination: {
      mode: 'off',
    },
    sorters: [
      {
        field: 'sortOrder',
        order: 'asc',
      },
    ],
  })

  const { data: countries, isLoading: isCountriesLoading } = useList<ILocaleCountry>({
    resource: 'admin/countries',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { data: channels, isLoading: isChannelsLoading } = useList<IChannel>({
    resource: 'admin/channels',
    meta: {
      join: [{ field: 'countries', select: ['id', 'name__en'] }],
    },
    pagination: {
      mode: 'off',
    },
  })

  const [currentChannel, setCurrentChannel] = useState<IChannel | undefined>()

  const { data: shippingConfigs, isLoading: isShippingConfigsLoading } = useList<IShippingConfig>({
    resource: 'admin/shipping-configs',
    meta: {
      join: [
        {
          field: 'warehouse',
        },
      ],
    },
    filters: [
      {
        field: 'channelId',
        operator: 'eq',
        value: currentChannel?.id ?? null,
      },
    ],
    pagination: {
      mode: 'off',
    },
  })

  const [changedShippingConfigs, setChangedShippingConfigs] = useState<Partial<IShippingConfig>[] | undefined>()

  const invalidate = useInvalidate()
  const { mutate } = useCustomMutation<IShippingConfig[]>()

  const apiUrl = useApiUrl()

  const { open } = useNotification()

  const [overrideDefault, setOverrideDefault] = useState(false)

  const saveConfig = useCallback(
    ({ channelId, configs }: { channelId?: string | null; configs: Partial<IShippingConfig>[] }) => {
      mutate(
        {
          url: `${apiUrl}/admin/shipping-configs/save-for-channel`,
          method: 'post',
          values: {
            channelId: channelId ?? null,
            configs,
          },
        },
        {
          onSuccess: () => {
            setChangedShippingConfigs(undefined)

            invalidate({
              resource: 'admin/shipping-configs',
              invalidates: ['list'],
            })

            open?.({
              type: 'success',
              message: 'Successfully saved shipping configurations',
              description: 'The shipping configurations have been updated',
            })
          },
          onError: (error) => {
            open?.({
              type: 'error',
              message: 'Failed to save shipping configurations',
              description: error?.message || 'An error occurred while saving',
            })
          },
        }
      )
    },
    [apiUrl, invalidate, open, mutate]
  )

  const handleSubmit = () => {
    if (!changedShippingConfigs) return

    saveConfig({
      channelId: currentChannel?.id ?? null,
      configs: changedShippingConfigs,
    })
  }

  const tabItems = useMemo(
    () =>
      [{ id: undefined, name: undefined }, ...(channels?.data ?? [])].map((channel) => ({
        key: channel?.id ?? 'default',
        label: <span className="select-none">{channel?.name ?? 'Default'}</span>,
      })),
    [channels?.data]
  )

  const isLoading =
    isWarehousesLoading ||
    isCountriesLoading ||
    isChannelsLoading ||
    isShippingConfigsLoading ||
    !countries?.data ||
    !shippingConfigs?.data ||
    !warehouses?.data

  const buyerCountries =
    currentChannel === undefined
      ? (countries?.data ?? [])
      : currentChannel.disableBuyer
        ? []
        : (currentChannel.countries ?? [])
  const sellerCountries =
    currentChannel === undefined
      ? (countries?.data ?? [])
      : currentChannel.disableSeller
        ? []
        : (currentChannel.countries ?? [])

  const shouldShowOverrideButton = currentChannel && !overrideDefault && !shippingConfigs?.data?.length
  const shouldShowRestoreButton = currentChannel && shippingConfigs?.data?.length

  useEffect(() => {
    setChangedShippingConfigs(undefined)
    setOverrideDefault(false)
  }, [currentChannel, shippingConfigs, warehouses, countries, channels])

  return (
    <List headerButtons={[]}>
      <Card
        actions={[
          <Space size="small" className="float-right mr-6 flex flex-wrap">
            {shouldShowRestoreButton ? (
              <Popconfirm
                title="Are you sure?"
                okText="Restore"
                okButtonProps={{ danger: true, ghost: true }}
                cancelText="Cancel"
                onConfirm={() => {
                  setOverrideDefault(false)
                  saveConfig({
                    channelId: currentChannel?.id ?? null,
                    configs: [],
                  })
                }}
              >
                <Button danger icon={<AiOutlineRollback />}>
                  Restore Default
                </Button>
              </Popconfirm>
            ) : null}
            {shouldShowOverrideButton ? (
              <Button type="primary" onClick={() => setOverrideDefault(true)} icon={<AiOutlineEdit />}>
                Override Default
              </Button>
            ) : null}
            <Button type="primary" disabled={!changedShippingConfigs} onClick={handleSubmit} icon={<AiOutlineSave />}>
              Save
            </Button>
          </Space>,
        ]}
      >
        <Tabs
          activeKey={currentChannel?.id ?? 'default'}
          items={tabItems}
          onChange={(key) => {
            setCurrentChannel(key === 'default' ? undefined : channels?.data.find((c) => c.id === key))
          }}
        />

        {isLoading ? (
          <div className="flex h-[700px] items-center justify-center">
            <Spin />
          </div>
        ) : (
          <BlockShippingConfig
            className={clsx({ 'pointer-events-none grayscale opacity-30': shouldShowOverrideButton })}
            buyerCountries={buyerCountries}
            sellerCountries={sellerCountries}
            warehouses={warehouses.data}
            value={shippingConfigs.data}
            onChange={setChangedShippingConfigs}
          />
        )}
      </Card>
    </List>
  )
}
