/**
 * Shipping option response structure for V3 API
 */
export type IShippingOption = {
  /** Unique identifier for the shipping option */
  id: string

  /** Human-readable name of the shipping option */
  name: string

  /** Carrier providing this shipping option */
  carrier: string

  /** Shipping method friendly name */
  shipping_method_name: string

  /** Shipping product friendly name */
  shipping_product_name: string

  /** Price information */
  price: {
    /** Price amount */
    amount: number
    /** Currency code */
    currency: string
  }

  /** Lead time information */
  lead_time?: {
    /** Lead time in hours */
    hours?: number
    /** Lead time description */
    description?: string
  }

  /** Available functionalities */
  functionalities?: {
    /** Age check minimum age */
    age_check?: number
    /** Business to business */
    b2b?: boolean
    /** Business to consumer */
    b2c?: boolean
    /** Whether the shipment fits in a box */
    boxable?: boolean
    /** Suitable for bulky goods */
    bulky_goods?: boolean
    /** Carrier billing type */
    carrier_billing_type?: string
    /** Cash on delivery maximum value */
    cash_on_delivery?: number
    /** Dangerous goods allowed */
    dangerous_goods?: boolean
    /** Delivery attempts */
    delivery_attempts?: number
    /** Delivery before time */
    delivery_before_time?: boolean
    /** Delivery deadline */
    delivery_deadline?: string
    /** Direct contract only */
    direct_contract_only?: boolean
    /** Eco delivery */
    eco_delivery?: boolean
    /** First mile pickup */
    first_mile?: boolean
    /** Flexible goods */
    flexible_goods?: boolean
    /** Fresh goods */
    fresh_goods?: boolean
    /** Harmonized label */
    harmonized_label?: boolean
    /** ID check required */
    id_check?: boolean
    /** Insurance available */
    insurance?: boolean
    /** Last mile delivery */
    last_mile?: boolean
    /** Manually attach label */
    manually?: boolean
    /** Multicollo support */
    multicollo?: boolean
    /** Neighbor delivery */
    neighbor_delivery?: boolean
    /** Non-conveyable */
    non_conveyable?: boolean
    /** Personalized delivery */
    personalized_delivery?: boolean
    /** Premium service */
    premium?: boolean
    /** Priority level */
    priority?: string
    /** Registered delivery */
    registered_delivery?: boolean
    /** Returns */
    returns?: boolean
    /** Respect functionality */
    respect?: boolean
    /** Service area */
    service_area?: string
    /** Signature required */
    signature?: boolean
    /** Size category */
    size?: string
    /** Sorted */
    sorted?: boolean
    /** Surcharge */
    surcharge?: boolean
    /** Tracked */
    tracked?: boolean
    /** Tyres allowed */
    tyres?: boolean
    /** Weekend delivery */
    weekend_delivery?: string
    /** Era */
    era?: boolean
  }

  /** Weight range constraints */
  weight_range?: {
    /** Minimum weight */
    min_weight: number
    /** Maximum weight */
    max_weight: number
  }
}

/**
 * Response structure for shipping options
 */
export type IFindShippingOptionsResponse = {
  /** Array of available shipping options */
  shipping_options: IShippingOption[]
  /** Total count of options */
  count?: number
}
