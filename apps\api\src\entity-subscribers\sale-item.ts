import { BadRequestException, Injectable } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import { startOfDay } from 'date-fns'
import { compare } from 'fast-json-patch'
import { isNil, omit, pick } from 'lodash'
import ms from 'ms'
import { ClsService } from 'nestjs-cls'
import { I18nService } from 'nestjs-i18n'
import type { EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm'
import { DataSource, EventSubscriber } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'

import { envConfig } from '~/configs'
import {
  ChangeHistoryChangedByType,
  EmailType,
  LicensePlateStatusEnum,
  LOCALE_ENABLED_LANGUAGES,
  PartnerWebhookEntityType,
  PartnerWebhookEvent,
  ProductModelSellerQuestionType,
  SaleItemOfferStatus,
  SaleItemPlanType,
  SaleItemStatus,
  SalePaymentType,
  SaleStatus,
} from '~/constants'
import {
  CharityEntity,
  EmailHistoryEntity,
  LicensePlateDeviceEntity,
  LicensePlateProductEntity,
  LicensePlatePurchaseEntity,
  LicensePlateSaleEntity,
  LicensePlateStatusEntity,
  PartnerEntity,
  ProductVariantEntity,
  SaleEntity,
  SaleItemEntity,
  SaleItemHistoryEntity,
  SellerPaymentPeriodEntity,
} from '~/entities'
import { ClsStoreType } from '~/interfaces'
import { PartnerWebhookService, SaleItemOfferService } from '~/services'
import { dateAddBusinessDays, formatMoney, getEmailTemplatePrefix, sendmail } from '~/utils'

const CHANGE_LOG_OMITTED_FIELDS = [
  // Ignored fields
  'createdAt',
  'updatedAt',
  'version',
  'payoutDueDate',
  'note',
  'legacyId',
  'trelloId',
  // Relations
  'channel',
  'currency',
  'productVariant',
  'productModel',
  'sale',
  'productSku',
  'stripePayments',
  'stock',
  'imei',
  'returnAddress',
  'returnShipment',
  'user',
  'partner',
  'offers',
  'offer',
  'histories',
] as const

@Injectable()
@EventSubscriber()
export class SaleItemSubscriber implements EntitySubscriberInterface<SaleItemEntity> {
  constructor(
    private readonly cls: ClsService<ClsStoreType>,
    private readonly saleItemOfferService: SaleItemOfferService,
    private readonly partnerWebhookService: PartnerWebhookService,
    private readonly i18n: I18nService,
    private readonly dataSource?: DataSource
  ) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return SaleItemEntity
  }

  async afterInsert(event: InsertEvent<SaleItemEntity>) {}

  async beforeUpdate(event: UpdateEvent<SaleItemEntity>) {
    const { manager, databaseEntity, entity } = event

    if (!databaseEntity || !entity) {
      return
    }

    if (entity?.answers?.type === ProductModelSellerQuestionType.CONDITION) {
      entity.answers.PROBLEM = []
    } else if (entity?.answers?.type === ProductModelSellerQuestionType.PROBLEM) {
      entity.answers.CONDITION = []
    }

    // Received at
    if (databaseEntity.status === SaleItemStatus.SUBMITTED && entity.status === SaleItemStatus.RECEIVED) {
      entity.receivedAt = new Date()
    }

    // Manually accept an offer
    if (entity.status !== SaleItemStatus.PENDING_PAYMENT && entity.status === SaleItemStatus.PENDING_PAYMENT) {
      entity.offerStatus = SaleItemOfferStatus.ACCEPTED
      entity.offerAcceptedAt = new Date()
    }
    if (entity.status !== SaleItemStatus.PENDING_RETURN && entity.status === SaleItemStatus.PENDING_RETURN) {
      entity.offerStatus = SaleItemOfferStatus.DECLINED_RETURN
      entity.offerAcceptedAt = new Date()
    }
    if (entity.status !== SaleItemStatus.PENDING_RECYCLE && entity.status === SaleItemStatus.PENDING_RECYCLE) {
      entity.offerStatus = SaleItemOfferStatus.DECLINED_RECYCLE
      entity.offerAcceptedAt = new Date()
    }

    // Update product model id when product variant id is changed
    const beforeVariantId = databaseEntity.productVariantId ?? databaseEntity.productVariant?.id
    const afterVariantId = entity.productVariantId ?? entity.productVariant?.id
    if (beforeVariantId && afterVariantId && beforeVariantId !== afterVariantId) {
      try {
        const { modelId } =
          (await manager.findOne(ProductVariantEntity, {
            where: { id: afterVariantId },
            select: { modelId: true },
          })) ?? {}
        if (modelId) {
          entity.productModelId = modelId
        } else {
          Sentry.captureException(new Error('Error updating sale item.'), {
            tags: {
              type: 'sale-item:update',
            },
            extra: {
              saleItem: entity,
            },
          })
        }
      } catch {
        Sentry.captureException(new Error('Error updating sale item.'), {
          tags: {
            type: 'sale-item:update',
          },
          extra: {
            saleItem: entity,
          },
        })
      }
    }

    const entityProperties = this.dataSource
      ?.getMetadata(SaleItemEntity)
      .ownColumns.map((column) => column.propertyName)

    const previousState = databaseEntity

    // Convert date to ISO string for JSON patch diff
    Object.entries(previousState).forEach(([key, value]) => {
      if (value instanceof Date) {
        ;(previousState as unknown as Record<string, string>)[key] = value.toISOString()
      }
    })

    const currentState = {
      ...databaseEntity,
      ...pick(entity, entityProperties),
    }

    Object.entries(currentState).forEach(([key, value]) => {
      if (value instanceof Date) {
        ;(currentState as unknown as Record<string, string>)[key] = value.toISOString()
      }
    })

    if (isNil(entity.isTested)) {
      entity.isTested = databaseEntity.isTested
    }
    if (previousState && currentState) {
      // Generate JSON patch diff between old and new states
      const undoPatch = compare(
        omit(currentState, CHANGE_LOG_OMITTED_FIELDS),
        omit(previousState, CHANGE_LOG_OMITTED_FIELDS)
      )

      if (undoPatch.length) {
        let changedByType: ChangeHistoryChangedByType = ChangeHistoryChangedByType.SYSTEM
        let changedById: string
        let adminUserId: string
        let partnerId: string
        try {
          adminUserId = this.cls.get('adminUserId')
          partnerId = this.cls.get('partnerId')
        } catch {
          Sentry.captureException(new Error('Error updating sale item.'), {
            tags: {
              type: 'sale-item:update',
            },
            extra: {
              saleItem: entity,
            },
          })
        }
        if (adminUserId) {
          changedById = adminUserId
          changedByType = ChangeHistoryChangedByType.ADMIN
        } else if (partnerId) {
          changedById = partnerId
          changedByType = ChangeHistoryChangedByType.PARTNER
        }

        const createNewOffer =
          // 1. Variant changed
          (previousState.productVariantId !== currentState.productVariantId ||
            // 2. Current price is different from previous price
            previousState.price !== currentState.price) &&
          // 3. isTested is true
          currentState.isTested

        if (createNewOffer) {
          // create a new offer
          const offer = await this.saleItemOfferService.create(currentState as SaleItemEntity)
          entity.offerStatus = SaleItemOfferStatus.PENDING
          entity.offer = offer
          entity.offerId = offer.id
          entity.status = SaleItemStatus.PENDING_OFFER
          entity.__offerCreated = true
        }

        // Save to SaleItemHistoryEntity (new functionality)
        const historyEntity = SaleItemHistoryEntity.create({
          saleItemId: databaseEntity.id,
          version: databaseEntity.version,
          changedByType,
          changedById,
          // Copy all fields from the previous state
          answers: databaseEntity.answers,
          customerNote: databaseEntity.customerNote,
          type: databaseEntity.type,
          status: databaseEntity.status,
          offerStatus: databaseEntity.offerStatus,
          offerAcceptedAt: databaseEntity.offerAcceptedAt,
          price: databaseEntity.price,
          initialPrice: databaseEntity.initialPrice,
          returnAddressId: databaseEntity.returnAddressId,
          returnShipmentId: databaseEntity.returnShipmentId,
          isTested: databaseEntity.isTested,
          productVariantId: databaseEntity.productVariantId,
          productModelId: databaseEntity.productModelId,
          offerId: databaseEntity.offerId,
        })
        await manager.save(historyEntity)

        entity.__itemUpdated = true

        entity.version = databaseEntity.version + 1
      }

      // Normal flow
      if (
        // Current state is accepted or initial
        [SaleItemOfferStatus.ACCEPTED, SaleItemOfferStatus.INITIAL].includes(
          entity.offerStatus ?? databaseEntity.offerStatus
        ) &&
        // isTested is true
        (entity.isTested ?? databaseEntity.isTested) === true &&
        // Current state is received
        (entity.status ?? databaseEntity.status) === SaleItemStatus.RECEIVED
      ) {
        entity.status = SaleItemStatus.PENDING_PAYMENT
      }

      // Update payout due date
      if (
        databaseEntity.status !== SaleItemStatus.PENDING_PAYMENT &&
        entity.status === SaleItemStatus.PENDING_PAYMENT
      ) {
        const saleItem = await manager.findOne(SaleItemEntity, {
          where: { id: databaseEntity.id },
          relations: { sale: true },
        })
        Object.assign(saleItem, omit(entity, 'sale'))
        const sellerPaymentPeriod = await SellerPaymentPeriodEntity.findOne({
          where: { channelId: saleItem.channelId },
          cache: envConfig.isProd ? ms('2 hours') : false,
        })
        const planPrefix =
          saleItem.sale.paymentType === SalePaymentType.DONATION
            ? 'donation'
            : saleItem.type === SaleItemPlanType.C2C
              ? 'c2c'
              : 'c2b'
        const maxPayoutDays = sellerPaymentPeriod[`${planPrefix}To`]
        // The basis date is either sale received date or sale item offer accepted date
        const basisDate =
          saleItem.offerId && saleItem.offerStatus === SaleItemOfferStatus.ACCEPTED
            ? (saleItem.offerAcceptedAt ?? new Date())
            : (saleItem.sale.receivedAt ?? new Date())
        // Calculate payout date using business days instead of calendar days
        const plannedPayoutDate = startOfDay(dateAddBusinessDays(basisDate, maxPayoutDays - 1))
        entity.payoutDueDate = plannedPayoutDate
      }

      const ACCEPTED_STATUSSES = [
        SaleItemStatus.PAID,
        SaleItemStatus.PAYMENT_IN_PROCESS,
        SaleItemStatus.PENDING_PAYMENT,
        SaleItemStatus.RECYCLED,
        SaleItemStatus.PENDING_RECYCLE,
        SaleItemStatus.PAYMENT_FAILED,
      ]

      if (!ACCEPTED_STATUSSES.includes(databaseEntity.status) && ACCEPTED_STATUSSES.includes(entity.status)) {
        await this.createLicensePlate(event)
      }
    }
  }

  async afterUpdate(event: UpdateEvent<SaleItemEntity>) {
    const { databaseEntity, entity, manager } = event

    if (!databaseEntity || !entity) {
      return
    }

    // From beforeUpdate
    const offerCreated = entity.__offerCreated ?? false
    delete entity.__offerCreated
    const itemUpdated = entity.__itemUpdated ?? false
    delete entity.__itemUpdated

    const saleId = databaseEntity.saleId ?? entity.saleId
    if (saleId) {
      const sale = await manager.findOne(SaleEntity, {
        where: { id: saleId },
        relations: { user: true, address: true, bankAccount: true, saleItems: { productVariant: true } },
      })
      const lang = sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]
      // If every sale item is in end status, set sale status to complete or canceled
      if (
        [SaleStatus.SUBMITTED, SaleStatus.RECEIVED].includes(sale.status) &&
        sale.saleItems.every(({ status }) =>
          [
            SaleItemStatus.CUSTOMER_ABANDONED,
            SaleItemStatus.RETURNED,
            SaleItemStatus.NOT_RECEIVED,
            SaleItemStatus.PAID,
            SaleItemStatus.RECYCLED,
          ].includes(status)
        )
      ) {
        sale.status = SaleStatus.COMPLETED
        await manager.save(sale)
      }

      let emailType: EmailType.SELLER_DEVICE_ACCEPTED | EmailType.SELLER_DEVICE_PAID_OUT
      let emailSubject: string
      // Device accepted
      if (
        databaseEntity.isTested === false &&
        entity.isTested === true &&
        entity.offerStatus === SaleItemOfferStatus.INITIAL
      ) {
        emailType = EmailType.SELLER_DEVICE_ACCEPTED
        emailSubject = this.i18n.t('emails.sellerDeviceAcceptedSubject', {
          lang,
          args: { saleNumber: sale.saleNumber },
        })
        // Device paid out
      } else if (databaseEntity.status === SaleItemStatus.PENDING_PAYMENT && entity.status === SaleItemStatus.PAID) {
        emailType = EmailType.SELLER_DEVICE_PAID_OUT
        emailSubject = this.i18n.t('emails.sellerDevicePaidOutSubject', {
          lang,
          args: { saleNumber: sale.saleNumber },
        })
      }
      if (emailType && emailSubject) {
        // Send device accepted email or device paid out email (the variables are the same)

        const { manageCustomerEmails } = (await manager.findOne(PartnerEntity, {
          where: { id: sale.partnerId },
          select: { id: true, manageCustomerEmails: true },
          cache: envConfig.isProd ? ms('1 hour') : false,
        })) ?? { manageCustomerEmails: true }

        const channelId = sale.channelId
        if ((!sale.partnerId || manageCustomerEmails) && !sale.isLegacy) {
          let paymentPeriodC2b: string
          let paymentPeriodC2c: string
          let paymentPeriodDonation: string
          const paymentType = sale.paymentType

          // SELLER_DEVICE_PAID_OUT doesn't need to get payment period
          if (emailType === EmailType.SELLER_DEVICE_ACCEPTED) {
            if (paymentType === SalePaymentType.DONATION) {
              paymentPeriodDonation = await this.saleItemOfferService.getPaymentPeriod({
                channelId,
                plan: SaleItemPlanType.C2B,
                lang,
                paymentType: sale.paymentType,
              })
            } else {
              const plan = entity.type
              if (plan === SaleItemPlanType.C2B && !paymentPeriodC2b) {
                paymentPeriodC2b = await this.saleItemOfferService.getPaymentPeriod({
                  channelId,
                  plan,
                  lang,
                  paymentType,
                })
              }
              if (plan === SaleItemPlanType.C2C && !paymentPeriodC2c) {
                paymentPeriodC2c = await this.saleItemOfferService.getPaymentPeriod({
                  channelId,
                  plan,
                  lang,
                  paymentType,
                })
              }
            }
          }

          let charityName: string = undefined
          let iban: string = undefined
          if (sale.paymentType === SalePaymentType.DONATION) {
            // TODO: get charity name from sale
            const charity = await CharityEntity.findOneOrFail({ where: {} })
            charityName = charity[`name__${sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]}`]
          } else {
            iban = sale.paymentType === SalePaymentType.BANK_TRANSFER ? sale.bankAccount?.accountNumber : undefined
          }
          const saleItem = sale.saleItems.find(({ id }) => id === entity.id)
          const locale = `${sale.languageId}-${sale.address.countryId}`

          const data = {
            firstName: sale.address.firstName,
            saleNumber: sale.saleNumber,
            paymentPeriodC2b,
            paymentPeriodC2c,
            paymentPeriodDonation,
            iban,
            charityName,
            item: {
              name: saleItem.productVariant[`name__${lang}`],
              price: formatMoney(saleItem.price, sale.currencyId, locale),
            },
          }

          // send email

          try {
            const subject = emailSubject
            const templatePrefix = await getEmailTemplatePrefix({
              isPartner: !!sale.partnerId,
              partnerSlug: sale.partner?.slug,
              type: emailType,
              lang,
            })
            await sendmail(
              {
                type: emailType,
                lang,
                to: sale.user.email,
                subject,
                data,
                templatePrefix,
              },
              3
            )
            await manager.save(
              EmailHistoryEntity.create({
                email: sale.user.email,
                subject,
                userId: sale.user.id,
                type: emailType,
                relatedIds: [sale.id, ...sale.saleItems.map(({ id }) => id)],
                lang,
                data,
                templatePrefix,
              })
            )
          } catch (error) {
            Sentry.captureMessage('Error sending mail', {
              tags: {
                type: 'sale-item:sendEmail',
              },
              extra: {
                error,
              },
            })
          }
        }
      }
    }

    const id = databaseEntity.id ?? entity.id

    if (id) {
      let saleItem: SaleItemEntity
      if (itemUpdated) {
        saleItem = await manager.findOne(SaleItemEntity, { where: { id }, select: { partnerId: true, version: true } })
        if (saleItem?.partnerId) {
          this.partnerWebhookService.callWebhook({
            event: PartnerWebhookEvent.TRADE_IN_ITEM_UPDATED,
            partnerId: saleItem.partnerId,
            entityType: PartnerWebhookEntityType.TRADE_IN_ITEM,
            entityId: id,
            version: saleItem.version,
          })
        }
      }
      if (offerCreated) {
        if (!saleItem) {
          saleItem = await manager.findOne(SaleItemEntity, {
            where: { id },
            select: { partnerId: true, version: true },
          })
        }
        if (saleItem?.partnerId) {
          this.partnerWebhookService.callWebhook({
            event: PartnerWebhookEvent.TRADE_IN_ITEM_NEW_OFFER_CREATED,
            partnerId: saleItem.partnerId,
            entityType: PartnerWebhookEntityType.TRADE_IN_ITEM,
            entityId: id,
            version: saleItem.version,
          })
        }
      }
    }
  }

  async createLicensePlate(event?: UpdateEvent<SaleItemEntity>) {
    try {
      const { entity, manager } = event

      if (entity.imei && entity.offerAcceptedAt) {
        let imei = entity.imei

        let licensePlate = LicensePlateDeviceEntity.create()
        licensePlate.guid = uuidv4()
        licensePlate.imei = imei
        licensePlate.serial = imei
        licensePlate.updated_by = 'Refine'

        let licensePlateProduct = LicensePlateProductEntity.create()
        licensePlateProduct.guid = licensePlate.guid
        licensePlate.product = licensePlateProduct

        let licensePlatePurchase = LicensePlatePurchaseEntity.create()
        licensePlatePurchase.guid = licensePlate.guid
        licensePlatePurchase.base_purchase_price = entity.price
        licensePlatePurchase.purchase_currency = entity.currencyId
        licensePlatePurchase.purchase_channel = entity.channelId
        licensePlate.purchase = licensePlatePurchase

        let licensePlateSale = LicensePlateSaleEntity.create()
        licensePlateSale.guid = licensePlate.guid
        licensePlate.sale = licensePlateSale

        let licensePlateStatus = LicensePlateStatusEntity.create({
          status: LicensePlateStatusEnum.INBOUND,
          guid: licensePlate.guid,
        })
        licensePlate.status = [licensePlateStatus]
        await manager.save(LicensePlateDeviceEntity, licensePlate)
      }
    } catch (error) {
      console.error('Failed to create license plate:', error)
      throw error // important if you want to rollback the transaction
    }
  }
}
