import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

import { SeoContentType } from '~/constants'
import { SeoFooterContent } from '~/dtos'

registerEnumType(SeoContentType, { name: 'SeoContentType' })

@ObjectType('SeoContent')
@Entity('seo_content')
export class SeoContentEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field(() => SeoContentType)
  @Column({ type: 'varchar' })
  @Index()
  type: SeoContentType

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  header__en?: string | null

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  header__nl?: string | null

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  header__de?: string | null

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  header__pl?: string | null

  @Field(() => [SeoFooterContent], { nullable: true })
  @Column({ type: 'jsonb', nullable: true })
  footer__en?: InstanceType<typeof SeoFooterContent>[] | null

  @Field(() => [SeoFooterContent], { nullable: true })
  @Column({ type: 'jsonb', nullable: true })
  footer__nl?: InstanceType<typeof SeoFooterContent>[] | null

  @Field(() => [SeoFooterContent], { nullable: true })
  @Column({ type: 'jsonb', nullable: true })
  footer__de?: InstanceType<typeof SeoFooterContent>[] | null

  @Field(() => [SeoFooterContent], { nullable: true })
  @Column({ type: 'jsonb', nullable: true })
  footer__pl?: InstanceType<typeof SeoFooterContent>[] | null

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ISeoContent = Omit<SeoContentEntity, keyof BaseEntity>
