import { <PERSON><PERSON>, <PERSON>rudController } from '@dataui/crud'
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, UseGuards } from '@nestjs/common'

import { SaleEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudSaleService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: SaleEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      user: {},
      address: {},
      saleItems: {},
      'saleItems.productVariant': {},
      bankAccount: {},
      shipment: {},
      'shipment.shippingMethod': {},
      partner: {},
      histories: {},
      'histories.changedByAdminUser': {},
      'histories.changedByPartner': {},
    },
  },
})
@Controller('admin/sales')
export class CrudSaleController implements CrudController<SaleEntity> {
  constructor(public service: CrudSaleService) {}

  @Get(':id/histories')
  async getHistories(@Param('id', ParseUUIDPipe) id: string) {
    return this.service.getHistories(id)
  }

  @Get('imei/:imei')
  async getByImei(@Param('imei') imei: string) {
    return this.service.getByImei(imei)
  }

  @Post(':id/create-shipment')
  async createShipment(@Param('id', ParseUUIDPipe) id: string, @Body('override') override?: boolean) {
    return this.service.createShipment(id, override)
  }
}
