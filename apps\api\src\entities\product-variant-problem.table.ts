import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IProductVariant,
  type IProductVariantProblemPrice,
  type IQuestionTypeProblem,
  ProductVariantEntity,
  ProductVariantProblemPriceEntity,
  QuestionTypeProblemEntity,
} from '~/entities'

@ObjectType('ProductVariantProblem')
@Entity('product_variant_problem')
export class ProductVariantProblemEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @ManyToOne(() => QuestionTypeProblemEntity)
  @JoinColumn()
  problem: Relation<QuestionTypeProblemEntity>

  @Column()
  @RelationId((problem: ProductVariantProblemEntity) => problem.problem)
  problemId: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @OneToMany(() => ProductVariantProblemPriceEntity, (price) => price.variantProblem, {
    nullable: false,
    cascade: true,
  })
  prices: Relation<ProductVariantProblemPriceEntity>[]

  @ManyToOne(() => ProductVariantEntity, (productVariant) => productVariant.problems, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  variant: Relation<ProductVariantEntity>

  @Column()
  @RelationId((problem: ProductVariantProblemEntity) => problem.variant)
  variantId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductVariantProblem = Omit<ProductVariantProblemEntity, keyof BaseEntity> & {
  problem: IQuestionTypeProblem
  prices: IProductVariantProblemPrice[]
  variant: IProductVariant
}
