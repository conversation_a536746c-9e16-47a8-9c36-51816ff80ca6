import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { ITestedItem, ITestedItemList } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const TestedItemListList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<ITestedItemList>({
    syncWithLocation: true,
    meta: {
      fields: ['internalName', 'items.name__en', 'totalItemsCount', 'createdAt', 'publishedAt'],
      join: [{ field: 'items' }],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/tested-item-lists', id!)))}
      >
        <Table.Column dataIndex="internalName" title="Internal name" className="cursor-pointer" />
        <Table.Column
          dataIndex="lists.id"
          title="Used in lists"
          className="cursor-pointer"
          render={(_, record: ITestedItemList) => record.items.map((item: ITestedItem) => item.name__en).join(', ')}
        />
        <Table.Column dataIndex="totalItemsCount" title="Total items count" className="cursor-pointer" />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column<ITestedItemList>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
