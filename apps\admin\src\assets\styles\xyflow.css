/* .react-flow__node {
  @apply bg-white rounded-lg shadow-md border border-gray-200;
}

.react-flow__node-default {
  @apply min-w-[150px] p-4;
}

.react-flow__node-group {
  @apply bg-transparent shadow-none border-0;
} */

.react-flow__handle {
  @apply !w-2 !h-5 !rounded-none;
}

.react-flow__attribution {
  @apply !hidden;
}

.react-flow__node-warehouse {
  @apply p-2.5 w-[150px] text-xs text-center;
  border-radius: var(--xy-node-border-radius, var(--xy-node-border-radius-default));
  color: var(--xy-node-color, var(--xy-node-color-default));
  border: var(--xy-node-border, var(--xy-node-border-default));
  background-color: var(--xy-node-background-color, var(--xy-node-background-color-default));
  line-height: 1.5714285714285714;
}
/* .react-flow__edge-path {
  @apply stroke-2 stroke-gray-400;
}

.react-flow__controls {
  @apply rounded-lg shadow-md;
}

.react-flow__controls-button {
  @apply border-gray-200 bg-white hover:bg-gray-50;
} */