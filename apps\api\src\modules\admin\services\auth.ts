import { Injectable, UnauthorizedException } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { addMilliseconds } from 'date-fns'
import { google } from 'googleapis'
import { pick } from 'lodash'
import ms from 'ms'

import { envConfig } from '~/configs'
import { AdminUserEntity } from '~/entities'
import { ADMIN_JWT_ACCESS_TOKEN_EXPIRATION, AdminUserProviderType } from '~admin/constants'

import { AdminUserFrontendType, AdminUserJtwPayloadType } from '../interfaces'

@Injectable()
export class AuthService {
  constructor(private readonly jwtService: JwtService) {}

  async signToken(id: string, expiresIn: string) {
    return this.jwtService.sign({ sub: id } as AdminUserJtwPayloadType, { expiresIn })
  }

  async googleLogin(accessToken: string) {
    const oauth2Client = new google.auth.OAuth2(envConfig.GOOGLE_OAUTH_CLIENT_ID, envConfig.GOOGLE_OAUTH_CLIENT_SECRET)
    oauth2Client.setCredentials({ access_token: accessToken })
    const oauth2 = google.oauth2({
      auth: oauth2Client,
      version: 'v2',
    })

    try {
      const { data: profile } = await oauth2.userinfo.get()
      if (!profile || !profile.email || !profile.verified_email) {
        throw new Error()
      }

      const user = await AdminUserEntity.findOne({
        where: { email: profile.email, provider: AdminUserProviderType.GOOGLE },
      })
      if (user && user.enabled) {
        user.lastLoginAt = new Date()
        await user.save()
        return {
          ...pick(user, ['id', 'email', 'name', 'picture', 'roles']),
          token: await this.signToken(user.id, ADMIN_JWT_ACCESS_TOKEN_EXPIRATION),
          expiresIn: addMilliseconds(new Date(), ms(ADMIN_JWT_ACCESS_TOKEN_EXPIRATION)),
        } as AdminUserFrontendType
      } else {
        await AdminUserEntity.create({
          email: profile.email,
          provider: AdminUserProviderType.GOOGLE,
          name: profile.name,
          picture: profile.picture,
        }).save()
        return {}
      }
    } catch {
      throw new UnauthorizedException('Invalid user')
    }
  }

  async me(accessToken: string) {
    let { sub: id } = this.jwtService.decode(accessToken) as { sub: string; roles: string[] }

    // TODO: ugly hack, remove it
    if (id === '<EMAIL>') {
      id = '18373595-3a47-4aea-860e-e08e0ed9d8e4'
    }

    const user = await AdminUserEntity.findOneOrFail({ where: { id } })

    return {
      sub: user.id,
      roles: user.roles,
    }
  }
}
