import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { BlogTagEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudBlogTagService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: BlogTagEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      posts: {},
    },
  },
})
@Controller('admin/blog-tags')
export class CrudBlogTagController implements CrudController<BlogTagEntity> {
  constructor(public service: CrudBlogTagService) {}
}
