import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { ProductModelEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudProductModelService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ProductModelEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      image: {},
      brand: {},
      category: {},
      series: {},
      attributes: {},
      'attributes.options': {},
      variants: {},
      attributeCombinations: {},
      'attributeCombinations.attribute': {},
      'attributeCombinations.attribute.choices': {},
      'attributeCombinations.variant': {},
      skus: {},
      accessoryProducts: {},
      questionType: {},
      metrics: {},
      seoContent: {},
      saleItems: {},
      orderItems: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/product-models')
export class CrudProductModelController implements CrudController<ProductModelEntity> {
  constructor(public service: CrudProductModelService) {}
}
