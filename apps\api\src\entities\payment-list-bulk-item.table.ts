import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IPartner,
  type IPaymentList,
  type ISaleItem,
  PartnerEntity,
  PaymentListEntity,
  SaleItemEntity,
} from '~/entities'

@Entity('payment_list_bulk_item')
export class PaymentListBulkItemEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  dueDate: Date

  @Column()
  beneficiaryName: string

  @Column()
  iban: string

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  amount: number

  @Column({
    type: 'integer',
    unsigned: true,
  })
  itemsCount: number

  @ManyToOne(() => PartnerEntity, (partner) => partner.paymentLists, { nullable: false })
  partner: PartnerEntity

  @Column()
  @RelationId((paymentListBulkItem: PaymentListBulkItemEntity) => paymentListBulkItem.partner)
  partnerId: string

  @ManyToOne(() => PaymentListEntity, (paymentList) => paymentList.bulkItems, {
    onDelete: 'CASCADE',
  })
  paymentList: Relation<PaymentListEntity>

  @Column()
  @RelationId((paymentListBulkItem: PaymentListBulkItemEntity) => paymentListBulkItem.paymentList)
  paymentListId: string

  @OneToMany(() => SaleItemEntity, (saleItem) => saleItem.paymentListBulkItem, {
    onDelete: 'CASCADE',
  })
  saleItems: Relation<SaleItemEntity[]>

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IPaymentListBulkItem = Omit<PaymentListBulkItemEntity, keyof BaseEntity> & {
  partner: IPartner
  paymentList: IPaymentList
  saleItems: ISaleItem[]
}
