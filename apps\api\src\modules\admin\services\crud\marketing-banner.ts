import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { MarketingBannerEntity } from '~/entities'

export class CrudMarketingBannerService extends TypeOrmCrudService<MarketingBannerEntity> {
  constructor(@InjectRepository(MarketingBannerEntity) repo: Repository<MarketingBannerEntity>) {
    super(repo)
  }
}
