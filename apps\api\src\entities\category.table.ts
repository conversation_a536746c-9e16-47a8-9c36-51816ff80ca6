import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  CategoryWarrantyRuleEntity,
  type ICategoryWarrantyRule,
  type IImage,
  ImageEntity,
  type IProductModel,
  type IProductSeries,
  type IProductSkuOriginalAccessory,
  type IProductVariant,
  ProductModelEntity,
  ProductSeriesEntity,
  ProductSkuOriginalAccessoryEntity,
  ProductVariantEntity,
  SeoContentEntity,
} from '~/entities'

@ObjectType('Category')
@Entity('category')
export class CategoryEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column({ unique: true })
  slug__en: string

  @Field()
  @Column({ unique: true })
  slug__nl: string

  @Field()
  @Column({ unique: true })
  slug__de: string

  @Field()
  @Column({ unique: true })
  slug__pl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.categoryIcon, { nullable: false, cascade: true })
  @JoinColumn()
  icon: Relation<ImageEntity>

  @Column()
  @RelationId((category: CategoryEntity) => category.icon)
  iconId: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.categoryImage, { nullable: false, cascade: true })
  @JoinColumn()
  image: Relation<ImageEntity>

  @Column()
  @RelationId((category: CategoryEntity) => category.image)
  imageId: string

  @Field({ description: 'Save how many grams of CO2 emission for each purchase' })
  @Column('int', { unsigned: true })
  savedCo2: number

  @Field({ description: 'Save how many grams of eWaste for each purchase' })
  @Column('int', { unsigned: true })
  savedEwaste: number

  @Field({ description: 'Description text for product with condition A in this category (English)' })
  @Column('text')
  gradingADesc__en: string

  @Field({ description: 'Description text for product with condition A in this category (Dutch)' })
  @Column('text')
  gradingADesc__nl: string

  @Field({ description: 'Description text for product with condition A in this category (German)' })
  @Column('text')
  gradingADesc__de: string

  @Field({ description: 'Description text for product with condition A in this category (Polish)' })
  @Column('text')
  gradingADesc__pl: string

  @Field({ description: 'Description text for product with condition B in this category (English)' })
  @Column('text')
  gradingBDesc__en: string

  @Field({ description: 'Description text for product with condition B in this category (Dutch)' })
  @Column('text')
  gradingBDesc__nl: string

  @Field({ description: 'Description text for product with condition B in this category (German)' })
  @Column('text')
  gradingBDesc__de: string

  @Field({ description: 'Description text for product with condition B in this category (Polish)' })
  @Column('text')
  gradingBDesc__pl: string

  @Field({ description: 'Description text for product with condition C in this category (English)' })
  @Column('text')
  gradingCDesc__en: string

  @Field({ description: 'Description text for product with condition C in this category (Dutch)' })
  @Column('text')
  gradingCDesc__nl: string

  @Field({ description: 'Description text for product with condition C in this category (German)' })
  @Column('text')
  gradingCDesc__de: string

  @Field({ description: 'Description text for product with condition C in this category (Polish)' })
  @Column('text')
  gradingCDesc__pl: string

  @Field({ description: 'Description text for product with condition D in this category (English)' })
  @Column('text')
  gradingDDesc__en: string

  @Field({ description: 'Description text for product with condition D in this category (Dutch)' })
  @Column('text')
  gradingDDesc__nl: string

  @Field({ description: 'Description text for product with condition D in this category (German)' })
  @Column('text')
  gradingDDesc__de: string

  @Field({ description: 'Description text for product with condition D in this category (Polish)' })
  @Column('text')
  gradingDDesc__pl: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @Field(() => [ProductSeriesEntity])
  @OneToMany(() => ProductSeriesEntity, (productSeries) => productSeries.category, { nullable: false })
  productSeries: Relation<ProductSeriesEntity>[]

  @OneToMany(() => ProductModelEntity, (productModel) => productModel.category, { nullable: false })
  productModels: Relation<ProductModelEntity>[]

  @OneToMany(() => ProductVariantEntity, (variant) => variant.category, { nullable: false })
  productVariants: Relation<ProductVariantEntity>[]

  @OneToMany(() => CategoryWarrantyRuleEntity, (categoryWarranty) => categoryWarranty.category, { nullable: false })
  warrantyRules: Relation<CategoryWarrantyRuleEntity>[]

  @Field(() => SeoContentEntity, { nullable: true })
  @OneToOne(() => SeoContentEntity, { nullable: true, cascade: true })
  @JoinColumn()
  seoContent?: Relation<SeoContentEntity>

  @Column({ nullable: true })
  @RelationId((collection: CategoryEntity) => collection.seoContent)
  seoContentId?: string

  @ManyToMany(() => ProductSkuOriginalAccessoryEntity, {
    nullable: false,
    cascade: true,
  })
  @JoinTable({ name: 'category_product_sku_default_original_accessories' })
  defaultOriginalAccessories: Relation<ProductSkuOriginalAccessoryEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type ICategory = Omit<CategoryEntity, keyof BaseEntity> & {
  productSeries: IProductSeries[]
  productModels: IProductModel[]
  productVariants: IProductVariant[]
  image: IImage
  warrantyRules: ICategoryWarrantyRule[]
  defaultOriginalAccessories: IProductSkuOriginalAccessory[]
}
