import { Edit, EditButton, useForm, useSelect, useTable } from '@refinedev/antd'
import {
  type HttpError,
  type IResourceComponentsProps,
  useApiUrl,
  useCustom,
  useCustomMutation,
  useInvalidate,
  useList,
  useNavigation,
  useNotification,
} from '@refinedev/core'
import {
  ProductModelSellerQuestionType,
  SaleItemOfferStatus,
  SaleItemPlanType,
  SaleItemStatus,
  SalePaymentType,
  SaleShippingLabelType,
  SaleStatus,
} from '@valyuu/api/constants'
import type { ILocaleCountry, IPartner, ISale, ISaleItem } from '@valyuu/api/entities'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Button, Descriptions, Form, Modal, Popconfirm, Space, Table, Tabs, Tag, Typography } from 'antd'
import { Input, RadioGroup, Select, TextArea } from 'antx'
import clsx from 'clsx'
import { capitalize } from 'lodash'
import { type FC, useState } from 'react'
import { AiOutlineMail, AiOutlinePlus } from 'react-icons/ai'

import {
  BlockChangeHistory,
  BlockEmailHistory,
  ButtonCopy,
  LabelWithDetails,
  LinkShipmentDocument,
  LinkTrello,
} from '~/components'
import { type HistoryRecord } from '~/components'
import {
  formatMoney,
  formatSelectOptionsFromEnum,
  getAddressText,
  getSendCloudParcelUrl,
  handleTableRowClick,
} from '~/utils'

enum PageTab {
  SALE = 'sale',
  SALE_ITEMS = 'sale-items',
  SHIPMENT = 'shipment',
  EMAIL_HISTORY = 'email-history',
  CHANGE_HISTORY = 'change-history',
}

const PlanTypeMap = {
  [SaleItemPlanType.C2C]: 'Best price (C2C)',
  [SaleItemPlanType.C2B]: 'Fast pay (C2B)',
}

const statusColorMap = {
  [SaleItemStatus.SUBMITTED]: 'default',
  [SaleItemStatus.RECEIVED]: 'processing',
  [SaleItemStatus.PENDING_OFFER]: 'orange',
  [SaleItemStatus.PENDING_PAYMENT]: 'cyan',
  [SaleItemStatus.PENDING_RETURN]: 'magenta',
  [SaleItemStatus.PENDING_RECYCLE]: 'volcano',
  [SaleItemStatus.RETURNED]: 'geekblue',
  [SaleItemStatus.PAYMENT_IN_PROCESS]: 'gold',
  [SaleItemStatus.PAYMENT_FAILED]: 'error',
  [SaleItemStatus.NOT_RECEIVED]: 'red',
  [SaleItemStatus.PAID]: 'success',
  [SaleItemStatus.ON_HOLD]: 'purple',
  [SaleItemStatus.CUSTOMER_ABANDONED]: 'grey',
}

const offerStatusColorMap = {
  [SaleItemOfferStatus.PENDING]: 'blue',
  [SaleItemOfferStatus.ACCEPTED]: 'green',
  [SaleItemOfferStatus.DECLINED_RECYCLE]: 'volcano',
  [SaleItemOfferStatus.DECLINED_RETURN]: 'orange',
  [SaleItemOfferStatus.EXPIRED]: 'default',
}

export const SaleEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, query, formLoading } = useForm<ISale, HttpError, ISale>({
    meta: {
      join: [
        {
          field: 'user',
          select: ['email'],
        },
        {
          field: 'address',
        },
        {
          field: 'bankAccount',
          select: ['id', 'accountNumber'],
        },
        {
          field: 'partner',
          select: ['id', 'name', 'manageCustomerEmails'],
        },
        {
          field: 'shipment',
        },
        {
          field: 'shipment.shippingMethod',
          select: ['shippingProductName'],
        },
      ],
    },
  })

  const { selectProps: partnerSelectProps } = useSelect<IPartner>({
    resource: 'admin/partners',
    optionLabel: 'name',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { data: { data: countries } = {} } = useList<ILocaleCountry>({
    resource: 'admin/countries',
    meta: { fields: ['id', 'name__en'] },
    pagination: { mode: 'off' },
  })

  const record = query?.data?.data
  const { address } = record ?? {}

  const { tableProps: saleItemsTableProps } = useTable<ISaleItem>({
    syncWithLocation: false,
    resource: 'admin/sale-items',
    filters: { permanent: [{ field: 'saleId', operator: 'eq', value: id }] },
    meta: {
      join: [
        {
          field: 'productVariant',
          select: ['name__en'],
        },
        {
          field: 'productSku',
          select: ['name__en'],
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()
  const apiUrl = useApiUrl()

  const { data: { data: histories } = {}, isLoading: isLoadingHistories } = useCustom<HistoryRecord[]>({
    url: `${apiUrl}/admin/sales/${id}/histories`,
    method: 'get',
  })

  const { open } = useNotification()

  const goToSaleItemPage = (id: string) => {
    push(editUrl('admin/sale-items', id))
  }

  const resendSaleEmail = async () => {
    try {
      await axios.post(`${apiUrl}/admin/emails/resend/seller-sale`, { id })
      open?.({
        key: 'resend-email-seller-sale',
        type: 'success',
        message: 'Email sent',
      })
    } catch {
      open?.({
        key: 'resend-email-seller-sale',
        type: 'error',
        message: 'Failed to send email',
      })
    }
  }

  const [activeTab, setActiveTab] = useState<PageTab>(PageTab.SALE)

  const StatusTransitionMap: Record<SaleStatus, SaleStatus[]> = {
    [SaleStatus.SUBMITTED]: [SaleStatus.RECEIVED, SaleStatus.CANCELLED, SaleStatus.IN_TRANSIT],
    [SaleStatus.IN_TRANSIT]: [SaleStatus.RECEIVED],
    [SaleStatus.RECEIVED]: [SaleStatus.CANCELLED],
    [SaleStatus.CANCELLED]: [],
    [SaleStatus.COMPLETED]: [],
  }

  const partnerManageCustomerEmails = record?.partner && !record.partner.manageCustomerEmails

  const { mutate: createShipment, isLoading: isCreatingShipment } = useCustomMutation<{ override?: boolean }>()
  const invalidate = useInvalidate()

  const handleCreateShipment = (override: boolean) => {
    createShipment(
      {
        method: 'post',
        url: `${apiUrl}/admin/sales/${id}/create-shipment`,
        values: { override },
        successNotification: {
          message: `Shipment ${override ? 'recreate' : 'created'} successfully`,
          type: 'success',
        },
        errorNotification: {
          message: `Failed to ${override ? 'recreate' : 'create'} shipment`,
          type: 'error',
        },
      },
      {
        onSuccess: () => {
          invalidate({
            resource: 'admin/sales',
            invalidates: ['detail'],
            id,
          })
          query?.refetch()
        },
      }
    )
  }

  const handleRecreateShipmentClick = () => {
    Modal.confirm({
      title: 'Recreate Shipment',
      content: (
        <div className="space-y-2">
          <p>This action will:</p>
          <ul className="list-disc pl-4">
            <li>Invalidate the current shipping label and cancel the existing parcel</li>
            <li>Create a new shipping label</li>
            {record?.partner?.manageCustomerEmails ? (
              <li>Send a new email to the customer with the new shipping label</li>
            ) : null}
          </ul>
          <p className="font-bold text-red-500">Please be extra careful with this action.</p>
        </div>
      ),
      okText: 'Yes, Recreate',
      cancelText: 'Cancel',
      okButtonProps: {
        danger: true,
      },
      onOk: () => handleCreateShipment(true),
    })
  }

  const handleFormFinish = async (values: ISale) => {
    const originalStatus = record?.status
    const newStatus = values.status

    if (originalStatus !== SaleStatus.CANCELLED && newStatus === SaleStatus.CANCELLED) {
      const confirmed = await new Promise((resolve) => {
        Modal.confirm({
          title: 'Cancel Sale',
          content: 'Are you sure you want to cancel this sale? This action cannot be undone.',
          okText: 'Yes, Cancel Sale',
          cancelText: 'No',
          okButtonProps: { danger: true },
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        })
      })

      if (!confirmed) {
        return
      }
    }

    formProps.onFinish?.(values)
  }

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={{
        ...saveButtonProps,
        disabled: activeTab !== PageTab.SALE,
      }}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      headerButtons={({ defaultButtons }) => (
        <Space>
          {defaultButtons}
          {(record && !record.partner) || record?.partner?.manageCustomerEmails ? (
            <Popconfirm title="Resend customer sale email?" onConfirm={resendSaleEmail}>
              <Button
                disabled={!id || formLoading}
                icon={
                  <span className="anticon">
                    <AiOutlineMail />
                  </span>
                }
              >
                Resend sale email
              </Button>
            </Popconfirm>
          ) : null}
        </Space>
      )}
    >
      <Tabs
        activeKey={activeTab}
        className="select-none"
        onChange={(key) => setActiveTab(key as PageTab)}
        items={[
          {
            key: PageTab.SALE,
            label: 'Sale',
            children: (
              <Form
                {...formProps}
                onFinish={handleFormFinish}
                onFinishFailed={(errorInfo) => {
                  setActiveTab(PageTab.SALE)
                  formProps.onFinishFailed?.(errorInfo)
                }}
                layout="vertical"
              >
                <Input label="Sale number" name="saleNumber" rules={['required']} disabled />
                <Select
                  label="Status"
                  name="status"
                  rules={['required']}
                  options={formatSelectOptionsFromEnum(SaleStatus)
                    .map((option) => ({
                      ...option,
                      disabled: record
                        ? !StatusTransitionMap[record.status as keyof typeof StatusTransitionMap].includes(
                            option.value as SaleStatus
                          ) && option.value !== record.status
                        : false,
                    }))
                    .sort((a, b) => {
                      if (a.disabled === b.disabled) return 0
                      return a.disabled ? 1 : -1
                    })}
                />
                <Select
                  label="Shipping label"
                  name="shippingLabel"
                  rules={['required']}
                  options={formatSelectOptionsFromEnum(SaleShippingLabelType)}
                  disabled={partnerManageCustomerEmails}
                />
                <RadioGroup
                  label="Is label sent"
                  name="isLabelSent"
                  className={clsx({ hidden: partnerManageCustomerEmails })}
                  options={[
                    { label: 'Yes', value: true },
                    { label: 'No', value: false },
                  ]}
                  rules={['required']}
                />
                <Select
                  label="Payment type"
                  name="paymentType"
                  rules={['required']}
                  options={formatSelectOptionsFromEnum(SalePaymentType)}
                  disabled
                />
                <RadioGroup
                  label="Is returning customer"
                  name="isReturningCustomer"
                  options={[
                    { label: 'Yes', value: true },
                    { label: 'No', value: false },
                  ]}
                  rules={['required']}
                  disabled
                />
                {record?.bankAccount ? (
                  <Select
                    label={
                      <LabelWithDetails
                        label="Bank account"
                        link={record?.bankAccount ? editUrl('admin/bank-accounts', record.bankAccount.id) : undefined}
                      />
                    }
                    name="bankAccountId"
                    options={
                      record?.bankAccount
                        ? [{ value: record.bankAccount.id, label: record.bankAccount.accountNumber }]
                        : []
                    }
                    disabled
                  />
                ) : null}
                {record?.partner ? (
                  <Select
                    label="Partner"
                    name={['partner', 'id']}
                    rules={['required']}
                    {...(partnerSelectProps as any)}
                    disabled
                  />
                ) : null}
                <Input
                  label={
                    <LabelWithDetails
                      label="User"
                      link={record?.userId ? editUrl('admin/users', record.userId) : undefined}
                    />
                  }
                  help={
                    partnerManageCustomerEmails ? (
                      <span className="text-red-500">
                        Customer service is managed by our partner, please don't send any emails to this user!
                      </span>
                    ) : undefined
                  }
                  value={record?.user?.email}
                  disabled
                />
                <TextArea label="Note" name="note" className="col-span-2" />
              </Form>
            ),
          },
          {
            key: PageTab.SALE_ITEMS,
            label: 'Sale Items',
            disabled: !saleItemsTableProps?.dataSource?.length,
            children: (
              <Table
                {...saleItemsTableProps}
                rowKey="id"
                onRow={({ id }) => handleTableRowClick(() => goToSaleItemPage(id))}
              >
                <Table.Column
                  dataIndex="id"
                  title="ID"
                  className="max-w-10 cursor-pointer"
                  render={(value) => <span title={value}>{value}</span>}
                  width="5rem"
                  ellipsis={true}
                />
                <Table.Column
                  dataIndex="productVariant"
                  title="Product variant"
                  className="cursor-pointer"
                  render={(value) => value.name__en}
                />
                <Table.Column dataIndex="stockId" title="Stock ID" className="cursor-pointer" width="6rem" />
                <Table.Column
                  dataIndex="answers"
                  title="Functional"
                  className="cursor-pointer"
                  width="8rem"
                  render={(value) => {
                    const isWorking = value.type === ProductModelSellerQuestionType.CONDITION
                    return <Tag color={isWorking ? 'success' : 'error'}>{isWorking ? 'Yes' : 'No'}</Tag>
                  }}
                />
                <Table.Column
                  dataIndex="type"
                  title="Type"
                  className="cursor-pointer"
                  render={(value) => PlanTypeMap[value as keyof typeof PlanTypeMap]}
                  width="8rem"
                />
                <Table.Column
                  dataIndex="price"
                  title="Price to pay"
                  className="cursor-pointer"
                  width="5rem"
                  render={(value, row: ISaleItem) => formatMoney(value, row.currencyId)}
                />
                <Table.Column
                  dataIndex="status"
                  title="Status"
                  className="cursor-pointer"
                  width="4rem"
                  render={(value) => (
                    <Tag color={statusColorMap[value as keyof typeof statusColorMap]}>
                      {capitalize(value.replace(/_/g, ' '))}
                    </Tag>
                  )}
                />
                <Table.Column
                  dataIndex="offerStatus"
                  title="Offer Status"
                  className="cursor-pointer"
                  width="4rem"
                  render={(value) => (
                    <Tag color={offerStatusColorMap[value as keyof typeof offerStatusColorMap]}>
                      {capitalize(value.replace(/_/g, ' '))}
                    </Tag>
                  )}
                />
                <Table.Column
                  dataIndex="isTested"
                  title="Tested"
                  className="cursor-pointer"
                  width="4rem"
                  render={(value) => <Tag color={value ? 'success' : 'warning'}>{value ? 'Yes' : 'No'}</Tag>}
                />
                <Table.Column
                  dataIndex="trelloId"
                  title="Trello ID"
                  className="cursor-pointer"
                  width="8rem"
                  render={(value) => <LinkTrello id={value} className="max-w-24" />}
                />
                <Table.Column dataIndex="note" title="Note" className="cursor-pointer" width="20rem" />
                <Table.Column<ISale>
                  title="Actions"
                  dataIndex="actions"
                  render={(_, record) => (
                    <EditButton
                      size="small"
                      resource="admin/sale-items"
                      recordItemId={record.id}
                      onClick={() => goToSaleItemPage(record.id)}
                    />
                  )}
                  width="6rem"
                  fixed="right"
                />
              </Table>
            ),
          },
          {
            key: PageTab.SHIPMENT,
            label: 'Shipment',
            children: (
              <>
                {address ? (
                  <Descriptions
                    title="Shipping address"
                    extra={
                      <Space>
                        <ButtonCopy text={getAddressText(address, countries)} label="Copy address" />
                        <EditButton resource="admin/addresses" recordItemId={address.id} />
                      </Space>
                    }
                    bordered
                  >
                    <Descriptions.Item label="First name">{address.firstName}</Descriptions.Item>
                    <Descriptions.Item label="Last name">{address.lastName}</Descriptions.Item>
                    <Descriptions.Item label="Phone">
                      {address.phoneAreaCode + ' ' + address.phoneNumber}
                    </Descriptions.Item>
                    <Descriptions.Item label="Street">{address.street}</Descriptions.Item>
                    <Descriptions.Item label="House number">{address.houseNumber}</Descriptions.Item>
                    <Descriptions.Item label="Addition">{address.addition}</Descriptions.Item>
                    <Descriptions.Item label="Postal code">{address.postalCode}</Descriptions.Item>
                    <Descriptions.Item label="City">{address.city}</Descriptions.Item>
                    <Descriptions.Item label="Country">
                      {countries ? countries.find(({ id }) => id === address.countryId)?.name__en : address.countryId}
                    </Descriptions.Item>
                  </Descriptions>
                ) : null}
                <Descriptions
                  title="Shipment information"
                  className="mt-4"
                  extra={
                    record?.shipment ? (
                      <Space>
                        <Button danger loading={isCreatingShipment} onClick={handleRecreateShipmentClick}>
                          <span className="anticon">
                            <AiOutlinePlus />
                          </span>
                          Recreate shipment
                        </Button>
                        <EditButton resource="admin/shipments" recordItemId={record.shipment.id} />
                      </Space>
                    ) : (
                      <Button type="primary" onClick={() => handleCreateShipment(false)} loading={isCreatingShipment}>
                        <span className="anticon">
                          <AiOutlinePlus />
                        </span>
                        Create shipment
                      </Button>
                    )
                  }
                  bordered={record?.shipment}
                  labelStyle={{ color: record?.shipment ? undefined : '#ff4d4f' }}
                >
                  {!record?.shipment ? (
                    <Descriptions.Item label="Error">
                      <Typography.Text type="danger">
                        No shipment record found. This could be due to an invalid shipping address or a system error.
                        Please try to create a new shipment. If the problem persists, please contact the development
                        team.
                      </Typography.Text>
                    </Descriptions.Item>
                  ) : (
                    <>
                      <Descriptions.Item label="Tracking number">
                        <Typography.Text copyable={{ text: record?.shipment?.trackingNumber }}>
                          {record?.shipment?.trackingUrl ? (
                            <a title="View tracking page" href={record.shipment.trackingUrl} target="_blank">
                              {record?.shipment.trackingNumber}
                            </a>
                          ) : (
                            (record?.shipment?.trackingNumber ?? '')
                          )}
                        </Typography.Text>
                      </Descriptions.Item>
                      <Descriptions.Item label="Parcel ID">
                        <Typography.Text copyable={{ text: record?.shipment?.parcelId }}>
                          <a
                            title="View parcel details on SendCloud"
                            href={getSendCloudParcelUrl(record.shipment.parcelId)}
                            target="_blank"
                          >
                            {record?.shipment?.parcelId ?? ''}
                          </a>
                        </Typography.Text>
                      </Descriptions.Item>
                      <Descriptions.Item label="Status">{record?.shipment?.statusMessage ?? ''}</Descriptions.Item>
                      <Descriptions.Item label="Is return">
                        {record?.shipment?.isReturn ? 'Yes' : 'No'}
                      </Descriptions.Item>
                      <Descriptions.Item label="Is paperless">
                        {record?.shipment?.isPaperless ? 'Yes' : 'No'}
                      </Descriptions.Item>
                      <Descriptions.Item label="Shipping label / Paperless code">
                        {record?.shipment ? (
                          <LinkShipmentDocument
                            shipmentId={record.shipment.id}
                            trackingNumber={record.shipment.trackingNumber}
                          />
                        ) : (
                          ''
                        )}
                      </Descriptions.Item>
                      <Descriptions.Item label="Shipping method">
                        {record?.shipment?.shippingMethod?.shippingProductName}
                      </Descriptions.Item>
                      <Descriptions.Item label="Note">{record?.shipment?.note ?? ''}</Descriptions.Item>
                    </>
                  )}
                </Descriptions>
              </>
            ),
          },
          {
            key: PageTab.EMAIL_HISTORY,
            label: 'Email History',
            children: <BlockEmailHistory userId={record?.userId} relatedIds={id ? [id as string] : undefined} />,
          },
          ...(histories?.length
            ? [
                {
                  key: PageTab.CHANGE_HISTORY,
                  label: 'Change History',
                  children: <BlockChangeHistory histories={histories} currentValue={record} />,
                },
              ]
            : []),
        ]}
      />
    </Edit>
  )
}
