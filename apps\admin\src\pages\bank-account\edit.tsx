import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IBankAccount } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, Select } from 'antx'
import { type FC } from 'react'

import { LabelWithDetails } from '~/components'

export const BankAccountEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, query, formLoading } = useForm<IBankAccount, HttpError, IBankAccount>({
    meta: {
      join: [
        {
          field: 'user',
          select: ['id', 'email'],
        },
      ],
    },
  })

  const record = query?.data?.data

  const { editUrl } = useNavigation()

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Select
          label={
            <LabelWithDetails link={record?.userId ? editUrl('admin/users', record.userId) : undefined} label="User" />
          }
          name="userId"
          rules={['required']}
          options={record?.user ? [{ value: record.userId, label: record.user.email }] : []}
          disabled
        />
        <Input label="Beneficiary name" name="holderName" rules={['required']} />
        <Input label="IBAN number" name="accountNumber" rules={['required']} />
        <Input
          label={
            <LabelWithDetails
              label="Stripe bank account"
              link={
                record?.stripeBankAccount
                  ? `https://dashboard.stripe.com/search?query=${record.stripeBankAccount}`
                  : undefined
              }
              target="_blank"
            />
          }
          name="stripeBankAccount"
          disabled
        />
      </Form>
    </Edit>
  )
}
