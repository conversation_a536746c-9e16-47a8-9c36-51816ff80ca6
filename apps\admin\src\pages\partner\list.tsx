import { DateField, EditButton, FilterDropdown, getDefaultSortOrder, List, useSelect, useTable } from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IChannel, IPartner } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { Select } from 'antx'
import type { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const PartnerList: FC<IResourceComponentsProps> = () => {
  const { tableProps, filters, sorters } = useTable<IPartner>({
    syncWithLocation: true,
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
    filters: {
      defaultBehavior: 'replace',
    },
  })

  const { selectProps: channelSelectProps } = useSelect<IChannel>({
    resource: 'admin/channels',
    optionLabel: 'name',
    meta: {
      fields: ['id', 'name'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/partners', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column dataIndex="name" title="Name" className="cursor-pointer" width="8rem" />
        <Table.Column dataIndex="slug" title="Slug" className="cursor-pointer" width="8rem" />
        <Table.Column
          dataIndex="channelId"
          title="Channel"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('channelId', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select placeholder="Channel ID" style={{ minWidth: 200 }} {...(channelSelectProps as any)} />
            </FilterDropdown>
          )}
          width="12rem"
        />
        <Table.Column
          dataIndex="manageCustomerEmails"
          title="Manage Customer Emails"
          className="cursor-pointer"
          render={(value) => (value ? 'Yes' : 'No')}
          width="12rem"
        />
        <Table.Column
          dataIndex="apiEndpointUrl"
          title="API Endpoint URL"
          className="cursor-pointer"
          ellipsis={true}
          width="12rem"
        />
        <Table.Column
          dataIndex="standaloneSiteUrl"
          title="Standalone Site URL"
          className="cursor-pointer"
          ellipsis={true}
          width="12rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IPartner>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
