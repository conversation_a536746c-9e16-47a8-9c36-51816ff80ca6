import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { compare } from 'bcrypt'
import { ILike } from 'typeorm'

import { AuthTokenOutput, UserCreateInput } from '~/dtos'
import { UserEntity } from '~/entities'

@Injectable()
export class UserService {
  constructor(private readonly jwtService: JwtService) {}
  // Auth
  async login(email: string, password: string): Promise<AuthTokenOutput | undefined> {
    const user = await UserEntity.findOneBy({ email: ILike(email.trim()) })
    if (user?.password && (await compare(password, user.password))) {
      return {
        accessToken: this.jwtService.sign({ id: user.id }),
      }
    } else {
      throw new UnauthorizedException('User does not exist or password mismatches')
    }
  }

  async create({ email, password }: UserCreateInput) {
    if (await UserEntity.findOneBy({ email: <PERSON>ike(email.trim()) })) {
      throw new BadRequestException('Username already exists')
    }
    return UserEntity.create({ email, password })
  }
}
