import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { type IImage, ImageEntity, type IQuestionType, QuestionTypeEntity } from '~/entities'

@ObjectType('QuestionTypeImageText')
@Entity('question_type_image_text')
export class QuestionTypeImageTextEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, { nullable: false, cascade: true })
  @JoinColumn()
  image: Relation<ImageEntity>

  @Column()
  @RelationId((imageText: QuestionTypeImageTextEntity) => imageText.image)
  imageId: string

  @ManyToOne(() => QuestionTypeEntity, (questionType) => questionType.problemImageTexts, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  questionType: Relation<QuestionTypeEntity>

  @Column()
  @RelationId((imageText: QuestionTypeImageTextEntity) => imageText.questionType)
  questionTypeId: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IQuestionTypeProblemImageText = Omit<QuestionTypeImageTextEntity, keyof BaseEntity> & {
  image: IImage
  questionType: IQuestionType
}
