import { BadRequestException } from '@nestjs/common'
import { isNil, isNumber } from 'lodash'

import { SaleItemPlanType } from '~/constants'
import {
  ProductSkuPriceEntity,
  ProductVariantBasePriceEntity,
  ProductVariantConditionCombinationEntity,
  ProductVariantProblemEntity,
} from '~/entities'
import { UpdateSkuPriceData, UpdateVariantPriceData } from '~admin/controllers'

export class PricingService {
  async updateVariantPrice(data: UpdateVariantPriceData) {
    if (!data?.variantId) {
      throw new BadRequestException('Variant ID is required')
    }
    if (!data?.channelId) {
      throw new BadRequestException('Channel ID is required')
    }
    if (data.conditions?.length) {
      const combinations = await ProductVariantConditionCombinationEntity.find({
        where: { variantId: data.variantId, prices: { channelId: data.channelId } },
        relations: {
          choices: true,
          prices: true,
        },
      })
      for (const condition of data.conditions) {
        if (!condition.choices) {
          throw new BadRequestException('Choices is required')
        }
        if (!condition.market) {
          throw new BadRequestException('Market is required')
        }
        if (isNil(condition.price)) {
          throw new BadRequestException('Price is required')
        }
        const choices = Object.entries(condition.choices).map(([conditionId, optionId]) => ({ conditionId, optionId }))
        const combination = combinations.find(
          (combination) =>
            combination.choices.length === choices.length &&
            combination.choices.every((choice) =>
              choices.some((c) => c.conditionId === choice.conditionId && c.optionId === choice.optionId)
            )
        )
        if (combination?.prices?.length) {
          const priceRecord = combination.prices[0]
          if (condition.market === SaleItemPlanType.C2B) {
            priceRecord.c2bPrice = condition.price
          } else if (condition.market === SaleItemPlanType.C2C) {
            priceRecord.c2cPrice = condition.price
          }
          await priceRecord.save()
        }
      }
    }
    if (isNumber(data.basePrice)) {
      const basePrice = await ProductVariantBasePriceEntity.findOne({
        where: { variantId: data.variantId, channelId: data.channelId },
      })
      if (basePrice) {
        basePrice.basePrice = data.basePrice
        await basePrice.save()
      }
    }
    if (data.problems?.length) {
      for (const problem of data.problems) {
        if (!problem.problemId) {
          throw new BadRequestException('Problem ID is required')
        }
        if (isNil(problem.priceReduction)) {
          throw new BadRequestException('Problem price is required')
        }
        const problemRecord = await ProductVariantProblemEntity.findOne({
          where: { problemId: problem.problemId, variantId: data.variantId, prices: { channelId: data.channelId } },
          relations: { prices: true },
        })
        if (problemRecord?.prices?.length) {
          const priceRecord = problemRecord.prices[0]
          priceRecord.priceReduction = problem.priceReduction
          await priceRecord.save()
        }
      }
    }
    return { success: true }
  }

  async updateSkuPrice(data: UpdateSkuPriceData) {
    const priceRecord = await ProductSkuPriceEntity.findOne({ where: { skuId: data.skuId, channelId: data.channelId } })
    if (priceRecord) {
      if (!isNil(data.price)) {
        priceRecord.price = data.price
      }
      if (!isNil(data.originalPrice)) {
        priceRecord.originalPrice = data.originalPrice
      }
      if (!isNil(data.brandNewPrice)) {
        priceRecord.brandNewPrice = data.brandNewPrice
      }
      if (!isNil(data.margin)) {
        priceRecord.margin = data.margin
      }
      if (!isNil(data.isDeal)) {
        priceRecord.isDeal = data.isDeal
      }
      await priceRecord.save()
      return { success: true }
    }
    return { success: false }
  }
}
