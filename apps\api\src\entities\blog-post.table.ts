import { Field, ID, Int, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { BlogAuthorEntity, BlogTagEntity, type IBlogAuthor, type IBlogTag, ImageEntity } from '~/entities'

@ObjectType('BlogPost')
@Entity('blog_post')
export class BlogPostEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  title__en: string

  @Field()
  @Column()
  title__nl: string

  @Field()
  @Column()
  title__de: string

  @Field()
  @Column()
  title__pl: string

  @Field()
  @Column({ unique: true })
  slug__en: string

  @Field()
  @Column({ unique: true })
  slug__nl: string

  @Field()
  @Column({ unique: true })
  slug__de: string

  @Field()
  @Column({ unique: true })
  slug__pl: string

  @Field()
  @Column({ type: 'text' })
  content__en: string

  @Field()
  @Column({ type: 'text' })
  content__nl: string

  @Field()
  @Column({ type: 'text' })
  content__de: string

  @Field()
  @Column({ type: 'text' })
  content__pl: string

  @Field()
  @Column({ type: 'text' })
  summary__en: string

  @Field()
  @Column({ type: 'text' })
  summary__nl: string

  @Field()
  @Column({ type: 'text' })
  summary__de: string

  @Field()
  @Column({ type: 'text' })
  summary__pl: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @Field(() => Int, { description: 'Estimated time to read in minutes' })
  @Column({ type: 'int2', unsigned: true })
  minuteRead: number

  @Field(() => ImageEntity, { description: 'Hero image for blog post, also used as thumbnail' })
  @OneToOne(() => ImageEntity, (image) => image.blogPostHeroImage, { cascade: true, nullable: false })
  @JoinColumn()
  heroImage: Relation<ImageEntity>

  @Column()
  @RelationId((blogPost: BlogPostEntity) => blogPost.heroImage)
  heroImageId: string

  @Field(() => BlogAuthorEntity)
  @ManyToOne(() => BlogAuthorEntity, { cascade: true, nullable: false })
  author: Relation<BlogAuthorEntity>

  @Column()
  @RelationId((blogPost: BlogPostEntity) => blogPost.author)
  authorId: string

  @Field(() => BlogTagEntity)
  @ManyToOne(() => BlogTagEntity, { cascade: true, nullable: false })
  tag: Relation<BlogTagEntity>

  @Column()
  @RelationId((blogPost: BlogPostEntity) => blogPost.tag)
  tagId: string

  @Field()
  @CreateDateColumn()
  @Index()
  createdAt: Date

  @Field()
  @UpdateDateColumn()
  updatedAt: Date

  @Field(() => Date)
  @Column({ nullable: true, default: null })
  @Index()
  publishedAt: Date | null
}

export type IBlogPost = Omit<BlogPostEntity, keyof BaseEntity> & {
  author: IBlogAuthor
  tag: IBlogTag
}
