import { Field, ID, ObjectType } from '@nestjs/graphql'
import { IsIn } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToMany,
  PrimaryColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { type ILocaleCountry, LocaleCountryEntity } from '~/entities'

@ObjectType('LocaleLanguage')
@Entity('locale_language')
export class LocaleLanguageEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryColumn()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  id: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field(() => [LocaleCountryEntity])
  @ManyToMany(() => LocaleCountryEntity, (country) => country.languages, { nullable: false })
  countries: Relation<LocaleCountryEntity>[]

  @Field()
  @CreateDateColumn()
  @Index()
  createdAt: Date

  @Field()
  @UpdateDateColumn()
  updatedAt: Date
}

export type ILocaleLanguage = Omit<LocaleLanguageEntity, keyof BaseEntity> & {
  countries: ILocaleCountry[]
}
