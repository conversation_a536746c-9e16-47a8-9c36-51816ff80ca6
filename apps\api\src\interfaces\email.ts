import { MailDataRequired } from '@sendgrid/mail'

import type { EmailType, LOCALE_ENABLED_LANGUAGES, ProductModelSellerQuestionType } from '~/constants'

export type IEmailCommonData = {
  year: number
  trustpilot: {
    stars: ('full' | 'half' | 'empty')[]
    score: number
    reviews: number
  }
  imagePrefix: string
}

export type IEmailBuyerNewOrderData = {
  orderNumber: string
  shippingFirstName: string
  shippingLastName: string
  createdAt: string
  skuItems: Array<{
    thumbnail: string
    name: string
    grading: string
    attributes: string
    accessories: string[]
    price: string
    warrantyName: string
    warrantyPrice: string
  }>
  accessoryItems: Array<{
    thumbnail: string
    name: string
    description: string
    quantity: number
    price: string
  }>
  total: string
  toPickupPoint: boolean
  shippingAddress1: string
  shippingAddress2: string
  shippingCountry: string
}

export type IEmailBuyerNewOrderInternalData = IEmailBuyerNewOrderData & {
  trackingNumber: string
  parcelId: string
  isReturningCustomer: boolean
}

export type IEmailBuyerPackageReceivedData = {
  firstName: string
  orderNumber: string
}

export type IEmailSellerNewSaleEmailData = {
  items: {
    name: string
    attributes: string[]
    conditionType: ProductModelSellerQuestionType
    conditions: Array<{ question: string; answer: string }>
    problems: string[]
    thumbnail: string
    type: string
    price: string
    isFunctional: boolean
  }[]
  saleNumber: string
  createdAt: string
  isDonation: boolean
  shippingFirstName: string
  isNL: boolean
  isBE: boolean
  isDE: boolean
  paymentPeriodC2b?: string
  paymentPeriodC2c?: string
  paymentPeriodDonation?: string
  iban?: string
  charityName?: string
}

export type IEmailSellerPackageReceivedEmailData = {
  firstName: string
  paymentPeriodC2b?: string
  paymentPeriodC2c?: string
  paymentPeriodDonation?: string
  iban?: string
  charityName?: string
}

export type IEmailSellerReminderData = {
  shippingFirstName: string
  saleNumber: string
  expirationDate: string
  estimatedPrice: string
}

export type IEmailRebrandingData = {
  firstName: string
}

export type IEmailSellerOfferItem = {
  name: string
  attributes: string[]
  answers: string[]
  thumbnail: string
  type: string
  price: string
  isFunctional: boolean
  notes?: string
}

export type IEmailSellerOfferItemUpdated = {
  name: string
  attributes: string[]
  answers: string[]
  thumbnail: string
  type: string
  price: string
  initialPrice: string
  isFunctional: boolean
  notes?: string
}

export type IEmailSellerNewOfferHasValueData = {
  acceptUrl: string
  declineUrl: string
  item: IEmailSellerOfferItem
  saleNumber: string
  expirationDate: string
  inspectionDate: string
  shippingFirstName: string
  iban?: string
  charityName?: string
  paymentPeriod: string
}

export type IEmailSellerNewOfferNoValueData = {
  recycleUrl: string
  returnUrl: string
  item: IEmailSellerOfferItem
  saleNumber: string
  inspectionDate: string
  shippingFirstName: string
}

export type IEmailSellerOfferConfirmationAcceptedData = {
  item: Omit<IEmailSellerOfferItem, 'answers' | 'isFunctional' | 'notes'> // These fields aren't present in confirmation
  saleNumber: string
  confirmationDate: string
  shippingFirstName: string
  iban?: string
  charityName?: string
  paymentPeriod: string
}

export type IEmailSellerOfferConfirmationDeclinedRecycleData = {
  item: Omit<IEmailSellerOfferItem, 'answers' | 'isFunctional' | 'notes'> // These fields aren't present in confirmation
  saleNumber: string
  confirmationDate: string
  shippingFirstName: string
}

export type IEmailSellerOfferConfirmationDeclinedReturnData = {
  item: Omit<IEmailSellerOfferItem, 'answers' | 'isFunctional' | 'notes'> // These fields aren't present in confirmation
  saleNumber: string
  confirmationDate: string
  shippingFirstName: string
  trackingUrl: string
}

export type IEmailSellerOfferUpdatedHasValueData = {
  acceptUrl: string
  declineUrl: string
  item: IEmailSellerOfferItemUpdated
  saleNumber: string
  inspectionDate: string
  expirationDate: string
  shippingFirstName: string
  iban: string
  paymentPeriod: string
}

export type IEmailSellerOfferUpdatedNoValueData = {
  recycleUrl: string
  returnUrl: string
  item: IEmailSellerOfferItemUpdated
  saleNumber: string
  inspectionDate: string
  shippingFirstName: string
}

export type IEmailSellerDeviceAcceptedData = {
  saleNumber: string
  firstName: string
  paymentPeriodC2b?: string
  paymentPeriodC2c?: string
  paymentPeriodDonation?: string
  item: {
    name: string
    price: string
  }
  iban?: string
  charityName?: string
}

export type IEmailSellerDeviceRecycledData = {
  firstName: string
  saleNumber: string
}

export type IEmailSellerDeviceReturnedData = {
  firstName: string
  saleNumber: string
  packageTrackingUrl: string
}

export type IEmailSellerDevicePaidOutData = {
  saleNumber: string
  firstName: string
  item: {
    name: string
    price: string
  }
  iban?: string
  charityName?: string
}

export type ISendmailInputType =
  | {
      type: EmailType.BUYER_NEW_ORDER
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailBuyerNewOrderData
    }
  | {
      type: EmailType.BUYER_NEW_ORDER_INTERNAL
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailBuyerNewOrderInternalData
    }
  | {
      type: EmailType.BUYER_PACKAGE_RECEIVED
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailBuyerPackageReceivedData
    }
  | {
      type: EmailType.SELLER_NEW_SALE
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      attachments: MailDataRequired['attachments']
      data: IEmailSellerNewSaleEmailData
    }
  | {
      type: EmailType.SELLER_PACKAGE_RECEIVED
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerPackageReceivedEmailData
    }
  | {
      type: EmailType.SELLER_REMINDER
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      attachments: MailDataRequired['attachments']
      data: IEmailSellerReminderData
    }
  | {
      type: EmailType.SELLER_NEW_OFFER_HAS_VALUE
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerNewOfferHasValueData
    }
  | {
      type: EmailType.SELLER_NEW_OFFER_NO_VALUE
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerNewOfferNoValueData
    }
  | {
      type: EmailType.SELLER_OFFER_CONFIRMATION_ACCEPTED
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerOfferConfirmationAcceptedData
    }
  | {
      type: EmailType.SELLER_OFFER_CONFIRMATION_DECLINED_RECYCLE
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerOfferConfirmationDeclinedRecycleData
    }
  | {
      type: EmailType.SELLER_OFFER_CONFIRMATION_DECLINED_RETURN
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerOfferConfirmationDeclinedReturnData
    }
  | {
      type: EmailType.SELLER_OFFER_UPDATED_HAS_VALUE
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerOfferUpdatedHasValueData
    }
  | {
      type: EmailType.SELLER_OFFER_UPDATED_NO_VALUE
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerOfferUpdatedNoValueData
    }
  | {
      type: EmailType.SELLER_DEVICE_ACCEPTED
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerDeviceAcceptedData
    }
  | {
      type: EmailType.SELLER_DEVICE_PAID_OUT
      templatePrefix?: string
      to: string | string[]
      from?: string
      lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
      subject: string
      data: IEmailSellerDevicePaidOutData
    }
