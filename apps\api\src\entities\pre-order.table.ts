import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { PreOrderStatus, UserFrom } from '~/constants'
import {
  AddressEntity,
  ChannelEntity,
  type IChannel,
  type ILocaleCurrency,
  type IOrder,
  type IProductSku,
  LocaleCurrencyEntity,
  LocaleLanguageEntity,
  OrderEntity,
  ProductSkuEntity,
  UserEntity,
} from '~/entities'

export type IPreOrderCart = {
  skuItems: {
    id: string
    cartItemId: string
    warrantyItemId: string
    warrantyPrice: number
    price: number
  }[]
  accessoryItems: {
    id: string
    cartItemId: string
    quantity: number
    price: number
  }[]
}

@Entity('pre_order')
export class PreOrderEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((order: PreOrderEntity) => order.channel)
  channelId: string

  @Column({ nullable: false, unique: true })
  stripePaymentIntent: string

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  totalAmount: number

  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: PreOrderEntity) => item.currency)
  currencyId: string

  @Column({ type: 'jsonb' })
  cart: IPreOrderCart

  @Index()
  @Column({ type: 'varchar' })
  status: PreOrderStatus

  @Column({ nullable: true })
  reason?: string

  @Column({ nullable: true })
  paymentMethod?: string

  @OneToOne(() => OrderEntity, (order) => order.preOrder, { nullable: true })
  order?: Relation<OrderEntity>

  @Column()
  email: string

  @ManyToOne(() => UserEntity, (user) => user.orders, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  user: Relation<UserEntity>

  @Column()
  @RelationId((preOrder: PreOrderEntity) => preOrder.user)
  userId: string

  @ManyToOne(() => LocaleLanguageEntity)
  language: Relation<LocaleLanguageEntity>

  @Column()
  @RelationId((user: UserEntity) => user.language)
  languageId: string

  @Column()
  anonymousId: string

  @Column({ type: 'varchar', nullable: false, default: UserFrom.VALYUU })
  from: UserFrom

  @ManyToOne(() => AddressEntity, { nullable: false })
  shippingAddress: Relation<AddressEntity>

  @Column()
  @RelationId((preOrder: PreOrderEntity) => preOrder.shippingAddress)
  shippingAddressId: string

  @Column()
  toPickupPoint: boolean

  @ManyToOne(() => AddressEntity, { nullable: false })
  billingAddress: Relation<AddressEntity>

  @Column()
  @RelationId((preOrder: PreOrderEntity) => preOrder.billingAddress)
  billingAddressId: string

  @ManyToMany(() => ProductSkuEntity, (product) => product.preOrders, { nullable: false })
  @JoinTable({ name: 'pre_order_product_skus' })
  productSkus: Relation<ProductSkuEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IPreOrder = Omit<PreOrderEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
  order: IOrder
  productSkus: IProductSku[]
}
