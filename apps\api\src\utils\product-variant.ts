import { EntityManager } from 'typeorm'

import {
  ChannelEntity,
  ProductVariantConditionCombinationChoiceEntity,
  ProductVariantConditionCombinationEntity,
  ProductVariantConditionCombinationPriceEntity,
  ProductVariantEntity,
  QuestionTypeEntity,
} from '~/entities'

const cartesianProduct = (arrays: any[][]): any[][] => {
  return arrays.reduce(
    (acc, curr) => {
      return acc.flatMap((a) => curr.map((b) => [...a, b]))
    },
    [[]]
  )
}

const getAllPossibleCombinations = (
  questionType: QuestionTypeEntity
): { choices: { conditionId: string; optionId: string }[] }[] => {
  const allChoices = questionType.conditions.map((condition) =>
    condition.options.map((option) => ({
      conditionId: condition.id,
      optionId: option.id,
    }))
  )

  const combinations = cartesianProduct(allChoices)
  return combinations.map((combination) => ({ choices: combination }))
}

const areChoicesEqual = (
  choices1: { conditionId: string; optionId: string }[],
  choices2: { conditionId: string; optionId: string }[]
): boolean => {
  if (choices1.length !== choices2.length) return false
  return choices1.every((choice1) =>
    choices2.some((choice2) => choice1.conditionId === choice2.conditionId && choice1.optionId === choice2.optionId)
  )
}

const checkMissingCombinations = (
  productVariant: ProductVariantEntity
): { choices: { conditionId: string; optionId: string }[] }[] => {
  const allPossibleCombinations = getAllPossibleCombinations(productVariant.questionType)

  const existingCombinations = productVariant.conditionCombinations.map((combination) => combination.choices)

  const missingCombinations = allPossibleCombinations.filter((possibleCombination) => {
    return !existingCombinations.some((existingCombination) =>
      areChoicesEqual(possibleCombination.choices, existingCombination)
    )
  })

  return missingCombinations
}

export const productVariantGenerateConditionCombinations = async (
  variantId: string,
  manager: EntityManager = ProductVariantEntity.getRepository().manager
) => {
  const variant = await manager.findOne(ProductVariantEntity, {
    where: { id: variantId },
    relations: { questionType: { conditions: { options: true } }, conditionCombinations: { choices: true } },
    select: {
      id: true,
      questionType: {
        id: true,
        conditions: {
          id: true,
          sortOrder: true,
          options: {
            id: true,
            sortOrder: true,
          },
        },
      },
      conditionCombinations: {
        id: true,
        choices: {
          conditionId: true,
          optionId: true,
        },
      },
    },
    order: {
      questionType: {
        conditions: {
          sortOrder: 'ASC',
          options: {
            sortOrder: 'ASC',
          },
        },
      },
    },
  })
  const missingCombinations = checkMissingCombinations(variant)
  if (missingCombinations?.length) {
    const channels = await manager.find(ChannelEntity, { select: { id: true, currencyId: true } })
    for (const newCombination of missingCombinations) {
      const combination = await manager.save(manager.create(ProductVariantConditionCombinationEntity, { variant }))
      for (const choice of newCombination.choices) {
        await manager.save(
          manager.create(ProductVariantConditionCombinationChoiceEntity, {
            conditionId: choice.conditionId,
            optionId: choice.optionId,
            combination,
          })
        )
      }
      for (const channel of channels) {
        await manager.save(
          manager.create(ProductVariantConditionCombinationPriceEntity, {
            channelId: channel.id,
            currencyId: channel.currencyId,
            conditionCombinationId: combination.id,
            c2bPrice: 0,
            c2cPrice: 0,
          })
        )
      }
    }
  }
}
