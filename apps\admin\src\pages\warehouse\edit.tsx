import { Edit, useForm, useSelect } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { ILocaleCountry, IWarehouse } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, InputNumber, Select } from 'antx'
import { FC } from 'react'

import { InputMultiEntitySelect } from '~/components'

export const WarehouseEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, formLoading } = useForm<IWarehouse, HttpError, IWarehouse>({
    meta: {
      join: [
        { field: 'shippingToCountries', select: ['id'] },
        { field: 'receivingFromCountries', select: ['id'] },
      ],
    },
  })

  const { selectProps: countrySelectProps } = useSelect<ILocaleCountry>({
    resource: 'admin/countries',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Select label="Country" name="countryId" rules={['required']} {...(countrySelectProps as any)} />
        <Input label="Company name" name="companyName" rules={['required']} />
        <Input label="Contact name" name="contactName" />
        <Input label="Email" name="email" rules={['required', 'email']} />
        <Input label="City" name="city" rules={['required']} />
        <Input label="Postal code" name="postalCode" rules={['required']} />
        <Input
          label="Phone"
          name="phone"
          rules={[
            'required',
            { pattern: /^\+\d+ \d+/, message: 'Phone number must be in the format of "+[country_code] [pure_number]"' },
          ]}
        />
        <Input label="Street" name="street" rules={['required']} />
        <Input label="House number" name="houseNumber" rules={['required']} />
        <InputMultiEntitySelect
          label="Shipping to countries"
          name="shippingToCountries"
          {...(countrySelectProps as any)}
        />
        <InputMultiEntitySelect
          label="Receiving from countries"
          name="receivingFromCountries"
          {...(countrySelectProps as any)}
        />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
      </Form>
    </Edit>
  )
}
