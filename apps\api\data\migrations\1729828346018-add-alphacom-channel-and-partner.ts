import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddAlphacomChannelAndPartner1729828346018 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add Alphacom channel
    await queryRunner.query(`
      INSERT INTO "public"."channel" (
        "id", "name", "desc", "currency_id", 
        "created_at", "updated_at", "published_at", 
        "disable_buyer", "disable_seller", "type"
      ) VALUES (
        'NL-EUR-ALPHACOM',
        'Alphacom',
        'Channel elusively for partner Alphacom',
        'EUR',
        '2024-10-25 08:48:06.711725',
        '2024-10-25 08:48:06.711725',
        '2024-10-25 08:48:06.711725',
        't',
        'f',
        'PARTNER'
      );
    `)

    // Add table creation before partner insertion
    await queryRunner.query(`
      DROP TABLE IF EXISTS "public"."partner" CASCADE;
      CREATE TABLE "public"."partner" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
        "channel_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
        "created_at" timestamp(6) NOT NULL DEFAULT now(),
        "updated_at" timestamp(6) NOT NULL DEFAULT now(),
        "secret" varchar COLLATE "pg_catalog"."default" NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'::text),
        "manage_customer_emails" bool NOT NULL DEFAULT true,
        "api_endpoint" varchar COLLATE "pg_catalog"."default"
      );

      CREATE INDEX "IDX_192d072f322b9ad0883b5c0dbf" ON "public"."partner" USING btree (
        "created_at" "pg_catalog"."timestamp_ops" ASC NULLS LAST
      );

      ALTER TABLE "public"."partner" ADD CONSTRAINT "PK_8f34ff11ddd5459eacbfacd48ca" PRIMARY KEY ("id");
      ALTER TABLE "public"."partner" ADD CONSTRAINT "FK_e18644d50ea5641bc5373b7d51c" FOREIGN KEY ("channel_id") REFERENCES "public"."channel" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
    `)

    await queryRunner.query(`
      INSERT INTO "public"."partner" (
        "id", "name", "channel_id", "created_at", "updated_at", "manage_customer_emails", "api_endpoint"
      ) VALUES 
      (
        '55aef518-e9da-4084-a32e-1fdb80f39979',
        'Ben',
        'NL-EUR-BEN',
        '2024-10-25 12:29:51.042426',
        '2024-10-25 12:29:51.042426',
        't',
        NULL
      ),
      (
        'a9d50797-c10f-40cc-b38e-dff867d57e9e',
        'Odido',
        'NL-EUR-BEN',
        '2024-10-25 12:29:51.042426',
        '2024-10-25 12:29:51.042426',
        't',
        NULL
      ),
      (
        '22831168-cc5b-4b98-ba23-45a616bad65b',
        'Alphacom',
        'NL-EUR-ALPHACOM',
        '2024-10-25 08:48:06.711725',
        '2024-10-25 08:48:06.711725',
        'f',
        NULL
      );
    `)

    // Copy product_model_selling_promise records
    await queryRunner.query(`
      INSERT INTO product_model_selling_promise (
        c2c_days, c2b_number_from, c2b_number_to, channel_id, model_id, 
        created_at, updated_at, c2b_unit
      )
      SELECT 
        c2c_days, c2b_number_from, c2b_number_to, 'NL-EUR-ALPHACOM', model_id,
        created_at, updated_at, c2b_unit
      FROM product_model_selling_promise
      WHERE channel_id = 'NL-EUR-BEN';
    `)

    // Copy product_variant_base_price records
    await queryRunner.query(`
      INSERT INTO product_variant_base_price (
        base_price, channel_id, currency_id, variant_id, 
        created_at, updated_at
      )
      SELECT 
        base_price, 'NL-EUR-ALPHACOM', currency_id, variant_id,
        created_at, updated_at
      FROM product_variant_base_price
      WHERE channel_id = 'NL-EUR-BEN';
    `)

    // Copy product_variant_condition_combination_price records
    await queryRunner.query(`
      INSERT INTO product_variant_condition_combination_price (
        channel_id, currency_id, c2b_price, c2c_price,
        created_at, updated_at, condition_combination_id
      )
      SELECT 
        'NL-EUR-ALPHACOM', currency_id, c2b_price, c2c_price,
        created_at, updated_at, condition_combination_id
      FROM product_variant_condition_combination_price
      WHERE channel_id = 'NL-EUR-BEN';
    `)

    // Copy product_variant_problem_price records
    await queryRunner.query(`
      INSERT INTO product_variant_problem_price (
        price_reduction, channel_id, currency_id, variant_problem_id,
        created_at, updated_at
      )
      SELECT 
        price_reduction, 'NL-EUR-ALPHACOM', currency_id, variant_problem_id,
        created_at, updated_at
      FROM product_variant_problem_price
      WHERE channel_id = 'NL-EUR-BEN';
    `)

    // Copy recycle_threshold records
    await queryRunner.query(`
      INSERT INTO recycle_threshold (
        channel_id, currency_id, price, updated_at
      )
      SELECT 
        'NL-EUR-ALPHACOM', currency_id, price, updated_at
      FROM recycle_threshold
      WHERE channel_id = 'NL-EUR-BEN';
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
