export enum ShipmentType {
  SALE = 'SALE',
  SALE_ITEM_RETURN = 'SALE_ITEM_RETURN',
  ORDER = 'ORDER',
}

export enum ShipmentTrackingStatusCode {
  NOT_SORTED = 6,
  ERROR_COLLECTING = 15,
  PA<PERSON><PERSON>_CANCELLATION_FAILED = 94,
  CA<PERSON><PERSON>LATION_REQUEST = 1999,
  EXCEPTION = 62996,
  AT_CUSTOMS = 62989,
  DELIVERY_METHOD_CHANGED = 62993,
  AT_SORTING_CENTRE = 62990,
  CANCELLED_UPSTREAM = 1998,
  ANNOUNCEMENT_FAILED = 1002,
  REFUSED_BY_RECIPIENT = 62991,
  RETURNED_TO_SENDER = 62992,
  DELIVERY_ADDRESS_CHANGED = 62995,
  SUBMITTING_CANCELLATION_REQUEST = 2001,
  DRIVER_EN_ROUTE = 92,
  DELIVERY_DATE_CHANGED = 62994,
  ADDRESS_INVALID = 62997,
  AWAITING_CUSTOMER_PICKUP = 12,
  DELIVERED = 11,
  SHIPMENT_COLLECTED_BY_CUSTOMER = 93,
  PARCEL_EN_ROUTE = 91,
  UNABLE_TO_DELIVER = 80,
  SHIPMENT_PICKED_UP_BY_DRIVER = 22,
  ANNOUNCED_NOT_COLLECTED = 13,
  DELIVERY_ATTEMPT_FAILED = 8,
  BEING_SORTED = 7,
  SORTED = 5,
  DELIVERY_DELAYED = 4,
  EN_ROUTE_TO_SORTING_CENTER = 3,
  ANNOUNCED = 1,
  UNKNOWN_STATUS = 1337,
  NO_LABEL = 999,
  BEING_ANNOUNCED = 1001,
  CANCELLED = 2000,
  READY_TO_SEND = 1000,
}

export const SHIPMENT_BEFORE_CUSTOMER_DROP_OFF_STATUSES = [
  ShipmentTrackingStatusCode.READY_TO_SEND,
  ShipmentTrackingStatusCode.ANNOUNCED,
  ShipmentTrackingStatusCode.BEING_ANNOUNCED,
]

export enum ShipmentTrackingStatusMessage {
  NOT_SORTED = 'Not sorted',
  ERROR_COLLECTING = 'Error collecting',
  PARCEL_CANCELLATION_FAILED = 'Parcel cancellation failed.',
  CANCELLATION_REQUEST = 'Cancellation requested',
  EXCEPTION = 'Exception',
  AT_CUSTOMS = 'At Customs',
  DELIVERY_METHOD_CHANGED = 'Delivery method changed',
  AT_SORTING_CENTRE = 'At sorting centre',
  CANCELLED_UPSTREAM = 'Cancelled upstream',
  ANNOUNCEMENT_FAILED = 'Announcement failed',
  REFUSED_BY_RECIPIENT = 'Refused by recipient',
  RETURNED_TO_SENDER = 'Returned to sender',
  DELIVERY_ADDRESS_CHANGED = 'Delivery address changed',
  SUBMITTING_CANCELLATION_REQUEST = 'Submitting cancellation request',
  DRIVER_EN_ROUTE = 'Driver en route',
  DELIVERY_DATE_CHANGED = 'Delivery date changed',
  ADDRESS_INVALID = 'Address invalid',
  AWAITING_CUSTOMER_PICKUP = 'Awaiting customer pickup',
  DELIVERED = 'Delivered',
  SHIPMENT_COLLECTED_BY_CUSTOMER = 'Shipment collected by customer',
  PARCEL_EN_ROUTE = 'Parcel en route',
  UNABLE_TO_DELIVER = 'Unable to deliver',
  SHIPMENT_PICKED_UP_BY_DRIVER = 'Shipment picked up by driver',
  ANNOUNCED_NOT_COLLECTED = 'Announced: not collected',
  DELIVERY_ATTEMPT_FAILED = 'Delivery attempt failed',
  BEING_SORTED = 'Being sorted',
  SORTED = 'Sorted',
  DELIVERY_DELAYED = 'Delivery delayed',
  EN_ROUTE_TO_SORTING_CENTER = 'En route to sorting center',
  ANNOUNCED = 'Announced',
  UNKNOWN_STATUS = 'Unknown status - check carrier track & trace page for more insights',
  NO_LABEL = 'No label',
  BEING_ANNOUNCED = 'Being announced',
  CANCELLED = 'Cancelled',
  READY_TO_SEND = 'Ready to send',
}

export enum ShippingMethodFeatures {
  // Insurance provided by the carrier
  CARRIER_INSURANCE = 'carrier_insurance',
  // How the package enters the shipping network
  FIRST_MILE = 'first_mile',
  // Whether fragile goods are accepted
  FRAGILE_GOODS = 'fragile_goods',
  // General insurance coverage
  INSURANCE = 'insurance',
  // Final delivery method
  LAST_MILE = 'last_mile',
  // Whether delivery to neighbors is allowed
  NEIGHBOR_DELIVERY = 'neighbor_delivery',
  // Whether registered delivery is available
  REGISTERED_DELIVERY = 'registered_delivery',
  // Whether returns are supported
  RETURNS = 'returns',
  // Geographic coverage area
  SERVICE_AREA = 'service_area',
  // Whether signature is required
  SIGNATURE = 'signature',
  // Whether tracking is available
  TRACKED = 'tracked',
  // Whether weekend delivery is supported
  WEEKEND_DELIVERY = 'weekend_delivery',
  // Whether labelless shipping is supported
  LABELLESS = 'labelless',
}

export const SHIPMENT_RETURN_LABEL_EXPIRATION_DAYS = 36
