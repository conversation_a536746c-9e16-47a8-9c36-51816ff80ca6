import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { OrderAccessoryProductItemEntity } from '~/entities'

export class CrudOrderAccessoryProductItemService extends TypeOrmCrudService<OrderAccessoryProductItemEntity> {
  constructor(@InjectRepository(OrderAccessoryProductItemEntity) repo: Repository<OrderAccessoryProductItemEntity>) {
    super(repo)
  }
}
