import { MigrationInterface, QueryRunner } from 'typeorm'

export class CharityBankAccountId1734177420400 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO "organization_bank_account" ("id", "type", "holder_name", "account_number", "created_at", "updated_at") 
        VALUES 
        ('715df6a1-5e85-4018-9a9e-34157aef42c0', 'CHARITY', '<PERSON>rge<PERSON>', '******************', '2024-12-14 12:10:40.769214', '2024-12-14 15:29:31.325133'),
        ('605a29be-cc1f-466e-b120-c9339fa362c7', 'PARTNER', 'SoldMine B.V.', '******************', '2024-12-14 15:29:57.189328', '2024-12-14 15:29:57.189328')
      `)

    // Then add bank_account_id column if not exists
    const hasColumn = await queryRunner.hasColumn('charity', 'bank_account_id')

    if (!hasColumn) {
      await queryRunner.query(`
        ALTER TABLE "charity"
        ADD COLUMN "bank_account_id" uuid
      `)
    }

    // Update the charity record with the charity bank account id
    await queryRunner.query(`
      UPDATE "charity"
      SET "bank_account_id" = '715df6a1-5e85-4018-9a9e-34157aef42c0'
      WHERE id IN (SELECT id FROM "charity" LIMIT 1)
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
