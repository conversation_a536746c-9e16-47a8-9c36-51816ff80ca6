import { limitFit } from '@cloudinary/url-gen/actions/resize'
import { DateField, DeleteButton, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { MarketingBannerType } from '@valyuu/api/constants'
import type { IChannel, IMarketingBanner } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import type { FC } from 'react'

import { PublishStatus } from '~/components'
import { cloudinary, handleTableRowClick } from '~/utils'

export const MarketingBannerList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<IMarketingBanner>({
    syncWithLocation: true,
    meta: {
      fields: [
        'type',
        'category.name__en',
        'category.name__pl',
        'model.name__en',
        'model.name__pl',
        'collection.name__en',
        'collection.name__pl',
        'channels.id',
        'createdAt',
        'publishedAt',
        'sortOrder',
      ],
      join: [
        {
          field: 'category',
        },
        {
          field: 'model',
        },
        {
          field: 'collection',
        },
        {
          field: 'imageDesktop__en',
          select: ['id', 'publicId'],
        },
        {
          field: 'imageDesktop__pl',
          select: ['id', 'publicId'],
        },
        {
          field: 'channels',
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/marketing-banners', id!)))}
      >
        <Table.Column
          dataIndex="imageDesktop__en"
          title="Desktop Image (English)"
          className="cursor-pointer"
          render={(value) => (
            <img
              src={cloudinary.image(value.publicId).resize(limitFit(250, 60)).toURL()}
              className="h-auto max-h-[60px] max-w-[250px]"
              alt=""
            />
          )}
          width={260}
        />
        <Table.Column
          title="Name"
          className="cursor-pointer"
          render={(_, record: IMarketingBanner & { type: MarketingBannerType }) => {
            switch (record.type) {
              case MarketingBannerType.CATEGORY:
                return record.category?.name__en
              case MarketingBannerType.MODEL:
                return record.model?.name__en
              case MarketingBannerType.MARKETING_SKU_COLLECTION:
                return record.collection?.name__en
              default:
                return ''
            }
          }}
        />
        <Table.Column
          dataIndex="type"
          title="Type"
          className="cursor-pointer"
          render={(value) => {
            switch (value) {
              case MarketingBannerType.CATEGORY:
                return 'Category'
              case MarketingBannerType.MODEL:
                return 'Model'
              case MarketingBannerType.MARKETING_SKU_COLLECTION:
                return 'SKU Collection'
              default:
                return ''
            }
          }}
        />
        <Table.Column
          dataIndex="channels"
          title="Channels"
          className="cursor-pointer"
          render={(values: IChannel[]) =>
            values.map((value) => <div key={value.id}>{value.id + ` (${value.name})`}</div>)
          }
        />
        <Table.Column
          dataIndex="sortOrder"
          title="Order"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
          width="5rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IMarketingBanner>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
