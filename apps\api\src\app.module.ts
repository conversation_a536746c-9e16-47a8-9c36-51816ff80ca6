import { join, resolve } from 'node:path'

import { StripeModule } from '@golevelup/nestjs-stripe'
import { RedisModule } from '@liaoliaots/nestjs-redis'
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo'
import { BullModule } from '@nestjs/bullmq'
import { Global, Module } from '@nestjs/common'
import { APP_FILTER } from '@nestjs/core'
import { GraphQLModule } from '@nestjs/graphql'
import { JwtModule } from '@nestjs/jwt'
import { ScheduleModule } from '@nestjs/schedule'
import { TypeOrmModule } from '@nestjs/typeorm'
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup'
import { ClsModule } from 'nestjs-cls'
import { AcceptLanguageResolver, I18nModule } from 'nestjs-i18n'
import { DataSource } from 'typeorm'
import { GraphRelationBuilder } from 'typeorm-relations-graphql'

import { envConfig, redisConfig, typeOrmConfig } from '~/configs'
import * as controllers from '~/controllers'
import * as subscribers from '~/entity-subscribers'
import * as resolvers from '~/resolvers'
import * as services from '~/services'
import * as workers from '~/workers'
import { AdminModule } from '~admin/admin.module'
import { PartnerModule } from '~partner/partner.module'

const PROCESSOR_METADATA = 'bullmq:processor_metadata'

@Global()
@Module({
  imports: [
    SentryModule.forRoot(),
    TypeOrmModule.forRoot(typeOrmConfig),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      playground: envConfig.isDev,
      csrfPrevention: true,
      introspection: envConfig.isDev,
      autoSchemaFile: resolve(process.cwd(), 'src/schema.gql'),
      sortSchema: true,
      context: ({ req }: any) => ({ ...req }),
    }),
    RedisModule.forRoot({
      config: redisConfig,
    }),
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
      },
    }),
    JwtModule.register({
      global: true,
      secret: envConfig.JWT_SECRET,
    }),
    AdminModule,
    PartnerModule,
    BullModule.forRoot({
      connection: redisConfig,
    }),
    StripeModule.forRoot(StripeModule, {
      apiKey: envConfig.isProd ? envConfig.STRIPE_SK_LIVE : envConfig.STRIPE_SK_TEST,
      webhookConfig: {
        stripeSecrets: {
          account: envConfig.isProd ? envConfig.STRIPE_WEBHOOK_SECRET_LIVE : envConfig.STRIPE_WEBHOOK_SECRET_TEST,
          connect: envConfig.isProd ? envConfig.STRIPE_WEBHOOK_SECRET_LIVE : envConfig.STRIPE_WEBHOOK_SECRET_TEST,
        },
        requestBodyProperty: 'rawBody',
      },
    }),
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [AcceptLanguageResolver],
    }),
    ScheduleModule.forRoot(),
    ...Object.values(workers)
      .filter((worker) => worker?.name?.endsWith('Processor'))
      .map((worker) => Reflect.getMetadata(PROCESSOR_METADATA, worker))
      .map(({ name }) => BullModule.registerQueue({ name, connection: redisConfig })),
  ],
  exports: [...Object.values(services).filter((service) => service?.name?.endsWith('Service'))],
  providers: [
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
    {
      provide: GraphRelationBuilder,
      useFactory: (dataSource: DataSource) => {
        return new GraphRelationBuilder(dataSource)
      },
      inject: [DataSource],
    },
    ...Object.values(subscribers).filter((subscriber) => subscriber?.name?.endsWith('Subscriber')),
    ...Object.values(workers).filter((worker) => worker?.name?.endsWith('Processor')),
    ...Object.values(services).filter((service) => service?.name?.endsWith('Service')),
    ...Object.values(resolvers).filter((resolver) => resolver?.name?.endsWith('Resolver')),
  ],
  controllers: Object.values(controllers).filter((service) => service?.name?.endsWith('Controller')),
})
export class AppModule {}
