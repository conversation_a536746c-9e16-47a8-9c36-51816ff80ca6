import * as parse from 'yargs-parser'

type CliArgsType = {
  _: (string | number)[]
  // select the seeds to run
  name?: string | string[]
  n?: string | string[]
  // purge the database before running the seeds
  purge?: boolean
  p?: boolean
  // use both dummy data and files for the seeders
  dummy?: boolean
  d?: boolean
  // use dummy data for the seeders
  dummyData?: boolean
  t?: boolean
  // use dummy files for the seeders
  dummyFiles?: boolean
  f?: boolean
  // import all skus
  allSkus?: boolean
  a?: boolean
  // min seed order number
  minOrder?: number
  m?: number
  // handle order and sale
  handleUser?: boolean
  h?: boolean
}

type CliOptionsType = {
  names?: string[]
  purge?: boolean
  dummyData?: boolean
  dummyFiles?: boolean
  allSkus?: boolean
  minOrder?: number
  handleUser?: boolean
}

export const cliOptions = (() => {
  const args: CliArgsType = parse(process.argv.slice(2))

  const options: CliOptionsType = {
    purge: false,
    dummyData: false,
    dummyFiles: false,
    allSkus: false,
    handleUser: false,
  }

  if (args.name || args.n) {
    const name = args.name || args.n
    options.names = Array.isArray(name) ? name : [name]
  } else {
    if (args.purge || args.p) {
      options.purge = true
    }
    if (args.minOrder || args.m) {
      options.minOrder = args.minOrder ?? args.m
    }
  }

  if (args.dummy || args.d) {
    options.dummyData = true
    options.dummyFiles = true
  }

  if (args.dummyData || args.t) {
    options.dummyData = true
  }

  if (args.dummyFiles || args.f) {
    options.dummyFiles = true
  }

  if (args.allSkus || args.a) {
    options.allSkus = true
  }

  if (args.handleUser || args.h) {
    options.handleUser = true
  }

  return options
})()
