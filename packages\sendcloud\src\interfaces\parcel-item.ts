/**
 * Represents an item in the parcel for customs declaration
 */
export type IParcelItem = {
  /** Harmonized System Code Wikipedia Link. Providing a complete HS code with more characters increases the delivery rate. <= 12 characters */
  hs_code: string
  /** Weight of the product in kg */
  weight: string
  /** Quantity of the product. >= 1*/
  quantity: number
  /** Description of the product. <= 255 characters */
  description: string
  /** Origin country code (ISO 3166-1 alpha-2) */
  origin_country?: string
  /** Value of the product */
  value: number
  /** SKU of the product */
  sku?: string
  /** The internal ID of the product. <= 255 characters*/
  product_id?: string
  /** Properties of the product */
  properties?: Record<string, string>
  /** External ID of the item generated by a shop system or similar. <= 255 characters */
  item_id?: string
  /** The return reason identifier matching the ones provided from Sendcloud. Only applicable for returns. >= 1*/
  return_reason?: number
  /** Optional a message relating to the return. Only applicable for returns. */
  return_message?: string
  /** Code to identify the manufacturer of the product. It is required when shipping to the US and is used for generating commercial invoice. <= 15 characters Example:
  US1234567 */
  mid_code?: string
  /** Composition of the items. Used for commercial invoice generation. <= 255 characters */
  material_content?: string
  /** 
  intended_use
  string
  Text that identifies the Intended Use of the item. This will be used to classify the item based on the new ICS2 system. Used for commercial invoice generation. <= 255 characters Example:
  Personal use */
  intended_use?: string
}
