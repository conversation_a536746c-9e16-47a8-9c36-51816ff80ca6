import { IParcelCustomsInformation } from './parcel-customs-information'
import { IParcelItem } from './parcel-item'
import { IParcelShipment } from './parcel-shipment'

/**
 * Represents the request body for creating a parcel in Sendcloud
 */
export type IParcelCreation = {
  /** Name of the recipient */
  name: string
  /** Company name of the recipient the parcel will be shipped to */
  company_name?: string
  /** Id of the contract that you would like to use to create the parcel with */
  contract?: number
  /** Address of the recipient */
  address: string
  /** Additional address information, e.g. 2nd level */
  address_2?: string
  /** House number of the recipient */
  house_number?: string
  /** City of the recipient */
  city: string
  /** Zip code of the recipient */
  postal_code: string
  /** Phone number of the recipient */
  telephone?: string
  /** Should the parcel request a label. This property used to be called requestLabel. We kept it backwards compatible by still accepting the previous name */
  request_label?: boolean
  /** E-mail address of the recipient */
  email: string
  /** Country of the recipient */
  country: string
  /** Shipping method object for a parcel */
  shipment: IParcelShipment
  /** Weight of the parcel in kilograms, if none given the default weight from settings is used. If you provide no weight in your request we’ll use the default weight set in your settings. */
  weight: string
  /** Order number for the shipment */
  order_number: string
  /** This field is mutually exclusive with the total_insured_value. Amount of Sendcloud Insurance to add.
This must be a positive value, larger than 2. If a value between 0 and 2 is sent, it is rounded up to 2. The maximum insurance is 5000. This field does not take the carrier provided shipping method insurance into consideration. Note: this value is an integer. If decimal numbers are sent, these are rounded up to the nearest whole number. Example: sending 72.35 results in a insured_value of 73. (optional) */
  insured_value?: number
  /** The currency of the total order value. Validated against a format of “XYZ” (ISO 4217). (optional) */
  total_order_value_currency?: string
  /** The value paid by the buyer (via various payment methods supported by the shop(cash on delivery, pre-paid or post-paid), it will also be used for the cash on delivery amount for example “99.99”. (optional) */
  total_order_value?: string
  /** Create a multi-collo shipment. Default value: 1. Minimal value: 1. Maximal value: 20. When request_label is true, it is required to use the Create multiple parcels method, since multiple parcels will be returned. When request_label is false, the number of parcels will be set according to this value in the incoming order overview. */
  quantity?: number
  /** Shipping method name from checkout (optional) */
  shipping_method_checkout_name?: string
  /** Post box/locker code (optional) */
  to_post_number?: string
  /** State/province code (optional) */
  country_state?: string
  /** Sender address ID (optional) */
  sender_address?: number
  /** A reference to be assigned to this parcel. Must be unique across parcels. This field is used to create idempotence. (optional) */
  external_reference?: string
  /** A reference to be assigned to this parcel. Multiple parcels can have the same reference. Default: 0 (optional) */
  reference?: string
  /** Service point ID (optional) */
  to_service_point?: number
  /** This field is mutually exclusive with the insured_value. Amount of total insurance to add. This must be a positive value, larger than 2. If a value between 0 and 2 is sent, it is rounded up to 2. The maximum insurance is 5000 plus your shipping method’s insurance depending on the carrier. This field works by automatically calculating how much Sendcloud Insurance you’d need to add plus your shipping method’s insurance so it matches the exact value you’ve given. As an example, DPD insures all their shipments by 520€ by default. If you pass the total_insured_value: 5000 your shipment will have a total insurance coverage of 5000€, but you’re only paying for 4480€. Note: this value is an integer. If decimal numbers are sent, these are rounded up to the nearest whole number. Example: sending 72.35 results in a total_insured_value of 73. (optional) */
  total_insured_value?: number
  /** Unique identifier that we assign to your shipment within the Sendcloud system. (optional) */
  shipment_uuid?: string
  /** List of items the order contains. Check the structure of a parcel_item in the “Parcel_items” section (remember, it’s a list of them!). */
  parcel_items?: IParcelItem[]
  /** Set to true if this parcel is a return */
  is_return?: boolean
  /** Parcel length in centimeters (will be used for volumetric weight calculation) */
  length?: string
  /** Parcel width in centimeters (will be used for volumetric weight calculation) */
  width?: string
  /** Parcel height in centimeters (will be used for volumetric weight calculation). Note: You must provide length, width and height all at once for calculating volumetric weight or passing them as-is to carriers that require these properties Parcel height in centimeters (will be used for volumetric weight calculation). Note: You must provide length, width and height all at once for calculating volumetric weight or passing them as-is to carriers that require these properties */
  height?: string
  /** Makes sure that the label is requested asynchronously. The parcel is returned, but without label. You will need to poll for status changes on the parcel. (optional) */
  request_label_async?: boolean
  /** When set to True configured shipping rules will be applied before creating the label and announcing the Parcel */
  apply_shipping_rules?: boolean
  /** Requested delivery date in ISO 8601 format (optional) */
  requested_delivery_date?: string
  from_name?: string
  from_company_name?: string
  from_address_1?: string
  from_address_2?: string
  from_house_number?: string
  from_city?: string
  from_postal_code?: string
  from_country?: string
  from_telephone?: string
  from_email?: string
  /** VAT number of the sender (optional) */
  from_vat_number?: string
  /** Customs information (optional) */
  customs_information?: IParcelCustomsInformation
}
