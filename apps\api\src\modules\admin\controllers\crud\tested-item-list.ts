import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { TestedItemListEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudTestedItemListService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: TestedItemListEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      items: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/tested-item-lists')
export class CrudTestedItemListController implements CrudController<TestedItemListEntity> {
  constructor(public service: CrudTestedItemListService) {}
}
