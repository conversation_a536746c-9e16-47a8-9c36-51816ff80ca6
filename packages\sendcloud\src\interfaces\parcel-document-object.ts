import { ParcelDocumentType } from '../constants'

/**
 * Paper sizes supported for document printing
 * Currently only supports A4 and A6 formats
 */
export type IParcelDocumentPaperSize = 'a4' | 'a6'

/**
 * Represents a document object from the Sendcloud API
 * Documents can be retrieved via GET request to:
 * @see https://panel.sendcloud.sc/api/v2/parcels/{parcelID}/documents/label
 */
export type IParcelDocumentObject = {
  /** Type of the document (e.g., 'label', 'cp71', etc.) */
  type: ParcelDocumentType
  /** Paper size for printing (a4 or a6) */
  size: IParcelDocumentPaperSize
  /**
   * URL to download the document in PDF, PNG, or ZPL format
   * Resolution can be specified in request parameters
   * @default 203 dpi
   */
  link: string
}
