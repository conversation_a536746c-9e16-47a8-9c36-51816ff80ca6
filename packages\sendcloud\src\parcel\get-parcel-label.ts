import { sendcloudClientV2 } from '../client'
import { ParcelPrinter, ParcelPrinterStartFrom } from '../constants'
import { IParcelLabel } from '../interfaces'

export type IGetParcelLabelResponse = {
  label: IParcelLabel
}

export const getParcelLabel = async (
  parcelId: number | string,
  printer: ParcelPrinter,
  startFrom: ParcelPrinterStartFrom = ParcelPrinterStartFrom.TOP_LEFT
): Promise<ArrayBuffer> => {
  if (printer === ParcelPrinter.NORMAL && !startFrom) {
    throw new Error('startFrom is required for normal printer')
  }
  const { data } = await sendcloudClientV2.get(`/labels/${printer}/${parcelId}?start_from=${startFrom}`)
  return data
}
