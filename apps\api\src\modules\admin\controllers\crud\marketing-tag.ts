import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { MarketingTagEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudMarketingTagService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: MarketingTagEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      channels: {},
      category: {},
      model: {},
      collection: {},
      image: {},
    },
  },
})
@Controller('admin/marketing-tags')
export class CrudMarketingTagController implements CrudController<MarketingTagEntity> {
  constructor(public service: CrudMarketingTagService) {}
}
