import { Injectable } from '@nestjs/common'
import * as pMap from 'p-map'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ProductModelAttributeCombinationChoiceEntity, ProductModelAttributeOptionEntity } from '~/entities'
import { productModelGenerateAttributeCombinations } from '~/utils'

@Injectable()
@EventSubscriber()
export class ProductModelAttributeOptionSubscriber
  implements EntitySubscriberInterface<ProductModelAttributeOptionEntity>
{
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ProductModelAttributeOptionEntity
  }

  async afterInsert(event: InsertEvent<ProductModelAttributeOptionEntity>) {
    const { entity, manager } = event

    if (entity.id) {
      const modelAttributeOption = await manager.findOneOrFail(ProductModelAttributeOptionEntity, {
        where: { id: entity.id },
        relations: { attribute: true },
        select: { id: true, attribute: { id: true, modelId: true } },
      })
      const modelId = modelAttributeOption.attribute?.modelId
      if (modelId) {
        await productModelGenerateAttributeCombinations(modelId, manager)
      }
    }
  }

  async afterRemove(event: RemoveEvent<ProductModelAttributeOptionEntity>) {
    const { entity, manager } = event
    if (entity.id) {
      const choices = await manager.find(ProductModelAttributeCombinationChoiceEntity, {
        where: { optionId: entity.id },
        relations: { combination: true },
      })
      await pMap(
        choices,
        async ({ combination }) => {
          if (combination) {
            await manager.remove(combination)
          }
        },
        { concurrency: 5 }
      )
    }
  }
}
