import { MigrationInterface, QueryRunner } from 'typeorm'

export class AdminUserId1731057121049 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop dependent tables
    await queryRunner.query(`
      DROP TABLE IF EXISTS "admin_user_roles";
      DROP TABLE IF EXISTS "admin_role";
      DROP TABLE IF EXISTS "email_history";
    `)

    // Enable uuid-ossp extension if not already enabled
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`)

    // Get the current primary key constraint name
    const [{ constraint_name }] = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name = 'admin_user'
      AND constraint_type = 'PRIMARY KEY'
    `)

    // Drop the primary key constraint using the found name
    await queryRunner.query(`ALTER TABLE "admin_user" DROP CONSTRAINT "${constraint_name}"`)

    // Rename id column to email
    await queryRunner.query(`ALTER TABLE "admin_user" RENAME COLUMN "id" TO "email"`)

    // Add new id column with UUID
    await queryRunner.query(`ALTER TABLE "admin_user" ADD COLUMN "id" uuid NOT NULL DEFAULT uuid_generate_v4()`)

    // Set the new id column as primary key
    await queryRunner.query(`ALTER TABLE "admin_user" ADD CONSTRAINT "PK_admin_user_id" PRIMARY KEY ("id")`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Get the current primary key constraint name (will be PK_admin_user_id)
    const [{ constraint_name }] = await queryRunner.query(`
      SELECT constraint_name
      FROM information_schema.table_constraints
      WHERE table_name = 'admin_user'
      AND constraint_type = 'PRIMARY KEY'
    `)

    // Drop the new primary key constraint
    await queryRunner.query(`ALTER TABLE "admin_user" DROP CONSTRAINT "${constraint_name}"`)

    // Drop the id column
    await queryRunner.query(`ALTER TABLE "admin_user" DROP COLUMN "id"`)

    // Rename email back to id
    await queryRunner.query(`ALTER TABLE "admin_user" RENAME COLUMN "email" TO "id"`)

    // Restore primary key on the id column
    await queryRunner.query(`ALTER TABLE "admin_user" ADD CONSTRAINT "PK_admin_user_id_original" PRIMARY KEY ("id")`)

    // Restore the foreign key with a new name
    await queryRunner.query(`
      ALTER TABLE "email_history" 
      ADD CONSTRAINT "FK_email_history_admin_user" 
      FOREIGN KEY ("id") REFERENCES "admin_user"("id");
    `)
  }
}
