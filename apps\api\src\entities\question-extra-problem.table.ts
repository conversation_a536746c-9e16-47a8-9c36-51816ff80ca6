import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

@Entity('question_extra_problem')
export class QuestionExtraProblemEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  internalName: string

  @Column()
  template__en: string

  @Column()
  template__nl: string

  @Column()
  template__de: string

  @Column()
  template__pl: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IQuestionExtraProblem = Omit<QuestionExtraProblemEntity, keyof BaseEntity>
