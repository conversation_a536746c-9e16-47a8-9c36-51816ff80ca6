import OpenAI from 'openai'

import { envConfig } from '~/configs'

export const openAI = new OpenAI({ apiKey: envConfig.OPENAI_API_KEY })

type CreateChatCompletionsFirstArg = Parameters<typeof openAI.chat.completions.create>[0]

export const openAIGetCompletion = async (
  messages: CreateChatCompletionsFirstArg['messages'] | CreateChatCompletionsFirstArg['messages'][] | string,
  model: CreateChatCompletionsFirstArg['model'] = 'gpt-3.5-turbo-1106'
) => {
  if (typeof messages === 'string') {
    messages = [{ role: 'user', content: messages }]
  } else if (!Array.isArray(messages)) {
    messages = [messages]
  }
  const result = await openAI.chat.completions.create({
    messages: messages as CreateChatCompletionsFirstArg['messages'],
    model,
    stream: false,
  })

  return result.choices[0].message.content
}
