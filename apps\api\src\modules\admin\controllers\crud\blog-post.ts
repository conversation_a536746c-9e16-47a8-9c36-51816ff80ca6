import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { BlogPostEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudBlogPostService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: BlogPostEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      tag: {},
      author: {},
      heroImage: {},
    },
  },
})
@Controller('admin/blog-posts')
export class CrudBlogPostController implements CrudController<BlogPostEntity> {
  constructor(public service: CrudBlogPostService) {}
}
