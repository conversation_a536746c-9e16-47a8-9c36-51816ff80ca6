# Server Configuration
PORT=5000
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/valyuu?sslmode=require
SESSION_SECRET={secret}

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379

# Application URLs
SITE_URL=https://prioont.com
VERCEL_SHARE={vercel_share_key}
API_URL=http://127.0.0.1:5000

# AWS S3 Configuration for File Storage
S3_ACCESS_KEY_ID={real_access_key_id}
S3_DOMAIN_URL=https://files.prioont.com
S3_REGION=eu-central-1
S3_SECRET_ACCESS_KEY={real_secret_access_key}
S3_BUCKET_NAME={real_bucket_name}

# Authentication
JWT_SECRET={real_jwt_secret}

# Email Notifications
LOGISTIC_EMAILS=<EMAIL>;<EMAIL>
ERROR_LOG_EMAILS=<EMAIL>;<EMAIL>

# Stripe Payment Configuration
STRIPE_SK_LIVE={real_stripe_sk_live}
STRIPE_SK_VERIFICATION={real_stripe_sk_live_test}
STRIPE_SK_TEST={real_stripe_sk_test}
STRIPE_WEBHOOK_SECRET_LIVE={real_stripe_webhook_secret_live}
STRIPE_WEBHOOK_SECRET_TEST={real_stripe_webhook_secret_test}

# Security
PASSWORD_HASH_ROUNDS=10

# Email Service Providers
SENDGRID_API_KEY={real_sendgrid_api_key}
SENDCLOUD_PK={real_sendcloud_pk}
SENDCLOUD_SK={real_sendcloud_sk}
EMAIL_GENERAL_FROM=<EMAIL>

# DeepL Translation Service
DEEPL_KEY={real_deepl_key}

# Cloudinary Image Storage
CLOUDINARY_URL=cloudinary://{real_cloudinary_api_key}:{real_cloudinary_api_secret}@{cloudinary_cloud_id}
CLOUDINARY_PREFIX=backend/

# Algolia Search Configuration
ALGOLIA_APP_ID={algoliasearch_app_id}
ALGOLIA_ADMIN_API_KEY={algoliasearch_admin_api_key}
ALGOLIA_INDEX_PREFIX={index_prefix_for_staging_site}

# AI and External Services
OPENAI_API_KEY={real_open_ai_key}

# Ben API Secret
BEN_API_SECRET={real_ben_api_secret}

# Google Services Configuration
GOOGLE_SERVICE_ACCOUNT_KEY={google_service_account_json}
GOOGLE_OAUTH_CLIENT_ID={real_google_oauth_client_id}
GOOGLE_OAUTH_CLIENT_SECRET={real_google_oauth_client_secret}

# Trello Integration
TRELLO_KEY={real_trello_key}
TRELLO_TOKEN={real_trello_token}
TRELLO_BOARD={real_trello_board_id}
TRELLO_LIST={real_trello_list_id}
TRELLO_LABEL_C2C={real_trello_c2c_label_id}
TRELLO_LABEL_C2B={real_trello_c2b_label_id}
TRELLO_LABEL_DONATION={real_trello_donation_label_id}
TRELLO_LABEL_RETURNING={real_trello_returning_label_id}

# Error Tracking
SENTRY_DSN={real_sentry_dsn}

# Development Tools
ENABLE_MAIN_SWAGGER=false

# Environment
NODE_ENV=development
