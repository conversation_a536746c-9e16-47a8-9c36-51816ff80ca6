import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { ChannelType } from '~/constants'
import {
  type ILocaleCountry,
  type ILocaleCurrency,
  type ISellerPaymentPeriod,
  LocaleCountryEntity,
  LocaleCurrencyEntity,
  PriceThresholdEntity,
  SellerPaymentPeriodEntity,
} from '~/entities'

@ObjectType('Channel')
@Entity('channel')
export class ChannelEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryColumn()
  id: string

  @Field()
  @Column()
  name: string

  @Field()
  @Column('text')
  desc: string

  @ManyToMany(() => LocaleCountryEntity, (country) => country.channels, { nullable: false })
  @JoinTable({ name: 'channel_countries' })
  countries: Relation<LocaleCountryEntity>[]

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((channel: ChannelEntity) => channel.currency)
  currencyId: string

  @Column({ type: 'varchar' })
  type: ChannelType

  @Column({ default: false })
  disableBuyer: boolean

  @Column({ default: false })
  disableSeller: boolean

  @OneToOne(() => SellerPaymentPeriodEntity, (sellerPaymentPeriod) => sellerPaymentPeriod.channel, {
    nullable: false,
    cascade: true,
  })
  sellerPaymentPeriod: Relation<SellerPaymentPeriodEntity>

  @OneToOne(() => PriceThresholdEntity, (priceThreshold) => priceThreshold.channel, {
    nullable: false,
    cascade: true,
  })
  priceThreshold: Relation<PriceThresholdEntity>

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IChannel = Omit<ChannelEntity, keyof BaseEntity> & {
  countries: ILocaleCountry[]
  currency: ILocaleCurrency
  sellerPaymentPeriod: ISellerPaymentPeriod
}
