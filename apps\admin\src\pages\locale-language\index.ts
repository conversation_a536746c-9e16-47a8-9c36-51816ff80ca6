import type { PageType } from '~/interfaces'

import { LocaleLanguageCreate } from './create'
import { LocaleLanguageEdit } from './edit'
import { LocaleLanguageList } from './list'

export const LocaleLanguagePage: PageType = {
  path: 'languages',
  label: 'Languages',
  index: LocaleLanguageList,
  create: LocaleLanguageCreate,
  edit: LocaleLanguageEdit,
  parent: 'localization',
  withLayout: true,
}
