import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { ShipmentTrackingStatusCode, ShipmentTrackingStatusMessage, ShipmentType } from '~/constants'
import {
  AddressEntity,
  type IAddress,
  type IOrder,
  type IPartner,
  type ISale,
  type ISaleItem,
  type IShippingMethod,
  type IWarehouse,
  OrderEntity,
  PartnerEntity,
  SaleEntity,
  SaleItemEntity,
  ShippingMethodEntity,
  WarehouseEntity,
} from '~/entities'

@Entity('shipment')
export class ShipmentEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'varchar' })
  type: ShipmentType

  @Column({ type: 'varchar', nullable: true })
  statusCode?: ShipmentTrackingStatusCode

  @Column({ type: 'varchar', nullable: true })
  statusMessage?: ShipmentTrackingStatusMessage

  @Column({ default: false })
  isReturn: boolean

  @Index({ unique: true })
  @Column({ nullable: true })
  parcelId: string

  @Index({ unique: true })
  @Column({ nullable: false })
  trackingNumber: string

  @Column({ nullable: true })
  trackingUrl?: string

  @Column()
  isPaperless: boolean

  @ManyToOne(() => ShippingMethodEntity, { nullable: false })
  shippingMethod: Relation<ShippingMethodEntity>

  @Column({ nullable: true })
  shippingMethodId: string

  @ManyToOne(() => WarehouseEntity, { nullable: false })
  warehouse: Relation<WarehouseEntity>

  @Column()
  @RelationId((shipment: ShipmentEntity) => shipment.warehouse)
  warehouseId: string

  @ManyToOne(() => AddressEntity, { nullable: false })
  customerAddress: Relation<AddressEntity>

  @Column()
  @RelationId((shipment: ShipmentEntity) => shipment.customerAddress)
  customerAddressId: string

  @ManyToOne(() => PartnerEntity, { nullable: true })
  partner?: Relation<PartnerEntity>

  @Column({ nullable: true })
  @RelationId((shipment: ShipmentEntity) => shipment.partner)
  partnerId?: string

  @Column({ nullable: true })
  note?: string

  @OneToOne(() => SaleEntity, (sale) => sale.shipment, { nullable: true })
  sale?: Relation<SaleEntity>

  @OneToOne(() => OrderEntity, (order) => order.shipment, { nullable: true })
  order?: Relation<OrderEntity>

  @OneToOne(() => SaleItemEntity, (saleItem) => saleItem.returnShipment, { nullable: true })
  saleItem?: Relation<SaleItemEntity>

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IShipment = Omit<ShipmentEntity, keyof BaseEntity> & {
  shippingMethod: IShippingMethod
  warehouse: IWarehouse
  customerAddress: IAddress
  partner?: IPartner
  sale?: ISale
  order?: IOrder
  saleItems?: ISaleItem[]
}
