import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { BadRequestException, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { v4 as uuidv4 } from 'uuid'

import { LicensePlateStatusEnum } from '~/constants'
import {
  LicensePlateDeviceEntity,
  LicensePlateProductEntity,
  LicensePlatePurchaseEntity,
  LicensePlateSaleEntity,
  LicensePlateStatusEntity,
  LicensePlateUnmatchedEntity,
  LicensePlateUnmatchedPieEntity,
} from '~/entities'
import { CountryHelper } from '~/utils/countries'

const RECENT_THRESHOLD_MS = 7 * 24 * 60 * 60 * 1000 // 7 days

export class CrudLicensePlateService extends TypeOrmCrudService<LicensePlateDeviceEntity> {
  constructor(
    @InjectRepository(LicensePlateDeviceEntity) repo: Repository<LicensePlateDeviceEntity>,
    @InjectRepository(LicensePlateProductEntity) private readonly productRepo: Repository<LicensePlateProductEntity>,
    @InjectRepository(LicensePlatePurchaseEntity) private readonly purchaseRepo: Repository<LicensePlatePurchaseEntity>,
    @InjectRepository(LicensePlateSaleEntity) private readonly saleRepo: Repository<LicensePlateSaleEntity>,
    @InjectRepository(LicensePlateStatusEntity) private readonly statusRepo: Repository<LicensePlateStatusEntity>
  ) {
    super(repo)
  }

  async getByGuid(guid: string): Promise<LicensePlateDeviceEntity> {
    return this.getByField('guid', guid)
  }

  async getByImei(imei: string): Promise<LicensePlateDeviceEntity> {
    return this.getByField('imei', imei)
  }

  async getBySerial(serial: string): Promise<LicensePlateDeviceEntity> {
    return this.getByField('serial', serial)
  }

  private async getByField(field: 'guid' | 'imei' | 'serial', value: string): Promise<LicensePlateDeviceEntity> {
    const licensePlate = await this.repo.findOne({
      where: { [field]: value },
      relations: ['product', 'purchase', 'sale', 'status'],
      order: { created_at: 'DESC' },
    })

    if (!licensePlate) {
      throw new NotFoundException(`License plate with ${field.toUpperCase()} ${value} not found`)
    }

    return licensePlate
  }

  async createMultiple(licensePlates: LicensePlateDeviceEntity[]): Promise<{
    created: LicensePlateDeviceEntity[]
    failed: { input: LicensePlateDeviceEntity; error: any }[]
  }> {
    const created: LicensePlateDeviceEntity[] = []
    const failed: { input: LicensePlateDeviceEntity; error: any }[] = []

    for (const plate of licensePlates) {
      try {
        const createdPlate = await this.create(plate)
        created.push(createdPlate)
      } catch (error) {
        console.error(error)
        failed.push({ input: plate, error })
      }
    }

    return { created, failed }
  }

  async updateMultiple(licensePlates: LicensePlateDeviceEntity[]): Promise<{
    updated: LicensePlateDeviceEntity[]
    failed: { input: LicensePlateDeviceEntity; error: any }[]
  }> {
    const updated: LicensePlateDeviceEntity[] = []
    const failed: { input: LicensePlateDeviceEntity; error: any }[] = []

    for (const plate of licensePlates) {
      try {
        const updatedPlate = await this.update(plate)
        updated.push(updatedPlate)
      } catch (error) {
        console.error(error)
        failed.push({ input: plate, error })
      }
    }

    return { updated, failed }
  }

  async updateByImei(licensePlate: LicensePlateDeviceEntity, imei: string): Promise<LicensePlateDeviceEntity> {
    const existing = await this.getByImei(imei)
    licensePlate.guid = existing.guid
    return this.update(licensePlate)
  }

  async updateBySerial(licensePlate: LicensePlateDeviceEntity, serial: string): Promise<LicensePlateDeviceEntity> {
    const existing = await this.getBySerial(serial)
    licensePlate.guid = existing.guid
    return this.update(licensePlate)
  }

  async create(licensePlate: LicensePlateDeviceEntity): Promise<LicensePlateDeviceEntity> {
    const existing = await this.findExistingByIdentifiers(licensePlate)
    const isRecent = existing && new Date(existing.created_at).getTime() > Date.now() - RECENT_THRESHOLD_MS

    if (isRecent) {
      const merged = await this.mergeNestedEntities(existing, licensePlate)
      return this.save(merged)
    }

    licensePlate.guid ||= uuidv4()
    if (licensePlate.product) licensePlate.product.guid = licensePlate.guid
    if (licensePlate.sale) licensePlate.sale.guid = licensePlate.guid
    if (licensePlate.purchase) licensePlate.purchase.guid = licensePlate.guid

    return this.save(licensePlate)
  }

  async update(licensePlate: LicensePlateDeviceEntity): Promise<LicensePlateDeviceEntity> {
    const existing = await this.findExistingByIdentifiers(licensePlate)
    if (!existing) {
      throw new NotFoundException(`License plate could not be found`)
    }

    const pickedStatuses = existing.status.filter((s) => s.status === 'picked')
    const hasPickedStatus = existing.status.some((s) => s.status === 'picked')
    const isToInventoryStatusIncoming = licensePlate.status?.some((s) => s.status === 'to_inventory')

    // If the incoming license plate has a 'to_inventory' status and the existing one has a 'picked' status, create a new license plate
    if (isToInventoryStatusIncoming && hasPickedStatus) {
      licensePlate.status = []
      const merged = await this.mergeNestedEntities(existing, licensePlate)
      const newLicensePlate = await this.createNewLicensePlateFromExisting(licensePlate, existing, ['to_inventory'])
      merged.is_returned = true
      await this.save(merged)
      return await this.save(newLicensePlate)
    }

    // If the incoming license plate has a 'picked' status and the existing one already has a 'picked' status,
    // create a new license plate if the last picked status is more than 2 days old
    const incomingPickedStatuses = licensePlate.status?.filter((s) => s.status === 'picked') || []
    if (incomingPickedStatuses.length > 0 && pickedStatuses.length > 0) {
      const lastPicked = pickedStatuses[pickedStatuses.length - 1]
      const newPicked = incomingPickedStatuses[incomingPickedStatuses.length - 1]

      newPicked.created_at = newPicked.created_at || new Date()
      if (lastPicked.created_at && newPicked.created_at) {
        const diffInMs = new Date(newPicked.created_at).getTime() - new Date(lastPicked.created_at).getTime()
        const diffInDays = diffInMs / (1000 * 60 * 60 * 24)

        if (diffInDays >= 2) {
          const merged = await this.mergeNestedEntities(existing, licensePlate)
          const newLicensePlate = await this.createNewLicensePlateFromExisting(licensePlate, existing, ['picked'])

          merged.is_returned = true
          await this.save(merged)
          return await this.save(newLicensePlate)
        }
      }
    }

    // If the incoming license plate has a to_inventory status, but the existing one has a different sku
    // We log it as an unmatched PIE
    if (isToInventoryStatusIncoming && existing.product?.sku && existing.product?.sku !== licensePlate.product?.sku) {
      console.log(`Unmatched PIE detected for SKU: ${existing.product?.sku} vs ${licensePlate.product?.sku}`)
      const unmatchedPie = new LicensePlateUnmatchedPieEntity()
      unmatchedPie.guid = existing.guid
      unmatchedPie.licenseplate_json = JSON.stringify(existing)
      unmatchedPie.sku = existing.product?.sku || null
      await this.createUnmatchedPie(unmatchedPie)
    }

    const merged = await this.mergeNestedEntities(existing, licensePlate)
    console.log(`${merged.guid}: ${merged.imei}`)
    try {
      return this.save(merged)
    } catch (catchError) {
      console.error(`Error saving license plate: ${catchError}`)
      throw new BadRequestException(`Failed to update license plate: ${catchError}`)
    }
  }

  private async findExistingByIdentifiers(licensePlate: LicensePlateDeviceEntity) {
    return this.repo.findOne({
      where: [{ guid: licensePlate.guid }, { imei: licensePlate.imei }, { serial: licensePlate.serial }],
      relations: ['product', 'sale', 'purchase', 'status'],
      order: { created_at: 'DESC' },
    })
  }

  private async mergeNestedEntities(existing: LicensePlateDeviceEntity, incoming: LicensePlateDeviceEntity) {
    const existingStatuses = existing.status.map((s) => s.status)
    incoming.status = incoming.status.filter((s) => !existingStatuses.includes(s.status))
    const merged = this.repo.merge(existing, incoming)

    const guid = existing.guid

    // Scenario: A device is being processed twice in PIE, but has no outbound sale.
    const hasPickedStatus = existing.status.some((s) => s.status === 'picked')
    const existingToInventory = existing.status.find((s) => s.status === 'to_inventory')
    if (existingToInventory && incoming.product && !hasPickedStatus) {
      existingToInventory.updated_at = new Date()
    }

    // Ensure the incoming entities have the correct GUID
    if (incoming.product) {
      incoming.product.guid = guid
      merged.product = existing.product
        ? this.repo.manager.merge(LicensePlateProductEntity, existing.product, incoming.product)
        : incoming.product
    }

    if (incoming.sale) {
      incoming.sale.guid = guid
      merged.sale = existing.sale
        ? this.repo.manager.merge(LicensePlateSaleEntity, existing.sale, incoming.sale)
        : incoming.sale
    }

    if (incoming.purchase) {
      incoming.purchase.guid = guid
      merged.purchase = existing.purchase
        ? this.repo.manager.merge(LicensePlatePurchaseEntity, existing.purchase, incoming.purchase)
        : incoming.purchase
    }

    return merged
  }

  async save(licensePlate: LicensePlateDeviceEntity): Promise<LicensePlateDeviceEntity> {
    return this.repo.manager.transaction(async (manager) => {
      await this.sanitizeLicensePlate(licensePlate)
      return manager.save(LicensePlateDeviceEntity, licensePlate)
    })
  }

  async deleteByGuid(guid: string): Promise<void> {
    const licensePlate = await this.repo.findOne({
      where: { guid },
      order: { created_at: 'DESC' },
    })

    if (!licensePlate) {
      throw new NotFoundException(`License plate with GUID ${guid} not found`)
    }

    await this.repo.manager.transaction(async (manager) => {
      await manager.delete(LicensePlateDeviceEntity, { guid })
    })
  }

  async addStatusByGuid(guid: string, status: LicensePlateStatusEntity): Promise<LicensePlateDeviceEntity> {
    const licensePlate = await this.repo.findOne({
      where: { guid },
      order: { created_at: 'DESC' },
    })

    if (!licensePlate) {
      throw new NotFoundException(`License plate with GUID ${guid} not found`)
    }

    if (!Object.values(LicensePlateStatusEnum).includes(status.status as LicensePlateStatusEnum)) {
      throw new BadRequestException(`Invalid status value: ${status.status}`)
    }

    const existingStatus = await this.statusRepo.findOne({ where: { guid, status: status.status } })
    if (existingStatus) {
      throw new BadRequestException(`Status '${status.status}' already exists for license plate ${guid}`)
    }

    status.guid ||= guid

    await this.statusRepo.manager.transaction(async (manager) => {
      await manager.save(LicensePlateStatusEntity, status)
    })

    return this.getByGuid(guid)
  }

  async addStatusByImei(imei: string, status: LicensePlateStatusEntity): Promise<LicensePlateDeviceEntity> {
    const licensePlate = await this.getByImei(imei)
    return this.addStatusByGuid(licensePlate.guid, status)
  }

  async addStatusBySerial(serial: string, status: LicensePlateStatusEntity): Promise<LicensePlateDeviceEntity> {
    const licensePlate = await this.getBySerial(serial)
    return this.addStatusByGuid(licensePlate.guid, status)
  }

  async createUnmatched(licensePlate: LicensePlateUnmatchedEntity): Promise<LicensePlateUnmatchedEntity> {
    if (!licensePlate.guid) {
      licensePlate.guid = uuidv4()
    }

    return this.repo.manager.transaction(async (manager) => {
      return manager.save(LicensePlateUnmatchedEntity, licensePlate)
    })
  }

  async createUnmatchedPie(licensePlate: LicensePlateUnmatchedPieEntity): Promise<LicensePlateUnmatchedPieEntity> {
    if (!licensePlate.guid) {
      licensePlate.guid = uuidv4()
    }

    return this.repo.manager.transaction(async (manager) => {
      return manager.save(LicensePlateUnmatchedPieEntity, licensePlate)
    })
  }

  private async createNewLicensePlateFromExisting(
    newData: LicensePlateDeviceEntity,
    existing: LicensePlateDeviceEntity,
    newStatusses: string[]
  ): Promise<LicensePlateDeviceEntity> {
    const newLicensePlate = new LicensePlateDeviceEntity()
    newLicensePlate.guid = uuidv4()
    newLicensePlate.imei = existing.imei
    newLicensePlate.serial = existing.serial
    newLicensePlate.grade_inbound = existing.grade_inbound
    newLicensePlate.grade_outbound = existing.grade_outbound

    // If scenario: New license plate for to_inventory:
    // A new license plate should be created with the old purchase data, new product data, and new status.
    if (newStatusses.includes('to_inventory') && !newStatusses.includes('picked')) {
      console.log('Creating new license plate for to_inventory scenario')
      newLicensePlate.grade_outbound = newData.grade_outbound
      newLicensePlate.updated_by = 'Scenario: to_inventory, without picked'
      const product = new LicensePlateProductEntity()
      Object.assign(product, newData.product, { guid: newLicensePlate.guid })
      product.guid = null
      newLicensePlate.product = product

      const purchase = new LicensePlatePurchaseEntity()
      Object.assign(purchase, existing.purchase, { guid: newLicensePlate.guid })
      purchase.guid = null
      newLicensePlate.purchase = purchase

      const sale = new LicensePlateSaleEntity() // No sale for to_inventory
      newLicensePlate.sale = sale
    }

    // If scenario: New license plate for picked, 2 days after last picked
    // A new license plate should be created with the old purchase data, old product data, new sale data, and new status.
    if (newStatusses.includes('picked') && !newStatusses.includes('to_inventory')) {
      console.log('Creating new license plate for picked scenario')
      newLicensePlate.updated_by = 'Scenario: picked, 2 days after last picked'
      const product = new LicensePlateProductEntity()
      Object.assign(product, existing.product, { guid: newLicensePlate.guid })
      product.guid = null
      newLicensePlate.product = product

      const purchase = new LicensePlatePurchaseEntity()
      Object.assign(purchase, existing.purchase, { guid: newLicensePlate.guid })
      purchase.guid = null
      newLicensePlate.purchase = purchase

      const sale = new LicensePlateSaleEntity()
      Object.assign(purchase, newData.sale, { guid: newLicensePlate.guid })
      sale.guid = null
      newLicensePlate.sale = sale
    }

    newLicensePlate.status = []
    newStatusses.forEach((status) => {
      const newStatus = new LicensePlateStatusEntity()
      newStatus.status = status as LicensePlateStatusEnum
      newStatus.created_at = new Date()
      newLicensePlate.status.push(newStatus)
    })

    return newLicensePlate
  }

  private async sanitizeLicensePlate(licensePlate: LicensePlateDeviceEntity): Promise<LicensePlateDeviceEntity> {
    if (!licensePlate) return licensePlate

    if (licensePlate.purchase) {
      licensePlate.purchase.purchase_price = this.formatPrice(licensePlate.purchase.purchase_price)
      licensePlate.purchase.base_purchase_price = this.formatPrice(licensePlate.purchase.base_purchase_price)
      licensePlate.purchase.purchase_tax_amount = this.formatPrice(licensePlate.purchase.purchase_tax_amount)

      if (licensePlate.purchase.purchase_country) {
        if (licensePlate.purchase.purchase_country.length !== 2) {
          const countryCode = CountryHelper.getCode(licensePlate.purchase.purchase_country)
          licensePlate.purchase.purchase_country = countryCode ?? licensePlate.purchase.purchase_country
        }
      }
    }

    if (licensePlate.sale) {
      licensePlate.sale.sale_price = this.formatPrice(licensePlate.sale.sale_price)
      licensePlate.sale.base_sale_price = this.formatPrice(licensePlate.sale.base_sale_price)
      licensePlate.sale.sale_tax_amount = this.formatPrice(licensePlate.sale.sale_tax_amount)

      if (licensePlate.sale.sale_country) {
        if (licensePlate.sale.sale_country.length !== 2) {
          const countryCode = CountryHelper.getCode(licensePlate.sale.sale_country)
          licensePlate.sale.sale_country = countryCode ?? licensePlate.sale.sale_country
        }
      }
    }

    return licensePlate
  }

  private formatPrice(value: string | number | null | undefined): number | null {
    if (value === null || value === undefined || value === '') return null

    const normalized = value.toString().replace(',', '.').trim()
    const num = parseFloat(normalized)
    return isNaN(num) ? null : parseFloat(num.toFixed(4))
  }
}
