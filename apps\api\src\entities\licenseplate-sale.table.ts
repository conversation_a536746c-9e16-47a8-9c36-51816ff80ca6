import { Field, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm'

import { LicensePlateDeviceEntity } from './licenseplate-device.table'
import { LicensePlateGrossMarginEnum } from '~/constants/licenseplate-grossmargin'

@ObjectType('LicensePlateSale')
@Entity('licenseplate_sale')
export class LicensePlateSaleEntity extends BaseEntity {
  @Field()
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  guid: string

  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  base_sale_price?: number

  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  sale_price?: number

  @Column('decimal', { nullable: true })
  sale_tax_percentage?: number

  @Column('decimal', { nullable: true, precision: 10, scale: 4 })
  sale_tax_amount?: number

  @Field(() => LicensePlateGrossMarginEnum)
  @Column({ type: 'enum', enum: LicensePlateGrossMarginEnum, nullable: true })
  sale_grossmargin_type: LicensePlateGrossMarginEnum

  @Column({ nullable: true })
  sale_currency?: string

  @Column({ nullable: true })
  sale_country?: string

  @Column({ type: 'timestamp', nullable: true })
  sale_date?: Date

  @Column({ nullable: true })
  sale_channel?: string

  @Column({ nullable: true })
  order_id?: string

  @OneToOne(() => LicensePlateDeviceEntity, (device) => device.sale, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'guid', referencedColumnName: 'guid' })
  device: LicensePlateDeviceEntity
}

export type ILicensePlateSale = Omit<LicensePlateSaleEntity, keyof BaseEntity> & {
  device: LicensePlateDeviceEntity
}
