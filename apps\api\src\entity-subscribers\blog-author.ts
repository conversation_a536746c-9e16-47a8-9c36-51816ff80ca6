import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageUsedInType } from '~/constants'
import { BlogAuthorEntity } from '~/entities'
import { entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'

@Injectable()
@EventSubscriber()
export class BlogAuthorSubscriber implements EntitySubscriberInterface<BlogAuthorEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return BlogAuthorEntity
  }

  async afterInsert(event: InsertEvent<BlogAuthorEntity>) {
    const { entity, manager } = event

    // process avatar image
    await entityImageAutoProcess({
      image: entity.avatar,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.BLOG_AUTHOR,
      manager,
    })
  }

  async afterUpdate(event: UpdateEvent<BlogAuthorEntity>) {
    const { entity, manager } = event

    // process avatar image
    await entityImageAutoProcess({
      image: entity.avatar ?? { id: entity.avatarId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.BLOG_AUTHOR,
      manager,
    })
  }

  async afterRemove(event: RemoveEvent<BlogAuthorEntity>) {
    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.BLOG_AUTHOR,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
