import { <PERSON><PERSON>, <PERSON>rudController } from '@dataui/crud'
import { Body, Controller, Param, ParseUUIDPipe, Post, UseGuards } from '@nestjs/common'

import { OrderEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudOrderService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: OrderEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      channel: {},
      currency: {},
      preOrder: {},
      orderSkuItems: {},
      'orderSkuItems.productSku': {},
      orderAccessoryItems: {},
      'orderAccessoryItems.internalName': {},
      shippingAddress: {},
      billingAddress: {},
      user: {},
      extraPayments: {},
      shipment: {},
      'shipment.shippingMethod': {},
    },
  },
})
@Controller('admin/orders')
export class CrudOrderController implements CrudController<OrderEntity> {
  constructor(public service: CrudOrderService) {}

  @Post(':id/create-shipment')
  async createShipment(@Param('id', ParseUUIDPipe) id: string, @Body('override') override?: boolean) {
    return this.service.createShipment(id, override)
  }
}
