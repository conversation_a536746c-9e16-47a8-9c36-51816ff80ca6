import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { BadRequestException, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { cancelParcel } from '@valyuu/sendcloud'
import { isEqual, pick } from 'lodash'
import { Repository } from 'typeorm'

import { ShipmentType } from '~/constants'
import { SaleEntity } from '~/entities'
import { SaleService, ShipmentService } from '~/services'

const SALE_HISTORY_COMPARE_FIELDS = [
  'status',
  'shippingLabel',
  'isLabelSent',
  'trackingNumber',
  'parcelId',
  'shipmentId',
  'paymentType',
  'surveySent',
] as const

type HistoryChange = {
  field: (typeof SALE_HISTORY_COMPARE_FIELDS)[number]
  from: unknown
  to: unknown
}

type HistoryChangeItem = {
  version: number
  changes: HistoryChange[]
  changedBy?: {
    id: string
    name: string
    type: string
  }
  changedAt: Date
}

export class CrudSaleService extends TypeOrmCrudService<SaleEntity> {
  constructor(
    @InjectRepository(SaleEntity) repo: Repository<SaleEntity>,
    private readonly saleService: SaleService,
    private readonly shipmentService: ShipmentService
  ) {
    super(repo)
  }

  async getHistories(id: string) {
    const sale = await SaleEntity.findOne({
      where: { id },
      relations: {
        histories: {
          changedByAdminUser: true,
          changedByPartner: true,
        },
      },
    })

    if (!sale) {
      throw new NotFoundException('Sale not found')
    }

    // Sort histories by version in ascending order for processing
    const sortedHistories = sale.histories.sort((a, b) => a.version - b.version)
    const historyChanges: HistoryChangeItem[] = []

    // Process each history item
    for (const currentHistory of sortedHistories) {
      const changes: HistoryChange[] = []

      // If this is the only history or the last history, compare with current sale state
      const compareWithSale =
        sortedHistories.length === 1 || currentHistory === sortedHistories[sortedHistories.length - 1]

      const nextVersion = compareWithSale ? sale : sortedHistories[sortedHistories.indexOf(currentHistory) + 1]

      // Compare fields between current and next version
      for (const field of SALE_HISTORY_COMPARE_FIELDS) {
        const fromValue = currentHistory[field]
        const toValue = nextVersion[field]

        if (!isEqual(fromValue, toValue)) {
          changes.push({
            field,
            from: Array.isArray(fromValue) ? fromValue.join('\n') : fromValue,
            to: Array.isArray(toValue) ? toValue.join('\n') : toValue,
          })
        }
      }

      const changedBy = currentHistory.changedBy
      const historyChange: HistoryChangeItem = {
        version: currentHistory.version + 1, // Version number represents the version that changed TO
        changes,
        changedAt: currentHistory.createdAt,
      }

      if (changedBy) {
        historyChange.changedBy = {
          id: changedBy.id,
          name: 'name' in changedBy ? changedBy.name : '',
          type: currentHistory.changedByType,
        }
      }

      historyChanges.push(historyChange)
    }

    // Sort history changes in descending order by version before returning
    return historyChanges.sort((a, b) => b.version - a.version)
  }

  async getByImei(imei: string) {
    const sales = await SaleEntity.find({
      where: { saleItems: {
          imei: imei
        }
      },
      relations: {
        user: true,
        address: true,
        saleItems: {
          productVariant: true
        }
      },
      order: {
        saleItems: {
          offerAcceptedAt: 'DESC'
        }
      },
    })

    if (!sales || sales.length === 0) {
      throw new NotFoundException('No sales items found for this IMEI number. Please check the IMEI number and try again.')
    }

    return sales.map(sale => {
      const matchingItem = sale.saleItems.find(item => item.imei === imei);

      return {
        id: sale.id,
        saleNumber: matchingItem?.stockId,
        channelId: sale.channelId,
        imei: matchingItem?.imei,
        price: matchingItem?.price,
        type: matchingItem?.type,
        country: sale.address?.countryId,
        purchaseDate: matchingItem?.offerAcceptedAt
      };
    });
  }

  async createShipment(id: string, override = false) {
    const sale = await SaleEntity.findOne({
      where: { id },
      relations: { user: true, address: true, shipment: true, partner: true },
    })
    if (!sale) {
      throw new NotFoundException('Sale not found')
    }

    if (!sale.user || !sale.address) {
      throw new BadRequestException('Sale must have user and address information')
    }

    if (!sale.saleNumber) {
      throw new BadRequestException('Sale must have a sale number')
    }

    if (!sale.channelId) {
      throw new BadRequestException('Sale must have channel information')
    }

    if (override && sale.shipment?.parcelId) {
      await cancelParcel(sale.shipment.parcelId).catch(console.error)
    }

    const shipment = await this.shipmentService.create({
      type: ShipmentType.SALE,
      orderNumber: sale.saleNumber,
      customerEmail: sale.user.email,
      customerAddress: sale.address,
      isReturn: true,
      channelId: sale.channelId,
      partnerId: sale.partnerId,
      saleId: sale.id,
    })
    if (shipment) {
      await this.saleService.sendSellerSaleEmail(sale.id)
    }
  }
}
