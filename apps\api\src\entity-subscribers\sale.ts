import { Injectable } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import { cancelParcel } from '@valyuu/sendcloud'
import { format } from 'date-fns'
import { isEqual, omit, pick } from 'lodash'
import ms from 'ms'
import { ClsService } from 'nestjs-cls'
import { I18nService } from 'nestjs-i18n'
import * as pMap from 'p-map'
import { TrelloClient } from 'trello.js'
import type { EntitySubscriberInterface, UpdateEvent } from 'typeorm'
import { DataSource, In } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { envConfig } from '~/configs'
import {
  ChangeHistoryChangedByType,
  EmailType,
  LOCALE_ENABLED_LANGUAGES,
  PartnerWebhookEntityType,
  PartnerWebhookEvent,
  ProductModelSellerQuestionType,
  SaleItemPlanType,
  SaleItemStatus,
  SalePaymentType,
  SaleShippingLabelType,
  SaleStatus,
} from '~/constants'
import {
  CharityEntity,
  EmailHistoryEntity,
  PartnerEntity,
  QuestionTypeConditionEntity,
  QuestionTypeConditionOptionEntity,
  QuestionTypeProblemEntity,
  SaleEntity,
  SaleHistoryEntity,
} from '~/entities'
import { ClsStoreType } from '~/interfaces'
import { PartnerWebhookService, SaleItemOfferService } from '~/services'
import { formatMoney, getEmailTemplatePrefix, sendmail } from '~/utils'

const trello = new TrelloClient({ key: envConfig.TRELLO_KEY, token: envConfig.TRELLO_TOKEN })

// Fields that will be omitted from change detection
const CHANGE_LOG_OMITTED_FIELDS = [
  'id',
  'createdAt',
  'updatedAt',
  'note',
  'version',
  // Relations
  'saleItems',
  'bankAccount',
  'address',
  'channel',
  'user',
  'language',
  'partner',
  'shipment',
  'histories',
] as const

@Injectable()
@EventSubscriber()
export class SaleSubscriber implements EntitySubscriberInterface<SaleEntity> {
  constructor(
    private readonly i18n: I18nService,
    private readonly cls: ClsService<ClsStoreType>,
    private readonly partnerWebhookService: PartnerWebhookService,
    private readonly saleItemOfferService: SaleItemOfferService,
    private readonly dataSource?: DataSource
  ) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return SaleEntity
  }

  async beforeUpdate(event: UpdateEvent<SaleEntity>) {
    const { manager, databaseEntity, entity } = event

    if (!databaseEntity || !entity) {
      return
    }

    // Set receivedAt to current date
    if (
      [SaleStatus.SUBMITTED, SaleStatus.IN_TRANSIT].includes(databaseEntity.status) &&
      entity.status === SaleStatus.RECEIVED
    ) {
      entity.receivedAt = new Date()
    }

    // Prevent isLabelSent to be overwritten manually
    if (databaseEntity.shippingLabel === SaleShippingLabelType.EMAIL && databaseEntity.isLabelSent) {
      entity.isLabelSent = databaseEntity.isLabelSent
    }

    const previousState = omit(databaseEntity, CHANGE_LOG_OMITTED_FIELDS)
    const currentState = omit(entity, CHANGE_LOG_OMITTED_FIELDS)

    // Check if any of the tracked fields have changed
    const hasChanges = !isEqual(previousState, currentState)

    if (hasChanges) {
      let changedByType: ChangeHistoryChangedByType = ChangeHistoryChangedByType.SYSTEM
      let changedById: string
      let adminUserId: string
      let partnerId: string

      try {
        adminUserId = this.cls.get('adminUserId')
        partnerId = this.cls.get('partnerId')
      } catch {
        Sentry.captureException(new Error('Error getting adminUserId or partnerId from cls'), {
          tags: {
            type: 'sale:update',
          },
          extra: {
            sale: entity,
          },
          level: 'warning',
        })
      }

      if (adminUserId) {
        changedById = adminUserId
        changedByType = ChangeHistoryChangedByType.ADMIN
      } else if (partnerId) {
        changedById = partnerId
        changedByType = ChangeHistoryChangedByType.PARTNER
      }

      // Save to SaleHistoryEntity
      await manager.getRepository(SaleHistoryEntity).upsert(
        {
          saleId: databaseEntity.id,
          version: databaseEntity.version,
          changedByType,
          changedById,
          // Copy all relevant fields from the previous state
          saleNumber: databaseEntity.saleNumber,
          status: databaseEntity.status,
          shippingLabel: databaseEntity.shippingLabel,
          isLabelSent: databaseEntity.isLabelSent,
          trackingNumber: databaseEntity.trackingNumber,
          parcelId: databaseEntity.parcelId,
          shipmentId: databaseEntity.shipmentId,
          paymentType: databaseEntity.paymentType,
          bankAccountId: databaseEntity.bankAccountId,
          addressId: databaseEntity.addressId,
          userId: databaseEntity.userId,
          languageId: databaseEntity.languageId,
          from: databaseEntity.from,
          reminderSentCount: databaseEntity.reminderSentCount,
          surveySent: databaseEntity.surveySent,
          note: databaseEntity.note,
          receivedAt: databaseEntity.receivedAt,
        },
        ['saleId', 'version']
      )

      entity.__saleUpdated = true

      entity.version = databaseEntity.version + 1
    }
  }

  async afterUpdate(event: UpdateEvent<SaleEntity>) {
    const { entity, databaseEntity, manager } = event

    if (!entity || !databaseEntity) {
      return
    }

    const saleUpdated = entity.__saleUpdated ?? false
    delete entity.__saleUpdated

    // create trello card

    if (databaseEntity.status === SaleStatus.SUBMITTED && entity.status === SaleStatus.RECEIVED) {
      const sale = await manager.findOne(SaleEntity, {
        where: { id: entity.id },
        relations: {
          user: true,
          address: true,
          bankAccount: true,
          partner: true,
          saleItems: {
            productVariant: true,
          },
        },
      })

      const conditionIds: string[] = []
      const optionIds: string[] = []
      const problemIds: string[] = []
      sale.saleItems
        .filter((saleItem) => saleItem.answers.type === ProductModelSellerQuestionType.CONDITION)
        .forEach((saleItem) => {
          saleItem.answers.CONDITION.forEach(({ conditionId, optionId }) => {
            conditionIds.push(conditionId)
            optionIds.push(optionId)
          })
        })
      sale.saleItems
        .filter((saleItem) => saleItem.answers.type === ProductModelSellerQuestionType.PROBLEM)
        .forEach((saleItem) => {
          saleItem.answers.PROBLEM.forEach((problemId) => {
            problemIds.push(problemId)
          })
        })

      const idToTextMap: Record<string, string> = {}

      if (conditionIds.length) {
        await manager
          .find(QuestionTypeConditionEntity, {
            where: { id: In(conditionIds) },
            select: {
              id: true,
              name__en: true,
            },
          })
          .then((conditions) => {
            conditions.forEach(({ id, name__en }) => {
              idToTextMap[id] = name__en
            })
          })
      }
      if (optionIds.length) {
        await manager
          .find(QuestionTypeConditionOptionEntity, {
            where: { id: In(optionIds) },
            select: {
              id: true,
              name__en: true,
            },
          })
          .then((options) => {
            options.forEach(({ id, name__en }) => {
              idToTextMap[id] = name__en
            })
          })
      }
      if (problemIds.length) {
        await manager
          .find(QuestionTypeProblemEntity, {
            where: { id: In(problemIds) },
            select: {
              id: true,
              name__en: true,
            },
          })
          .then((problems) => {
            problems.forEach(({ id, name__en }) => {
              idToTextMap[id] = name__en
            })
          })
      }

      await pMap(sale.saleItems ?? [], async (saleItem) => {
        if (saleItem.trelloId) {
          return
        }
        const isReturning = sale.isReturningCustomer

        const itemCount = sale.saleItems.length
        let description = `DEPRECATED, PLEASE USE REFINE TO HANDLE THIS |${sale.user.from} | ${sale.address.countryId} | ${sale.address.firstName} ${sale.address.lastName} | Received: ${format(new Date(), 'dd/MM/yyyy')}`
        let name =
          `${sale.saleNumber} (${itemCount === 1 ? '1 item' : `${itemCount} items`}) | ` +
          description +
          ' | ' +
          saleItem.stockId
        description =
          `${format(saleItem.createdAt, 'dd/MM/yyyy')} | ` + description + ` | ${sale.user.email} ${sale.saleNumber}\n`
        if (sale.note) {
          description += '\n' + sale.note
        }
        description += `\n${saleItem.productVariant.name__en}    ${saleItem.type}${sale.paymentType === SalePaymentType.DONATION ? '    donation\n' : ''}\nStock ID    ${saleItem.stockId}`

        switch (saleItem.answers.type) {
          case ProductModelSellerQuestionType.CONDITION:
            for (const row of saleItem.answers.CONDITION) {
              description += '\n' + idToTextMap[row.conditionId] + '    ' + idToTextMap[row.optionId]
            }
            break
          case ProductModelSellerQuestionType.PROBLEM:
            description +=
              "\nWhat's the issue?    " + saleItem.answers.PROBLEM.map((key) => idToTextMap[key]).join(', ')
            break
        }
        description += '\nEstimated price    ' + formatMoney(saleItem.price, 'EUR', 'nl-NL')

        const card = await trello.cards.createCard({ name, desc: description, idList: process.env.TRELLO_LIST })
        if (sale.paymentType === SalePaymentType.DONATION) {
          await trello.cards.addCardLabel({ id: card.id, value: process.env.TRELLO_LABEL_DONATION })
        }
        if (saleItem.type === SaleItemPlanType.C2C) {
          await trello.cards.addCardLabel({ id: card.id, value: process.env.TRELLO_LABEL_C2C })
        } else if (saleItem.type === SaleItemPlanType.C2B) {
          await trello.cards.addCardLabel({ id: card.id, value: process.env.TRELLO_LABEL_C2B })
        }
        if (isReturning) {
          await trello.cards.addCardLabel({ id: card.id, value: process.env.TRELLO_LABEL_RETURNING })
        }

        saleItem.trelloId = card.id
        await manager.save(saleItem)
      })
    }

    const id = databaseEntity.id ?? entity.id

    // send email
    if (
      id &&
      [SaleStatus.SUBMITTED, SaleStatus.IN_TRANSIT].includes(databaseEntity.status) &&
      entity.status === SaleStatus.RECEIVED
    ) {
      const sale = await manager.findOne(SaleEntity, {
        where: { id },
        relations: {
          user: true,
          address: true,
          bankAccount: true,
          partner: true,
          saleItems: {
            productVariant: true,
          },
        },
      })

      const lang = sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]

      const { manageCustomerEmails } = (await manager.findOne(PartnerEntity, {
        where: { id: sale.partnerId },
        select: { id: true, manageCustomerEmails: true },
        cache: envConfig.isProd ? ms('1 hour') : false,
      })) ?? { manageCustomerEmails: true }

      const channelId = sale.channelId
      if ((!sale.partnerId || manageCustomerEmails) && !sale.isLegacy) {
        let paymentPeriodC2b: string
        let paymentPeriodC2c: string
        let paymentPeriodDonation: string
        const paymentType = sale.paymentType

        if (paymentType === SalePaymentType.DONATION) {
          paymentPeriodDonation = await this.saleItemOfferService.getPaymentPeriod({
            channelId,
            plan: SaleItemPlanType.C2B,
            lang,
            paymentType: sale.paymentType,
          })
        } else {
          await pMap(sale.saleItems, async (saleItem) => {
            const plan = saleItem.type
            if (plan === SaleItemPlanType.C2B && !paymentPeriodC2b) {
              paymentPeriodC2b = await this.saleItemOfferService.getPaymentPeriod({
                channelId,
                plan,
                lang,
                paymentType,
              })
            }
            if (plan === SaleItemPlanType.C2C && !paymentPeriodC2c) {
              paymentPeriodC2c = await this.saleItemOfferService.getPaymentPeriod({
                channelId,
                plan,
                lang,
                paymentType,
              })
            }
          })
        }

        let charityName: string = undefined
        let iban: string = undefined
        if (sale.paymentType === SalePaymentType.DONATION) {
          // TODO: get charity name from sale
          const charity = await CharityEntity.findOneOrFail({ where: {} })
          charityName = charity[`name__${sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]}`]
        } else {
          iban = sale.paymentType === SalePaymentType.BANK_TRANSFER ? sale.bankAccount?.accountNumber : undefined
        }

        const data = {
          firstName: sale.address.firstName,
          saleNumber: sale.saleNumber,
          paymentPeriodC2b,
          paymentPeriodC2c,
          paymentPeriodDonation,
          iban,
          charityName,
        }

        // send email

        try {
          const subject = this.i18n.t('emails.sellerPackageReceivedSubject', {
            lang,
            args: { saleNumber: sale.saleNumber },
          })
          const templatePrefix = await getEmailTemplatePrefix({
            isPartner: !!sale.partnerId,
            partnerSlug: sale.partner?.slug,
            type: EmailType.SELLER_PACKAGE_RECEIVED,
            lang,
          })
          await sendmail(
            {
              type: EmailType.SELLER_PACKAGE_RECEIVED,
              lang,
              to: sale.user.email,
              subject,
              data,
              templatePrefix,
            },
            3
          )
          await manager.save(
            EmailHistoryEntity.create({
              email: sale.user.email,
              subject,
              userId: sale.user.id,
              type: EmailType.SELLER_PACKAGE_RECEIVED,
              relatedIds: [sale.id, ...sale.saleItems.map(({ id }) => id)],
              lang,
              data,
              templatePrefix,
            })
          )
        } catch (error) {
          const message = 'Error sending mail'
          console.error(message)
          Sentry.captureException(error, {
            tags: {
              type: 'sale:sendSellerPackageReceivedEmail',
            },
            extra: {
              data,
            },
          })
        }
      }
    }

    // Cancel parcel
    if (databaseEntity.status !== SaleStatus.CANCELLED && entity.status === SaleStatus.CANCELLED) {
      const sale = await manager.findOne(SaleEntity, {
        where: { id: entity.id },
        relations: {
          saleItems: true,
          shipment: true,
        },
      })
      for (const saleItem of sale.saleItems) {
        saleItem.status = SaleItemStatus.CANCELLED
        await manager.save(saleItem)
      }
      if (sale.shipment?.parcelId) {
        await cancelParcel(sale.shipment.parcelId).catch(() => {})
      }
    }

    // partner webhook
    if (id) {
      if (saleUpdated) {
        const sale = await manager.findOne(SaleEntity, {
          where: { id },
          select: { partnerId: true, version: true },
        })
        if (sale?.partnerId) {
          this.partnerWebhookService.callWebhook({
            event: PartnerWebhookEvent.TRADE_IN_UPDATED,
            entityType: PartnerWebhookEntityType.TRADE_IN,
            entityId: id,
            version: sale.version,
            partnerId: sale.partnerId,
          })
        }
      }
    }
  }
}
