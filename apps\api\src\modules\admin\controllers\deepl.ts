import { Body, Controller, Post, UseGuards } from '@nestjs/common'

import { deeplTranslate, TranslateOptions } from '~/utils'

import { AdminJwtAuthGuard } from '../guards'

@UseGuards(AdminJwtAuthGuard)
@Controller('admin/deepl')
export class DeeplController {
  @Post('translate')
  translate(
    @Body()
    { text, target, source }: TranslateOptions
  ) {
    return deeplTranslate({ text, target, source })
  }
}
