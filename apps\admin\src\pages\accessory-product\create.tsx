import { Create, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useList } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type { IAccessoryProduct, IChannel } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber, Watch } from 'antx'
import cx from 'clsx'
import { type FC, Fragment, useEffect, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputImage, InputLanguageTab, InputMultiLang, InputPublish, LanguageTabChoices } from '~/components'
import { formatCurrencySymbol } from '~/utils'

export const AccessoryProductCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<IAccessoryProduct, HttpError, IAccessoryProduct>()
  const id = useMemo(() => uuid(), [])
  const publishedAt = Form.useWatch('publishedAt', form)

  const { data: { data: channels = [] } = {} } = useList<IChannel, HttpError, IChannel>({
    resource: 'admin/channels',
    filters: [{ field: 'disableBuyer', operator: 'eq', value: false }],
    pagination: { mode: 'off' },
  })

  const [priceItems, setPriceItems] = useState<IAccessoryProduct['prices']>([])

  useEffect(() => {
    if (channels.length) {
      const prices: IAccessoryProduct['prices'] = []
      setPriceItems(
        channels.map((channel) => {
          prices.push({ id: uuid(), channelId: channel.id, currencyId: channel.currencyId })
          return {
            id: uuid(),
            desc: channel.desc,
            channelId: channel.id,
            currencyId: channel.currencyId,
            currencySymbol: formatCurrencySymbol(channel.currencyId),
          }
        })
      )
      form.setFieldValue('prices', prices)
    }
  }, [form, channels])

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Input label="Internal name" name="internalName" rules={['required']} required />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl', 'desc__en', 'desc__nl', 'desc__de', 'desc__pl']}>
          {([name__en, name__nl, name__de, name__pl, desc__en, desc__nl, desc__de, desc__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="duplicate"
              />
              <InputMultiLang
                element="textarea"
                sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                targetLang="en"
                label="Description (English)"
                name="desc__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                element="textarea"
                sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                targetLang="nl"
                label="Description (Dutch)"
                name="desc__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                element="textarea"
                sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                targetLang="de"
                label="Description (German)"
                name="desc__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                element="textarea"
                sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                targetLang="pl"
                label="Description (Polish)"
                name="desc__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
              <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
              <InputImage
                label="Image"
                name="heroImage"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className="col-span-2"
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.ACCESSORY_PRODUCT}
                processName={ImageProcessNameType.COPY}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
            </>
          )}
        </Watch>

        {priceItems.map((channel, index) => {
          return (
            <Fragment key={channel.channelId}>
              <Input type="hidden" name={['prices', index, 'id']} hidden noStyle />
              <Input type="hidden" name={['prices', index, 'channelId']} hidden noStyle />
              <Input type="hidden" name={['prices', index, 'currencyId']} hidden noStyle />
              <InputNumber
                label={
                  <span>
                    Price for channel{' '}
                    <span className="underline decoration-dashed" title={channel.desc}>
                      {channel.channelId}
                    </span>
                  </span>
                }
                name={['prices', index, 'price']}
                min={0}
                prefix={channel.currencySymbol}
                precision={2}
                controls={false}
                rules={['required']}
                required
              />
            </Fragment>
          )
        })}
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
