import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleItemOfferAcceptedAt1733019618293 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new column allowing null initially
    await queryRunner.query(`
      ALTER TABLE sale_item 
      ADD COLUMN IF NOT EXISTS offer_accepted_at TIMESTAMP
    `)

    // Update existing records to use created_at value
    await queryRunner.query(`
      UPDATE sale_item 
      SET offer_accepted_at = created_at 
      WHERE offer_accepted_at IS NULL
    `)

    // Make the column not nullable
    await queryRunner.query(`
      ALTER TABLE sale_item 
      ALTER COLUMN offer_accepted_at SET NOT NULL
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
