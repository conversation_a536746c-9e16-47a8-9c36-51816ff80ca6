import { <PERSON><PERSON>, Crud<PERSON>ontroller } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { OrderAccessoryProductItemEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudOrderAccessoryProductItemService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: OrderAccessoryProductItemEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      channel: {},
      currency: {},
      order: {},
      accessoryProduct: {},
    },
  },
})
@Controller('admin/order-accessory-product-items')
export class CrudOrderAccessoryProductItemController implements CrudController<OrderAccessoryProductItemEntity> {
  constructor(public service: CrudOrderAccessoryProductItemService) {}
}
