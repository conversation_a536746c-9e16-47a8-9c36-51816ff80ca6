import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { LocaleCurrencyEntity } from '~/entities'

export class CrudLocaleCurrencyService extends TypeOrmCrudService<LocaleCurrencyEntity> {
  constructor(
    @InjectRepository(LocaleCurrencyEntity)
    repo: Repository<LocaleCurrencyEntity>
  ) {
    super(repo)
  }
}
