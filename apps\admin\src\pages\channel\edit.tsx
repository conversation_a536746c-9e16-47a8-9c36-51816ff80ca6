import { Edit, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useUpdate } from '@refinedev/core'
import { ChannelType, SellerPaymentPeriodUnit } from '@valyuu/api/constants'
import type { IChannel, ILocaleCountry, ILocaleCurrency } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, InputNumber, RadioGroup, Select } from 'antx'
import { type FC } from 'react'

import { InputMultiEntitySelect, InputPublish } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const ChannelEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, id, saveButtonProps, query, formLoading } = useForm<IChannel, HttpError, IChannel>({
    meta: {
      join: [{ field: 'countries' }, { field: 'sellerPaymentPeriod' }, { field: 'priceThreshold' }],
    },
  })

  const record = query?.data?.data
  if (formProps.initialValues?.publishedAt) {
    formProps.initialValues.publishedAt = new Date(formProps.initialValues.publishedAt)
  }
  const { mutate } = useUpdate<IChannel, HttpError, Partial<IChannel>>()

  const { selectProps: currencySelectProps, queryResult: currencyQueryResult } = useSelect<ILocaleCurrency>({
    resource: 'admin/currencies',
    optionLabel: 'id',
    meta: {
      fields: ['id', 'symbol'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: countrySelectProps } = useSelect<ILocaleCountry>({
    resource: 'admin/countries',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const disableSeller = Form.useWatch('disableSeller', form)
  const currencyId = Form.useWatch('currencyId', form)

  const selectedCurrency = currencyQueryResult?.data?.data?.find((currency) => currency.id === currencyId) as
    | ILocaleCurrency
    | undefined

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!record?.publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
            mutate({
              resource: 'admin/channels',
              id: id! as string,
              values: {
                publishedAt: value ? new Date() : null,
              },
              successNotification: () => {
                return {
                  type: 'success',
                  message: `Channel has been ${value ? 'published' : 'unpublished'} successfully`,
                }
              },
              errorNotification: () => {
                return {
                  type: 'error',
                  message: `Failed to ${value ? 'publish' : 'unpublish'} channel`,
                }
              },
            })
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form {...formProps} layout="vertical">
        <Input label="ID" name="id" rules={['required']} disabled />
        <Input label="Name" name="name" rules={['required']} />
        <Input label="Description" name="desc" rules={['required']} />
        <Select label="Currency" name="currencyId" rules={['required']} {...(currencySelectProps as any)} />
        <InputMultiEntitySelect
          label="Countries"
          name="countries"
          rules={['required']}
          {...(countrySelectProps as any)}
        />
        <Select label="Type" name="type" rules={['required']} options={formatSelectOptionsFromEnum(ChannelType)} />
        <RadioGroup
          label="Disable buyer"
          name="disableBuyer"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
        />
        <RadioGroup
          label="Disable seller"
          name="disableSeller"
          options={[
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          rules={['required']}
        />

        <Input noStyle type="hidden" name={['sellerPaymentPeriod', 'id']} value={record?.sellerPaymentPeriod?.id} />
        {!disableSeller && (
          <>
            <Input noStyle type="hidden" name={['priceThreshold', 'id']} value={record?.priceThreshold?.id} />
            <Input noStyle type="hidden" name={['priceThreshold', 'currencyId']} value={currencyId} />
            <InputNumber
              label="Price change threshold"
              precision={0}
              min={0}
              rules={['required']}
              name={['priceThreshold', 'priceChangeThreshold']}
              prefix={selectedCurrency?.symbol}
            />
            <InputNumber
              label="Recycle threshold"
              precision={0}
              min={0}
              rules={['required']}
              name={['priceThreshold', 'recycleThreshold']}
              prefix={selectedCurrency?.symbol}
            />
            <Form.Item label="C2B payment period" required>
              <div className="flex items-baseline gap-2">
                <InputNumber label="from" name={['sellerPaymentPeriod', 'c2bFrom']} precision={0} min={0} />
                <InputNumber
                  label="to"
                  name={['sellerPaymentPeriod', 'c2bTo']}
                  precision={0}
                  min={0}
                  rules={['required']}
                />
                <Select
                  label="unit"
                  options={formatSelectOptionsFromEnum(SellerPaymentPeriodUnit)}
                  name={['sellerPaymentPeriod', 'c2bUnit']}
                  rules={['required']}
                  required
                />
              </div>
            </Form.Item>
            <Form.Item label="C2C payment period" required>
              <div className="flex items-baseline gap-2">
                <InputNumber label="from" name={['sellerPaymentPeriod', 'c2cFrom']} precision={0} min={0} />
                <InputNumber
                  label="to"
                  name={['sellerPaymentPeriod', 'c2cTo']}
                  precision={0}
                  min={0}
                  rules={['required']}
                />
                <Select
                  label="unit"
                  options={formatSelectOptionsFromEnum(SellerPaymentPeriodUnit)}
                  name={['sellerPaymentPeriod', 'c2cUnit']}
                  rules={['required']}
                  required
                />
              </div>
            </Form.Item>
            <Form.Item label="Donation payment period" required>
              <div className="flex items-baseline gap-2">
                <InputNumber label="from" name={['sellerPaymentPeriod', 'donationFrom']} precision={0} min={0} />
                <InputNumber
                  label="to"
                  name={['sellerPaymentPeriod', 'donationTo']}
                  precision={0}
                  min={0}
                  rules={['required']}
                />
                <Select
                  label="unit"
                  options={formatSelectOptionsFromEnum(SellerPaymentPeriodUnit)}
                  name={['sellerPaymentPeriod', 'donationUnit']}
                  rules={['required']}
                  required
                />
              </div>
            </Form.Item>
          </>
        )}
        <Input noStyle type="datetime" name="publishedAt" hidden />
      </Form>
    </Edit>
  )
}
