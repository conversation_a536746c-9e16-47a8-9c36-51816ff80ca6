import { ProCard, ProForm, ProFormList } from '@ant-design/pro-components'
import { Create, useForm } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { IQuestionType } from '@valyuu/api/entities'
import { Input, InputNumber, TextArea, Watch } from 'antx'
import cx from 'clsx'
import { FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputLanguageTab, InputMultiLang, InputPublish, LanguageTabChoices } from '~/components'

export const QuestionTypeCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<IQuestionType, HttpError, IQuestionType>()
  const id = useMemo(() => uuid(), [])
  const publishedAt = ProForm.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <ProForm
        {...formProps}
        onFinish={async (values) => {
          formProps.onFinish?.(values)
        }}
        submitter={false}
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
        layout="vertical"
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Input label="Internal name" name="internalName" rules={['required']} required />
        <ProFormList
          required
          actionGuard={{
            beforeAddRow: async (defaultValue) => {
              defaultValue.id = uuid()
              return true
            },
          }}
          rules={[
            {
              validator: async (_, value) => {
                if ((value || []).length === 0) {
                  return Promise.reject(new Error('At least one condition is required'))
                }
              },
            },
          ]}
          name="conditions"
          label="Condition types"
          creatorButtonProps={{
            creatorButtonText: 'Add a new condition type',
          }}
          copyIconProps={{
            tooltipText: 'Duplicate this condition type',
          }}
          deleteIconProps={{
            tooltipText: 'Delete this condition type',
          }}
          itemRender={({ listDom, action }, { index }) => {
            return (
              <ProCard
                bordered
                extra={action}
                className="mb-3"
                title={<span className="list-item-title">Condition type {index + 1}</span>}
              >
                {listDom}
              </ProCard>
            )
          }}
        >
          {({ name: conditionName }, conditionIndex) => {
            return (
              <>
                <Input type="hidden" name={[conditionName, 'id']} hidden noStyle />
                <Watch
                  list={[
                    ['conditions', conditionName, 'name__en'],
                    ['conditions', conditionName, 'name__nl'],
                    ['conditions', conditionName, 'name__de'],
                    ['conditions', conditionName, 'name__pl'],
                  ]}
                >
                  {([name__en, name__nl, name__de, name__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="en"
                        label="Name (English)"
                        name={[conditionName, 'name__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="nl"
                        label="Name (Dutch)"
                        name={[conditionName, 'name__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="de"
                        label="Name (German)"
                        name={[conditionName, 'name__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="pl"
                        label="Name (Polish)"
                        name={[conditionName, 'name__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
                <Input
                  label="Key"
                  name={[conditionName, 'key']}
                  required
                  rules={[
                    'required',
                    {
                      validator: async (_, value) => {
                        const conditions = form.getFieldValue(['conditions'])
                        let hasDuplicate = false
                        conditions.forEach((condition: { key: string | number }, index: number) => {
                          if (index !== conditionIndex && condition.key === value) {
                            hasDuplicate = true
                          }
                        })
                        return hasDuplicate ? Promise.reject(new Error('Key must be unique')) : Promise.resolve()
                      },
                    },
                  ]}
                  normalize={(value) => value.replace(/[^a-zA-Z]/g, '').toUpperCase()}
                />
                <Watch
                  list={[
                    ['conditions', conditionName, 'question__en'],
                    ['conditions', conditionName, 'question__nl'],
                    ['conditions', conditionName, 'question__de'],
                    ['conditions', conditionName, 'question__pl'],
                  ]}
                >
                  {([question__en, question__nl, question__de, question__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="en"
                        label="Question (English)"
                        name={[conditionName, 'question__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="nl"
                        label="Question (Dutch)"
                        name={[conditionName, 'question__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="de"
                        label="Question (German)"
                        name={[conditionName, 'question__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                        targetLang="pl"
                        label="Question (Polish)"
                        name={[conditionName, 'question__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
                <InputNumber
                  label="Sort order"
                  precision={0}
                  name={[conditionName, 'sortOrder']}
                  rules={['required']}
                  initialValue={10}
                />
                <TextArea
                  label="Description (English)"
                  name={[conditionName, 'desc__en']}
                  rows={3}
                  className={clsHideEn}
                />
                <TextArea
                  label="Description (Dutch)"
                  name={[conditionName, 'desc__nl']}
                  rows={3}
                  className={clsHideNl}
                />
                <TextArea
                  label="Description (German)"
                  name={[conditionName, 'desc__de']}
                  rows={3}
                  className={clsHideDe}
                />
                <TextArea
                  label="Description (Polish)"
                  name={[conditionName, 'desc__pl']}
                  rows={3}
                  className={clsHidePl}
                />
                <Input noStyle name={[conditionName, 'questionTypeId']} hidden />
                <ProFormList
                  required
                  actionGuard={{
                    beforeAddRow: async (defaultValue) => {
                      defaultValue.id = uuid()
                      return true
                    },
                  }}
                  rules={[
                    {
                      validator: async (_, value) => {
                        if ((value || []).length === 0) {
                          return Promise.reject(new Error('At least one option is required'))
                        }
                      },
                    },
                  ]}
                  name="options"
                  label="Options"
                  creatorButtonProps={{
                    creatorButtonText: 'Add a new option',
                  }}
                  copyIconProps={{
                    tooltipText: 'Duplicate this option',
                  }}
                  deleteIconProps={{
                    tooltipText: 'Delete this option',
                  }}
                  itemRender={({ listDom, action }, { index }) => {
                    return (
                      <ProCard
                        bordered
                        extra={action}
                        className="mb-3"
                        title={
                          <span className="list-item-title">
                            Condition {conditionIndex + 1} option {index + 1}
                          </span>
                        }
                      >
                        {listDom}
                      </ProCard>
                    )
                  }}
                >
                  {({ name: optionName }, optionIndex) => {
                    return (
                      <>
                        <Input type="hidden" name={[optionName, 'id']} hidden noStyle />
                        <Watch
                          list={[
                            ['conditions', conditionName, 'options', optionName, 'name__en'],
                            ['conditions', conditionName, 'options', optionName, 'name__nl'],
                            ['conditions', conditionName, 'options', optionName, 'name__de'],
                            ['conditions', conditionName, 'options', optionName, 'name__pl'],
                          ]}
                        >
                          {([name__en, name__nl, name__de, name__pl]) => (
                            <>
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="en"
                                label="Name (English)"
                                name={[optionName, 'name__en']}
                                rules={['required']}
                                className={clsHideEn}
                                fillType="translate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="nl"
                                label="Name (Dutch)"
                                name={[optionName, 'name__nl']}
                                rules={['required']}
                                className={clsHideNl}
                                fillType="translate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="de"
                                label="Name (German)"
                                name={[optionName, 'name__de']}
                                rules={['required']}
                                className={clsHideDe}
                                fillType="translate"
                              />
                              <InputMultiLang
                                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                                targetLang="pl"
                                label="Name (Polish)"
                                name={[optionName, 'name__pl']}
                                rules={['required']}
                                className={clsHidePl}
                                fillType="translate"
                              />
                            </>
                          )}
                        </Watch>
                        <Input
                          label="Key"
                          name={[optionName, 'key']}
                          required
                          rules={[
                            'required',
                            {
                              validator: async (_, value) => {
                                // @ts-expect-error antd's typing is problematic, it doesn't read the nested array
                                const options = form.getFieldValue(['conditions', conditionName, 'options'])
                                let hasDuplicate = false
                                options.forEach((option: { key: string | number }, index: number) => {
                                  if (index !== optionIndex && option.key === value) {
                                    hasDuplicate = true
                                  }
                                })
                                return hasDuplicate
                                  ? Promise.reject(new Error('Key must be unique'))
                                  : Promise.resolve()
                              },
                            },
                          ]}
                          normalize={(value) => value.replace(/[^a-zA-Z]/g, '').toUpperCase()}
                        />
                        <InputNumber
                          label="Percent of choice"
                          precision={0}
                          name={[optionName, 'percentOfChoice']}
                          min={0}
                          max={100}
                          addonAfter="%"
                        />
                        <InputNumber
                          label="Sort order"
                          precision={0}
                          name={[optionName, 'sortOrder']}
                          rules={['required']}
                          initialValue={10}
                        />
                        <TextArea label="Description (English)" name={[optionName, 'desc__en']} className={clsHideEn} />
                        <TextArea label="Description (Dutch)" name={[optionName, 'desc__nl']} className={clsHideNl} />
                        <TextArea label="Description (German)" name={[optionName, 'desc__de']} className={clsHideDe} />
                        <TextArea label="Description (Polish)" name={[optionName, 'desc__pl']} className={clsHidePl} />
                      </>
                    )
                  }}
                </ProFormList>
              </>
            )
          }}
        </ProFormList>
        <ProFormList
          required
          actionGuard={{
            beforeAddRow: async (defaultValue) => {
              defaultValue.id = uuid()
              return true
            },
          }}
          rules={[
            {
              validator: async (_, value) => {
                if ((value || []).length === 0) {
                  return Promise.reject(new Error('At least one problem is required'))
                }
              },
            },
          ]}
          name="problems"
          label="Problem types"
          creatorButtonProps={{
            creatorButtonText: 'Add a new problem type',
          }}
          copyIconProps={{
            tooltipText: 'Duplicate this problem type',
          }}
          deleteIconProps={{
            tooltipText: 'Delete this problem type',
          }}
          itemRender={({ listDom, action }, { index }) => {
            return (
              <ProCard
                bordered
                extra={action}
                className="mb-3"
                title={<span className="list-item-title">Problem {index + 1}</span>}
              >
                {listDom}
              </ProCard>
            )
          }}
        >
          {({ name: problemName }) => {
            return (
              <>
                <Input type="hidden" name={[problemName, 'id']} hidden noStyle />
                <Watch
                  list={[
                    ['problems', problemName, 'name__en'],
                    ['problems', problemName, 'name__nl'],
                    ['problems', problemName, 'name__de'],
                    ['problems', problemName, 'name__pl'],
                  ]}
                >
                  {([name__en, name__nl, name__de, name__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="en"
                        label="Name (English)"
                        name={[problemName, 'name__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="nl"
                        label="Name (Dutch)"
                        name={[problemName, 'name__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="de"
                        label="Name (German)"
                        name={[problemName, 'name__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                        targetLang="pl"
                        label="Name (Polish)"
                        name={[problemName, 'name__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
                <Watch
                  list={[
                    ['problems', problemName, 'desc__en'],
                    ['problems', problemName, 'desc__nl'],
                    ['problems', problemName, 'desc__de'],
                    ['problems', problemName, 'desc__pl'],
                  ]}
                >
                  {([desc__en, desc__nl, desc__de, desc__pl]) => (
                    <>
                      <InputMultiLang
                        sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                        targetLang="en"
                        element="textarea"
                        label="Description (English)"
                        name={[problemName, 'desc__en']}
                        rules={['required']}
                        className={clsHideEn}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                        targetLang="nl"
                        element="textarea"
                        label="Description (Dutch)"
                        name={[problemName, 'desc__nl']}
                        rules={['required']}
                        className={clsHideNl}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                        targetLang="de"
                        element="textarea"
                        label="Description (German)"
                        name={[problemName, 'desc__de']}
                        rules={['required']}
                        className={clsHideDe}
                        fillType="translate"
                      />
                      <InputMultiLang
                        sources={{ en: desc__en, nl: desc__nl, de: desc__de, pl: desc__pl }}
                        targetLang="pl"
                        element="textarea"
                        label="Description (Polish)"
                        name={[problemName, 'desc__pl']}
                        rules={['required']}
                        className={clsHidePl}
                        fillType="translate"
                      />
                    </>
                  )}
                </Watch>
                <Input
                  label="Key"
                  name={[problemName, 'key']}
                  required
                  rules={['required']}
                  normalize={(value) => value.replace(/[^a-zA-Z]/g, '').toUpperCase()}
                />
                <InputNumber
                  label="Sort order"
                  precision={0}
                  name={[problemName, 'sortOrder']}
                  rules={['required']}
                  initialValue={10}
                />
              </>
            )
          }}
        </ProFormList>
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </ProForm>
    </Create>
  )
}
