import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { SaleItemPlanType, SalePaymentType } from '~/constants'
import {
  ChannelEntity,
  type IBankAccount,
  type IChannel,
  type IPaymentList,
  OrganizationBankAccountEntity,
  PaymentListBulkItemEntity,
} from '~/entities'

@Entity('partner')
export class PartnerEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  name: string

  @Column({ unique: true })
  slug: string

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((partner: PartnerEntity) => partner.channel)
  channelId: string

  @Column({ default: () => "encode(gen_random_bytes(32), 'hex')" })
  secret: string

  @Index()
  @Column({ nullable: true })
  apiEndpointUrl?: string

  @Column({ nullable: true })
  standaloneSiteUrl?: string

  @Column({ type: 'varchar', array: true, default: [] })
  supportedPlanTypes: SaleItemPlanType[]

  @Column({ type: 'varchar', array: true, default: [] })
  supportedPaymentTypes: SalePaymentType[]

  @Column({ default: true })
  manageCustomerEmails: boolean

  @OneToOne(() => OrganizationBankAccountEntity, (bankAccount) => bankAccount.partner, { nullable: true })
  @JoinColumn()
  bankAccount: Relation<OrganizationBankAccountEntity>

  @Column({ nullable: true })
  @RelationId((partner: PartnerEntity) => partner.bankAccount)
  bankAccountId?: string

  @OneToMany(() => PaymentListBulkItemEntity, (paymentList) => paymentList.partner)
  paymentLists: PaymentListBulkItemEntity

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IPartner = Omit<PartnerEntity, keyof BaseEntity> & {
  channel: IChannel
  bankAccount: IBankAccount
  paymentLists: IPaymentList
}
