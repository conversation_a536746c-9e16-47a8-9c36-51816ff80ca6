import { IsPhoneNumber } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { type ILocaleCountry, LocaleCountryEntity } from '~/entities'

@Entity('warehouse')
export class WarehouseEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  companyName: string

  @Column({ nullable: true })
  contactName?: string

  @Column()
  email: string

  @ManyToOne(() => LocaleCountryEntity, { nullable: false })
  country: Relation<LocaleCountryEntity>

  @Column()
  @RelationId((warehouse: WarehouseEntity) => warehouse.country)
  countryId: string

  @Column()
  city: string

  @Column()
  postalCode: string

  @Column()
  @IsPhoneNumber()
  phone: string

  @Column()
  street: string

  @Column()
  houseNumber: string

  @ManyToMany(() => LocaleCountryEntity, { cascade: true })
  @JoinTable({
    name: 'warehouse_shipping_to_countries',
  })
  shippingToCountries: Relation<LocaleCountryEntity[]>

  @ManyToMany(() => LocaleCountryEntity, { cascade: true })
  @JoinTable({
    name: 'warehouse_receiving_from_countries',
  })
  receivingFromCountries: Relation<LocaleCountryEntity[]>

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IWarehouse = Omit<WarehouseEntity, keyof BaseEntity> & {
  country: ILocaleCountry
  shippingToCountries: ILocaleCountry[]
  receivingFromCountries: ILocaleCountry[]
}
