import { DateF<PERSON>, EditButton, FilterDropdown, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IBankAccount } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { Input } from 'antx'
import type { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const BankAccountList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IBankAccount>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'userId', 'holderName', 'accountNumber', 'createdAt'],
      join: [
        {
          field: 'user',
          select: ['id', 'email'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/bank-accounts', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex={['user', 'email']}
          title="User"
          className="cursor-pointer"
          align="center"
          defaultFilteredValue={getDefaultFilter('user.email', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 280 }} placeholder="User email" normalize={(value) => value.trim()} />
            </FilterDropdown>
          )}
          width="7rem"
        />
        <Table.Column dataIndex="holderName" title="Beneficiary name" className="cursor-pointer" />
        <Table.Column
          dataIndex="accountNumber"
          title="IBAN number"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('accountNumber', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 280 }} placeholder="IBAN number" normalize={(value) => value.trim()} />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IBankAccount>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
