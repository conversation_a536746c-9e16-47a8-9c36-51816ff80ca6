import { CrudRequest, GetManyDefaultResponse } from '@dataui/crud'
import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { FaqEntity } from '~/entities'

const findAndRemoveCollectionsSearch = (obj: any, result: string[] = []): string[] => {
  if (Array.isArray(obj)) {
    obj.forEach((item) => findAndRemoveCollectionsSearch(item, result))
  } else if (typeof obj === 'object' && obj !== null) {
    for (const key in obj) {
      if (key === 'collections' && obj[key].$eq && !result.includes(obj[key].$eq)) {
        result.push(obj[key].$eq)
        delete obj[key]
      } else {
        findAndRemoveCollectionsSearch(obj[key], result)
      }
    }
  }
  return result
}

export class CrudFaqService extends TypeOrmCrudService<FaqEntity> {
  constructor(@InjectRepository(FaqEntity) repo: Repository<FaqEntity>) {
    super(repo)
  }

  // FIXME: ugly hack to fix collections filter, because searching for an varchar array is not supported by the library
  public async getMany(req: CrudRequest): Promise<GetManyDefaultResponse<FaqEntity> | FaqEntity[]> {
    const { parsed, options } = req

    const collections = findAndRemoveCollectionsSearch(parsed.search)
    const builder = await this.createBuilder(parsed, options)
    if (collections.length) {
      builder.andWhere(`${this.getFieldWithAlias('collections')} @> ARRAY[:...collections]::character varying[]`, {
        collections,
      })
    }
    return this.doGetMany(builder, parsed, options)
  }
}
