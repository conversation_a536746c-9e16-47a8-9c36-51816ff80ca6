import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IProductModel,
  type IProductVariant,
  type IQuestionTypeCondition,
  type IQuestionTypeProblem,
  type IQuestionTypeProblemImageText,
  ProductModelEntity,
  ProductVariantEntity,
  QuestionTypeConditionEntity,
  QuestionTypeImageTextEntity,
  QuestionTypeProblemEntity,
} from '~/entities'

@ObjectType('QuestionType')
@Entity('question_type')
export class QuestionTypeEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  internalName: string

  @Field(() => [QuestionTypeConditionEntity])
  @OneToMany(() => QuestionTypeConditionEntity, (condition) => condition.questionType, {
    nullable: false,
    cascade: true,
  })
  conditions: Relation<QuestionTypeConditionEntity>[]

  @Field(() => [QuestionTypeProblemEntity])
  @OneToMany(() => QuestionTypeProblemEntity, (problem) => problem.questionType, {
    nullable: false,
    cascade: true,
  })
  problems: Relation<QuestionTypeProblemEntity>[]

  @Field(() => [QuestionTypeImageTextEntity])
  @OneToMany(() => QuestionTypeImageTextEntity, (problemGroupImageText) => problemGroupImageText.questionType, {
    nullable: false,
    cascade: true,
  })
  problemImageTexts: Relation<QuestionTypeImageTextEntity>[]

  @OneToMany(() => ProductModelEntity, (productModel) => productModel.questionType, { nullable: false })
  productModels: Relation<ProductModelEntity>[]

  @OneToMany(() => ProductVariantEntity, (productVariant) => productVariant.questionType, { nullable: false })
  productVariants: Relation<ProductVariantEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IQuestionType = Omit<QuestionTypeEntity, keyof BaseEntity> & {
  conditions: IQuestionTypeCondition[]
  problems: IQuestionTypeProblem[]
  problemImageTexts: IQuestionTypeProblemImageText[]
  productModels: IProductModel[]
  productVariants: IProductVariant[]
}
