import { CheckCard } from '@ant-design/pro-components'
import { <PERSON><PERSON><PERSON>, Edit, EditButton, useForm, useSelect } from '@refinedev/antd'
import {
  type HttpError,
  type IResourceComponentsProps,
  useApiUrl,
  useCustom,
  useCustomMutation,
  useInvalidate,
  useList,
  useNavigation,
} from '@refinedev/core'
import { LOCALE_ENABLED_LANGUAGES, SaleItemOfferStatus, SaleItemPlanType, SaleItemStatus } from '@valyuu/api/constants'
import type {
  ILocaleCountry,
  IPriceThreshold,
  IProductVariant,
  IQuestionExtraProblem,
  ISaleItem,
} from '@valyuu/api/entities'
import { Button, Collapse, Descriptions, Form, Modal, Space, Tabs, Typography } from 'antd'
import { Input, InputNumber, Select, Switch, TextArea } from 'antx'
import currency from 'currency.js'
import { escapeRegExp, isEqual, omit } from 'lodash'
import { type FC, useMemo, useState } from 'react'
import { AiOutlineEdit, AiOutlinePlus } from 'react-icons/ai'
import { BsTranslate } from 'react-icons/bs'
import { useParams } from 'react-router-dom'
import { useDeepCompareEffect } from 'use-deep-compare'

import {
  BlockChangeHistory,
  BlockEmailHistory,
  ButtonCopy,
  getTrelloLink,
  type HistoryRecord,
  InputExtraProblems,
  InputMultiLang,
  InputSaleItemAnswers,
  LabelWithDetails,
  LinkShipmentDocument,
} from '~/components'
import {
  countryToEmoji,
  formatCurrencySymbol,
  formatMoney,
  formatSelectOptionsFromEnum,
  getAddressText,
  getSendCloudParcelUrl,
} from '~/utils'

enum PageTab {
  SALE_ITEM = 'sale-item',
  EMAIL_HISTORY = 'email-history',
  RETURN_SHIPMENT = 'return-shipment',
  CHANGE_HISTORY = 'change-history',
}

const StatusTransitionMap: Record<SaleItemStatus, SaleItemStatus[]> = {
  [SaleItemStatus.SUBMITTED]: [SaleItemStatus.RECEIVED, SaleItemStatus.NOT_RECEIVED],
  [SaleItemStatus.RECEIVED]: [SaleItemStatus.ON_HOLD, SaleItemStatus.PENDING_RECYCLE, SaleItemStatus.PENDING_RETURN],
  [SaleItemStatus.CANCELLED]: [],
  [SaleItemStatus.PENDING_OFFER]: [
    SaleItemStatus.PENDING_PAYMENT,
    SaleItemStatus.PENDING_RETURN,
    SaleItemStatus.PENDING_RECYCLE,
  ],
  [SaleItemStatus.NOT_RECEIVED]: [],
  [SaleItemStatus.PENDING_PAYMENT]: [
    SaleItemStatus.PAID,
    SaleItemStatus.PAYMENT_FAILED,
    SaleItemStatus.RECEIVED,
    SaleItemStatus.PENDING_OFFER,
  ],
  [SaleItemStatus.PAYMENT_IN_PROCESS]: [],
  [SaleItemStatus.PAYMENT_FAILED]: [SaleItemStatus.PENDING_PAYMENT, SaleItemStatus.RECEIVED],
  [SaleItemStatus.PAID]: [],
  [SaleItemStatus.PENDING_RECYCLE]: [SaleItemStatus.RECYCLED],
  [SaleItemStatus.PENDING_RETURN]: [SaleItemStatus.RETURNED],
  [SaleItemStatus.RETURNED]: [SaleItemStatus.PAID],
  [SaleItemStatus.RECYCLED]: [],
  [SaleItemStatus.ON_HOLD]: [SaleItemStatus.RECEIVED],
  [SaleItemStatus.CUSTOMER_ABANDONED]: [],
}

export const SaleItemEdit: FC<IResourceComponentsProps> = () => {
  const { editUrl } = useNavigation()

  const { id } = useParams()

  const { form, formProps, saveButtonProps, query, formLoading } = useForm<ISaleItem, HttpError, ISaleItem>({
    meta: {
      join: [
        {
          field: 'user',
          select: ['email'],
        },
        {
          field: 'sale',
          select: ['id', 'userId', 'saleNumber', 'languageId'],
        },
        {
          field: 'productVariant',
          select: ['name__en'],
        },
        {
          field: 'productSku',
          select: ['name__en'],
        },
        {
          field: 'payments',
          select: ['status', 'createdAt', 'amount', 'currencyId'],
        },
        {
          field: 'returnAddress',
        },
        {
          field: 'returnShipment',
        },
        {
          field: 'returnShipment.shippingMethod',
          select: ['shippingProductName'],
        },
        {
          field: 'channel',
          select: ['name'],
        },
        {
          field: 'partner',
          select: ['name', 'supportedPlanTypes', 'manageCustomerEmails'],
        },
      ],
    },
    resource: 'admin/sale-items',
    id,
  })

  const record = query?.data?.data
  const returnShipment = record?.returnShipment
  const returnAddress = record?.returnAddress

  const { selectProps: variantSelectProps } = useSelect<IProductVariant>({
    resource: 'admin/product-variants',
    optionLabel: 'name__en',
    optionValue: 'id',
    filters: [{ field: 'publishedAt', operator: 'nnull', value: true }],
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'server',
    },
    debounce: 500,
    onSearch: (value) => [
      {
        field: 'name__en',
        operator: 'contains',
        value,
      },
    ],
  })
  if (
    record?.productVariant &&
    variantSelectProps?.options &&
    !variantSelectProps.options.find((o) => o.value === record.productVariant.id)
  ) {
    variantSelectProps.options.push({
      value: record.productVariant.id,
      label: record.productVariant.name__en,
    })
  }

  const { data: { data: countries } = {} } = useList<ILocaleCountry>({
    resource: 'admin/countries',
    meta: { fields: ['id', 'name__en'] },
    pagination: { mode: 'off' },
  })

  const supportedPlanTypes = record?.partner ? record.partner.supportedPlanTypes : Object.values(SaleItemPlanType)

  const partnerManageCustomerEmails = record?.partner ? !record.partner.manageCustomerEmails : false

  const { data: thresholdData } = useList<IPriceThreshold>({
    resource: 'admin/price-thresholds',
    filters: [
      {
        field: 'channelId',
        operator: 'eq',
        value: record?.channelId,
      },
    ],
    queryOptions: {
      enabled: !!record?.channelId,
    },
  })

  const lang: (typeof LOCALE_ENABLED_LANGUAGES)[number] = record?.sale?.languageId
  const { data: { data: extraProblems } = {} } = useList<IQuestionExtraProblem>({
    resource: 'admin/question-extra-problems',
    meta: { fields: ['id', `template__${lang}`] },
    queryOptions: {
      enabled: !!lang,
    },
  })

  const { priceChangeThreshold } = thresholdData?.data?.[0] ?? {
    priceChangeThreshold: 10,
    recycleThreshold: 10,
  }

  const [priceEditable, setPriceEditable] = useState(false)

  const [estimatedC2bPrice, setEstimatedC2bPrice] = useState<number | null>(null)
  const [estimatedC2cPrice, setEstimatedC2cPrice] = useState<number | null>(null)

  const [activeTab, setActiveTab] = useState<PageTab>(PageTab.SALE_ITEM)

  const apiUrl = useApiUrl()

  const { data: { data: histories } = {}, isLoading: isLoadingHistories } = useCustom<HistoryRecord[]>({
    url: `${apiUrl}/admin/sale-items/${id}/histories`,
    method: 'get',
  })

  const { mutate: createShipment, isLoading: isCreatingShipment } = useCustomMutation<{ override?: boolean }>()
  const invalidate = useInvalidate()

  const handleCreateShipment = (override: boolean) => {
    createShipment(
      {
        method: 'post',
        url: `${apiUrl}/admin/sale-items/${id}/create-return-shipment`,
        values: { override },
        successNotification: {
          message: `Shipment ${override ? 'recreate' : 'created'} successfully`,
          type: 'success',
        },
        errorNotification: {
          message: `Failed to ${override ? 'recreate' : 'create'} shipment`,
          type: 'error',
        },
      },
      {
        onSuccess: () => {
          invalidate({
            resource: 'admin/sale-items',
            invalidates: ['detail'],
            id,
          })
          query?.refetch()
        },
      }
    )
  }

  const handleRecreateShipmentClick = () => {
    Modal.confirm({
      title: 'Recreate Shipment',
      content: (
        <div className="space-y-2">
          <p>This action will:</p>
          <ul className="list-disc pl-4">
            <li>Invalidate the current return shipping label and cancel the existing parcel</li>
            <li>Create a new return shipping label</li>
          </ul>
          <p className="font-bold text-red-500">Please be extra careful with this action.</p>
        </div>
      ),
      okText: 'Yes, Recreate',
      cancelText: 'Cancel',
      okButtonProps: {
        danger: true,
      },
      onOk: () => handleCreateShipment(true),
    })
  }

  saveButtonProps.disabled = saveButtonProps.disabled || activeTab !== PageTab.SALE_ITEM

  const status = Form.useWatch('status', form)
  const plan = Form.useWatch('type', form)
  const price = Form.useWatch('price', form)
  const isTested = Form.useWatch('isTested', form)
  const variantId = Form.useWatch('productVariantId', form)
  const answers = Form.useWatch('answers', form)
  const customerNote = Form.useWatch('customerNote', form)

  const variantChanged = variantId !== record?.productVariantId
  const disabled = ![SaleItemStatus.PENDING_OFFER, SaleItemStatus.RECEIVED].includes(status)

  useDeepCompareEffect(() => {
    const currentPrice = plan === SaleItemPlanType.C2B ? estimatedC2bPrice : estimatedC2cPrice
    if (record && currentPrice !== null) {
      const hasPriceChanges =
        isTested &&
        plan &&
        !disabled &&
        // Answers changed
        (!isEqual(omit(record.answers, 'EXTRA_PROBLEM'), omit(answers, 'EXTRA_PROBLEM')) ||
          // Variant changed
          variantChanged ||
          // If this record has a price difference but no offer (because isTested was false), creating a new offer is allowed when isTested becomes true
          (!record.offerId && currency(record.initialPrice).subtract(currentPrice).value > priceChangeThreshold))

      if (hasPriceChanges) {
        const notInitialPrice = record.price === record.initialPrice && price < record.price
        if (notInitialPrice || currency(record.price).subtract(currentPrice).value > priceChangeThreshold) {
          form.setFieldValue('price', Math.min(currentPrice, record.price))
          return
        }
      }
      form.setFieldValue('price', record.price)
    }
  }, [omit(answers, 'EXTRA_PROBLEM'), variantChanged, plan, estimatedC2bPrice, estimatedC2cPrice, isTested])

  // Create new offer when:
  // 1. Variant changed
  // 2. record.price equals record.initialPrice and record.price-price is greater than priceThreshold. Or, record.price is different than record.initialPrice and price is not equal to record.price
  // 3. Only set the createNewOffer to true when isTested is true
  const createNewOffer =
    isTested &&
    record &&
    price !== record.initialPrice &&
    [SaleItemStatus.PENDING_OFFER, SaleItemStatus.RECEIVED].includes(status)

  const extraProblemNote = useMemo(
    () =>
      answers?.EXTRA_PROBLEM?.map((ep: { id: string; variables?: Record<string, string> }) => {
        const template = extraProblems?.find(({ id }) => id === ep.id)?.[`template__${lang}`] ?? ''
        let message = template
        Object.entries(ep.variables ?? {}).forEach(([key, value]) => {
          message = message.replace(new RegExp(`{{${escapeRegExp(key)}}}`, 'g'), value)
        })
        return <div key={ep.id}>{message}</div>
      }),
    [answers?.EXTRA_PROBLEM, extraProblems, lang]
  )

  return (
    <Edit
      isLoading={formLoading || !record}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Tabs
        activeKey={activeTab}
        className="select-none"
        onChange={(key) => setActiveTab(key as PageTab)}
        items={[
          {
            key: PageTab.SALE_ITEM,
            label: 'Sale Item',
            children: (
              <Form {...formProps} layout="vertical">
                {record?.sale ? (
                  <Select
                    label={
                      <LabelWithDetails
                        label="Sale"
                        link={record?.saleId ? editUrl('admin/sales', record.saleId) : undefined}
                      />
                    }
                    name="sale"
                    options={[
                      {
                        key: record.sale.saleId,
                        label: record.sale.saleNumber,
                      },
                    ]}
                    rules={['required']}
                    disabled
                  />
                ) : null}

                <Select
                  label={
                    <LabelWithDetails
                      label="Product variant"
                      link={variantId ? editUrl('admin/product-variants', variantId) : undefined}
                    />
                  }
                  name="productVariantId"
                  {...(variantSelectProps as any)}
                  rules={['required']}
                  disabled={disabled}
                />
                <Input label="Stock ID" name="stockId" disabled required />
                <Select
                  label="Status"
                  name="status"
                  rules={['required']}
                  options={formatSelectOptionsFromEnum(SaleItemStatus)
                    .map((option) => ({
                      ...option,
                      disabled: record
                        ? !StatusTransitionMap[record.status as keyof typeof StatusTransitionMap]?.includes(
                            option.value as SaleItemStatus
                          ) && option.value !== record.status
                        : false,
                    }))
                    .sort((a, b) => {
                      if (a.disabled === b.disabled) return 0
                      return a.disabled ? 1 : -1
                    })}
                />
                <Select
                  label="Offer status"
                  name="offerStatus"
                  rules={['required']}
                  options={formatSelectOptionsFromEnum(SaleItemOfferStatus)}
                  disabled
                />
                {record?.offerAcceptedAt ? (
                  <Form.Item label="Offer accepted at">
                    <DateField value={record.offerAcceptedAt} format="YYYY-MM-DD HH:mm:ss" />
                  </Form.Item>
                ) : null}
                <Input
                  label={
                    <LabelWithDetails
                      label="User"
                      link={record?.userId ? editUrl('admin/users', record.userId) : undefined}
                    />
                  }
                  help={
                    partnerManageCustomerEmails ? (
                      <span className="text-red-500">
                        Customer service is managed by our partner, please don't send any emails to this user!
                      </span>
                    ) : undefined
                  }
                  value={record?.user?.email}
                  disabled
                />
                <InputNumber
                  className="[&.ant-form-item_.ant-input-number-group-addon>span]:flex [&.ant-form-item_.ant-input-number-group-addon>span]:h-[30px] [&.ant-form-item_.ant-input-number-group-addon>span]:w-9 [&.ant-form-item_.ant-input-number-group-addon>span]:cursor-pointer [&.ant-form-item_.ant-input-number-group-addon>span]:items-center [&.ant-form-item_.ant-input-number-group-addon>span]:justify-center [&.ant-form-item_.ant-input-number-group-addon]:bg-black/[0.02] [&.ant-form-item_.ant-input-number-group-addon]:p-0 [&.ant-form-item_.ant-input-number-group-addon]:text-black/[0.88]"
                  label="Price"
                  name="price"
                  help={
                    <>
                      Last price:{' '}
                      <span className="font-semibold">
                        {record ? formatMoney(record.price ?? 0, record.currencyId ?? '') : ''}
                      </span>
                      <br />
                      When changing plans, the price will be automatically capped at the last price.
                      <br />
                      If you want to set a price higher than the last price, click the edit icon to enter it manually.
                    </>
                  }
                  rules={['required']}
                  min={0}
                  prefix={record?.currencyId && formatCurrencySymbol(record.currencyId)}
                  disabled={!priceEditable}
                  addonAfter={
                    priceEditable ? null : (
                      <span onClick={() => setPriceEditable(true)} title="Edit price">
                        <AiOutlineEdit />
                      </span>
                    )
                  }
                />
                <InputNumber
                  className="[&.ant-form-item_.ant-input-number-group-addon>span]:flex [&.ant-form-item_.ant-input-number-group-addon>span]:h-[30px] [&.ant-form-item_.ant-input-number-group-addon>span]:w-9 [&.ant-form-item_.ant-input-number-group-addon>span]:cursor-pointer [&.ant-form-item_.ant-input-number-group-addon>span]:items-center [&.ant-form-item_.ant-input-number-group-addon>span]:justify-center [&.ant-form-item_.ant-input-number-group-addon]:bg-black/[0.02] [&.ant-form-item_.ant-input-number-group-addon]:p-0 [&.ant-form-item_.ant-input-number-group-addon]:text-black/[0.88]"
                  label="Initial price"
                  name="initialPrice"
                  rules={['required']}
                  min={0}
                  prefix={record?.currencyId && formatCurrencySymbol(record.currencyId)}
                  disabled
                />
                <Input
                  label="IMEI"
                  name="imei"
                  normalize={(value: string) => value?.trim()}
                  rules={isTested ? ['required'] : []}
                  required={isTested}
                />
                {record ? (
                  <Collapse
                    className="col-span-2 mb-6"
                    defaultActiveKey={['1']}
                    items={[
                      {
                        key: '1',
                        label: 'Condition details and price estimation',
                        children: (
                          <>
                            <Form.Item name="answers">
                              <InputSaleItemAnswers
                                disabled={disabled}
                                variantId={variantId}
                                variantChanged={variantChanged}
                                channelId={record.channelId}
                                partnerId={record.partnerId ?? undefined}
                                onPriceEstimated={(c2bPrice, c2cPrice) => {
                                  if (formLoading) {
                                    return
                                  }
                                  setEstimatedC2bPrice(c2bPrice)
                                  setEstimatedC2cPrice(c2cPrice)
                                  if (plan === SaleItemPlanType.C2B) {
                                    if (c2bPrice === null) {
                                      form.setFieldValue('type', undefined)
                                    }
                                  } else if (plan === SaleItemPlanType.C2C) {
                                    if (c2cPrice === null) {
                                      form.setFieldValue('type', undefined)
                                    }
                                  } else if (plan === undefined) {
                                    if (record?.type === SaleItemPlanType.C2B && c2bPrice !== null) {
                                      form.setFieldValue('type', SaleItemPlanType.C2B)
                                    } else if (record?.type === SaleItemPlanType.C2C && c2cPrice !== null) {
                                      form.setFieldValue('type', SaleItemPlanType.C2C)
                                    }
                                  }
                                }}
                              />
                            </Form.Item>
                            <Form.Item
                              label="Sale plan"
                              name="type"
                              className="select-none"
                              rules={[{ required: true, message: 'Please select a sale plan' }]}
                            >
                              <CheckCard.Group size="small" value={plan}>
                                <CheckCard
                                  title="Best price (C2C)"
                                  description={
                                    estimatedC2cPrice ? (
                                      <span className="text-black">
                                        {formatMoney(estimatedC2cPrice, record.currencyId)}
                                      </span>
                                    ) : (
                                      '€0 - No value'
                                    )
                                  }
                                  value={SaleItemPlanType.C2C}
                                  disabled={
                                    disabled ||
                                    !supportedPlanTypes.includes(SaleItemPlanType.C2C) ||
                                    estimatedC2cPrice === null
                                  }
                                />
                                <CheckCard
                                  title="Fast pay (C2B)"
                                  description={
                                    estimatedC2bPrice ? (
                                      <span className="text-black">
                                        {formatMoney(estimatedC2bPrice, record.currencyId)}
                                      </span>
                                    ) : (
                                      '€0 - No value'
                                    )
                                  }
                                  value={SaleItemPlanType.C2B}
                                  disabled={
                                    disabled ||
                                    !supportedPlanTypes.includes(SaleItemPlanType.C2B) ||
                                    estimatedC2bPrice === null
                                  }
                                />
                              </CheckCard.Group>
                            </Form.Item>

                            <Switch label="Is tested" name="isTested" disabled={disabled} />

                            {(plan && createNewOffer && isTested) || record.answers?.EXTRA_PROBLEM?.length ? (
                              <>
                                <InputExtraProblems
                                  name={['answers', 'EXTRA_PROBLEM']}
                                  disabled={disabled}
                                  label="Extra problems"
                                />
                                <InputMultiLang
                                  sources={{ auto: customerNote }}
                                  targetLang={lang}
                                  element="textarea"
                                  fillType="translate"
                                  name="customerNote"
                                  label="Extra notes for customer"
                                  help={
                                    <span>
                                      Customer language: {countryToEmoji(lang)}. If you write the content in a different
                                      language, please don't forget to translate it by clicking the{' '}
                                      <BsTranslate className="-mb-0.5" title="translate" /> icon in the top right.
                                    </span>
                                  }
                                  value={customerNote}
                                  disabled={disabled}
                                  rows={4}
                                />
                                {answers?.EXTRA_PROBLEM?.length || customerNote ? (
                                  <Form.Item label="Preview for customer">
                                    <div className="space-y-2 rounded-md border border-solid border-[#d9d9d9] bg-[#fafafa] p-4">
                                      {extraProblemNote}
                                      {customerNote && <div>{customerNote}</div>}
                                    </div>
                                  </Form.Item>
                                ) : null}
                              </>
                            ) : null}
                            {createNewOffer && isTested && (
                              <Typography.Text type="danger" className="mt-2 block">
                                Warning: A new offer will be sent to the user for confirmation due to price changes
                              </Typography.Text>
                            )}
                          </>
                        ),
                      },
                    ]}
                  />
                ) : null}
                <TextArea label="Note" name="note" className="col-span-2" />
                <Input
                  label={
                    <LabelWithDetails
                      label="Trello ID"
                      link={record?.trelloId ? getTrelloLink(record.trelloId) : undefined}
                      target="_blank"
                    />
                  }
                  name="trelloId"
                />
                <Form.Item label="Created at">
                  <DateField value={record?.createdAt} format="YYYY-MM-DD HH:mm:ss" />
                </Form.Item>
                {record?.channel?.name ? (
                  <Form.Item label="Channel">
                    <Typography.Text>{record.channel.name}</Typography.Text>
                  </Form.Item>
                ) : null}
                {record?.partner?.name ? (
                  <Form.Item label="Partner">
                    <Typography.Text>{record.partner.name}</Typography.Text>
                  </Form.Item>
                ) : null}
              </Form>
            ),
          },
          returnShipment ||
          (returnAddress && [SaleItemStatus.PENDING_RETURN, SaleItemStatus.RETURNED].includes(record?.status))
            ? {
                key: PageTab.RETURN_SHIPMENT,
                label: 'Return Shipment',
                children: (
                  <>
                    {returnAddress ? (
                      <Descriptions
                        title="Shipping address"
                        extra={
                          <Space>
                            <ButtonCopy text={getAddressText(returnAddress, countries)} label="Copy address" />
                            <EditButton resource="admin/addresses" recordItemId={returnAddress.id} />
                          </Space>
                        }
                        bordered
                      >
                        <Descriptions.Item label="First name">{returnAddress.firstName}</Descriptions.Item>
                        <Descriptions.Item label="Last name">{returnAddress.lastName}</Descriptions.Item>
                        <Descriptions.Item label="Phone">
                          {returnAddress.phoneAreaCode && returnAddress.phoneNumber
                            ? returnAddress.phoneAreaCode + ' ' + returnAddress.phoneNumber
                            : ''}
                        </Descriptions.Item>
                        <Descriptions.Item label="Street">{returnAddress.street}</Descriptions.Item>
                        <Descriptions.Item label="House number">{returnAddress.houseNumber}</Descriptions.Item>
                        <Descriptions.Item label="Addition">{returnAddress.addition}</Descriptions.Item>
                        <Descriptions.Item label="Postal code">{returnAddress.postalCode}</Descriptions.Item>
                        <Descriptions.Item label="City">{returnAddress.city}</Descriptions.Item>
                        <Descriptions.Item label="Country">
                          {countries
                            ? countries.find(({ id }) => id === returnAddress.countryId)?.name__en
                            : returnAddress.countryId}
                        </Descriptions.Item>
                      </Descriptions>
                    ) : null}
                    <Descriptions
                      title="Shipment information"
                      className="mt-4"
                      extra={
                        record?.returnShipment ? (
                          <Space>
                            <Button danger loading={isCreatingShipment} onClick={handleRecreateShipmentClick}>
                              <span className="anticon">
                                <AiOutlinePlus />
                              </span>
                              Recreate shipment
                            </Button>
                            <EditButton resource="admin/shipments" recordItemId={record.returnShipment.id} />
                          </Space>
                        ) : (
                          <Button
                            type="primary"
                            onClick={() => handleCreateShipment(false)}
                            loading={isCreatingShipment}
                          >
                            <span className="anticon">
                              <AiOutlinePlus />
                            </span>
                            Create shipment
                          </Button>
                        )
                      }
                      bordered={record?.returnShipment}
                      labelStyle={{ color: record?.returnShipment ? undefined : '#ff4d4f' }}
                    >
                      {!record?.returnShipment ? (
                        <Descriptions.Item label="Error">
                          <Typography.Text type="danger">
                            No shipment record found. This could be due to an invalid shipping address or a system
                            error. Please try to create a new shipment. If the problem persists, please contact the
                            development team.
                          </Typography.Text>
                        </Descriptions.Item>
                      ) : (
                        <>
                          <Descriptions.Item label="Tracking number">
                            <Typography.Text copyable={{ text: record?.returnShipment?.trackingNumber }}>
                              {record?.returnShipment?.trackingUrl ? (
                                <a title="View tracking page" href={record.returnShipment.trackingUrl} target="_blank">
                                  {record?.returnShipment.trackingNumber}
                                </a>
                              ) : (
                                (record?.returnShipment?.trackingNumber ?? '')
                              )}
                            </Typography.Text>
                          </Descriptions.Item>
                          <Descriptions.Item label="Parcel ID">
                            <Typography.Text copyable={{ text: record?.returnShipment?.parcelId }}>
                              <a
                                title="View parcel details on SendCloud"
                                href={getSendCloudParcelUrl(record.returnShipment.parcelId)}
                                target="_blank"
                              >
                                {record?.returnShipment?.parcelId ?? ''}
                              </a>
                            </Typography.Text>
                          </Descriptions.Item>
                          <Descriptions.Item label="Status">
                            {record?.returnShipment?.statusMessage ?? ''}
                          </Descriptions.Item>
                          <Descriptions.Item label="Is return">
                            {record?.returnShipment?.isReturn ? 'Yes' : 'No'}
                          </Descriptions.Item>
                          <Descriptions.Item label="Is paperless">
                            {record?.returnShipment?.isPaperless ? 'Yes' : 'No'}
                          </Descriptions.Item>
                          <Descriptions.Item label="Shipping label / Paperless code">
                            {record?.returnShipment ? (
                              <LinkShipmentDocument
                                shipmentId={record.returnShipment.id}
                                trackingNumber={record.returnShipment.trackingNumber}
                              />
                            ) : (
                              ''
                            )}
                          </Descriptions.Item>
                          <Descriptions.Item label="Shipping method">
                            {record?.returnShipment?.shippingMethod?.shippingProductName}
                          </Descriptions.Item>
                          <Descriptions.Item label="Note">{record?.returnShipment?.note ?? ''}</Descriptions.Item>
                        </>
                      )}
                    </Descriptions>
                  </>
                ),
              }
            : { key: PageTab.RETURN_SHIPMENT, label: 'Return Shipment', disabled: true },
          {
            key: PageTab.EMAIL_HISTORY,
            label: 'Email History',
            children: <BlockEmailHistory userId={record?.sale?.userId} relatedIds={id ? [id] : undefined} />,
          },
          ...(histories?.length
            ? [
                {
                  key: PageTab.CHANGE_HISTORY,
                  label: 'Change History',
                  children: <BlockChangeHistory histories={histories} currentValue={record} />,
                },
              ]
            : []),
        ]}
      />
    </Edit>
  )
}
