import { IShippingConfig, IWarehouse } from '@valyuu/api/entities'
import { Edge, MarkerType } from '@xyflow/react'
import { isEqual } from 'lodash'

export const defaultEdgeOptions = {
  type: 'default',
  deletable: true,
  animated: true,
  markerEnd: { type: MarkerType.Arrow },
  style: { strokeWidth: 2 },
}

export const createFlowEdges = (shippingConfigs: Partial<IShippingConfig>[], warehouses: IWarehouse[]) => {
  const edges: Edge[] = []

  shippingConfigs.forEach((config) => {
    const edgeData = {
      isReturn: config.isReturn!,
      warehouseId: config.warehouseId!,
      warehouseCountryId: warehouses.find((warehouse) => warehouse.id === config.warehouseId)!.countryId,
      customerCountryId: config.customerCountryId!,
      fromCountryId: config.isReturn
        ? config.customerCountryId!
        : warehouses.find((warehouse) => warehouse.id === config.warehouseId)!.countryId,
      toCountryId: config.isReturn
        ? warehouses.find((warehouse) => warehouse.id === config.warehouseId)!.countryId
        : config.customerCountryId!,
    }
    const existingEdge = edges.find((edge) => isEqual(edge.data, edgeData))
    if (existingEdge) {
      existingEdge.label = (Number(existingEdge.label) + 1).toString()
    } else {
      edges.push({
        ...defaultEdgeOptions,
        id: `${config.isReturn ? 'return' : 'shipping'}-${config.warehouseId}-${config.customerCountryId}`,
        source: config.isReturn ? `sellers-${config.customerCountryId}` : `warehouse-${config.warehouseId}`,
        target: config.isReturn ? `warehouse-${config.warehouseId}` : `buyers-${config.customerCountryId}`,
        sourceHandle: null,
        targetHandle: null,
        label: '1',
        data: edgeData,
      })
    }
  })

  return edges
}
