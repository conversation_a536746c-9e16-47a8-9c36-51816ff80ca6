import { Body, Controller, Post, UseGuards } from '@nestjs/common'

import { EmailType } from '~/constants'
import { EmailService } from '~admin/services'

import { AdminJwtAuthGuard } from '../guards'

@UseGuards(AdminJwtAuthGuard)
@Controller('admin/emails')
export class EmailController {
  constructor(public service: EmailService) {}

  @Post('resend/buyer-order')
  resendOrderEmail(@Body() { id }: { id: string }) {
    return this.service.resendEmail({ type: EmailType.BUYER_NEW_ORDER, id })
  }

  @Post('resend/seller-sale')
  resendSaleEmail(@Body() { id }: { id: string }) {
    return this.service.resendEmail({ type: EmailType.SELLER_NEW_SALE, id })
  }
}
