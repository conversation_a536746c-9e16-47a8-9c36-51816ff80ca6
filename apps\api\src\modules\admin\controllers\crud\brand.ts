import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { BrandEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudBrandService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: BrandEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      image: {},
      productSeries: {},
      productModels: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/brands')
export class CrudBrandController implements CrudController<BrandEntity> {
  constructor(public service: CrudBrandService) {}
}
