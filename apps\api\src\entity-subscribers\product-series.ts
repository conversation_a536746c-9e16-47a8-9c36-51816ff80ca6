import type { EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ProductSeriesEntity } from '~/entities'
import { entityFixPublishedAt } from '~/utils'

@EventSubscriber()
export class ProductSeriesSubscriber implements EntitySubscriberInterface<ProductSeriesEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ProductSeriesEntity
  }

  async beforeUpdate(event: UpdateEvent<ProductSeriesEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async beforeInsert(event: InsertEvent<ProductSeriesEntity>) {
    // TODO: investigate why entity is empty in TestedItem seed

    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }
}
