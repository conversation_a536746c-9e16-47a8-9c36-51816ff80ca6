import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { WarrantyEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudWarrantyService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: WarrantyEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
})
@Controller('admin/warranties')
export class CrudWarrantyController implements CrudController<WarrantyEntity> {
  constructor(public service: CrudWarrantyService) {}
}
