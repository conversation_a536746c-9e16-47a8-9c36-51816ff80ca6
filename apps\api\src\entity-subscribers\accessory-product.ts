import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageUsedInType } from '~/constants'
import { AccessoryProductEntity } from '~/entities'
import { entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'
import { entityFixPublishedAt } from '~/utils'

@Injectable()
@EventSubscriber()
export class AccessoryProductSubscriber implements EntitySubscriberInterface<AccessoryProductEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return AccessoryProductEntity
  }

  async beforeInsert(event: InsertEvent<AccessoryProductEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async afterInsert(event: InsertEvent<AccessoryProductEntity>) {
    const { entity, manager } = event

    // process heroImage
    await entityImageAutoProcess({
      image: entity.heroImage,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.ACCESSORY_PRODUCT,
      manager,
    })
  }

  async beforeUpdate(event: UpdateEvent<AccessoryProductEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }

  async afterUpdate(event: UpdateEvent<AccessoryProductEntity>) {
    const { entity, manager } = event

    // process heroImage
    await entityImageAutoProcess({
      image: entity.heroImage ?? { id: entity.heroImageId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.ACCESSORY_PRODUCT,
      manager,
    })
  }

  async afterRemove(event: RemoveEvent<AccessoryProductEntity>) {
    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.ACCESSORY_PRODUCT,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
