import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq'
import * as Sentry from '@sentry/nestjs'
import { Job } from 'bullmq'

import { StripeAccountApiType } from '~/constants'
import { stripeDeleteAccount, stripeDeleteExternalAccount } from '~/utils'

export type StripeDeleteAccountJobData = {
  accountId: string
  apiType: StripeAccountApiType
}

type StripeDeleteAccountJob = Job<StripeDeleteAccountJobData> & {
  name: 'deleteAccount'
}

export type StripeDeleteBankAccountJobData = {
  accountId: string
  bankAccountId: string
  apiType: StripeAccountApiType
}

export type StripeDeleteBankAccountJob = Job<StripeDeleteBankAccountJobData> & {
  name: 'deleteBankAccount'
}

@Processor('stripe')
export class StripeProcessor extends WorkerHost {
  async process(job: StripeDeleteAccountJob | StripeDeleteBankAccountJob) {
    switch (job.name) {
      case 'deleteAccount':
        return this.deleteAccount(job.data)
      case 'deleteBankAccount':
        return this.deleteBankAccount(job.data)
    }
  }

  deleteAccount({ accountId, apiType }: StripeDeleteAccountJobData) {
    return stripeDeleteAccount(accountId, apiType)
  }

  deleteBankAccount({ accountId, bankAccountId, apiType }: StripeDeleteBankAccountJobData) {
    return stripeDeleteExternalAccount({ accountId, bankAccountId }, apiType)
  }

  @OnWorkerEvent('error')
  onFailed(job: Job<any>, error: Error) {
    Sentry.captureException(error, {
      tags: {
        type: 'stripe:onFailed',
      },
      extra: {
        job,
      },
    })
  }
}
