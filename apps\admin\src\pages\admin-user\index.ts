import type { PageType } from '~/interfaces'

import { AdminUserEdit } from './edit'
import { AdminUserList } from './list'
import { AdminUserLogin } from './login'

export const AdminUserPage: PageType = {
  path: 'admin-users',
  label: 'Admin Users',
  index: AdminUserList,
  edit: AdminUserEdit,
  parent: 'admin',
  withLayout: true,
}

export const AdminUserLoginPage: PageType = {
  path: 'login',
  index: AdminUserLogin,
  withLayout: false,
}
