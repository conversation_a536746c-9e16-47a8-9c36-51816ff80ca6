import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { type IImage, ImageEntity, type IProductSku, ProductSkuEntity } from '~/entities'

@ObjectType('ProductSkuOriginalAccessory')
@Entity('original_accessory')
export class ProductSkuOriginalAccessoryEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ unique: true })
  key: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column()
  desc__en: string

  @Field()
  @Column()
  desc__nl: string

  @Field()
  @Column()
  desc__de: string

  @Field()
  @Column()
  desc__pl: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  highlight__en?: string | null

  @Field({ nullable: true })
  @Column({ nullable: true })
  highlight__nl?: string | null

  @Field({ nullable: true })
  @Column({ nullable: true })
  highlight__de?: string | null

  @Field({ nullable: true })
  @Column({ nullable: true })
  highlight__pl?: string | null

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.productSkuOriginalAccessoryThumbnail, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn()
  thumbnail: Relation<ImageEntity>

  @Column()
  @RelationId((productSkuOriginalAccessory: ProductSkuOriginalAccessoryEntity) => productSkuOriginalAccessory.thumbnail)
  thumbnailId: string

  @Field()
  @Column()
  displayInResults: boolean

  @ManyToMany(() => ProductSkuEntity, (sku) => sku.originalAccessories, { nullable: false, onDelete: 'CASCADE' })
  skus: Relation<ProductSkuEntity>[]

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IProductSkuOriginalAccessory = Omit<ProductSkuOriginalAccessoryEntity, keyof BaseEntity> & {
  thumbnail: IImage
  skus: IProductSku[]
}
