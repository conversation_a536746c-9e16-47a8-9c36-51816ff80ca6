import { isEmail } from 'class-validator'
import { config } from 'dotenv'
import { bool, cleanEnv, EnvError, host, makeValidator, num, port, Spec, str, url } from 'envalid'

const emails = <T extends string[] = string[]>(spec?: Spec<T>) => {
  return makeValidator((input: string) => {
    const emailsArray = (input ?? '').trim().split(';')
    if (emailsArray.length && emailsArray.every((line) => isEmail(line))) {
      return emailsArray as T
    }
    throw new EnvError(`Invalid emails: "${input}"`)
  })(spec)
}

export const envConfig =
  config() &&
  cleanEnv(process.env, {
    PORT: port({ desc: 'Server port' }),
    DATABASE_URL: str({ desc: 'Database URL' }),
    SESSION_SECRET: str({ desc: 'Session secret' }),
    REDIS_HOST: host({ desc: 'Redis host' }),
    REDIS_PORT: port({ desc: 'Redis port' }),
    SITE_URL: url({ desc: 'Site URL', default: 'https://prioont.com' }),
    VERCEL_SHARE: str({ desc: 'Vercel share', default: undefined }),
    API_URL: url({ desc: 'API URL' }),
    S3_ACCESS_KEY_ID: str({ desc: 'S3 access key ID' }),
    S3_DOMAIN_URL: url({ desc: 'S3 domain URL' }),
    S3_REGION: str({ desc: 'S3 region' }),
    S3_SECRET_ACCESS_KEY: str({ desc: 'S3 secret access key' }),
    S3_BUCKET_NAME: str({ desc: 'S3 bucket name' }),
    JWT_SECRET: str({ desc: 'JWT secret' }),
    LOGISTIC_EMAILS: emails({ desc: 'Email receivers for logistics events' }),
    ERROR_LOG_EMAILS: emails({ desc: 'Email receivers for error logs' }),
    STRIPE_SK_LIVE: str({ desc: 'Stripe live secret key' }),
    STRIPE_SK_VERIFICATION: str({ desc: 'Stripe live test secret key' }),
    STRIPE_SK_TEST: str({ desc: 'Stripe test secret key' }),
    STRIPE_WEBHOOK_SECRET_LIVE: str({ desc: 'Stripe webhook live secret' }),
    STRIPE_WEBHOOK_SECRET_TEST: str({ desc: 'Stripe webhook test secret' }),
    PASSWORD_HASH_ROUNDS: num({ desc: 'Password hash rounds', default: 10 }),
    SENDGRID_API_KEY: str({ desc: 'Sendgrid API key' }),
    SENDCLOUD_PK: str({ desc: 'Sendgrid public key' }),
    SENDCLOUD_SK: str({ desc: 'Sendgrid secret key' }),
    DEEPL_KEY: str({ desc: 'Deepl key' }),
    EMAIL_GENERAL_FROM: str({
      desc: "General emails' sender",
      default: 'Prioont <<EMAIL>>',
    }),
    CLOUDINARY_URL: str({ desc: 'Cloudinary URL' }),
    CLOUDINARY_PREFIX: str({ desc: 'Cloudinary prefix' }),
    ALGOLIA_APP_ID: str({ desc: 'Algolia app ID' }),
    ALGOLIA_ADMIN_API_KEY: str({ desc: 'Algolia admin API key' }),
    ALGOLIA_INDEX_PREFIX: str({ desc: 'Algolia index prefix', default: '' }),
    OPENAI_API_KEY: str({ desc: 'OpenAI API key' }),
    BEN_API_SECRET: str({ desc: 'BEN API secret' }),
    GOOGLE_SERVICE_ACCOUNT_KEY: str({ desc: 'Google service account key' }),
    GOOGLE_OAUTH_CLIENT_ID: str({ desc: 'Google OAuth client ID' }),
    GOOGLE_OAUTH_CLIENT_SECRET: str({ desc: 'Google OAuth client secret' }),
    SENTRY_DSN: str({ desc: 'Sentry DSN' }),
    TRELLO_KEY: str({ desc: 'Trello key' }),
    TRELLO_TOKEN: str({ desc: 'Trello token' }),
    TRELLO_BOARD: str({ desc: 'Trello board' }),
    TRELLO_LIST: str({ desc: 'Trello list' }),
    TRELLO_LABEL_C2C: str({ desc: 'Trello label C2C' }),
    TRELLO_LABEL_C2B: str({ desc: 'Trello label C2B' }),
    TRELLO_LABEL_DONATION: str({ desc: 'Trello label donation' }),
    TRELLO_LABEL_RETURNING: str({ desc: 'Trello label returning' }),
    ENABLE_MAIN_SWAGGER: bool({ desc: 'Enable Valyuu 2.0 Refine Swagger', default: false }),
  })
