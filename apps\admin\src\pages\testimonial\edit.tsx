import { Edit, useForm } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps, useUpdate } from '@refinedev/core'
import { TestimonialRole } from '@valyuu/api/constants'
import type { ITestimonial } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { DatePicker, Input, InputNumber, Select, TextArea } from 'antx'
import dayjs from 'dayjs'
import { FC } from 'react'

import { InputPublish } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const TestimonialEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, id, saveButtonProps, query, formLoading } = useForm<
    ITestimonial,
    HttpError,
    ITestimonial
  >()

  const record = query?.data?.data
  if (formProps.initialValues?.publishedAt) {
    formProps.initialValues.publishedAt = new Date(formProps.initialValues.publishedAt)
  }
  if (formProps.initialValues?.date) {
    formProps.initialValues.date = dayjs(new Date(formProps.initialValues.date))
  }
  const { mutate } = useUpdate<ITestimonial, HttpError, Partial<ITestimonial>>()

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!record?.publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
            mutate({
              resource: 'admin/testimonials',
              id: id! as string,
              values: {
                publishedAt: value ? new Date() : null,
              },
              successNotification: (data) => {
                return {
                  type: 'success',
                  message: `Testimonial has been ${value ? 'published' : 'unpublished'} successfully`,
                }
              },
              errorNotification: () => {
                return {
                  type: 'error',
                  message: `Failed to ${value ? 'publish' : 'unpublish'} testimonial`,
                }
              },
            })
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form {...formProps} layout="vertical">
        <Input label="Customer name" name="name" rules={['required']} />
        <Select
          label="Role"
          name="role"
          style={{ minWidth: 200 }}
          options={formatSelectOptionsFromEnum(TestimonialRole)}
          placeholder="Role"
          rules={['required']}
        />
        <TextArea label="Review" name="review" rules={['required']} />
        <DatePicker
          label="Date of review"
          name="date"
          format="DD-MM-YYYY HH:mm"
          maxDate={dayjs(new Date())}
          showTime
          rules={['required']}
        />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
        <Input noStyle type="datetime" name="publishedAt" hidden />
      </Form>
    </Edit>
  )
}
