export const formatMoney = (amount: number, currencyCode: string, locale = 'en-NL') =>
  new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount)

export const formatDate = (
  date: number | Date,
  locale = 'en-NL',
  styles: Intl.DateTimeFormatOptions = { dateStyle: 'medium', timeStyle: 'medium' }
) => {
  const { format } = new Intl.DateTimeFormat(locale, styles)
  return format(date)
}

export const getCurrencySymbol = (currencyCode: string, locale = 'en-NL') =>
  new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })
    .format(0)
    .replace(/\d/g, '')
    .trim()
