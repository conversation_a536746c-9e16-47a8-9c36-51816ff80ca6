import { MigrationInterface, QueryRunner } from 'typeorm'

export class Charity1731937602988 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS organization_bank_account (
        id UUID DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
        type VARCHAR NOT NULL,
        holder_name VARCHAR NOT NULL,
        account_number VARCHAR NOT NULL,
        partner_id UUID NOT NULL,
        charity_id UUID NOT NULL,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
      CREATE TABLE IF NOT EXISTS charity (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name__en VARCHAR NOT NULL,
        name__nl VARCHAR NOT NULL,
        name__de VARCHAR NOT NULL,
        "bankAccountId" UUID,
        "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_bank_account
          FOREIGN KEY("bankAccountId") 
          REFERENCES organization_bank_account(id)
      );

      INSERT INTO charity (name__en, name__nl, name__de)
      VALUES ('Stichting Leergeld', 'Stichting Leergeld', 'Stichting Leergeld')
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
