import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ProductModelEntity, ProductVariantEntity } from '~/entities'
import { entityFixPublishedAt } from '~/utils/entity-subscriber'

const fillRelationIds = async (event: InsertEvent<ProductVariantEntity> | UpdateEvent<ProductVariantEntity>) => {
  const { entity } = event

  if (entity) {
    const modelId = entity.model?.id ?? entity.modelId
    const model = await ProductModelEntity.findOne({
      where: { id: modelId },
      select: { id: true, categoryId: true, questionTypeId: true },
    })
    entity.categoryId = model?.categoryId
    entity.questionTypeId = model?.questionTypeId
  }
}

@Injectable()
@EventSubscriber()
export class ProductVariantSubscriber implements EntitySubscriberInterface<ProductVariantEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ProductVariantEntity
  }

  async beforeInsert(event: InsertEvent<ProductVariantEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })

    await fillRelationIds(event)
  }

  async beforeUpdate(event: UpdateEvent<ProductVariantEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })

    await fillRelationIds(event)
  }
}
