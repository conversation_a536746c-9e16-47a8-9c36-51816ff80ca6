import { Injectable } from '@nestjs/common'
import * as pMap from 'p-map'
import type { EntitySubscriberInterface, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber, InsertEvent } from 'typeorm'

import { ProductModelIndex } from '~/algolia-indices'
import { ImageUsedInType } from '~/constants'
import { ProductModelEntity, ProductModelMetricsEntity, ProductSeriesEntity, ProductVariantEntity } from '~/entities'
import { entityFixPublishedAt, entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'

@Injectable()
@EventSubscriber()
export class ProductModelSubscriber implements EntitySubscriberInterface<ProductModelEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ProductModelEntity
  }

  async beforeInsert(event: InsertEvent<ProductModelEntity>) {
    const { entity, manager } = event

    // add brand and category based on series
    const series = await manager.findOneOrFail(ProductSeriesEntity, {
      where: { id: entity.seriesId },
      select: { id: true, brandId: true, categoryId: true },
    })
    entity.brandId = series.brandId
    entity.categoryId = series.categoryId

    // add metrics
    if (!entity.metrics) {
      entity.metrics = manager.create(ProductModelMetricsEntity, {
        recentOrdersCount: 0,
        recentSalesCount: 0,
        isBestseller: false,
        isMvp: false,
      })
    }

    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async afterInsert(event: InsertEvent<ProductModelEntity>) {
    const { entity, manager } = event

    // process image
    await entityImageAutoProcess({
      image: entity.image,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.PRODUCT_MODEL,
      manager,
    })

    // add to algolia index
    if (entity?.publishedAt && !process.env.ALGOLIA_DISABLE_INDEXING) {
      await ProductModelIndex.addIndex({ id: entity.id, manager })
    }
  }

  async beforeUpdate(event: UpdateEvent<ProductModelEntity>) {
    // add brand and category based on series
    entityFixPublishedAt({ event, type: 'update' })
  }

  async afterUpdate(event: UpdateEvent<ProductModelEntity>) {
    const { entity, databaseEntity, manager } = event

    if (!entity || !databaseEntity) {
      return
    }

    // update question type in variants
    if (entity.questionTypeId && entity.questionTypeId !== databaseEntity.questionTypeId) {
      // update question type
      const variants = await manager.find(ProductVariantEntity, { where: { modelId: entity.id } })
      await pMap(variants, async (variant) => {
        variant.questionTypeId = entity.questionTypeId
        return manager.save(variant)
      })
    }

    // update algolia index
    if (!process.env.ALGOLIA_DISABLE_INDEXING) {
      if (typeof entity?.publishedAt !== 'undefined') {
        if (databaseEntity.publishedAt && !entity.publishedAt) {
          await ProductModelIndex.deleteIndex({ id: entity.id, manager })
          return
        } else if (!databaseEntity.publishedAt && entity.publishedAt) {
          await ProductModelIndex.addIndex({ id: entity.id, manager })
          return
        }
      }
      await ProductModelIndex.updateIndex({ id: entity.id, manager })
    }

    // process image
    await entityImageAutoProcess({
      image: entity.image ?? { id: entity.imageId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.PRODUCT_MODEL,
      manager,
    })
  }

  async afterRemove(event: RemoveEvent<ProductModelEntity>) {
    const { databaseEntity, manager } = event

    // remove from algolia index
    if (!process.env.ALGOLIA_DISABLE_INDEXING) {
      await ProductModelIndex.deleteIndex({ id: databaseEntity.id, manager })
    }

    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.PRODUCT_MODEL,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
