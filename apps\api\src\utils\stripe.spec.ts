import { StripeAccountApiType } from '~/constants'

import { houseNumberWithAddition, stripeCreateAccount, stripeDeleteAccount } from './stripe'

describe('Stripe utility functions', () => {
  describe('houseNumberWithAddition', () => {
    it('should concatenate house number and addition correctly', () => {
      expect(houseNumberWithAddition('123', 'A')).toBe('123A')
      expect(houseNumberWithAddition('123', '123')).toBe('123-123')
      expect(houseNumberWithAddition('ABC', '123')).toBe('ABC123')
      expect(houseNumberWithAddition('ABC', 'ABC')).toBe('ABC-ABC')
      expect(houseNumberWithAddition('123', '')).toBe('123')
      expect(houseNumberWithAddition('123', ' ')).toBe('123')
      expect(houseNumberWithAddition('123', null)).toBe('123')
    })
  })

  describe('stripeCreateAccount', () => {
    it('should be able to create a Stripe account', async () => {
      const result = await stripeCreateAccount(
        {
          email: '<EMAIL>',
          address: {
            firstName: 'Test',
            lastName: 'User',
            phoneAreaCode: '+31',
            phoneNumber: '*********',
            countryId: 'NL',
            postalCode: '1051 KM',
            houseNumber: '4',
            addition: '',
            street: 'Jan van Galenstraat',
            city: 'Test City',
          },
          bank: {
            holderName: 'Test User',
            accountNumber: '******************',
          },
          currencyId: 'EUR',
          dob: '1990-01-01',
          ip: '127.0.0.1',
        },
        StripeAccountApiType.TEST
      )

      if (result.account) {
        await stripeDeleteAccount(result.account.id, StripeAccountApiType.TEST)
      }

      expect(result.success).toBe(true)
      expect(typeof result.account?.id).toBe('string')
      expect(result.error.field).toBeNull()
    }, 15_000)
    it('should return an error when the postalCode is invalid', async () => {
      const result = await stripeCreateAccount(
        {
          email: '<EMAIL>',
          address: {
            firstName: 'Test',
            lastName: 'User',
            phoneAreaCode: '+31',
            phoneNumber: '*********',
            countryId: 'NL',
            postalCode: '123',
            houseNumber: '123',
            addition: 'A',
            street: 'Test Street',
            city: 'Test City',
          },
          bank: {
            holderName: 'Test User',
            accountNumber: '******************',
          },
          dob: '1990-01-01',
          currencyId: 'EUR',
          ip: '127.0.0.1',
        },
        StripeAccountApiType.TEST
      )

      if (result.account) {
        await stripeDeleteAccount(result.account.id, StripeAccountApiType.TEST)
      }

      expect(result.success).toBe(false)
      expect(result.error.field).toBe('postalCode')
      expect(result.account).toBeNull()
    }, 15_000)
    it('should return an error when the dateOfBirth is invalid', async () => {
      const result = await stripeCreateAccount(
        {
          email: '<EMAIL>',
          address: {
            firstName: 'Test',
            lastName: 'User',
            phoneAreaCode: '+31',
            phoneNumber: '*********',
            countryId: 'NL',
            postalCode: '1051 KM',
            houseNumber: '123',
            addition: 'A',
            street: 'Test Street',
            city: 'Test City',
          },
          bank: {
            holderName: 'Test User',
            accountNumber: '******************',
          },
          dob: '2022-01-01',
          currencyId: 'EUR',
          ip: '127.0.0.1',
        },
        StripeAccountApiType.TEST
      )

      if (result.account) {
        await stripeDeleteAccount(result.account.id, StripeAccountApiType.TEST)
      }

      expect(result.success).toBe(false)
      expect(result.error.field).toBe('dateOfBirth')
      expect(result.account).toBeNull()
    }, 15_000)
    it('should return an error when the phoneNumber is invalid', async () => {
      const result = await stripeCreateAccount(
        {
          email: '<EMAIL>',
          address: {
            firstName: 'Test',
            lastName: 'User',
            phoneAreaCode: '1',
            phoneNumber: '',
            countryId: 'NL',
            postalCode: '1051 KM',
            houseNumber: '123',
            addition: 'A',
            street: 'Test Street',
            city: 'Test City',
          },
          bank: {
            holderName: 'Test User',
            accountNumber: '******************',
          },
          dob: '1990-01-01',
          currencyId: 'EUR',
          ip: '127.0.0.1',
        },
        StripeAccountApiType.TEST
      )

      if (result.account) {
        await stripeDeleteAccount(result.account.id, StripeAccountApiType.TEST)
      }

      expect(result.success).toBe(false)
      expect(result.error.field).toBe('phoneNumber')
      expect(result.account).toBeNull()
    }, 15_000)
    it('should return an error when the accountNumber is invalid', async () => {
      const result = await stripeCreateAccount(
        {
          email: '<EMAIL>',
          address: {
            firstName: 'Test',
            lastName: 'User',
            phoneAreaCode: '+31',
            phoneNumber: '*********',
            countryId: 'NL',
            postalCode: '1051 KM',
            houseNumber: '123',
            addition: 'A',
            street: 'Test Street',
            city: 'Test City',
          },
          bank: {
            holderName: 'Test User',
            accountNumber: '1',
          },
          dob: '1990-01-01',
          currencyId: 'EUR',
          ip: '127.0.0.1',
        },
        StripeAccountApiType.TEST
      )

      if (result.account) {
        await stripeDeleteAccount(result.account.id, StripeAccountApiType.TEST)
      }

      expect(result.success).toBe(false)
      expect(result.error.field).toBe('accountNumber')
      expect(result.account).toBeNull()
    }, 15_000)
    it('should return an error when the country is invalid', async () => {
      const result = await stripeCreateAccount(
        {
          email: '<EMAIL>',
          address: {
            firstName: 'Test',
            lastName: 'User',
            phoneAreaCode: '+31',
            phoneNumber: '*********',
            countryId: '1',
            postalCode: '1051 KM',
            houseNumber: '123',
            addition: 'A',
            street: 'Test Street',
            city: 'Test City',
          },
          bank: {
            holderName: 'Test User',
            accountNumber: '******************',
          },
          dob: '1990-01-01',
          currencyId: 'EUR',
          ip: '127.0.0.1',
        },
        StripeAccountApiType.TEST
      )

      if (result.account) {
        await stripeDeleteAccount(result.account.id, StripeAccountApiType.TEST)
      }

      expect(result.success).toBe(false)
      expect(result.error.field).toBe('country')
      expect(result.account).toBeNull()
    }, 15_000)
    it('should return an error when the email is invalid', async () => {
      const result = await stripeCreateAccount(
        {
          email: '-',
          address: {
            firstName: 'Test',
            lastName: 'User',
            phoneAreaCode: '+31',
            phoneNumber: '*********',
            countryId: 'NL',
            postalCode: '1051 KM',
            houseNumber: '123',
            addition: 'A',
            street: 'Test Street',
            city: 'Test City',
          },
          bank: {
            holderName: 'Test User',
            accountNumber: '******************',
          },
          dob: '1990-01-01',
          currencyId: 'EUR',
          ip: '127.0.0.1',
        },
        StripeAccountApiType.TEST
      )

      if (result.account) {
        await stripeDeleteAccount(result.account.id, StripeAccountApiType.TEST)
      }

      expect(result.success).toBe(false)
      expect(result.error.field).toBe('email')
      expect(result.account).toBeNull()
    }, 15_000)
  })
})
