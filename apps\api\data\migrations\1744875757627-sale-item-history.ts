import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleItemHistory1744875757627 implements MigrationInterface {
  name = 'SaleItemHistory1744875757627'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "sale_item_history" ("version" smallint NOT NULL, "sale_item_id" uuid NOT NULL, "changed_by_type" "public"."sale_item_history_changed_by_type_enum" NOT NULL, "changed_by_id" uuid, "answers" jsonb, "customer_note" text, "type" character varying NOT NULL, "status" character varying NOT NULL, "offer_status" character varying NOT NULL, "offer_accepted_at" TIMESTAMP, "price" numeric(10,2) NOT NULL, "initial_price" numeric(10,2), "return_address_id" character varying, "return_shipment_id" character varying, "is_tested" boolean NOT NULL, "channel_id" character varying NOT NULL, "currency_id" character varying NOT NULL, "product_variant_id" character varying NOT NULL, "product_model_id" character varying NOT NULL, "sale_id" character varying NOT NULL, "stock_id" character varying, "user_id" character varying NOT NULL, "partner_id" character varying, "offer_id" character varying, "received_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_163da05cbf7ccd91b782eeda113" PRIMARY KEY ("version", "sale_item_id"))`
    )
    await queryRunner.query(`CREATE INDEX "IDX_c517aabbf01e89198d66c03c27" ON "sale_item_history" ("created_at") `)
    await queryRunner.query(
      `ALTER TABLE "sale_item_history" ADD CONSTRAINT "FK_15e178f4438b3d2338986feb19b" FOREIGN KEY ("sale_item_id") REFERENCES "sale_item"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sale_item_history" DROP CONSTRAINT "FK_15e178f4438b3d2338986feb19b"`)
    await queryRunner.query(`DROP INDEX "public"."IDX_c517aabbf01e89198d66c03c27"`)
    await queryRunner.query(`DROP TABLE "sale_item_history"`)
  }
}
