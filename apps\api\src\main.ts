import { CrudConfigService } from '@dataui/crud'
import { ValidationPipe } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import type { NestExpressApplication } from '@nestjs/platform-express'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import * as Sentry from '@sentry/nestjs'

import { envConfig } from '~/configs'
import { PartnerModule } from '~partner/partner.module'

import { AppModule } from './app.module'

CrudConfigService.load({
  query: {
    alwaysPaginate: false,
  },
})

Sentry.init({
  dsn: envConfig.SENTRY_DSN,
  tracesSampleRate: 1.0,
  environment: envConfig.isProduction ? 'production' : 'development',
  maxValueLength: Infinity,
  attachStacktrace: true,
})

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, { rawBody: true })
  if (envConfig.isDev) {
    Error.stackTraceLimit = Infinity
    if (envConfig.ENABLE_MAIN_SWAGGER) {
      // Global API documentation
      const globalConfig = new DocumentBuilder()
        .setTitle('Valyuu API')
        .setDescription('API documentation for Valyuu 2.0 project')
        .addBearerAuth(
          {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'Authorization',
            description: 'JWT Authorization',
          },
          'BearerAuth'
        )
        .build()
      const globalDocument = SwaggerModule.createDocument(app, globalConfig)
      SwaggerModule.setup('swagger', app, globalDocument, {
        swaggerOptions: {
          persistAuthorization: true,
        },
      })
    }

    // Partner module documentation
    const partnerConfig = new DocumentBuilder()
      .setTitle('Valyuu Partner API')
      .setDescription("API integration documentation for Valyuu's Partners")
      .setVersion('1.0')
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-Api-Key',
          in: 'header',
          description: 'Your API key',
        },
        'APIKeyAuth'
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-Api-Secret',
          in: 'header',
          description: 'Your API secret key',
        },
        'APISecretAuth'
      )
      .addSecurityRequirements('APIKeyAuth')
      .addSecurityRequirements('APISecretAuth')
      .build()
    const partnerDocument = SwaggerModule.createDocument(app, partnerConfig, {
      include: [PartnerModule],
      autoTagControllers: false,
    })
    SwaggerModule.setup('partner/swagger', app, partnerDocument, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    })
  }
  app.useGlobalPipes(new ValidationPipe({ transform: true }))
  // TODO: fix cors
  app.enableCors()
  app.disable('x-powered-by')
  app.set('trust proxy', true)
  await app.listen(envConfig.PORT)
}
bootstrap()
