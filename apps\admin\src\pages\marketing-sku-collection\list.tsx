import { limitFit } from '@cloudinary/url-gen/actions/resize'
import { Date<PERSON>ield, DeleteButton, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IMarketingSkuCollection, IProductSku } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import type { FC } from 'react'

import { PublishStatus } from '~/components'
import { cloudinary, handleTableRowClick } from '~/utils'

export const MarketingSkuCollectionList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<IMarketingSkuCollection>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'name__en', 'slug__en', 'createdAt', 'publishedAt'],
      join: [
        {
          field: 'productSkus',
          select: ['id', 'name__en'],
        },
        {
          field: 'headerBgImageMobile__en',
          select: ['publicId'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/marketing-sku-collections', id!)))}
      >
        <Table.Column
          dataIndex="headerBgImageMobile__en"
          title="Image"
          className="cursor-pointer"
          align="center"
          render={(value) => (
            <img
              src={cloudinary.image(value.publicId).resize(limitFit(140, 60)).toURL()}
              className="h-auto max-h-[60px] max-w-[140px]"
              alt=""
            />
          )}
          width="7rem"
        />
        <Table.Column
          dataIndex="productSkus"
          title="Product SKUs"
          className="cursor-pointer"
          align="right"
          render={(values: IProductSku[]) => values.length}
          width="8rem"
        />
        <Table.Column
          dataIndex="name__en"
          title="Name (English)"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
        />
        <Table.Column
          dataIndex="slug__en"
          title="Slug (English)"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column<IMarketingSkuCollection>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
