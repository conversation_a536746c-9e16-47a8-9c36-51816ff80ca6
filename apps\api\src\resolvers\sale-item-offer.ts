import { BadRequestException } from '@nestjs/common'
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql'

import { SaleItemOfferErrorType, SaleItemOfferStatus, SaleItemStatus } from '~/constants'
import {
  ConfirmSaleItemOfferInput,
  ConfirmSaleItemOfferOutput,
  GetSaleItemOfferDetailsInput,
  GetSaleItemOfferDetailsOutput,
} from '~/dtos'
import { SaleItemEntity, SaleItemOfferEntity } from '~/entities'
import { ProductModelService, SaleItemOfferService, SaleItemService, SaleService } from '~/services'

@Resolver()
export class SaleItemOfferResolver {
  constructor(
    protected readonly modelService: ProductModelService,
    protected readonly saleService: SaleService,
    protected readonly saleItemService: SaleItemService,
    protected readonly saleItemOfferService: SaleItemOfferService
  ) {}

  private async validateOffer(
    input: GetSaleItemOfferDetailsInput
  ): Promise<{ success: boolean; error?: SaleItemOfferErrorType; saleItemId?: string }> {
    if (!input.offerId || !input.authToken) {
      throw new BadRequestException('Missing offerId or authToken')
    }

    const offer = await SaleItemOfferEntity.findOne({
      where: { id: input.offerId, authToken: input.authToken },
      relations: { saleItem: true },
      select: {
        id: true,
        saleItemId: true,
        authToken: true,
        saleItem: { id: true, status: true, offerId: true, offerStatus: true },
        expiresAt: true,
      },
    })

    let success = true
    let error: SaleItemOfferErrorType | undefined
    const saleItemId = offer?.saleItemId

    switch (true) {
      case !offer:
        success = false
        error = SaleItemOfferErrorType.NOT_FOUND
        break

      case !input.authToken || offer.authToken !== input.authToken:
        success = false
        error = SaleItemOfferErrorType.NOT_AUTHORIZED
        break

      case offer.saleItem.offerId !== input.offerId ||
        new Date(offer.expiresAt) < new Date() ||
        offer.saleItem.status !== SaleItemStatus.PENDING_OFFER:
        success = false
        error = SaleItemOfferErrorType.EXPIRED
        break

      case offer.saleItem.offerStatus === SaleItemOfferStatus.ACCEPTED:
        success = false
        error = SaleItemOfferErrorType.ALREADY_ACCEPTED
        break

      case offer.saleItem.offerStatus === SaleItemOfferStatus.DECLINED_RECYCLE ||
        offer.saleItem.offerStatus === SaleItemOfferStatus.DECLINED_RETURN:
        success = false
        error = SaleItemOfferErrorType.ALREADY_REJECTED
        break
    }

    return { success, error, saleItemId }
  }

  @Query(() => GetSaleItemOfferDetailsOutput)
  async getSaleItemOfferDetails(@Args() input: GetSaleItemOfferDetailsInput): Promise<GetSaleItemOfferDetailsOutput> {
    const validationError = await this.validateOffer(input)
    if (
      !validationError.success &&
      ([SaleItemOfferErrorType.NOT_FOUND, SaleItemOfferErrorType.NOT_AUTHORIZED].includes(validationError.error) ||
        !validationError.saleItemId)
    ) {
      return validationError
    }

    const saleItem = await SaleItemEntity.findOne({ where: { id: validationError.saleItemId } })
    const result = await this.saleItemService.getDisplay(saleItem)
    return {
      success: validationError.success,
      error: validationError.error,
      result,
    }
  }

  @Mutation(() => ConfirmSaleItemOfferOutput)
  async confirmSaleItemOffer(@Args() input: ConfirmSaleItemOfferInput): Promise<ConfirmSaleItemOfferOutput> {
    const validationError = await this.validateOffer(input)
    if (validationError.success === false) return validationError

    await this.saleItemOfferService.confirm(validationError.saleItemId, input.confirmationType)

    return {
      success: true,
    }
  }
}
