import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Edit<PERSON>utton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, IResourceComponentsProps, useApiUrl, useList, useNavigation } from '@refinedev/core'
import { IChannel, type IProductVariant } from '@valyuu/api/entities'
import { Input, Space, Table } from 'antd'
import { FC, useState } from 'react'

import { PublishStatus } from '~/components'
import { ButtonDataExchangeExport, ButtonDataExchangeImport } from '~/components'
import { filterSearchIcon, handleTableRowClick } from '~/utils'

export const ProductVariantList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IProductVariant>({
    syncWithLocation: true,
    meta: {
      fields: ['name__en', 'slug__en', 'sortOrder', 'createdAt', 'publishedAt'],
      join: [],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    filters: {
      initial: [
        {
          field: 'name__en',
          operator: 'contains',
          value: '',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { data } = useList<IChannel>({
    resource: 'admin/channels',
    filters: [
      {
        field: 'disableSeller',
        operator: 'eq',
        value: false,
      },
    ],
    sorters: [
      {
        field: 'createdAt',
        order: 'asc',
      },
    ],
    meta: {
      fields: ['id', 'desc'],
    },
  })

  const channels = data?.data

  const { editUrl, push } = useNavigation()
  const [disableImport, setDisableImport] = useState(false)
  const [disableExport, setDisableExport] = useState(false)

  const apiUrl = useApiUrl()

  return (
    <List
      headerButtons={({ defaultButtons }) => (
        <>
          <Space>
            <ButtonDataExchangeExport
              content="prices"
              url={`${apiUrl}/admin/data-exchange/variant-price-export`}
              channels={channels?.map((channel) => channel.id)}
              disabled={disableExport}
              onChange={setDisableImport}
            />
            <ButtonDataExchangeImport
              content="prices"
              url={`${apiUrl}/admin/data-exchange/variant-price-import`}
              channels={channels?.map((channel) => channel.id)}
              disabled={disableImport}
              onChange={setDisableExport}
            />
            {defaultButtons}
          </Space>
        </>
      )}
    >
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/product-variants', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="name__en"
          title="Name (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('name__en', filters, 'contains')}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Name (English)" />
            </FilterDropdown>
          )}
          filterIcon={filterSearchIcon}
        />
        <Table.Column
          dataIndex="slug__en"
          title="Slug (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('slug__en', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Slug (English)" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IProductVariant>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
