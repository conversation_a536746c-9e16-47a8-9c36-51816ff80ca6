import '@uppy/core/dist/style.min.css'
import '@uppy/dashboard/dist/style.min.css'
import '@uppy/webcam/dist/style.min.css'
import '@uppy/image-editor/dist/style.css'
import './index.css'

import { limitFit } from '@cloudinary/url-gen/actions/resize'
import { useGetIdentity } from '@refinedev/core'
import Uppy from '@uppy/core'
import ImageEditor from '@uppy/image-editor'
import { Dashboard } from '@uppy/react'
import Webcam from '@uppy/webcam'
import XHR from '@uppy/xhr-upload'
import { ImageProcessNameType, type ImageUsedInType } from '@valyuu/api/constants'
import type { IImage } from '@valyuu/api/entities'
import {
  Button,
  Card,
  Descriptions,
  Drawer,
  Flex,
  Form,
  Image,
  Modal,
  Popconfirm,
  Popover,
  Space,
  Typography,
} from 'antd'
import { create, Input } from 'antx'
import { cloneDeep, isEqual, pick } from 'lodash'
import { useEffect, useMemo, useRef, useState } from 'react'
import { AiOutlineDelete, AiOutlineEdit, AiOutlineUpload } from 'react-icons/ai'
import { useImmer } from 'use-immer'

import { AdminUserFrontendType } from '~/interfaces'
import { cloudinary } from '~/utils'

export type InputImageProps<T = IImage | IImage[]> = {
  entityType: ImageUsedInType
  entityId: string
  limit?: number
  value?: T
  onChange?: (value: T) => void
  plugins?: ('Webcam' | 'ImageEditor' | 'GoogleDrive')[]
  defaultNames?: {
    name__en?: string
    name__nl?: string
    name__de?: string
    name__pl?: string
  }
  processName?: ImageProcessNameType
}

export const InputImage = create(
  ({
    entityType,
    entityId,
    limit = Infinity,
    value = [],
    onChange,
    plugins = [],
    defaultNames,
    processName = undefined,
  }: InputImageProps) => {
    const [uploadOpen, setUploadOpen] = useState(false)
    const [editingImage, setEditingImage] = useState<IImage | null>(null)
    const normalizedValue = value ? cloneDeep(Array.isArray(value) ? value : ([value as IImage] as IImage[])) : []
    normalizedValue.forEach((image) => {
      if (!image.name__en) {
        image.name__en = defaultNames?.name__en || null
      }
      if (!image.name__nl) {
        image.name__nl = defaultNames?.name__nl || null
      }
      if (!image.name__de) {
        image.name__de = defaultNames?.name__de || null
      }
      if (!image.name__pl) {
        image.name__pl = defaultNames?.name__pl || null
      }
    })
    const [imageList, setImageList] = useImmer<IImage[]>(normalizedValue)
    const imageListLastValue = useRef(imageList)
    const { data: user } = useGetIdentity<AdminUserFrontendType>() ?? {}

    const uppy = useMemo(() => {
      const uppy = new Uppy({
        restrictions: {
          allowedFileTypes: ['image/*'],
          maxNumberOfFiles: Math.max(limit - imageList.length, 0),
        },
        debug: import.meta.env.DEV,
        meta: {
          entityId,
          entityType,
        },
      })

      uppy.use(XHR, {
        endpoint: import.meta.env.VITE_API_URL + '/admin/images/upload',
        formData: true,
        fieldName: 'file',
        onBeforeRequest: (request) => {
          if (user?.token) {
            request.setRequestHeader('Authorization', `Bearer ${user.token}`)
          }
        },
      })

      if (plugins.includes('ImageEditor')) {
        uppy.use(ImageEditor, {
          quality: 1,
        })
      }

      if (plugins.includes('Webcam')) {
        uppy.use(Webcam)
      }

      return uppy
    }, [limit, entityId, entityType, user?.token])

    useEffect(() => {
      uppy.setOptions({
        restrictions: {
          allowedFileTypes: ['image/*'],
          maxNumberOfFiles: Math.max(limit - imageList.length, 0),
        },
        debug: import.meta.env.DEV,
        meta: {
          entityId,
          entityType,
        },
      })
    }, [imageList.length, limit, entityId, entityType, uppy])

    useEffect(() => {
      if (onChange && !isEqual(imageList, imageListLastValue.current)) {
        if (limit === 1) {
          onChange(imageList[0] ?? undefined)
        } else {
          onChange(imageList)
        }
        imageListLastValue.current = imageList
      }
    }, [imageList, limit, onChange])

    const [form] = Form.useForm<{
      name__en?: string | null
      name__nl?: string | null
      name__de?: string | null
      name__pl?: string | null
    }>()

    const imageNameFieldsAllHaveDefaultValue = !!(
      defaultNames?.name__en &&
      defaultNames?.name__nl &&
      defaultNames?.name__de
    )

    return (
      <>
        <Button
          disabled={imageList.length >= limit}
          title={imageList.length >= limit ? 'Maximum number of images reached' : undefined}
          icon={<AiOutlineUpload />}
          onClick={() => setUploadOpen(true)}
        >
          Upload
        </Button>
        <Flex className="mt-4" wrap="wrap" gap="large">
          <Image.PreviewGroup>
            {imageList.map((image, index) => (
              <Card
                key={image.id}
                className="image-preview-card w-[322px]"
                cover={
                  <Image
                    src={cloudinary.image(image.publicId).resize(limitFit(286, 206)).toURL()}
                    className="mx-auto max-h-[206px] max-w-[286px] !rounded-none object-contain"
                    preview={{
                      src: image.url,
                      movable: false,
                    }}
                  />
                }
                actions={[
                  <div
                    className="size-full"
                    onClick={() => {
                      setEditingImage(image)
                    }}
                    title="Edit image info"
                  >
                    <AiOutlineEdit key="edit" />
                  </div>,
                  // <div
                  //   className="size-full"
                  //   onClick={() => {
                  //     setEditingImage(image)
                  //   }}
                  //   title="Replace image"
                  // >
                  //   <AiOutlineCloudUpload key="replace" className="size-4" />
                  // </div>,
                  <Popconfirm
                    title="Delete the task"
                    description="Are you sure to delete this task?"
                    onConfirm={() =>
                      setImageList((draft) => {
                        draft.splice(index, 1)
                      })
                    }
                    okText="Yes"
                    cancelText="No"
                  >
                    <div className="size-full" title="Delete image">
                      <AiOutlineDelete />
                    </div>
                  </Popconfirm>,
                ]}
                bordered
              >
                <Card.Meta
                  description={
                    <Popover
                      content={
                        <Descriptions bordered column={1}>
                          <Descriptions.Item label="Image ID" className="max-w-[200px]">
                            <Typography.Paragraph copyable ellipsis className="!mb-0 w-full">
                              {image.id}
                            </Typography.Paragraph>
                          </Descriptions.Item>
                          <Descriptions.Item label="URL" className="max-w-[200px]">
                            <Typography.Link
                              copyable
                              ellipsis
                              className="!mb-0 w-full"
                              href={image.url}
                              target="_blank"
                            >
                              {image.url}
                            </Typography.Link>
                          </Descriptions.Item>
                          <Descriptions.Item label="Format">
                            <Typography.Text>{image.format}</Typography.Text>
                          </Descriptions.Item>
                          <Descriptions.Item label="Size">
                            <Typography.Text>{`${image.width}x${image.height}`}</Typography.Text>
                          </Descriptions.Item>
                          <Descriptions.Item label="Name (EN)">
                            <Typography.Paragraph className="!mb-0">
                              <Typography.Text type={image.name__en ? undefined : 'danger'}>
                                {image.name__en || 'Not set'}
                              </Typography.Text>
                            </Typography.Paragraph>
                          </Descriptions.Item>
                          <Descriptions.Item label="Name (NL)">
                            <Typography.Paragraph className="!mb-0">
                              <Typography.Text type={image.name__nl ? undefined : 'danger'}>
                                {image.name__nl || 'Not set'}
                              </Typography.Text>
                            </Typography.Paragraph>
                          </Descriptions.Item>
                          <Descriptions.Item label="Name (DE)">
                            <Typography.Paragraph className="!mb-0">
                              <Typography.Text type={image.name__de ? undefined : 'danger'}>
                                {image.name__de || 'Not set'}
                              </Typography.Text>
                            </Typography.Paragraph>
                          </Descriptions.Item>
                        </Descriptions>
                      }
                    >
                      <div className="size-full cursor-help">
                        <Typography.Text
                          type={image.name__en && image.name__nl && image.name__de ? 'success' : 'danger'}
                          className="w-full"
                        >
                          <span>
                            {image.name__en && image.name__nl && image.name__de
                              ? 'All fields filled'
                              : 'Some fields are empty'}
                          </span>
                          <span className="ml-2 text-gray-400">(hover for details)</span>
                        </Typography.Text>
                      </div>
                    </Popover>
                  }
                />
              </Card>
            ))}
          </Image.PreviewGroup>
        </Flex>
        <Modal
          open={uploadOpen}
          title="Upload"
          onCancel={() => {
            setUploadOpen(false)
            uppy.cancelAll()
          }}
          width="80vh"
          centered
          maskClosable={false}
          footer={null}
        >
          <Dashboard
            uppy={uppy}
            plugins={plugins}
            width="100%"
            height="80vh"
            singleFileFullScreen={false}
            proudlyDisplayPoweredByUppy={false}
            doneButtonHandler={() => {
              setImageList((draft) => {
                draft.push(
                  ...uppy
                    .getFiles()
                    .filter((file) => file?.progress?.uploadComplete && file?.response?.body?.id)
                    .slice(0, limit - imageList.length)
                    .map(
                      (file) =>
                        ({
                          ...file.response?.body,
                          name__en: defaultNames?.name__en,
                          name__nl: defaultNames?.name__nl,
                          name__de: defaultNames?.name__de,
                        }) as IImage
                    )
                )
              })
              uppy.cancelAll()
              setUploadOpen(false)
            }}
          />
        </Modal>
        <Drawer
          title="Edit image info"
          open={!!editingImage}
          onClose={() => setEditingImage(null)}
          afterOpenChange={(open) => {
            form.setFieldsValue(pick(editingImage, ['name__en', 'name__nl', 'name__de']))
          }}
          footer={null}
        >
          <Form
            form={form}
            component={false}
            layout="horizontal"
            initialValues={{
              name__en: editingImage?.name__en,
              name__nl: editingImage?.name__nl,
              name__de: editingImage?.name__de,
            }}
          >
            <Form.Item className="flex justify-center">
              <img
                src={cloudinary.image(editingImage?.publicId).resize(limitFit(286, 206)).toURL()}
                className="h-auto max-h-[206px] max-w-[286px]"
                alt=""
              />
            </Form.Item>
            <Form.Item
              noStyle={!processName && !imageNameFieldsAllHaveDefaultValue}
              extra={
                processName === ImageProcessNameType.HIDE || !processName
                  ? undefined
                  : imageNameFieldsAllHaveDefaultValue
                    ? 'Please note: If any fields are left blank, they will automatically be filled with the default values shown beneath the input fields.'
                    : `Please note: If you enter a name in only one language and leave the other fields blank, they will automatically be ${
                        {
                          [ImageProcessNameType.COPY]: 'copied',
                          [ImageProcessNameType.TRANSLATE]: 'translated',
                        }[processName]
                      } into the corresponding languages.`
              }
            >
              <Input
                label="Name (EN)"
                name="name__en"
                autoComplete="off"
                extra={defaultNames?.name__en ? `Default to ${defaultNames.name__en}` : undefined}
                hidden={processName === ImageProcessNameType.HIDE}
              />
              <Input
                label="Name (NL)"
                name="name__nl"
                autoComplete="off"
                extra={defaultNames?.name__nl ? `Default to ${defaultNames.name__nl}` : undefined}
                hidden={processName === ImageProcessNameType.HIDE}
              />
              <Input
                label="Name (DE)"
                name="name__de"
                autoComplete="off"
                extra={defaultNames?.name__de ? `Default to ${defaultNames.name__de}` : undefined}
                hidden={processName === ImageProcessNameType.HIDE}
              />
              <Input
                label="Name (PL)"
                name="name__pl"
                autoComplete="off"
                extra={defaultNames?.name__pl ? `Default to ${defaultNames.name__pl}` : undefined}
                hidden={processName === ImageProcessNameType.HIDE}
              />
            </Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                onClick={() => {
                  const formValues = form.getFieldsValue()
                  let { name__en, name__nl, name__de, name__pl } = formValues
                  if (!name__en?.trim()) {
                    name__en = defaultNames?.name__en || null
                  }
                  if (!name__nl?.trim()) {
                    name__nl = defaultNames?.name__nl || null
                  }
                  if (!name__de?.trim()) {
                    name__de = defaultNames?.name__de || null
                  }
                  if (!name__pl?.trim()) {
                    name__pl = defaultNames?.name__pl || null
                  }
                  setImageList((draft) => {
                    const index = draft.findIndex((image) => image.id === editingImage?.id)
                    if (index !== -1) {
                      draft[index] = {
                        ...draft[index],
                        name__en,
                        name__nl,
                        name__de,
                        name__pl,
                        processName,
                      }
                    }
                  })
                  setEditingImage(null)
                }}
              >
                Submit
              </Button>
              <Button
                htmlType="button"
                onClick={() => form.resetFields()}
                hidden={processName === ImageProcessNameType.HIDE}
              >
                Reset
              </Button>
            </Space>
          </Form>
        </Drawer>
      </>
    )
  }
)
