import { sendcloudClientV2 } from '../client'
import { IParcelObject } from '../interfaces'

export type IFindParcelsParams = {
  /** ISO 8601 DateTime string (e.g., '2018-02-26T11:01:47.505309+00:00') */
  announced_after?: string

  /** Token for pagination, included in the response */
  cursor?: string

  /** Set to 'verbose-carrier' to visualize carrier errors */
  errors?: 'verbose-carrier'

  /** External reference to filter parcels */
  external_reference?: string

  /** Comma-separated list of Parcel IDs (max 100) */
  ids?: string

  /** Filter by specific order number */
  order_number?: string

  /** Page number for pagination */
  page?: number

  /** Number of results per page */
  page_size?: number

  /** Filter by parcel status (numeric value) */
  parcel_status?: number

  /** Filter by tracking number */
  tracking_number?: string

  /** ISO 8601 DateTime string to filter by last update time */
  updated_after?: string
}

export type IFindParcelsResponse = {
  next?: number
  previous?: number
  parcels: IParcelObject[]
}

export const findParcels = async (params?: IFindParcelsParams): Promise<IFindParcelsResponse> => {
  const { data } = await sendcloudClientV2.get('/parcels', {
    params,
  })

  return data
}
