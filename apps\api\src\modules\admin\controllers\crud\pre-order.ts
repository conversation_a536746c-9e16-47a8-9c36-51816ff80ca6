import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PreOrderEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudPreOrderService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: PreOrderEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      channel: {},
      currency: {},
      order: {},
      user: {},
      language: {},
      shippingAddress: {},
      billingAddress: {},
      productSku: {},
    },
  },
})
@Controller('admin/pre-orders')
export class CrudPreOrderController implements CrudController<PreOrderEntity> {
  constructor(public service: CrudPreOrderService) {}
}
