import type { RefineThemedLayoutV2HeaderProps } from '@refinedev/antd'
import { useActiveAuthProvider, useGetIdentity } from '@refinedev/core'
import { Avatar, Layout as AntdLayout, Space, theme, Typography } from 'antd'
import type { FC } from 'react'

const { Text } = Typography
const { useToken } = theme

export const ThemedHeaderV2: FC<RefineThemedLayoutV2HeaderProps> = () => {
  const { token } = useToken()

  const authProvider = useActiveAuthProvider()
  const { data: user } = useGetIdentity({
    v3LegacyAuthProviderCompatible: Bo<PERSON>an(authProvider?.isLegacy),
  })

  return (
    <AntdLayout.Header
      className="flex h-16 items-center justify-end px-6 py-0"
      style={{
        backgroundColor: token.colorBgElevated,
      }}
    >
      <Space>
        <Space size="small">
          {user?.name ? <Text strong>{user.name}</Text> : null}
          {user?.picture ? <Avatar src={user.picture} alt={user?.name} /> : null}
        </Space>
      </Space>
    </AntdLayout.Header>
  )
}
