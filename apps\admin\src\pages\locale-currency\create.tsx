import { Create, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps } from '@refinedev/core'
import type { ILocaleCurrency } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input } from 'antx'
import { type FC } from 'react'

export const LocaleCurrencyCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps } = useForm<ILocaleCurrency, HttpError, ILocaleCurrency>()

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Input
          label="ID"
          name="id"
          rules={['required']}
          normalize={(value) =>
            value
              .replace(/[^a-zA-Z]/g, '')
              .trim()
              .substring(0, 3)
              .toUpperCase()
          }
        />
        <Input label="Name" name="name" rules={['required']} />
        <Input
          label="Symbol"
          name="symbol"
          rules={['required']}
          normalize={(value) =>
            value
              .replace(/[a-zA-Z]/g, '')
              .trim()
              .substring(0, 1)
          }
        />
      </Form>
    </Create>
  )
}
