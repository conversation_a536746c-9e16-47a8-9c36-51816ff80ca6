import { Injectable } from '@nestjs/common'
import ms from 'ms'
import { ArrayContains, IsNull, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { FaqCollectionType } from '~/constants'
import { FaqEntity } from '~/entities'

@Injectable()
export class FaqService {
  async findAll(collection?: FaqCollectionType) {
    return FaqEntity.find({
      where: {
        ...(collection ? { collections: ArrayContains([collection]) } : {}),
        publishedAt: Not(IsNull()),
      },
      order: { collections: 'ASC', sortOrder: 'ASC' },
      cache: envConfig.isProd ? ms('30 minutes') : false,
    })
  }
}
