export enum PartnerWebhookEvent {
  TRADE_IN_CREATED = 'TRADE_IN_CREATED',
  TRADE_IN_SHIPMENT_CREATION_FAILED = 'TRADE_IN_SHIPMENT_CREATION_FAILED',
  TRADE_IN_UPDATED = 'TRADE_IN_UPDATED',
  TRADE_IN_ITEM_CREATED = 'TRADE_IN_ITEM_CREATED',
  TRADE_IN_ITEM_NEW_OFFER_CREATED = 'TRADE_IN_ITEM_NEW_OFFER_CREATED',
  TRADE_IN_ITEM_UPDATED = 'TRADE_IN_ITEM_UPDATED',
}

export enum PartnerWebhookEntityType {
  TRADE_IN = 'TRADE_IN',
  TRADE_IN_ITEM = 'TRADE_IN_ITEM',
}
