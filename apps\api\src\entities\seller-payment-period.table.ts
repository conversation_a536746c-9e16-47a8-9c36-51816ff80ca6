import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { SellerPaymentPeriodUnit } from '~/constants'
import { ChannelEntity, IChannel, PartnerEntity } from '~/entities'

@Entity('seller_payment_period')
export class SellerPaymentPeriodEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @OneToOne(() => ChannelEntity, (channel) => channel.sellerPaymentPeriod, { nullable: true })
  @JoinColumn()
  channel?: Relation<ChannelEntity>

  @Index()
  @Column({ nullable: true })
  @RelationId((channelSellerPaymentPeriod: SellerPaymentPeriodEntity) => channelSellerPaymentPeriod.channel)
  channelId?: string

  @OneToOne(() => PartnerEntity, { nullable: true })
  @JoinColumn()
  partner?: Relation<PartnerEntity>

  @Index()
  @Column({ nullable: true })
  @RelationId((channelSellerPaymentPeriod: SellerPaymentPeriodEntity) => channelSellerPaymentPeriod.partner)
  partnerId?: string

  @Column({ type: 'int2', unsigned: true, nullable: true })
  c2bFrom?: number

  @Column({ type: 'int2', unsigned: true })
  c2bTo: number

  @Column({ type: 'varchar' })
  c2bUnit: SellerPaymentPeriodUnit

  @Column({ type: 'int2', unsigned: true, nullable: true })
  c2cFrom?: number

  @Column({ type: 'int2', unsigned: true })
  c2cTo: number

  @Column({ type: 'varchar' })
  c2cUnit: SellerPaymentPeriodUnit

  @Column({ type: 'int2', unsigned: true, nullable: true })
  donationFrom?: number

  @Column({ type: 'int2', unsigned: true })
  donationTo: number

  @Column({ type: 'varchar' })
  donationUnit: SellerPaymentPeriodUnit

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ISellerPaymentPeriod = Omit<SellerPaymentPeriodEntity, keyof BaseEntity> & {
  channel?: IChannel
}
