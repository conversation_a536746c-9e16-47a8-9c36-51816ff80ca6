import { Date<PERSON><PERSON>, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { ChannelType } from '@valyuu/api/constants'
import type { IChannel } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import type { FC } from 'react'

import { PublishStatus } from '~/components'
import { handleTableRowClick } from '~/utils'

export const ChannelList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<IChannel>({
    syncWithLocation: true,
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/channels', id!)))}
      >
        <Table.Column dataIndex="id" title="ID" className="cursor-pointer" />
        <Table.Column dataIndex="name" title="Name" className="cursor-pointer" />
        <Table.Column dataIndex="currencyId" title="Currency" className="cursor-pointer" />
        <Table.Column
          dataIndex="disableBuyer"
          title="Disable buyer"
          className="cursor-pointer"
          render={(value) => (value ? 'Yes' : 'No')}
        />
        <Table.Column
          dataIndex="disableSeller"
          title="Disable seller"
          className="cursor-pointer"
          render={(value) => (value ? 'Yes' : 'No')}
        />
        <Table.Column
          dataIndex="type"
          title="Type"
          className="cursor-pointer"
          render={(value: ChannelType) => ({ [ChannelType.PARTNER]: 'Partner', [ChannelType.REGION]: 'Region' })[value]}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column<IChannel>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
