import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'

import * as entities from '~/entities'
import * as controllers from '~partner/controllers'
import * as services from '~partner/services'

@Module({
  imports: [TypeOrmModule.forFeature(Object.values(entities).filter((entity) => entity?.name?.endsWith('Entity')))],
  controllers: Object.values(controllers).filter((controller) => controller?.name?.endsWith('Controller')),
  providers: [...Object.values(services).filter((service) => service?.name?.endsWith('Service'))],
})
export class PartnerModule {}
