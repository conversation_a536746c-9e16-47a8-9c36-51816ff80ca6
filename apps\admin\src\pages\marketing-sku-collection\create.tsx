import { Create, useForm } from '@refinedev/antd'
import type { HttpError, IResourceComponentsProps } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type { IMarketingSkuCollection } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputImage, InputLanguageTab, InputMultiLang, InputPublish, InputSlug, LanguageTabChoices } from '~/components'

export const MarketingSkuCollectionCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<IMarketingSkuCollection, HttpError, IMarketingSkuCollection>()
  const id = useMemo(() => uuid(), [])
  const publishedAt = Form.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Polish)"
                name="slug__pl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__pl}
                className={clsHidePl}
              />
              <InputImage
                label="Header desktop image (English)"
                name="headerBgImageDesktop__en"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideEn}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Header mobile image (English)"
                name="headerBgImageMobile__en"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideEn}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Header desktop image (Dutch)"
                name="headerBgImageDesktop__nl"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideNl}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Header mobile image (Dutch)"
                name="headerBgImageMobile__nl"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideNl}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Header desktop image (German)"
                name="headerBgImageDesktop__de"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideDe}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Header mobile image (German)"
                name="headerBgImageMobile__de"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHideDe}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Header desktop image (Polish)"
                name="headerBgImageDesktop__pl"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHidePl}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Header mobile image (Polish)"
                name="headerBgImageMobile__pl"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className={clsHidePl}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.MARKETING_BANNER}
                processName={ImageProcessNameType.TRANSLATE}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
            </>
          )}
        </Watch>
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
