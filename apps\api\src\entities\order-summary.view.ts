import { ViewColumn, ViewEntity } from 'typeorm'

import { SUCCESSFUL_ORDER_STATUSES } from '~/constants'

@ViewEntity({
  name: 'order_summary',
  expression: `
    SELECT
      o.id,
      COUNT(DISTINCT oi.id) AS items_count,
      o.total,
      COALESCE(SUM(oi.warranty_price), 0) + COALESCE(SUM(oapi.unit_price * oapi.quantity), 0) AS additional_revenue,
      o.is_returning_customer,
      a.country_id AS country,
      INITCAP(o.from::text) AS source,
      o.user_id AS user_id,
      CASE
        WHEN o.status IN ('${SUCCESSFUL_ORDER_STATUSES.join("', '")}') THEN true
        ELSE false
      END AS is_successful,
      o.created_at,
      TO_CHAR(o.created_at, 'YYYY-MM-DD') AS day,
      TO_CHAR(o.created_at, 'YYYY-WW') AS week,
      TO_CHAR(o.created_at, 'YYYY-MM') AS month
    FROM "order" o
    LEFT JOIN "order_sku_item" oi ON oi.order_id = o.id
    LEFT JOIN "order_accessory_product_item" oapi ON oapi.order_id = o.id
    LEFT JOIN "address" a ON o.shipping_address_id = a.id
    GROUP BY
      o.id, o.total, o.is_returning_customer, a.country_id, o.from, o.created_at
  `,
})
export class OrderSummaryEntity {
  @ViewColumn()
  id: number

  @ViewColumn()
  itemsCount: number

  @ViewColumn()
  total: number

  @ViewColumn()
  additionalRevenue: number

  @ViewColumn()
  isReturningCustomer: boolean

  @ViewColumn()
  country: string

  @ViewColumn()
  source: string

  @ViewColumn()
  userId: number

  @ViewColumn()
  isSuccess: boolean

  @ViewColumn()
  createdAt: Date

  @ViewColumn()
  day: string

  @ViewColumn()
  week: string

  @ViewColumn()
  month: string
}
