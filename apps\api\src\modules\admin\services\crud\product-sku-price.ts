import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { ProductSkuPriceEntity } from '~/entities'

export class CrudProductSkuPriceService extends TypeOrmCrudService<ProductSkuPriceEntity> {
  constructor(
    @InjectRepository(ProductSkuPriceEntity)
    repo: Repository<ProductSkuPriceEntity>
  ) {
    super(repo)
  }
}
