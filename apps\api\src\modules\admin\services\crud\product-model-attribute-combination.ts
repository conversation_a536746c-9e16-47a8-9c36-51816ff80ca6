import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { ProductModelAttributeCombinationEntity } from '~/entities'

export class CrudProductModelAttributeCombinationService extends TypeOrmCrudService<ProductModelAttributeCombinationEntity> {
  constructor(
    @InjectRepository(ProductModelAttributeCombinationEntity)
    repo: Repository<ProductModelAttributeCombinationEntity>
  ) {
    super(repo)
  }
}
