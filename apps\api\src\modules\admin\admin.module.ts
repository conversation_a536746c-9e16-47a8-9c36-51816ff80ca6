import { Module } from '@nestjs/common'
import { PassportModule } from '@nestjs/passport'
import { TypeOrmModule } from '@nestjs/typeorm'

import * as entities from '~/entities'
import * as controllers from '~admin/controllers'
import * as passportStrategies from '~admin/passport-strategies'
import * as services from '~admin/services'

@Module({
  imports: [
    TypeOrmModule.forFeature(Object.values(entities).filter((entity) => entity?.name?.endsWith('Entity'))),
    PassportModule,
  ],
  controllers: Object.values(controllers).filter((controller) => controller?.name?.endsWith('Controller')),
  providers: [
    ...Object.values(services).filter((service) => service?.name?.endsWith('Service')),
    ...Object.values(passportStrategies).filter((service) => service?.name?.endsWith('Strategy')),
  ],
})
export class AdminModule {}
