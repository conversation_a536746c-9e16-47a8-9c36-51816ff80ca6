import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeEnumFieldsToVarchar1729240575663 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop views
    await queryRunner.query(`
      DROP VIEW IF EXISTS order_summary;
      DROP VIEW IF EXISTS sale_summary;
      DROP VIEW IF EXISTS user_summary;
    `)

    // address table
    await queryRunner.query(`
        ALTER TABLE address ADD COLUMN type_new VARCHAR;
        UPDATE address SET type_new = type::text;
        ALTER TABLE address DROP COLUMN type;
        ALTER TABLE address RENAME COLUMN type_new TO type;
    `)

    // blog_tag table
    await queryRunner.query(`
        ALTER TABLE blog_tag ADD COLUMN color_new VARCHAR;
        UPDATE blog_tag SET color_new = color::text;
        ALTER TABLE blog_tag DROP COLUMN color;
        ALTER TABLE blog_tag RENAME COLUMN color_new TO color;
    `)

    // channel table
    await queryRunner.query(`
        ALTER TABLE channel ADD COLUMN type_new VARCHAR;
        UPDATE channel SET type_new = type::text;
        ALTER TABLE channel DROP COLUMN type;
        ALTER TABLE channel RENAME COLUMN type_new TO type;
    `)

    // error_log table
    await queryRunner.query(`
        ALTER TABLE error_log ADD COLUMN type_new VARCHAR;
        UPDATE error_log SET type_new = type::text;
        ALTER TABLE error_log DROP COLUMN type;
        ALTER TABLE error_log RENAME COLUMN type_new TO type;

        ALTER TABLE error_log ADD COLUMN severity_new VARCHAR;
        UPDATE error_log SET severity_new = severity::text;
        ALTER TABLE error_log DROP COLUMN severity;
        ALTER TABLE error_log RENAME COLUMN severity_new TO severity;
    `)

    // file table
    await queryRunner.query(`
        ALTER TABLE file ADD COLUMN "used_in_new" VARCHAR;
        UPDATE file SET "used_in_new" = "used_in"::text;
        ALTER TABLE file DROP COLUMN "used_in";
        ALTER TABLE file RENAME COLUMN "used_in_new" TO "used_in";
    `)

    // image table
    await queryRunner.query(`
        ALTER TABLE image ADD COLUMN "used_in_new" VARCHAR;
        UPDATE image SET "used_in_new" = "used_in"::text;
        ALTER TABLE image DROP COLUMN "used_in";
        ALTER TABLE image RENAME COLUMN "used_in_new" TO "used_in";
    `)

    // marketing_banner table
    await queryRunner.query(`
        ALTER TABLE marketing_banner ADD COLUMN type_new VARCHAR;
        UPDATE marketing_banner SET type_new = type::text;
        ALTER TABLE marketing_banner DROP COLUMN type;
        ALTER TABLE marketing_banner RENAME COLUMN type_new TO type;
    `)

    // marketing_tag table
    await queryRunner.query(`
        ALTER TABLE marketing_tag ADD COLUMN type_new VARCHAR;
        UPDATE marketing_tag SET type_new = type::text;
        ALTER TABLE marketing_tag DROP COLUMN type;
        ALTER TABLE marketing_tag RENAME COLUMN type_new TO type;
    `)

    // order_sku_item table
    await queryRunner.query(`
        ALTER TABLE order_sku_item ADD COLUMN status_new VARCHAR;
        UPDATE order_sku_item SET status_new = status::text;
        ALTER TABLE order_sku_item DROP COLUMN status;
        ALTER TABLE order_sku_item RENAME COLUMN status_new TO status;
    `)

    // order table
    await queryRunner.query(`
        ALTER TABLE "order" ADD COLUMN status_new VARCHAR;
        UPDATE "order" SET status_new = status::text;
        ALTER TABLE "order" DROP COLUMN status;
        ALTER TABLE "order" RENAME COLUMN status_new TO status;
    `)

    // pre_order table
    await queryRunner.query(`
        ALTER TABLE pre_order ADD COLUMN status_new VARCHAR;
        UPDATE pre_order SET status_new = status::text;
        ALTER TABLE pre_order DROP COLUMN status;
        ALTER TABLE pre_order RENAME COLUMN status_new TO status;

        ALTER TABLE pre_order ADD COLUMN "from_new" VARCHAR;
        UPDATE pre_order SET "from_new" = "from"::text;
        ALTER TABLE pre_order DROP COLUMN "from";
        ALTER TABLE pre_order RENAME COLUMN "from_new" TO "from";
    `)

    // product_issue table
    await queryRunner.query(`
        ALTER TABLE product_issue ADD COLUMN type_new VARCHAR;
        UPDATE product_issue SET type_new = type::text;
        ALTER TABLE product_issue DROP COLUMN type;
        ALTER TABLE product_issue RENAME COLUMN type_new TO type;
    `)

    // product_model_selling_promise table
    await queryRunner.query(`
        ALTER TABLE product_model_selling_promise ADD COLUMN c2b_unit_new VARCHAR;
        UPDATE product_model_selling_promise SET c2b_unit_new = c2b_unit::text;
        ALTER TABLE product_model_selling_promise DROP COLUMN c2b_unit;
        ALTER TABLE product_model_selling_promise RENAME COLUMN c2b_unit_new TO c2b_unit;
    `)

    // product_sku table
    await queryRunner.query(`
        ALTER TABLE product_sku ADD COLUMN grading_new VARCHAR;
        UPDATE product_sku SET grading_new = grading::text;
        ALTER TABLE product_sku DROP COLUMN grading;
        ALTER TABLE product_sku RENAME COLUMN grading_new TO grading;

        ALTER TABLE product_sku ADD COLUMN color_new VARCHAR;
        UPDATE product_sku SET color_new = color::text;
        ALTER TABLE product_sku DROP COLUMN color;
        ALTER TABLE product_sku RENAME COLUMN color_new TO color;

        ALTER TABLE product_sku ADD COLUMN btw_new VARCHAR;
        UPDATE product_sku SET btw_new = btw::text;
        ALTER TABLE product_sku DROP COLUMN btw;
        ALTER TABLE product_sku RENAME COLUMN btw_new TO btw;

        ALTER TABLE product_sku ADD COLUMN source_new VARCHAR;
        UPDATE product_sku SET source_new = source::text;
        ALTER TABLE product_sku DROP COLUMN source;
        ALTER TABLE product_sku RENAME COLUMN source_new TO source;
    `)

    // sale_item_payment table
    await queryRunner.query(`
        ALTER TABLE sale_item_payment ADD COLUMN status_new VARCHAR;
        UPDATE sale_item_payment SET status_new = status::text;
        ALTER TABLE sale_item_payment DROP COLUMN status;
        ALTER TABLE sale_item_payment RENAME COLUMN status_new TO status;
    `)

    // sale_item table
    await queryRunner.query(`
        ALTER TABLE sale_item ADD COLUMN type_new VARCHAR;
        UPDATE sale_item SET type_new = type::text;
        ALTER TABLE sale_item DROP COLUMN type;
        ALTER TABLE sale_item RENAME COLUMN type_new TO type;

        ALTER TABLE sale_item ADD COLUMN "original_type_new" VARCHAR;
        UPDATE sale_item SET "original_type_new" = "original_type"::text;
        ALTER TABLE sale_item DROP COLUMN "original_type";
        ALTER TABLE sale_item RENAME COLUMN "original_type_new" TO "original_type";
    `)

    // sale table
    await queryRunner.query(`
        ALTER TABLE sale ADD COLUMN status_new VARCHAR;
        UPDATE sale SET status_new = status::text;
        ALTER TABLE sale DROP COLUMN status;
        ALTER TABLE sale RENAME COLUMN status_new TO status;

        ALTER TABLE sale ADD COLUMN "shipping_label_new" VARCHAR;
        UPDATE sale SET "shipping_label_new" = "shipping_label"::text;
        ALTER TABLE sale DROP COLUMN "shipping_label";
        ALTER TABLE sale RENAME COLUMN "shipping_label_new" TO "shipping_label";

        ALTER TABLE sale ADD COLUMN "payment_type_new" VARCHAR;
        UPDATE sale SET "payment_type_new" = "payment_type"::text;
        ALTER TABLE sale DROP COLUMN "payment_type";
        ALTER TABLE sale RENAME COLUMN "payment_type_new" TO "payment_type";
    `)

    // seo_content table
    await queryRunner.query(`
        ALTER TABLE seo_content ADD COLUMN type_new VARCHAR;
        UPDATE seo_content SET type_new = type::text;
        ALTER TABLE seo_content DROP COLUMN type;
        ALTER TABLE seo_content RENAME COLUMN type_new TO type;
    `)

    // shipping_method table
    await queryRunner.query(`
        ALTER TABLE shipping_method ADD COLUMN sendcloud_shipment_id_new VARCHAR;
        UPDATE shipping_method SET sendcloud_shipment_id_new = sendcloud_shipment_id::text;
        ALTER TABLE shipping_method DROP COLUMN sendcloud_shipment_id;
        ALTER TABLE shipping_method RENAME COLUMN sendcloud_shipment_id_new TO sendcloud_shipment_id;

        ALTER TABLE shipping_method ADD COLUMN shipping_direction_new VARCHAR;
        UPDATE shipping_method SET shipping_direction_new = shipping_direction::text;
        ALTER TABLE shipping_method DROP COLUMN shipping_direction;
        ALTER TABLE shipping_method RENAME COLUMN shipping_direction_new TO shipping_direction;
    `)

    // stock table
    await queryRunner.query(`
        ALTER TABLE stock ADD COLUMN type_new VARCHAR;
        UPDATE stock SET type_new = type::text;
        ALTER TABLE stock DROP COLUMN type;
        ALTER TABLE stock RENAME COLUMN type_new TO type;
    `)

    // testimonial table
    await queryRunner.query(`
        ALTER TABLE testimonial ADD COLUMN role_new VARCHAR;
        UPDATE testimonial SET role_new = role::text;
        ALTER TABLE testimonial DROP COLUMN role;
        ALTER TABLE testimonial RENAME COLUMN role_new TO role;
    `)

    // warranty table
    await queryRunner.query(`
        ALTER TABLE warranty ADD COLUMN period_unit_new VARCHAR;
        UPDATE warranty SET period_unit_new = period_unit::text;
        ALTER TABLE warranty DROP COLUMN period_unit;
        ALTER TABLE warranty RENAME COLUMN period_unit_new TO period_unit;
    `)

    // admin_user table
    await queryRunner.query(`
        ALTER TABLE admin_user ADD COLUMN provider_new VARCHAR;
        UPDATE admin_user SET provider_new = provider::text;
        ALTER TABLE admin_user DROP COLUMN provider;
        ALTER TABLE admin_user RENAME COLUMN provider_new TO provider;
    `)

    // category_warranty_rule table
    await queryRunner.query(`
        ALTER TABLE category_warranty_rule 
        ADD COLUMN "eligible_grades_new" varchar[];
        
        UPDATE category_warranty_rule 
        SET "eligible_grades_new" = array(select unnest("eligible_grades")::text);
        
        ALTER TABLE category_warranty_rule 
        DROP COLUMN "eligible_grades";
        
        ALTER TABLE category_warranty_rule 
        RENAME COLUMN "eligible_grades_new" TO "eligible_grades";
    `)

    // faq table
    await queryRunner.query(`
        ALTER TABLE faq 
        ADD COLUMN collections_new varchar[];
        
        UPDATE faq 
        SET collections_new = array(select unnest(collections)::text);
        
        ALTER TABLE faq 
        DROP COLUMN collections;
        
        ALTER TABLE faq 
        RENAME COLUMN collections_new TO collections;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
