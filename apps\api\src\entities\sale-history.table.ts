import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  Relation,
  RelationId,
} from 'typeorm'

import { ChangeHistoryChangedByType, SalePaymentType, SaleShippingLabelType, SaleStatus, UserFrom } from '~/constants'
import { AdminUserEntity, PartnerEntity, SaleEntity } from '~/entities'

@Entity('sale_history')
export class SaleHistoryEntity extends BaseEntity {
  @PrimaryColumn({ type: 'smallint' })
  version: number

  @ManyToOne(() => SaleEntity, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  sale: Relation<SaleEntity>

  @PrimaryColumn()
  @RelationId((history: SaleHistoryEntity) => history.sale)
  saleId: string

  @Column({
    type: 'enum',
    enum: ChangeHistoryChangedByType,
    nullable: false,
  })
  changedByType: ChangeHistoryChangedByType

  @Column('uuid', { nullable: true })
  changedById?: string

  @ManyToOne(() => AdminUserEntity, { createForeignKeyConstraints: false, nullable: true })
  @JoinColumn({
    name: 'changed_by_id',
    referencedColumnName: 'id',
  })
  changedByAdminUser?: Relation<AdminUserEntity>

  @ManyToOne(() => PartnerEntity, { createForeignKeyConstraints: false, nullable: true })
  @JoinColumn({
    name: 'changed_by_id',
    referencedColumnName: 'id',
  })
  changedByPartner?: Relation<PartnerEntity>

  get changedBy(): Relation<AdminUserEntity | PartnerEntity> | undefined {
    return this.changedByType === ChangeHistoryChangedByType.ADMIN ? this.changedByAdminUser : this.changedByPartner
  }

  // SaleEntity fields
  @Column()
  saleNumber: string

  @Index()
  @Column({ type: 'varchar', nullable: false })
  status: SaleStatus

  @Column({ type: 'varchar', nullable: false })
  shippingLabel: SaleShippingLabelType

  @Column({ default: false })
  isLabelSent: boolean

  @Column({ nullable: true })
  trackingNumber?: string

  @Column({ nullable: true })
  parcelId?: string

  @Column({ nullable: true })
  shipmentId?: string

  @Column({ type: 'varchar' })
  paymentType: SalePaymentType

  @Column({ nullable: true })
  bankAccountId?: string

  @Column()
  addressId: string

  @Column()
  userId: string

  @Column()
  languageId: string

  @Column({ type: 'varchar' })
  from: UserFrom

  @Column({ type: 'int2', unsigned: true })
  reminderSentCount: number

  @Column()
  surveySent: boolean

  @Column({ nullable: true })
  note?: string

  @Column({ type: 'timestamp', nullable: true })
  receivedAt?: Date | null

  @CreateDateColumn()
  @Index()
  createdAt: Date
}

export type ISaleHistory = Omit<SaleHistoryEntity, keyof BaseEntity>
