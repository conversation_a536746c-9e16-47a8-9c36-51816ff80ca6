import { DataSource } from 'typeorm'

import { typeOrmConfig } from '~/configs'

import * as migrations from '../migrations'
import { cliOptions } from '../utils'

console.info('Running migrations, using parameters:')
console.info(`- purge:`, cliOptions.purge)

new DataSource({
  ...typeOrmConfig,
  dropSchema: cliOptions.purge,
  migrations: Object.values(migrations).filter((migration) => migration?.name),
  migrationsTableName: 'typeorm_migration',
  synchronize: cliOptions.purge,
  logging: false,
})
  .initialize()
  .then((dataSource) => dataSource.runMigrations())
  .then(() => {
    console.info('Finished running migrations')
    process.exit(0)
  })
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
