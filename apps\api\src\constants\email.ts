export enum EmailType {
  BUYER_NEW_ORDER = 'buyer-new-order',
  BUYER_NEW_ORDER_INTERNAL = 'buyer-new-order-internal',
  BUYER_PACKAGE_RECEIVED = 'buyer-package-received',
  SELLER_NEW_OFFER_HAS_VALUE = 'seller-new-offer-has-value',
  SELLER_NEW_OFFER_NO_VALUE = 'seller-new-offer-no-value',
  SELLER_NEW_SALE = 'seller-new-sale',
  SELLER_OFFER_CONFIRMATION_ACCEPTED = 'seller-offer-confirmation-accepted',
  SELLER_OFFER_CONFIRMATION_DECLINED_RECYCLE = 'seller-offer-confirmation-declined-recycle',
  SELLER_OFFER_CONFIRMATION_DECLINED_RETURN = 'seller-offer-confirmation-declined-return',
  SELLER_OFFER_UPDATED_HAS_VALUE = 'seller-offer-updated-has-value',
  SELLER_OFFER_UPDATED_NO_VALUE = 'seller-offer-updated-no-value',
  SELLER_PACKAGE_RECEIVED = 'seller-package-received',
  SELLER_REMINDER = 'seller-reminder',
  SELLER_DEVICE_ACCEPTED = 'seller-device-accepted',
  SELLER_DEVICE_PAID_OUT = 'seller-device-paid-out',
}
