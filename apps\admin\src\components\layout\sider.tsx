import type { RefineThemedLayoutV2SiderProps } from '@refinedev/antd'
import { useSiderVisible } from '@refinedev/antd'
import {
  CanAccess,
  ITreeMenu,
  pickNotDeprecated,
  useActiveAuthProvider,
  useIsExistAuthentication,
  useLink,
  useLogout,
  useMenu,
  useRefineContext,
  useTranslate,
  useWarnAboutChange,
} from '@refinedev/core'
import { Button, Drawer, Grid, Layout, Menu } from 'antd'
import { CSSProperties, type FC } from 'react'
import {
  AiOutlineBars,
  AiOutlineDashboard,
  AiOutlineLeft,
  AiOutlineLogout,
  AiOutlineRight,
  AiOutlineUnorderedList,
} from 'react-icons/ai'

import { ThemedTitleV2 } from './title'

const drawerButtonStyles: CSSProperties = {
  borderTopLeftRadius: 0,
  borderBottomLeftRadius: 0,
  position: 'fixed',
  top: 64,
  zIndex: 999,
}

const { SubMenu } = Menu

const width = 250

export const ThemedSiderV2: FC<RefineThemedLayoutV2SiderProps> = ({ render, meta }) => {
  const { siderVisible, setSiderVisible, drawerSiderVisible, setDrawerSiderVisible } = useSiderVisible()

  const isExistAuthentication = useIsExistAuthentication()
  const NewLink = useLink()
  const { warnWhen, setWarnWhen } = useWarnAboutChange()
  const Link = NewLink
  const translate = useTranslate()
  const { menuItems, selectedKey, defaultOpenKeys } = useMenu({ meta })
  const breakpoint = Grid.useBreakpoint()
  const { hasDashboard } = useRefineContext()
  const authProvider = useActiveAuthProvider()
  const { mutate: mutateLogout } = useLogout({
    v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
  })

  const isMobile = typeof breakpoint.lg === 'undefined' ? false : !breakpoint.lg

  const RenderToTitle = ThemedTitleV2

  const renderTreeView = (tree: ITreeMenu[], selectedKey?: string) => {
    return tree.map((item: ITreeMenu) => {
      const { icon, label, route, key, name, children, parentName, meta, options } = item

      if (children.length > 0) {
        return (
          <CanAccess
            key={item.key}
            resource={name.toLowerCase()}
            action="list"
            params={{
              resource: item,
            }}
          >
            <SubMenu key={item.key} icon={icon ?? <AiOutlineUnorderedList />} title={label}>
              {renderTreeView(children, selectedKey)}
            </SubMenu>
          </CanAccess>
        )
      }
      const isSelected = key === selectedKey
      const isRoute = !(
        pickNotDeprecated(meta?.parent, options?.parent, parentName) !== undefined && children.length === 0
      )

      return (
        <CanAccess
          key={item.key}
          resource={name.toLowerCase()}
          action="list"
          params={{
            resource: item,
          }}
        >
          <Menu.Item
            key={item.key}
            icon={icon ?? (isRoute && <AiOutlineUnorderedList />)}
            style={{
              fontWeight: isSelected ? 'bold' : 'normal',
              backgroundColor: isSelected ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
            }}
          >
            <Link to={route ?? ''}>{label}</Link>
            {!drawerSiderVisible && isSelected && <div className="ant-menu-tree-arrow" />}
          </Menu.Item>
        </CanAccess>
      )
    })
  }

  const handleLogout = () => {
    if (warnWhen) {
      const confirm = window.confirm(
        translate('warnWhenUnsavedChanges', 'Are you sure you want to leave? You have unsaved changes.')
      )

      if (confirm) {
        setWarnWhen(false)
        mutateLogout()
      }
    } else {
      mutateLogout()
    }
  }

  const logout = isExistAuthentication && (
    <Menu.Item key="logout" onClick={() => handleLogout()} icon={<AiOutlineLogout />}>
      {translate('buttons.logout', 'Logout')}
    </Menu.Item>
  )

  const dashboard = hasDashboard ? (
    <Menu.Item key="dashboard" icon={<AiOutlineDashboard />}>
      <Link to="/">{translate('dashboard.title', 'Dashboard')}</Link>
      {!drawerSiderVisible && selectedKey === '/' && <div className="ant-menu-tree-arrow" />}
    </Menu.Item>
  ) : null

  const items = renderTreeView(menuItems, selectedKey)

  const renderSider = () => {
    if (render) {
      return render({
        dashboard,
        items,
        logout,
        collapsed: drawerSiderVisible,
      })
    }
    return (
      <>
        {dashboard}
        {items}
        {logout}
      </>
    )
  }

  const renderMenu = () => {
    return (
      <Menu
        theme="dark"
        selectedKeys={selectedKey ? [selectedKey] : []}
        defaultOpenKeys={defaultOpenKeys}
        mode="inline"
        className="mt-2 border-none"
        onClick={() => {
          setSiderVisible?.(false)
          if (!breakpoint.lg) {
            setDrawerSiderVisible?.(true)
          }
        }}
      >
        {renderSider()}
      </Menu>
    )
  }

  const renderDrawerSider = () => {
    return (
      <>
        <Drawer
          open={siderVisible}
          onClose={() => setSiderVisible?.(false)}
          placement="left"
          closable={false}
          width={width}
          styles={{ body: { padding: 0 } }}
          maskClosable={true}
        >
          <Layout>
            <Layout.Sider theme="dark" width={width} className="h-[100vh] overflow-hidden">
              <div
                style={{
                  width,
                  padding: '0 28px',
                  display: 'flex',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                }}
              >
                <RenderToTitle collapsed={false} />
              </div>
              {renderMenu()}
            </Layout.Sider>
          </Layout>
        </Drawer>
        <Button
          style={drawerButtonStyles}
          size="large"
          onClick={() => setSiderVisible?.(true)}
          icon={<AiOutlineBars />}
        ></Button>
      </>
    )
  }

  if (isMobile) {
    return renderDrawerSider()
  }

  return (
    <Layout.Sider
      theme="dark"
      width={width}
      collapsible
      collapsed={drawerSiderVisible}
      onCollapse={(collapsed) => setDrawerSiderVisible?.(collapsed)}
      collapsedWidth={80}
      breakpoint="lg"
      trigger={
        <Button
          type="text"
          style={{
            borderRadius: 0,
            height: '100%',
            width: '100%',
          }}
        >
          {drawerSiderVisible ? (
            <AiOutlineRight
              style={{
                color: '#fff',
              }}
            />
          ) : (
            <AiOutlineLeft
              style={{
                color: '#fff',
              }}
            />
          )}
        </Button>
      }
    >
      <div
        style={{
          width: drawerSiderVisible ? 80 : width,
          padding: drawerSiderVisible ? 12 : '6px 28px',
          display: 'flex',
          justifyContent: drawerSiderVisible ? 'center' : 'flex-start',
          alignItems: 'center',
        }}
      >
        <RenderToTitle collapsed={drawerSiderVisible} />
      </div>
      {renderMenu()}
    </Layout.Sider>
  )
}
