import { Edit, useForm, useSelect } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { ITestedItem, ITestedItemList } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, InputNumber } from 'antx'
import { FC } from 'react'

import { InputMultiEntitySelect } from '~/components'

export const TestedItemListEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, formLoading } = useForm<ITestedItemList, HttpError, ITestedItemList>({
    meta: {
      join: {
        field: 'items',
      },
    },
  })

  const { selectProps: listSelectProps } = useSelect<ITestedItem>({
    resource: 'admin/tested-items',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en', 'name__pl'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Input label="Internal name" name="internalName" rules={['required']} />
        <InputMultiEntitySelect label="Tested items" name="items" {...(listSelectProps as any)} rules={['required']} />
        <InputNumber label="Total items count" precision={0} min={0} name="totalItemsCount" rules={['required']} />
      </Form>
    </Edit>
  )
}
