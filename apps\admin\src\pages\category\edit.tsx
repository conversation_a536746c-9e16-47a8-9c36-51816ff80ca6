import { Edit, useForm, useSelect } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useUpdate } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type { ICategory, IProductSkuOriginalAccessory } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, InputNumber, TextArea, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useState } from 'react'

import {
  InputImage,
  InputLanguageTab,
  InputMultiEntitySelect,
  InputMultiLang,
  InputPublish,
  InputSlug,
  LanguageTabChoices,
} from '~/components'

export const CategoryEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, id, saveButtonProps, query, formLoading } = useForm<ICategory, HttpError, ICategory>({
    meta: {
      join: [
        {
          field: 'icon',
        },
        {
          field: 'image',
        },
        {
          field: 'defaultOriginalAccessories',
        },
      ],
    },
  })

  const record = query?.data?.data
  if (formProps.initialValues?.publishedAt) {
    formProps.initialValues.publishedAt = new Date(formProps.initialValues.publishedAt)
  }
  const { mutate } = useUpdate<ICategory, HttpError, Partial<ICategory>>()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const { selectProps: originalAccessorySelectProps } = useSelect<IProductSkuOriginalAccessory>({
    resource: 'admin/original-accessories',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!record?.publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
            mutate({
              resource: 'admin/categories',
              id: id! as string,
              values: {
                publishedAt: value ? new Date() : null,
              },
              successNotification: (data) => {
                return {
                  type: 'success',
                  message: `Category has been ${value ? 'published' : 'unpublished'} successfully`,
                }
              },
              errorNotification: () => {
                return {
                  type: 'error',
                  message: `Failed to ${value ? 'publish' : 'unpublish'} category`,
                }
              },
            })
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Polish)"
                name="slug__pl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__pl}
                className={clsHidePl}
              />
              <InputNumber label="Saved CO2" min={0} name="savedCo2" rules={['required']} suffix="g" />
              <InputNumber label="Saved e-waste" min={0} name="savedEwaste" rules={['required']} suffix="g" />
              <TextArea
                label="Condition A (English)"
                name="gradingADesc__en"
                rules={['required']}
                rows={3}
                className={clsHideEn}
              />
              <TextArea
                label="Condition B (English)"
                name="gradingBDesc__en"
                rules={['required']}
                rows={3}
                className={clsHideEn}
              />
              <TextArea
                label="Condition C (English)"
                name="gradingCDesc__en"
                rules={['required']}
                rows={3}
                className={clsHideEn}
              />
              <TextArea
                label="Condition D (English)"
                name="gradingDDesc__en"
                rules={['required']}
                rows={3}
                className={clsHideEn}
              />
              <TextArea
                label="Condition A (Dutch)"
                name="gradingADesc__nl"
                rules={['required']}
                rows={3}
                className={clsHideNl}
              />
              <TextArea
                label="Condition B (Dutch)"
                name="gradingBDesc__nl"
                rules={['required']}
                rows={3}
                className={clsHideNl}
              />
              <TextArea
                label="Condition C (Dutch)"
                name="gradingCDesc__nl"
                rules={['required']}
                rows={3}
                className={clsHideNl}
              />
              <TextArea
                label="Condition D (Dutch)"
                name="gradingDDesc__nl"
                rules={['required']}
                rows={3}
                className={clsHideNl}
              />
              <TextArea
                label="Condition A (German)"
                name="gradingADesc__de"
                rules={['required']}
                rows={3}
                className={clsHideDe}
              />
              <TextArea
                label="Condition B (German)"
                name="gradingBDesc__de"
                rules={['required']}
                rows={3}
                className={clsHideDe}
              />
              <TextArea
                label="Condition C (German)"
                name="gradingCDesc__de"
                rules={['required']}
                rows={3}
                className={clsHideDe}
              />
              <TextArea
                label="Condition D (German)"
                name="gradingDDesc__de"
                rules={['required']}
                rows={3}
                className={clsHideDe}
              />
              <TextArea
                label="Condition A (Polish)"
                name="gradingADesc__pl"
                rules={['required']}
                rows={3}
                className={clsHidePl}
              />
              <TextArea
                label="Condition B (Polish)"
                name="gradingBDesc__pl"
                rules={['required']}
                rows={3}
                className={clsHidePl}
              />
              <TextArea
                label="Condition C (Polish)"
                name="gradingCDesc__pl"
                rules={['required']}
                rows={3}
                className={clsHidePl}
              />
              <TextArea
                label="Condition D (Polish)"
                name="gradingDDesc__pl"
                rules={['required']}
                rows={3}
                className={clsHidePl}
              />
              <InputMultiEntitySelect
                label="Default SKU original accessories"
                name="defaultOriginalAccessories"
                rules={['required']}
                {...(originalAccessorySelectProps as any)}
              />
              <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
              <InputImage
                label="Icon"
                name="icon"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.CATEGORY}
                processName={ImageProcessNameType.COPY}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
              <InputImage
                label="Image"
                name="image"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.CATEGORY}
                processName={ImageProcessNameType.COPY}
                defaultNames={{ name__en, name__nl, name__de, name__pl }}
              />
            </>
          )}
        </Watch>
        <Input noStyle type="datetime" name="publishedAt" hidden />
      </Form>
    </Edit>
  )
}
