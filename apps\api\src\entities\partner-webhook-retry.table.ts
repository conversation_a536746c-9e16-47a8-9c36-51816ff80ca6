import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { PartnerWebhookEntityType, PartnerWebhookEvent } from '~/constants'
import { type IPartner, PartnerEntity } from '~/entities'

@Entity('partner_webhook_retry')
export class PartnerWebhookRetryEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @ManyToOne(() => PartnerEntity, { nullable: false })
  partner: Relation<PartnerEntity>

  @Column()
  @RelationId((partnerWebhookRetry: PartnerWebhookRetryEntity) => partnerWebhookRetry.partner)
  partnerId: string

  @Column()
  requestUrl: string

  @Column({ default: 0 })
  retries: number

  @Column({ type: 'jsonb' })
  headers: Record<string, string>

  @Column({ type: 'jsonb' })
  data: { event: PartnerWebhookEvent; entityType: PartnerWebhookEntityType; entityId: string; version: number }

  @UpdateDateColumn()
  updatedAt: Date

  @CreateDateColumn()
  @Index()
  createdAt: Date
}

export type IPartnerWebhookRetry = Omit<PartnerWebhookRetryEntity, keyof BaseEntity> & {
  partner: IPartner
}
