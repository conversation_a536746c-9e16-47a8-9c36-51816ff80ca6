import { limitFit } from '@cloudinary/url-gen/actions/resize'
import {
  DateField,
  DeleteButton,
  EditButton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { ProductSkuGrading } from '@valyuu/api/constants'
import type { IProductSku, IProductSkuPrice } from '@valyuu/api/entities'
import { Input, Radio, Select, Space, Table } from 'antd'
import type { FC } from 'react'

import { FormTableSearch, PublishStatus } from '~/components'
import {
  cloudinary,
  filterSearchIcon,
  formatSelectOptionsFromEnum,
  formatCurrencySymbol,
  handleTableRowClick,
  isUUID,
} from '~/utils'

export const ProductSkuList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters, searchFormProps } = useTable<IProductSku, HttpError, { search: string }>({
    syncWithLocation: true,
    meta: {
      join: [
        {
          field: 'heroImage',
          select: ['id', 'publicId'],
        },
        {
          field: 'prices',
          select: ['id', 'price', 'channelId', 'currencyId'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    filters: {
      initial: [
        {
          field: 'name__en',
          operator: 'contains',
          value: '',
        },
      ],
    },
    onSearch: ({ search }) => {
      search = (search ?? '').trim()
      const { groups: urlSlug } =
        search.match(/(?<lang>[a-zA-Z]{2})\/buy\/(?<slug>[a-zA-Z0-9_-]+?)\/(?<slugNumber>\d+?)($|\?|#)/) ?? {}
      switch (true) {
        case !search:
          return [
            {
              operator: 'or',
              value: [],
            },
          ]
        case isUUID(search):
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'id',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        case !!urlSlug: {
          const { lang, slug, slugNumber } = urlSlug
          return [
            {
              operator: 'or',
              value: [
                {
                  operator: 'and',
                  value: [
                    {
                      field: `slug__${lang}`,
                      operator: 'eq',
                      value: slug,
                    },
                    {
                      field: `slugNumber__${lang}`,
                      operator: 'eq',
                      value: slugNumber,
                    },
                  ],
                },
              ],
            },
          ]
        }
        default: {
          return [
            {
              operator: 'or',
              value: [
                {
                  field: 'name__en',
                  operator: 'contains',
                  value: search,
                },
                {
                  field: 'serialNumber',
                  operator: 'eq',
                  value: search,
                },
                {
                  field: 'stockId',
                  operator: 'eq',
                  value: search,
                },
              ],
            },
          ]
        }
      }
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List
      headerButtons={({ defaultButtons }) => (
        <Space>
          <FormTableSearch
            searchFormProps={searchFormProps}
            title="Search for a product SKU by its ID, name (English), product URL, serial number or stock ID"
          />
          {defaultButtons}
        </Space>
      )}
    >
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/product-skus', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="heroImage"
          title="Image"
          align="center"
          className="cursor-pointer"
          render={(value) => (
            <img
              src={cloudinary.image(value.publicId).resize(limitFit(30, 30)).toURL()}
              className="h-auto max-h-[30px] max-w-[30px]"
              alt=""
            />
          )}
          width="7rem"
        />
        <Table.Column
          dataIndex="name__en"
          title="Name (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('name__en', filters, 'contains')}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Name (English)" />
            </FilterDropdown>
          )}
          filterIcon={filterSearchIcon}
        />
        <Table.Column
          dataIndex="grading"
          title="Grading"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('grading', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                placeholder="Grading"
                options={formatSelectOptionsFromEnum(ProductSkuGrading)}
              />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="sold"
          title="Sold"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('sold', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Radio.Group
                options={[
                  { label: 'Yes', value: 1 },
                  { label: 'No', value: 0 },
                ]}
              />
            </FilterDropdown>
          )}
          render={(value) => (value ? 'Yes' : 'No')}
        />
        <Table.Column
          dataIndex="prices"
          title="Price"
          className="cursor-pointer"
          render={(value: IProductSkuPrice[]) =>
            (value ?? []).map((price) => (
              <div key={price.id} title={price.channelId}>
                {formatCurrencySymbol(price.currencyId)}
                {price.price}
              </div>
            ))
          }
        />
        <Table.Column
          dataIndex="stockId"
          title="Stock ID"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('stockId', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Stock ID" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="serialNumber"
          title="Serial number"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('serialNumber', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Serial number" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IProductSku>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
