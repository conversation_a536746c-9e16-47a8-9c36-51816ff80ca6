import axios from 'axios'

const publicKey = process.env.SENDCLOUD_PK
const secretKey = process.env.SENDCLOUD_SK

if (!publicKey || !secretKey) {
  throw new Error('SendCloud API keys not found in environment variables')
}

export const sendcloudClientV2 = axios.create({
  baseURL: 'https://panel.sendcloud.sc/api/v2',
  auth: {
    username: process.env.SENDCLOUD_PK!,
    password: process.env.SENDCLOUD_SK!,
  },
  headers: {
    'Content-Type': 'application/json',
  },
})
