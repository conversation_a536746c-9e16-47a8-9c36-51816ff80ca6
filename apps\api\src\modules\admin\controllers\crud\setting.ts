import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { SettingEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudSettingService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: SettingEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
})
@Controller('admin/settings')
export class CrudSettingsController implements CrudController<SettingEntity> {
  constructor(public service: CrudSettingService) {}
}
