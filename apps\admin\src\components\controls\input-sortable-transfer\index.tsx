import type { DragEndEvent } from '@dnd-kit/core'
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { restrictToParentElement, restrictToVerticalAxis } from '@dnd-kit/modifiers'
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Table, Transfer } from 'antd'
import { TransferItem, TransferProps } from 'antd/es/transfer'
import clsx from 'clsx'
import { difference } from 'lodash'
import { HTMLAttributes, Key } from 'react'

interface RecordType {
  key: string
  title: string
  data?: Record<string, any>
}

export const SortableRow = (props: HTMLAttributes<HTMLTableRowElement> & { 'data-row-key': string }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key'],
  })

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: 'move',
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  }

  return <tr {...props} ref={setNodeRef} style={style} {...attributes} {...listeners} />
}

export type SortableTransferProps<RecordType> = Omit<TransferProps, 'targetKeys'> & {
  dataSource: RecordType[]
  pageSize?: number
}

type SortableTransferPropsFormInput = Omit<SortableTransferProps<RecordType>, 'onChange'> & {
  value?: Key[]
  onChange?: (value: Key[]) => void
}

export const SortableTransfer = ({
  className,
  value,
  onChange,
  filterOption,
  titles,
  listStyle,
  render,
  pageSize,
  ...props
}: SortableTransferPropsFormInput) => {
  const targetKeys: Key[] = value?.map((key) => key) ?? []

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1,
      },
    })
  )

  return (
    <Transfer
      className={clsx(
        '[&_.ant-spin-container]:flex [&_.ant-spin-container]:h-full [&_.ant-spin-container]:flex-col [&_.ant-spin-container]:justify-between [&_.ant-spin-nested-loading]:h-full [&_.ant-transfer-list-body-customize-wrapper]:h-full [&_.ant-transfer-list-body-customize-wrapper_.ant-table-wrapper]:h-full [&_.ant-transfer-list]:overflow-hidden',
        className
      )}
      targetKeys={targetKeys}
      onChange={(targetKeys) => {
        onChange?.(targetKeys.map(String))
      }}
      filterOption={
        filterOption ??
        ((inputValue: string, item: TransferItem) => !!item.title?.toLowerCase()?.includes(inputValue.toLowerCase()))
      }
      titles={titles ?? ['Available', 'Selected (sortable)']}
      listStyle={
        listStyle ?? {
          minWidth: 250,
          minHeight: 500,
          userSelect: 'none',
        }
      }
      {...props}
    >
      {({ direction, filteredItems, onItemSelectAll, onItemSelect, selectedKeys }) => {
        const table = (
          <Table
            pagination={pageSize ? { pageSize, hideOnSinglePage: true } : undefined}
            showHeader={false}
            className="select-none [&_.ant-table-content_tbody>tr>td]:border-b-0 [&_.ant-table-content_tbody>tr]:cursor-pointer [&_.ant-table-placeholder]:!cursor-default"
            rowSelection={{
              getCheckboxProps: () => ({ disabled: false }),
              onSelectAll(selected, selectedRows) {
                const treeSelectedKeys = selectedRows.map(({ key }) => key)
                const diffKeys = selected
                  ? difference(treeSelectedKeys, selectedKeys)
                  : difference(selectedKeys, treeSelectedKeys)
                onItemSelectAll(diffKeys as string[], selected)
              },
              onSelect({ key }, selected) {
                onItemSelect(key as string, selected)
              },
              selectedRowKeys: selectedKeys,
            }}
            components={{
              body: {
                row: direction === 'left' ? undefined : filteredItems.length === 1 ? undefined : SortableRow,
              },
            }}
            rowKey="key"
            columns={[{ dataIndex: 'title', render: (title, record) => (render ? render(record) : title) }]}
            dataSource={filteredItems}
            size="small"
            onRow={({ key }) => ({
              onClick: () => {
                onItemSelect(key as string, !selectedKeys.includes(key as string))
              },
            })}
          />
        )

        const onDragEnd = ({ active, over }: DragEndEvent) => {
          if (active.id !== over?.id) {
            const prev = filteredItems.map(({ key }) => key)
            const activeIndex = prev.findIndex((key) => key === active.id)
            const overIndex = prev.findIndex((key) => key === over?.id)
            onChange?.(arrayMove(prev, activeIndex, overIndex).map(String))
          }
        }

        return direction === 'left' ? (
          table
        ) : (
          <DndContext
            sensors={sensors}
            modifiers={[restrictToVerticalAxis, restrictToParentElement]}
            onDragEnd={onDragEnd}
          >
            <SortableContext items={filteredItems.map((i) => String(i.key))} strategy={verticalListSortingStrategy}>
              {table}
            </SortableContext>
          </DndContext>
        )
      }}
    </Transfer>
  )
}
