import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import * as Sentry from '@sentry/nestjs'
import { addDays, subDays, subMonths } from 'date-fns'
import ms from 'ms'
import { I18nService } from 'nestjs-i18n'
import { LessThan } from 'typeorm'

import { envConfig } from '~/configs'
import {
  EmailType,
  LOCALE_ENABLED_LANGUAGES,
  OFFER_ABANDONMENT_MONTHS,
  OFFER_EXPIRATION_DAYS,
  SaleItemOfferConfirmationType,
  SaleItemOfferStatus,
  SaleItemPlanType,
  SaleItemStatus,
  SalePaymentType,
  SellerPaymentPeriodUnit,
  ShipmentType,
} from '~/constants'
import {
  CharityEntity,
  EmailHistoryEntity,
  PartnerEntity,
  SaleItemEntity,
  SaleItemOfferEntity,
  SellerPaymentPeriodEntity,
} from '~/entities'
import { SaleItemService, ShipmentService } from '~/services'
import { formatDate, formatMoney, getEmailTemplatePrefix, sendmail } from '~/utils'

const dateStyle = { day: '2-digit', month: 'short', year: 'numeric' } as const

@Injectable()
export class SaleItemOfferService {
  constructor(
    private readonly i18n: I18nService,
    private readonly saleItemService: SaleItemService,
    private readonly shipmentService: ShipmentService
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async renewExpiredOffers() {
    const expirationDate = subDays(new Date(), OFFER_EXPIRATION_DAYS)

    const expiredItems = await SaleItemEntity.find({
      relations: {
        offer: true,
      },
      where: {
        status: SaleItemStatus.PENDING_OFFER,
        offer: {
          createdAt: LessThan(expirationDate),
          // 7 days ago. fix the issue that sends duplicated emails to users
          updatedAt: LessThan(subDays(new Date(), 7)),
        },
      },
    })

    for (const item of expiredItems) {
      try {
        // const offer = await this.create(item, true)
        // item.offerId = offer.id
        // await item.save()
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'sale-item-offer:renewExpiredOffers',
          },
          extra: {
            saleItem: item,
          },
        })
      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_4AM)
  async handleAbandonedOffers() {
    const abandonmentDate = subMonths(new Date(), OFFER_ABANDONMENT_MONTHS)

    const abandonedItems = await SaleItemEntity.find({
      where: {
        offerStatus: SaleItemOfferStatus.PENDING,
        status: SaleItemStatus.PENDING_OFFER,
        createdAt: LessThan(abandonmentDate),
      },
    })

    for (const item of abandonedItems) {
      item.status = item.price > 0 ? SaleItemStatus.CUSTOMER_ABANDONED : SaleItemStatus.PENDING_RECYCLE
      item.offerStatus = SaleItemOfferStatus.EXPIRED
      await item.save()
    }
  }

  async create(saleItem: SaleItemEntity, isRenewal = false) {
    if (!saleItem?.version || !saleItem?.id) {
      throw new BadRequestException('Missing saleItem version or id')
    }

    const { sale, partner } = await SaleItemEntity.findOne({
      where: { id: saleItem.id },
      relations: { sale: { address: true, bankAccount: true, user: true }, partner: true },
    })

    if (!sale) {
      throw new NotFoundException('Sale not found')
    }

    const offer = await SaleItemOfferEntity.save(
      SaleItemOfferEntity.create({
        saleItemId: saleItem.id,
        version: saleItem.version,
        expiresAt: addDays(new Date(), OFFER_EXPIRATION_DAYS),
      })
    )

    if ((!partner || partner.manageCustomerEmails) && !saleItem.isLegacy) {
      const display = await this.saleItemService.getDisplay(saleItem)
      const lang = display.lang
      const notes = await this.saleItemService.getCustomerNotes(saleItem)

      const locale = `${display.lang}-${sale.address.countryId}`
      const lastOffer = await SaleItemOfferEntity.findOne({
        where: { saleItemId: saleItem.id },
        order: { createdAt: 'DESC' },
      })
      const inspectedAt = lastOffer.createdAt ?? new Date()

      let charityName: string = undefined
      if (sale.paymentType === SalePaymentType.DONATION) {
        // TODO: get charity name from sale
        const charity = await CharityEntity.findOneOrFail({ where: {} })
        charityName = charity[`name__${display.lang}`]
      }
      const iban = sale.paymentType === SalePaymentType.BANK_TRANSFER ? sale.bankAccount?.accountNumber : undefined

      const item = {
        name: display.name,
        attributes: display.attributes,
        answers: display.answers,
        thumbnail: display.image,
        price: formatMoney(display.price, display.currency, locale),
        initialPrice: formatMoney(display.initialPrice, display.currency, locale),
        isFunctional: display.isFunctional,
        type:
          display.plan === SaleItemPlanType.C2C
            ? this.i18n.t('emails.sellerNewPlanTypeC2C', { lang })
            : this.i18n.t('emails.sellerNewPlanTypeC2B', { lang }),
        notes,
      } as const
      const to = sale.user.email

      const paymentPeriod = await this.getPaymentPeriod({
        channelId: saleItem.channelId,
        plan: saleItem.type,
        lang: display.lang,
        paymentType: sale.paymentType,
      })

      const isPartner = !!partner
      if (display.price > 0) {
        const data = {
          acceptUrl:
            envConfig.SITE_URL +
            `/${display.lang}/sell/offer-confirmation?offerId=${encodeURIComponent(offer.id)}&authToken=${encodeURIComponent(offer.authToken)}&operation=accept` +
            (envConfig.isDev && envConfig.VERCEL_SHARE ? `&_vercel_share=${envConfig.VERCEL_SHARE}` : ''),
          declineUrl:
            envConfig.SITE_URL +
            `/${display.lang}/sell/offer-confirmation?offerId=${encodeURIComponent(offer.id)}&authToken=${encodeURIComponent(offer.authToken)}&operation=decline` +
            (envConfig.isDev && envConfig.VERCEL_SHARE ? `&_vercel_share=${envConfig.VERCEL_SHARE}` : ''),
          item,
          saleNumber: sale.saleNumber,
          expirationDate: formatDate(display.offerExpiresAt, locale, dateStyle),
          inspectionDate: formatDate(inspectedAt, locale, dateStyle),
          shippingFirstName: sale.address.firstName,
          iban,
          charityName,
          paymentPeriod,
        } as const
        const subject = isRenewal
          ? this.i18n.t('emails.sellerOfferUpdatedHasValueSubject', { lang })
          : this.i18n.t('emails.sellerNewOfferHasValueSubject', { lang })
        const templatePrefix = await getEmailTemplatePrefix({
          isPartner,
          partnerSlug: sale.partner?.slug,
          type: isRenewal ? EmailType.SELLER_OFFER_UPDATED_HAS_VALUE : EmailType.SELLER_NEW_OFFER_HAS_VALUE,
          lang,
        })
        await sendmail(
          {
            type: isRenewal ? EmailType.SELLER_OFFER_UPDATED_HAS_VALUE : EmailType.SELLER_NEW_OFFER_HAS_VALUE,
            lang: display.lang,
            to,
            subject,
            data,
            templatePrefix,
          },
          3
        )
        await EmailHistoryEntity.create({
          email: sale.user.email,
          subject,
          userId: sale.user.id,
          type: isRenewal ? EmailType.SELLER_OFFER_UPDATED_HAS_VALUE : EmailType.SELLER_NEW_OFFER_HAS_VALUE,
          relatedIds: [sale.id, saleItem.id],
          lang: display.lang,
          data,
          templatePrefix,
        }).save()
      } else {
        const data = {
          recycleUrl:
            envConfig.SITE_URL +
            `/${display.lang}/sell/no-value-confirmation?offerId=${encodeURIComponent(offer.id)}&authToken=${encodeURIComponent(offer.authToken)}&operation=recycle`,
          returnUrl:
            envConfig.SITE_URL +
            `/${display.lang}/sell/no-value-confirmation?offerId=${encodeURIComponent(offer.id)}&authToken=${encodeURIComponent(offer.authToken)}&operation=return`,
          item,
          saleNumber: sale.saleNumber,
          inspectionDate: formatDate(inspectedAt, locale, dateStyle),
          shippingFirstName: sale.address.firstName,
        } as const
        const subject = isRenewal
          ? this.i18n.t('emails.sellerOfferUpdatedNoValueSubject', { lang })
          : this.i18n.t('emails.sellerNewOfferNoValueSubject', { lang })
        const templatePrefix = await getEmailTemplatePrefix({
          isPartner,
          partnerSlug: sale.partner?.slug,
          type: isRenewal ? EmailType.SELLER_OFFER_UPDATED_NO_VALUE : EmailType.SELLER_NEW_OFFER_NO_VALUE,
          lang,
        })
        await sendmail(
          {
            type: isRenewal ? EmailType.SELLER_OFFER_UPDATED_NO_VALUE : EmailType.SELLER_NEW_OFFER_NO_VALUE,
            lang: display.lang,
            to,
            subject,
            data,
            templatePrefix,
          },
          3
        )
        await EmailHistoryEntity.create({
          email: sale.user.email,
          subject,
          userId: sale.user.id,
          type: isRenewal ? EmailType.SELLER_OFFER_UPDATED_NO_VALUE : EmailType.SELLER_NEW_OFFER_NO_VALUE,
          relatedIds: [sale.id, saleItem.id],
          lang: display.lang,
          data,
          templatePrefix,
        }).save()
      }
    }

    return offer
  }

  async getPaymentPeriod({
    channelId,
    plan,
    lang,
    paymentType,
  }: {
    channelId: string
    plan: SaleItemPlanType
    lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
    paymentType: SalePaymentType
  }) {
    const sellerPaymentPeriod = await SellerPaymentPeriodEntity.findOne({
      where: { channelId },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })
    const planPrefix: 'c2b' | 'c2c' | 'donation' =
      paymentType === SalePaymentType.DONATION ? 'donation' : plan === SaleItemPlanType.C2C ? 'c2c' : 'c2b'
    const args = {
      count: sellerPaymentPeriod[`${planPrefix}From`]
        ? `${sellerPaymentPeriod[`${planPrefix}From`]}-${sellerPaymentPeriod[`${planPrefix}To`]}`
        : sellerPaymentPeriod[`${planPrefix}To`],
    }
    return sellerPaymentPeriod?.[`${planPrefix}Unit`] === SellerPaymentPeriodUnit.HOURS
      ? this.i18n.t('sale.paymentPeriodHours', {
          lang,
          args,
        })
      : this.i18n.t('sale.paymentPeriodDays', {
          lang,
          args,
        })
  }

  async confirm(id: string, confirmationType: SaleItemOfferConfirmationType) {
    const saleItem = await SaleItemEntity.findOne({
      where: { id },
      relations: {
        sale: {
          address: true,
          bankAccount: true,
        },
        user: true,
        returnAddress: true,
        partner: true,
      },
    })

    const { manageCustomerEmails } = (await PartnerEntity.findOne({
      where: { id: saleItem.partnerId },
      select: { id: true, manageCustomerEmails: true },
      cache: envConfig.isProd ? ms('1 hour') : false,
    })) ?? { manageCustomerEmails: true }

    switch (confirmationType) {
      case SaleItemOfferConfirmationType.ACCEPT: {
        saleItem.status = SaleItemStatus.PENDING_PAYMENT
        saleItem.offerAcceptedAt = new Date()
        saleItem.offerStatus = SaleItemOfferStatus.ACCEPTED
        break
      }
      case SaleItemOfferConfirmationType.DECLINE_RECYCLE: {
        saleItem.status = SaleItemStatus.PENDING_RECYCLE
        saleItem.offerStatus = SaleItemOfferStatus.DECLINED_RECYCLE
        break
      }
      case SaleItemOfferConfirmationType.DECLINE_RETURN: {
        saleItem.status = SaleItemStatus.PENDING_RETURN
        saleItem.offerStatus = SaleItemOfferStatus.DECLINED_RETURN
        break
      }
    }

    await saleItem.save()

    if (!manageCustomerEmails || saleItem.isLegacy) {
      return
    }

    const display = await this.saleItemService.getDisplay(saleItem)
    const lang = display.lang
    const locale = `${display.lang}-${saleItem.sale.address.countryId}`
    const item = {
      name: display.name,
      attributes: display.attributes,
      answers: display.answers,
      thumbnail: display.image,
      price: formatMoney(display.price, display.currency, locale),
      type:
        display.plan === SaleItemPlanType.C2C
          ? this.i18n.t('emails.sellerNewPlanTypeC2C', { lang })
          : this.i18n.t('emails.sellerNewPlanTypeC2B', { lang }),
    } as const

    const paymentPeriod = await this.getPaymentPeriod({
      channelId: saleItem.channelId,
      plan: saleItem.type,
      lang: display.lang,
      paymentType: saleItem.sale.paymentType,
    })
    let charityName: string = undefined
    if (saleItem.sale.paymentType === SalePaymentType.DONATION) {
      // TODO: get charity name from sale
      const charity = await CharityEntity.findOneOrFail({ where: {} })
      charityName = charity[`name__${display.lang}`]
    }

    const data = {
      item,
      saleNumber: saleItem.sale.saleNumber,
      confirmationDate: formatDate(new Date(), locale, dateStyle),
      shippingFirstName: saleItem.sale.address.firstName,
      paymentPeriod,
      iban: saleItem.sale.bankAccount?.accountNumber,
      charityName,
    } as const
    const email = saleItem.user.email

    const isPartner = !!saleItem?.partner
    switch (confirmationType) {
      case SaleItemOfferConfirmationType.ACCEPT: {
        setTimeout(async () => {
          try {
            const subject = this.i18n.t('emails.sellerOfferConfirmationAcceptedSubject', { lang })
            const templatePrefix = await getEmailTemplatePrefix({
              isPartner,
              partnerSlug: saleItem?.partner?.slug,
              type: EmailType.SELLER_OFFER_CONFIRMATION_ACCEPTED,
              lang,
            })
            await sendmail(
              {
                type: EmailType.SELLER_OFFER_CONFIRMATION_ACCEPTED,
                lang,
                to: email,
                subject,
                data,
                templatePrefix,
              },
              3
            )
            await EmailHistoryEntity.create({
              email,
              subject,
              userId: saleItem.user.id,
              type: EmailType.SELLER_OFFER_CONFIRMATION_ACCEPTED,
              relatedIds: [saleItem.sale.id, saleItem.id],
              lang,
              data,
              templatePrefix,
            }).save()
          } catch (error) {
            Sentry.captureException(error, {
              tags: {
                type: 'sale-item-offer:confirm',
              },
              extra: {
                saleItemId: saleItem.id,
                saleId: saleItem.sale.id,
                confirmationType: SaleItemOfferConfirmationType.ACCEPT,
                email,
                userId: saleItem.user.id,
              },
            })
          }
        })
        break
      }
      case SaleItemOfferConfirmationType.DECLINE_RECYCLE: {
        setTimeout(async () => {
          try {
            const subject = this.i18n.t('emails.sellerOfferConfirmationDeclinedRecycleSubject', { lang })
            const templatePrefix = await getEmailTemplatePrefix({
              isPartner,
              partnerSlug: saleItem?.partner?.slug,
              type: EmailType.SELLER_OFFER_CONFIRMATION_DECLINED_RECYCLE,
              lang,
            })
            await sendmail(
              {
                type: EmailType.SELLER_OFFER_CONFIRMATION_DECLINED_RECYCLE,
                lang,
                to: email,
                subject,
                data,
                templatePrefix,
              },
              3
            )
            await EmailHistoryEntity.create({
              email,
              subject,
              userId: saleItem.user.id,
              type: EmailType.SELLER_OFFER_CONFIRMATION_DECLINED_RECYCLE,
              relatedIds: [saleItem.sale.id, saleItem.id],
              lang,
              data,
              templatePrefix,
            }).save()
          } catch (error) {
            Sentry.captureException(error, {
              tags: {
                type: 'sale-item-offer:confirm',
              },
              extra: {
                saleItemId: saleItem.id,
                saleId: saleItem.sale.id,
                confirmationType: SaleItemOfferConfirmationType.DECLINE_RECYCLE,
                email,
                userId: saleItem.user.id,
              },
            })
          }
        })
        break
      }
      case SaleItemOfferConfirmationType.DECLINE_RETURN: {
        setTimeout(async () => {
          try {
            // TODO: think about channel and partner
            const shipment = await this.shipmentService.create({
              type: ShipmentType.SALE_ITEM_RETURN,
              orderNumber: saleItem.stockId,
              customerEmail: email,
              customerAddress: saleItem.returnAddress,
              isReturn: false,
              // channelId: saleItem.channelId,
              // partnerId: saleItem.partnerId,
              saleItemId: saleItem.id,
            })
            const subject = this.i18n.t('emails.sellerOfferConfirmationDeclinedReturnSubject', { lang })
            const templatePrefix = await getEmailTemplatePrefix({
              isPartner,
              partnerSlug: saleItem?.partner?.slug,
              type: EmailType.SELLER_OFFER_CONFIRMATION_DECLINED_RETURN,
              lang,
            })
            await sendmail(
              {
                type: EmailType.SELLER_OFFER_CONFIRMATION_DECLINED_RETURN,
                lang,
                to: email,
                subject,
                data: {
                  ...data,
                  trackingUrl: shipment.trackingUrl,
                },
                templatePrefix,
              },
              3
            )
            await EmailHistoryEntity.create({
              email,
              subject,
              userId: saleItem.user.id,
              type: EmailType.SELLER_OFFER_CONFIRMATION_DECLINED_RETURN,
              relatedIds: [saleItem.sale.id, saleItem.id],
              lang,
              data,
              templatePrefix,
            }).save()
          } catch (error) {
            Sentry.captureException(error, {
              tags: {
                type: 'sale-item-offer:confirm',
              },
              extra: {
                saleItemId: saleItem.id,
                saleId: saleItem.sale.id,
                confirmationType: SaleItemOfferConfirmationType.DECLINE_RETURN,
                email,
                userId: saleItem.user.id,
                shipmentError: true,
                stockId: saleItem.stockId,
                returnAddressId: saleItem.returnAddress?.id,
              },
            })
          }
        })
        break
      }
    }
  }
}
