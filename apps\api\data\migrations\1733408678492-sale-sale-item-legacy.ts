import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleSaleItemLegacy1733408678492 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add is_legacy column to sale table
    await queryRunner.query(`
      ALTER TABLE sale
      ADD COLUMN IF NOT EXISTS is_legacy BOOLEAN DEFAULT false NOT NULL
    `)

    // Add is_legacy column to sale_item table
    await queryRunner.query(`
      ALTER TABLE sale_item
      ADD COLUMN IF NOT EXISTS is_legacy BOOLEAN DEFAULT false NOT NULL
    `)

    // Update existing records to set is_legacy to true
    await queryRunner.query(`
      UPDATE sale
      SET is_legacy = true
      WHERE is_legacy = false
    `)

    await queryRunner.query(`
      UPDATE sale_item
      SET is_legacy = true
      WHERE is_legacy = false
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
