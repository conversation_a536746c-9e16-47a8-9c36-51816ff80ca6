import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  ChannelEntity,
  type IChannel,
  type ILocaleCountry,
  type IShippingMethod,
  type IWarehouse,
  LocaleCountryEntity,
  ShippingMethodEntity,
  WarehouseEntity,
} from '~/entities'

@Entity('shipping_config')
@Index(['customerCountryId', 'warehouseId', 'isReturn', 'channelId'])
@Index(['customerCountryId', 'isReturn', 'channelId'])
export class ShippingConfigEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @ManyToOne(() => LocaleCountryEntity, { nullable: false })
  customerCountry: Relation<LocaleCountryEntity>

  @Column()
  @RelationId((config: ShippingConfigEntity) => config.customerCountry)
  customerCountryId: string

  @ManyToOne(() => WarehouseEntity, { nullable: false })
  warehouse: Relation<WarehouseEntity>

  @Column()
  @RelationId((config: ShippingConfigEntity) => config.warehouse)
  warehouseId: string

  @ManyToOne(() => ShippingMethodEntity, { nullable: false, cascade: true })
  shippingMethod: Relation<ShippingMethodEntity>

  @Column()
  @RelationId((config: ShippingConfigEntity) => config.shippingMethod)
  shippingMethodId: string

  @ManyToOne(() => ChannelEntity, { nullable: true })
  channel?: Relation<ChannelEntity>

  @Column({ nullable: true })
  @RelationId((config: ShippingConfigEntity) => config.channel)
  channelId?: string

  @Column()
  @Index()
  isReturn: boolean

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IShippingConfig = Omit<ShippingConfigEntity, keyof BaseEntity> & {
  customerCountry: ILocaleCountry
  warehouse: IWarehouse
  shippingMethod: IShippingMethod
  channel: IChannel
}
