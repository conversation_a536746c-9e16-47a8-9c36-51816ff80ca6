import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

import { TestimonialRole } from '~/constants'

registerEnumType(TestimonialRole, { name: 'TestimonialRoleType' })

@ObjectType('Testimonial')
@Entity('testimonial')
export class TestimonialEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column({ nullable: false })
  name: string

  @Field(() => TestimonialRole)
  @Column({ type: 'varchar', nullable: false })
  role: TestimonialRole

  @Field()
  @Column({ type: 'text' })
  review: string

  @Field()
  @Column()
  date: Date

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type ITestimonial = Omit<TestimonialEntity, keyof BaseEntity>
