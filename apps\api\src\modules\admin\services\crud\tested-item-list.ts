import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { TestedItemListEntity } from '~/entities'

export class CrudTestedItemListService extends TypeOrmCrudService<TestedItemListEntity> {
  constructor(@InjectRepository(TestedItemListEntity) repo: Repository<TestedItemListEntity>) {
    super(repo)
  }
}
