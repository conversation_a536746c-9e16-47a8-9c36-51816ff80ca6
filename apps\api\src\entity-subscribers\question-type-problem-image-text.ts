import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageUsedInType } from '~/constants'
import { QuestionTypeImageTextEntity } from '~/entities'
import { entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'

@Injectable()
@EventSubscriber()
export class QuestionTypeProblemImageTextSubscriber implements EntitySubscriberInterface<QuestionTypeImageTextEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return QuestionTypeImageTextEntity
  }

  async afterInsert(event: InsertEvent<QuestionTypeImageTextEntity>) {
    const { entity, manager } = event

    await entityImageAutoProcess({
      image: entity.image,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.QUESTION_TYPE_PROBLEM_IMAGE_TEXT,
      manager,
    })
  }

  async afterUpdate(event: UpdateEvent<QuestionTypeImageTextEntity>) {
    const { entity, manager } = event

    await entityImageAutoProcess({
      image: entity.image ?? { id: entity.imageId }, // added fallback for the case that only the image id is set
      entityId: entity.problemGroupId,
      imageUsedIn: ImageUsedInType.QUESTION_TYPE_PROBLEM_IMAGE_TEXT,
      manager,
    })
  }

  async afterRemove(event: RemoveEvent<QuestionTypeImageTextEntity>) {
    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.QUESTION_TYPE_PROBLEM_IMAGE_TEXT,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
