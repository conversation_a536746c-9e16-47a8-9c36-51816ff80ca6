import { ArgsType, Field, ObjectType, OmitType, PickType } from '@nestjs/graphql'
import { IsIn, IsNotEmpty } from 'class-validator'

import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { BlogAuthorEntity, BlogPostEntity, BlogTagEntity } from '~/entities'

@ArgsType()
export class GetBlogPostInput {
  @Field(() => String, { description: 'Current language in frontend' })
  @IsNotEmpty()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]

  @Field({ description: 'The slug from frontend URL' })
  @IsNotEmpty()
  slug: string
}

@ObjectType('GetBlogPostsTag')
export class GetBlogPostsTagOutput extends PickType(BlogTagEntity, [
  'id',
  'color',
  'name__en',
  'name__nl',
  'name__de',
  'name__pl',
  'slug__en',
  'slug__nl',
  'slug__de',
  'slug__pl',
]) {}

@ObjectType('GetBlogPosts')
export class GetBlogPostsOutput extends PickType(BlogPostEntity, [
  'id',
  'title__en',
  'title__nl',
  'title__de',
  'title__pl',
  'slug__en',
  'slug__nl',
  'slug__de',
  'slug__pl',
  'summary__en',
  'summary__nl',
  'summary__de',
  'summary__pl',
  'heroImage',
  'createdAt',
] as const) {
  @Field(() => GetBlogPostsTagOutput)
  tag: GetBlogPostsTagOutput
}

@ObjectType('GetBlogPostAuthor')
export class GetBlogPostAuthorOutput extends OmitType(BlogAuthorEntity, ['posts'] as const) {}

@ObjectType('GetBlogPostTag')
export class GetBlogPostTagOutput extends OmitType(BlogTagEntity, ['posts'] as const) {}

@ObjectType('GetBlogPostRelated')
export class GetBlogPostRelatedOutput extends PickType(BlogPostEntity, [
  'id',
  'title__en',
  'title__nl',
  'title__de',
  'title__pl',
  'slug__en',
  'slug__nl',
  'slug__de',
  'slug__pl',
  'summary__en',
  'summary__nl',
  'summary__de',
  'summary__pl',
  'heroImage',
] as const) {}

@ObjectType('GetBlogPost')
export class GetBlogPostOutput extends OmitType(BlogPostEntity, ['author', 'tag'] as const) {
  @Field(() => GetBlogPostAuthorOutput)
  author: GetBlogPostAuthorOutput

  @Field(() => GetBlogPostTagOutput)
  tag: GetBlogPostTagOutput

  @Field(() => [GetBlogPostRelatedOutput])
  related: GetBlogPostRelatedOutput[]
}
