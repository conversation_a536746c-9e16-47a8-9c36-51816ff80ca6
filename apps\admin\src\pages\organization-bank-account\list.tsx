import {
  DateField,
  <PERSON>ete<PERSON><PERSON>on,
  EditButton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { OrganizationBankAccountType } from '@valyuu/api/constants'
import type { IOrganizationBankAccount } from '@valyuu/api/entities'
import { Space, Table, Tag } from 'antd'
import { Input, Select } from 'antx'
import { capitalize } from 'lodash'
import type { FC } from 'react'

import { formatSelectOptionsFromEnum, handleTableRowClick } from '~/utils'

const typeColorMap = {
  [OrganizationBankAccountType.CHARITY]: 'green',
  [OrganizationBankAccountType.PARTNER]: 'blue',
}

export const OrganizationBankAccountList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IOrganizationBankAccount>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'type', 'holderName', 'accountNumber', 'partnerId', 'charityId', 'createdAt'],
      join: [
        {
          field: 'partner',
          select: ['id', 'name'],
        },
        {
          field: 'charity',
          select: ['id', 'name__en'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/organization-bank-accounts', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="type"
          title="Type"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('type', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} options={formatSelectOptionsFromEnum(OrganizationBankAccountType)} />
            </FilterDropdown>
          )}
          render={(value) => (
            <Tag color={typeColorMap[value as keyof typeof typeColorMap]}>{capitalize(value?.replace('_', ' '))}</Tag>
          )}
          width="10rem"
        />
        <Table.Column dataIndex="holderName" title="Beneficiary name" className="cursor-pointer" />
        <Table.Column
          dataIndex="accountNumber"
          title="Account number"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('accountNumber', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input
                style={{ minWidth: 280 }}
                placeholder="Account number"
                normalize={(value) => value.replace(/\s/g, '')}
              />
            </FilterDropdown>
          )}
          width="16rem"
        />
        <Table.Column
          title="Organization"
          className="cursor-pointer"
          align="center"
          render={(_, row) => row.partner?.name ?? row.charity?.name__en}
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IOrganizationBankAccount>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
