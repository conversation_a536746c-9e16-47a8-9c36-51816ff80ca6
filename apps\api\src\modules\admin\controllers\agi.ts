import { Controller, Get, Query, UseGuards } from '@nestjs/common'

import { AdminJwtAuthGuard } from '~admin/guards'
import { AgiService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Controller('admin/agi')
export class AgiController {
  constructor(private readonly service: AgiService) {}

  @Get('generate-mpn')
  async generateMpn(@Query('variant-name') variantName: string) {
    return this.service.generateMpn(variantName)
  }
}
