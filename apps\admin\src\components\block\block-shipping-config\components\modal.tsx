import { useApiUrl, useCustom } from '@refinedev/core'
import type { IShippingMethod } from '@valyuu/api/entities'
import { Modal, Spin, Tag } from 'antd'
import { TransferItem } from 'antd/es/transfer'
import { Key, useState } from 'react'

import { SortableTransfer } from '~/components'
import { countryToEmoji } from '~/utils'

import { ExtendedConnection } from '../interfaces'
import { PriceTag } from './price-tag'

type ShippingMethodModalProps = {
  currentConnection: ExtendedConnection
  defaultSelectedMethodKeys: string[]
  onOk: (methods: IShippingMethod[]) => void
  onCancel: () => void
}

export const ShippingMethodModal = ({
  currentConnection,
  defaultSelectedMethodKeys,
  onOk,
  onCancel,
}: ShippingMethodModalProps) => {
  const [selectedMethodKeys, setSelectedMethodKeys] = useState<Key[]>(defaultSelectedMethodKeys)

  const apiUrl = useApiUrl()
  const { data, isLoading } = useCustom<Omit<IShippingMethod, 'createdAt' | 'updatedAt'>[]>({
    url: `${apiUrl}/admin/shipping-methods/find`,
    method: 'get',
    config: {
      query: {
        from_country: currentConnection.data.fromCountryId,
        to_country: currentConnection.data.toCountryId,
        is_return: currentConnection.data.isReturn,
      },
    },
    queryOptions: {
      enabled: !!currentConnection,
    },
  })

  const shippingMethodResults = data?.data ?? []

  return currentConnection ? (
    <Modal
      key={`modal-${currentConnection.data.fromCountryId}-${currentConnection.data.toCountryId}-${currentConnection.data.isReturn}`}
      title={
        <span className="flex items-center gap-2">
          Shipping methods
          {currentConnection && (
            <span className="text-sm font-normal text-zinc-500">
              {currentConnection.data.isReturn ? (
                <>
                  (return: {countryToEmoji(currentConnection.data.fromCountryId)} customer →{' '}
                  {countryToEmoji(currentConnection.data.toCountryId)} warehouse)
                </>
              ) : (
                <>
                  (shipping: {countryToEmoji(currentConnection.data.fromCountryId)} warehouse →{' '}
                  {countryToEmoji(currentConnection.data.toCountryId)} customer)
                </>
              )}
            </span>
          )}
        </span>
      }
      open={true}
      onOk={() => {
        const shippingMethods = selectedMethodKeys.map((key) =>
          shippingMethodResults.find((method) => String(method.id) === String(key))
        )
        onOk(shippingMethods as IShippingMethod[])
        setSelectedMethodKeys([])
      }}
      onCancel={() => {
        onCancel()
        setSelectedMethodKeys([])
      }}
      width={900}
      className="p-4"
    >
      {isLoading ? (
        <div className="flex h-[400px] items-center justify-center">
          <Spin />
        </div>
      ) : (
        <>
          <SortableTransfer
            pageSize={5}
            dataSource={shippingMethodResults.map((shippingMethod) => ({
              key: shippingMethod.id,
              title: shippingMethod.name,
              data: shippingMethod,
            }))}
            value={selectedMethodKeys}
            onChange={setSelectedMethodKeys}
            showSearch
            render={(item: TransferItem) => (
              <>
                <div className="mb-1 font-semibold" title={item.title}>
                  {item.title}
                </div>
                <div className="mb-1 flex gap-1">
                  <Tag color="blue" title="Weight range">
                    {Number(item.data.minWeightG).toLocaleString()}-{Number(item.data.maxWeightG).toLocaleString()}g
                  </Tag>
                  {item.data.isPaperless ? (
                    <Tag color="green" title="Supports paperless (Paperless code) shipping">
                      Paperless code
                    </Tag>
                  ) : (
                    <Tag color="volcano" title="Supports shipping label">
                      Shipping label
                    </Tag>
                  )}
                  {currentConnection.data.fromCountryId && currentConnection.data.toCountryId ? (
                    <PriceTag
                      methodId={item.data.id}
                      fromCountry={currentConnection.data.fromCountryId}
                      toCountry={currentConnection.data.toCountryId}
                    />
                  ) : null}
                </div>
              </>
            )}
            className="w-full max-w-full select-none"
          />
        </>
      )}
    </Modal>
  ) : null
}
