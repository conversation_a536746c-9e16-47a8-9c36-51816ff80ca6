import { Injectable } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import { pick } from 'lodash'
import ms from 'ms'
import { In, IsNull, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { GetProductSkuAccessoryProductOutput } from '~/dtos'
import { ProductModelAttributeCombinationEntity, ProductModelEntity, ProductSkuEntity } from '~/entities'

@Injectable()
export class ProductSkuService {
  async getProductSku({
    channelId,
    lang,
    slug,
    slugNumber,
  }: {
    channelId: string
    lang: string
    slug: string
    slugNumber: string
  }) {
    const relations = {
      prices: true,
      testReport: true,
      originalAccessories: {
        thumbnail: true,
      },
      heroImage: true,
      images: true,
    } as const
    let sku = await ProductSkuEntity.findOne({
      where: {
        publishedAt: Not(IsNull()),
        [`slug__${lang}`]: slug,
        [`slugNumber__${lang}`]: slugNumber,
        prices: { channelId },
      },
      relations,
      cache: envConfig.isProd ? ms('1 hour') : false,
    })

    // Check other languages, make sure language switching has no issue
    if (!sku) {
      sku = await ProductSkuEntity.findOne({
        where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({
          publishedAt: Not(IsNull()),
          [`slug__${l}`]: slug,
          [`slugNumber__${l}`]: slugNumber,
          prices: { channelId },
        })),
        relations,
      })
    }
    if (!sku) {
      throw new Error(`Product with slug ${slug} and slugNumber ${slugNumber} not found`)
    }

    if (!sku?.prices?.[0]) {
      Sentry.captureMessage('Empty sku price', {
        tags: {
          type: 'product-sku:getProductSku',
        },
        extra: {
          sku,
        },
      })
      throw new Error('Empty sku price')
    }
    const model = await ProductModelEntity.findOne({
      where: { id: sku.modelId },
      relations: { series: { testComponentList: { items: true } }, category: true, brand: true },
      select: {
        id: true,
        slug__en: true,
        slug__nl: true,
        slug__de: true,
        slug__pl: true,
        category: {
          id: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          slug__pl: true,
          gradingADesc__en: true,
          gradingADesc__nl: true,
          gradingADesc__de: true,
          gradingADesc__pl: true,
          gradingBDesc__en: true,
          gradingBDesc__nl: true,
          gradingBDesc__de: true,
          gradingBDesc__pl: true,
          gradingCDesc__en: true,
          gradingCDesc__nl: true,
          gradingCDesc__de: true,
          gradingCDesc__pl: true,
          gradingDDesc__en: true,
          gradingDDesc__nl: true,
          gradingDDesc__de: true,
          gradingDDesc__pl: true,
          savedCo2: true,
          savedEwaste: true,
        },
        brand: {
          id: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          name__pl: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          slug__pl: true,
        },
        series: {
          id: true,
          testComponentList: {
            id: true,
            totalItemsCount: true,
            items: {
              id: true,
              name__en: true,
              name__nl: true,
              name__de: true,
              name__pl: true,
              sortOrder: true,
            },
          },
        },
      },
      order: { series: { testComponentList: { items: { sortOrder: 'ASC' } } } },
      cache: envConfig.isProd ? ms('1 hour') : false,
    })
    const attributeCombination = await ProductModelAttributeCombinationEntity.findOne({
      where: { variantId: sku.variantId },
      relations: {
        choices: {
          option: true,
        },
      },
      select: {
        id: true,
        choices: {
          sortOrder: true,
          option: {
            name__en: true,
            name__nl: true,
            name__de: true,
            name__pl: true,
          },
        },
      },
      order: { choices: { sortOrder: 'ASC' } },
      cache: envConfig.isProd ? ms('1 hour') : false,
    })
    if (!attributeCombination) {
      Sentry.captureException(new Error(`Attribute combination not found for sku ${sku.id}`), {
        tags: {
          type: 'product-sku:attribute-combination-not-found',
        },
        extra: {
          sku,
        },
      })
      return null
    }
    return {
      ...pick(sku, [
        'id',
        'slug__en',
        'slug__nl',
        'slug__de',
        'slug__pl',
        'slugNumber__en',
        'slugNumber__nl',
        'slugNumber__de',
        'slugNumber__pl',
        'name__en',
        'name__nl',
        'name__de',
        'name__pl',
        'desc__en',
        'desc__nl',
        'desc__de',
        'desc__pl',
        'grading',
        'testReport',
        'color',
        'sold',
        'heroImage',
        'originalAccessories',
        'sellerJoinedYear',
        'extraTestedItems',
      ]),
      ...pick(model.category, [
        'gradingADesc__en',
        'gradingADesc__nl',
        'gradingADesc__de',
        'gradingADesc__pl',
        'gradingBDesc__en',
        'gradingBDesc__nl',
        'gradingBDesc__de',
        'gradingBDesc__pl',
        'gradingCDesc__en',
        'gradingCDesc__nl',
        'gradingCDesc__de',
        'gradingCDesc__pl',
        'gradingDDesc__en',
        'gradingDDesc__nl',
        'gradingDDesc__de',
        'gradingDDesc__pl',
      ]),
      categorySlug__en: model.category.slug__en,
      categorySlug__nl: model.category.slug__nl,
      categorySlug__de: model.category.slug__de,
      categorySlug__pl: model.category.slug__pl,
      modelSlug__en: model.slug__en,
      modelSlug__nl: model.slug__nl,
      modelSlug__de: model.slug__de,
      modelSlug__pl: model.slug__pl,
      brandSlug__en: model.brand.slug__en,
      brandSlug__nl: model.brand.slug__nl,
      brandSlug__de: model.brand.slug__de,
      brandSlug__pl: model.brand.slug__pl,
      brandName__en: model.brand.name__en,
      brandName__nl: model.brand.name__nl,
      brandName__de: model.brand.name__de,
      brandName__pl: model.brand.name__pl,
      images: [sku.heroImage, ...sku.images],
      price: sku.prices?.[0].price,
      brandNewPrice: sku.prices?.[0].brandNewPrice,
      originalPrice: sku.prices?.[0].originalPrice,
      isDeal: sku.prices?.[0].isDeal,
      attributes: [
        {
          name__en: sku.displayColor__en,
          name__nl: sku.displayColor__nl,
          name__de: sku.displayColor__de,
          name__pl: sku.displayColor__pl,
        },
        ...(sku.extraAttributes ?? []).map((field) => pick(field, ['name__en', 'name__nl', 'name__de', 'name__pl'])),
        ...(attributeCombination?.choices ?? []).map((choice) =>
          pick(choice.option, ['name__en', 'name__nl', 'name__de', 'name__pl'])
        ),
      ],
      testedItemList: model.series.testComponentList,
      savedCo2: model.category.savedCo2,
      savedEwaste: model.category.savedEwaste,
    }
  }

  async getProductSkuWarranties({
    channelId,
    lang,
    slug,
    slugNumber,
  }: {
    channelId: string
    lang: string
    slug: string
    slugNumber: string
  }) {
    const restOptions = {
      relations: {
        warrantyRules: {
          warrantyItems: { warranty: true },
        },
      },
      select: {
        id: true,
        warrantyRules: {
          id: true,
          warrantyItems: true,
        },
      },
      order: { warrantyRules: { warrantyItems: { price: 'ASC' } } },
    } as const
    let sku = await ProductSkuEntity.findOne({
      where: {
        [`slug__${lang}`]: slug,
        [`slugNumber__${lang}`]: slugNumber,
        warrantyRules: { channelId },
      },
      ...restOptions,
      cache: envConfig.isProd ? ms('1 hour') : false,
    })

    // Check other languages, make sure language switching has no issue
    if (!sku) {
      sku = await ProductSkuEntity.findOne({
        where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({
          [`slug__${l}`]: slug,
          [`slugNumber__${l}`]: slugNumber,
          warrantyRules: { channelId },
        })),
        ...restOptions,
      })
    }

    return (sku.warrantyRules?.[0]?.warrantyItems ?? []).map((item) => ({
      ...pick(item, ['id', 'price', 'warrantyId', 'isDefault']),
      ...pick(item.warranty, ['name__en', 'name__nl', 'name__de', 'name__pl', 'periodNumber', 'periodUnit']),
      label__en: item.price ? item.warranty.labelWithPrice__en : item.warranty.labelFree__en,
      label__nl: item.price ? item.warranty.labelWithPrice__nl : item.warranty.labelFree__nl,
      label__de: item.price ? item.warranty.labelWithPrice__de : item.warranty.labelFree__de,
      label__pl: item.price ? item.warranty.labelWithPrice__pl : item.warranty.labelFree__pl,
    }))
  }

  async getProductSkuAccessoryProducts({
    channelId,
    lang,
    slug,
    slugNumber,
  }: {
    channelId: string
    lang: string
    slug: string
    slugNumber: string
  }) {
    const restOptions = {
      relations: {
        model: {
          accessoryProducts: {
            heroImage: true,
            prices: true,
          },
        },
      },
      select: {
        id: true,
        model: {
          id: true,
          accessoryProducts: true,
        },
      },
      order: { model: { accessoryProducts: { sortOrder: 'ASC' } } },
    } as const
    const restWhere = {
      publishedAt: Not(IsNull()),
      model: {
        accessoryProducts: {
          prices: { channelId },
          publishedAt: Not(IsNull()),
        },
      },
    } as const
    let sku = await ProductSkuEntity.findOne({
      where: {
        [`slug__${lang}`]: slug,
        [`slugNumber__${lang}`]: slugNumber,
        ...restWhere,
      },
      ...restOptions,
      cache: envConfig.isProd ? ms('1 hour') : false,
    })

    // Check other languages, make sure language switching has no issue
    if (!sku) {
      sku = await ProductSkuEntity.findOne({
        where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({
          [`slug__${l}`]: slug,
          [`slugNumber__${l}`]: slugNumber,
          ...restWhere,
        })),
        ...restOptions,
      })
    }

    return sku.model.accessoryProducts.map((product) => ({
      ...pick(product, [
        'id',
        'name__en',
        'name__nl',
        'name__de',
        'name__pl',
        'desc__en',
        'desc__nl',
        'desc__de',
        'desc__pl',
        'heroImage',
      ]),
      price: product.prices.find((price) => price.channelId === channelId)?.price ?? null,
    }))
  }

  async getProductSkuAccessoryProductsBySkuIds({ channelId, skuIds }: { channelId: string; skuIds: string[] }) {
    const skus = await ProductSkuEntity.find({
      where: {
        publishedAt: Not(IsNull()),
        id: In(skuIds),
        model: {
          accessoryProducts: {
            prices: { channelId },
            publishedAt: Not(IsNull()),
          },
        },
      },
      relations: { model: { accessoryProducts: { heroImage: true, prices: true } } },
      select: {
        id: true,
        model: {
          id: true,
          accessoryProducts: {
            id: true,
            name__en: true,
            name__nl: true,
            name__de: true,
            name__pl: true,
            desc__en: true,
            desc__nl: true,
            desc__de: true,
            desc__pl: true,
            heroImage: {
              id: true,
              url: true,
              publicId: true,
              name__en: true,
              name__nl: true,
              name__de: true,
              name__pl: true,
              width: true,
              height: true,
              format: true,
            },
            prices: {
              id: true,
              channelId: true,
              price: true,
            },
          },
        },
      },
      order: { model: { accessoryProducts: { sortOrder: 'ASC' } } },
      cache: envConfig.isProd ? ms('1 hour') : false,
    })

    const accessories: GetProductSkuAccessoryProductOutput[] = []

    skus.forEach((sku) => {
      sku.model.accessoryProducts.forEach((product) => {
        if (!accessories.find((accessory) => accessory.id === product.id)) {
          accessories.push({
            ...pick(product, [
              'id',
              'name__en',
              'name__nl',
              'name__de',
              'name__pl',
              'desc__en',
              'desc__nl',
              'desc__de',
              'desc__pl',
              'heroImage',
            ]),
            price: product.prices[0]?.price ?? null,
          })
        }
      })
    })

    return accessories
  }
}
