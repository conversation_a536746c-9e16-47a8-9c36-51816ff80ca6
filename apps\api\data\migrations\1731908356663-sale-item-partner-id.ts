import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleItemPartnerId1731908356663 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First add the partner_id column
    await queryRunner.query(`
      ALTER TABLE sale_item
      ADD COLUMN partner_id UUID REFERENCES partner(id)
    `)

    // Then update the values
    await queryRunner.query(`
      UPDATE sale_item
      SET partner_id = sale.partner_id
      FROM sale
      WHERE sale_item.sale_id = sale.id
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
