import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { Cron } from '@nestjs/schedule'
import * as Sentry from '@sentry/nestjs'
import ms from 'ms'
import * as pMap from 'p-map'
import type { JsonObject } from 'type-fest'
import { In, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import {
  ProductModelAttributeCombinationEntity,
  ProductModelAttributeEntity,
  ProductModelEntity,
  ProductModelMetricsEntity,
  QuestionTypeConditionEntity,
  QuestionTypeImageTextEntity,
  QuestionTypeProblemEntity,
} from '~/entities'

@Injectable()
export class ProductModelService {
  @Cron('0 6 */3 * *') // every 3 days at 6am
  async updateModelMetrics() {
    const models = await ProductModelEntity.find({
      relations: { metrics: true, orderItems: true },
      select: { id: true, metrics: { id: true, isBestseller: true }, orderItems: { id: true, createdAt: true } },
    })
    for (const model of models) {
      const recentOrdersCount = await ProductModelEntity.getRecentOrdersCount(model.id)
      const recentSalesCount = await ProductModelEntity.getRecentSalesCount(model.id)
      model.metrics.recentOrdersCount = recentOrdersCount
      model.metrics.recentSalesCount = recentSalesCount
      await model.metrics.save()
    }
    // find metcis with highest recentOrdersCount, limit 30 records
    const bestsellerMetrics = await ProductModelMetricsEntity.find({
      order: { recentOrdersCount: 'DESC' },
      take: 30,
      select: { id: true },
    })
    const metricsToRemoveBestseller = await ProductModelMetricsEntity.find({
      where: { isBestseller: true, id: Not(In(bestsellerMetrics.map((model) => model.id))) },
      select: { id: true },
    })
    for (const metrics of metricsToRemoveBestseller) {
      metrics.isBestseller = false
      await metrics.save()
    }
    for (const metrics of bestsellerMetrics) {
      metrics.isBestseller = true
      await metrics.save()
    }
  }

  async findSellerQuestions({ lang, slug }: { lang: string; slug: string }) {
    const restOptions = {
      select: {
        id: true,
        questionTypeId: true,
        recycle: true,
        name__en: true,
        name__nl: true,
        name__de: true,
        name__pl: true,
        slug__en: true,
        slug__nl: true,
        slug__de: true,
        slug__pl: true,
        category: {
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          slug__pl: true,
        },
        brand: {
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          slug__pl: true,
        },
      },
      relations: {
        image: true,
        category: true,
        brand: true,
      },
    } as const
    let model = await ProductModelEntity.findOne({
      where: { [`slug__${lang}`]: slug },
      ...restOptions,
      cache: envConfig.isProd ? ms('2 hours') : false,
    })

    // Check other languages, make sure language switching has no issue
    if (!model) {
      model = await ProductModelEntity.findOne({
        where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({ [`slug__${l}`]: slug })),
        ...restOptions,
      })
    }

    if (!model) {
      Sentry.captureException(new Error(`Model with slug ${slug} not found, lang: ${lang}`), {
        tags: {
          type: 'product-model:findSellerQuestions',
        },
        extra: {
          slug,
          lang,
        },
        level: 'warning',
      })
      throw new NotFoundException(`Model with slug ${slug} not found`)
    }

    let attributesQuestions: ProductModelAttributeEntity[]
    let conditionQuestions: QuestionTypeConditionEntity[]
    let problemQuestions: QuestionTypeProblemEntity[]
    let problemImageTexts: QuestionTypeImageTextEntity[]
    let attributeCombinations: { variantId: string; choices: JsonObject }[]
    await pMap(
      [
        async () => {
          attributesQuestions = await ProductModelAttributeEntity.find({
            where: { modelId: model.id },
            relations: { options: true },
            order: { sortOrder: 'ASC', options: { sortOrder: 'ASC' } },
            cache: envConfig.isProd ? ms('2 hours') : false,
          })
        },
        async () => {
          conditionQuestions = await QuestionTypeConditionEntity.find({
            where: { questionTypeId: model.questionTypeId },
            relations: { options: true },
            order: {
              sortOrder: 'ASC',
              options: {
                sortOrder: 'ASC',
              },
            },
            cache: envConfig.isProd ? ms('2 hours') : false,
          })
        },
        async () => {
          problemQuestions = await QuestionTypeProblemEntity.find({
            where: { questionTypeId: model.questionTypeId },
            order: {
              sortOrder: 'ASC',
            },
            cache: envConfig.isProd ? ms('2 hours') : false,
          })
        },
        async () => {
          problemImageTexts = await QuestionTypeImageTextEntity.find({
            where: { questionTypeId: model.questionTypeId },
            relations: {
              image: true,
            },
            order: {
              sortOrder: 'ASC',
            },
          })
        },
        async () => {
          attributeCombinations = (
            await ProductModelAttributeCombinationEntity.find({
              where: { modelId: model.id },
              relations: {
                choices: {
                  option: true,
                },
              },
              select: {
                id: true,
                variantId: true,
              },
              order: {
                choices: {
                  sortOrder: 'ASC',
                },
              },
              cache: envConfig.isProd ? ms('2 hours') : false,
            })
          )
            .map((combination) => ({
              variantId: combination.variantId,
              choices: combination.choices.reduce(
                (acc, choice) => Object.assign(acc, { [choice.attributeId]: choice.optionId }),
                {}
              ),
            }))
            .filter((combination) => combination.variantId)
        },
      ],
      (func) => func()
    )
    return {
      id: model.id,
      recycle: model.recycle,
      name__en: model.name__en,
      name__nl: model.name__nl,
      name__de: model.name__de,
      name__pl: model.name__pl,
      slug__en: model.slug__en,
      slug__nl: model.slug__nl,
      slug__de: model.slug__de,
      slug__pl: model.slug__pl,
      categorySlug__en: model.category.slug__en,
      categorySlug__nl: model.category.slug__nl,
      categorySlug__de: model.category.slug__de,
      categorySlug__pl: model.category.slug__pl,
      brandSlug__en: model.brand.slug__en,
      brandSlug__nl: model.brand.slug__nl,
      brandSlug__de: model.brand.slug__de,
      brandSlug__pl: model.brand.slug__pl,
      image: model.image,
      attributesQuestions,
      conditionQuestions,
      problemQuestions,
      problemImageTexts,
      attributeCombinations,
    }
  }

  async findModelMisc(modelId: string) {
    const model = await ProductModelEntity.findOne({
      where: { id: modelId },
      relations: { category: true },
      select: {
        id: true,
        recycle: true,
        category: {
          savedCo2: true,
          savedEwaste: true,
        },
      },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })

    if (!model) {
      Sentry.captureMessage(`Model with model ID ${modelId} not found`, {
        tags: {
          type: 'product-model:findModelMisc',
        },
        extra: {
          modelId,
        },
      })
      throw new BadRequestException(`Model with model ID ${modelId} not found`)
    }

    // TODO: fix here to make it more universal
    return {
      recycle: model.recycle,
      saved: {
        co2: model.category.savedCo2,
        ewaste: model.category.savedEwaste,
      },
    }
  }
}
