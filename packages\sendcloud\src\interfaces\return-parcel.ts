import { IParcelCustomsInformation } from './parcel-customs-information'

/**
 * Represents the details of a Return Parcel
 */
export type IReturnParcel = {
  /**
   * URL to the tracking page of the carrier
   * @example 'https://tracking.sendcloud.sc/forward?carrier=dhl&code=JVGL123456780000049'
   */
  tracking_url: string

  /**
   * Tracking number of the parcel as created by the carrier
   * @example 'JVGL123456780000049'
   */
  tracking_number: string

  /**
   * Identifier of the Parcel Status
   * @example 11
   */
  parcel_status: number

  /**
   * Lookup key for the Parcel Status
   * @example 'delivered'
   */
  global_status_slug: string

  /**
   * Name of the brand associated with this Parcel
   * @example 'My Brand'
   */
  brand_name: string

  /**
   * Identifier of the order associated with this Parcel
   * @example 'EU2548657452'
   */
  order_number: string

  /**
   * Email address of the sender of this Parcel
   * @example '<EMAIL>'
   */
  from_email: string

  /**
   * Whether this Parcel was marked as deleted
   */
  deleted: boolean

  /**
   * Number of individual Parcel objects associated with this shipment
   * @example 1
   */
  collo_count: number

  /**
   * Originating country of this Parcel in two-letter ISO 3166 format
   * @example 'NL'
   */
  from_country: string

  /**
   * Name of the sender of this Parcel
   * @example 'Jane Doe'
   */
  from_name: string

  /**
   * Identifier of the Shipping Method chosen for this Parcel
   * @example 994
   */
  shipping_method: number

  /**
   * Arbitrary data about this parcel
   */
  extra_data: object

  /**
   * Customs information
   */
  customs_information?: IParcelCustomsInformation
}
