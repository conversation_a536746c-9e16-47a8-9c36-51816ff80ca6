import { Injectable } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import axios from 'axios'
import { subDays } from 'date-fns'
import ms from 'ms'
import { MoreThan } from 'typeorm'

import { envConfig } from '~/configs'
import { PartnerWebhookEntityType, PartnerWebhookEvent } from '~/constants'
import { PartnerEntity, PartnerWebhookRetryEntity, SaleEntity, SaleItemEntity } from '~/entities'

export type PartnerWebhookEventInput = {
  event: PartnerWebhookEvent
  partnerId: string
  entityType: PartnerWebhookEntityType
  entityId: string
  version: number
  onSuccess?: (result: unknown) => void
  onError?: (error: unknown) => void
}

@Injectable()
export class PartnerWebhookService {
  @Cron(CronExpression.EVERY_HOUR)
  async retryFailedWebhooks() {
    const failedWebhooks = await PartnerWebhookRetryEntity.find({
      where: {
        createdAt: MoreThan(subDays(new Date(), 48)),
      },
      order: {
        createdAt: 'ASC',
      },
    })

    for (const webhook of failedWebhooks) {
      try {
        if (webhook.data.version && webhook.data.entityType && webhook.data.entityId) {
          if (webhook.data.entityType === PartnerWebhookEntityType.TRADE_IN_ITEM) {
            const tradeInItem = await SaleItemEntity.findOne({ where: { id: webhook.data.entityId } })
            webhook.data.version = tradeInItem.version
          } else if (webhook.data.entityType === PartnerWebhookEntityType.TRADE_IN) {
            const tradeIn = await SaleEntity.findOne({ where: { id: webhook.data.entityId } })
            webhook.data.version = tradeIn.version
          }
        }
        await axios.post(webhook.requestUrl, webhook.data, {
          headers: webhook.headers,
        })

        // If successful, remove the retry record
        await PartnerWebhookRetryEntity.delete(webhook.id)
      } catch (error) {
        // Update retry count
        await PartnerWebhookRetryEntity.update(webhook.id, {
          retries: (webhook.retries || 0) + 1,
        })
        console.error(`Failed to retry webhook ${webhook.id}:`, error)
      }
    }
  }

  async callWebhook({ event, partnerId, entityType, entityId, version, onSuccess, onError }: PartnerWebhookEventInput) {
    const partner = await PartnerEntity.findOne({
      where: {
        id: partnerId,
      },
      cache: envConfig.isProd ? ms('1 hour') : false,
    })
    if (partner.apiEndpointUrl) {
      setTimeout(async () => {
        try {
          const result = await axios.post(
            partner.apiEndpointUrl,
            {
              event,
              entityType,
              entityId,
              version,
            },
            {
              headers: {
                'X-Api-Key': partner.id,
              },
            }
          )
          onSuccess?.(result?.data as unknown)
        } catch (error) {
          await PartnerWebhookRetryEntity.create({
            partnerId,
            requestUrl: partner.apiEndpointUrl,
            headers: {
              'X-Api-Key': partner.id,
            },
            data: {
              event,
              entityType,
              entityId,
              version,
            },
          }).save()
          console.error(error)
          onError?.(error)
        }
      })
    }
  }
}
