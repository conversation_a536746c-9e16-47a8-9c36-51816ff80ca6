import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { type IProductModelAttribute, ProductModelAttributeEntity } from '~/entities'

@ObjectType('ProductModelAttributeOption')
@Entity('product_model_attribute_option')
export class ProductModelAttributeOptionEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @ManyToOne(() => ProductModelAttributeEntity, (attribute) => attribute.options, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  attribute: Relation<ProductModelAttributeEntity>

  @Column()
  @RelationId((option: ProductModelAttributeOptionEntity) => option.attribute)
  attributeId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductModelAttributeOption = Omit<
  ProductModelAttributeOptionEntity,
  keyof BaseEntity
> & {
  attribute: IProductModelAttribute
}
