import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import { ParcelDocumentFormat } from '@valyuu/sendcloud'
import { isEmail, isISO31661Alpha2 } from 'class-validator'
import * as currency from 'currency.js'
import { cloneDeep, pick, uniq } from 'lodash'
import ms from 'ms'
import { I18nService } from 'nestjs-i18n'
import * as pMap from 'p-map'
import { postcodeValidator, postcodeValidatorExistsForCountry } from 'postcode-validator'
import { ArrayContains, In, IsNull, Not } from 'typeorm'
import { v4 as uuid } from 'uuid'

import { envConfig } from '~/configs'
import {
  LOCALE_ENABLED_LANGUAGES,
  ProductModelSellerQuestionType,
  SaleItemOfferErrorType,
  SaleItemOfferStatus,
  SaleItemPlanType,
  SaleItemStatus,
  SalePaymentType,
  SaleShippingLabelType,
  SaleStatus,
  UserFrom,
} from '~/constants'
import { CreateSaleInput } from '~/dtos'
import {
  BrandEntity,
  CategoryEntity,
  FaqEntity,
  PartnerEntity,
  PriceThresholdEntity,
  ProductModelAttributeCombinationEntity,
  ProductModelAttributeEntity,
  ProductModelEntity,
  ProductSeriesEntity,
  ProductVariantBasePriceEntity,
  ProductVariantConditionCombinationChoiceEntity,
  ProductVariantConditionCombinationEntity,
  ProductVariantEntity,
  ProductVariantProblemEntity,
  QuestionTypeConditionEntity,
  QuestionTypeConditionOptionEntity,
  QuestionTypeImageTextEntity,
  QuestionTypeProblemEntity,
  SaleEntity,
  SaleItemEntity,
  SaleItemOfferEntity,
  SellerPaymentPeriodEntity,
} from '~/entities'
import { SaleItemOfferService, SaleItemService, SaleService } from '~/services'
import { sendcloudGetPaperlessCode, sendcloudGetShippingLabel } from '~/utils'
import { PartnerPlatform } from '~partner/constants'
import {
  V1ConfirmTradeInItemOfferInput,
  V1CreateTradeInInput,
  V1GetBrandsInput,
  V1GetCategoriesInput,
  V1GetFaqsInput,
  V1GetModelQuestionsInput,
  V1GetModelsInput,
  V1GetSeriesInput,
  V1GetTradeInItemDataInput,
  V1GetTradeInItemDataOutput,
  V1TradeInOrderDataOutput,
  V1ValidateSellerInfoInput,
  V1WrappedCancelTradeInOutput,
  V1WrappedConfirmTradeInItemOfferOutput,
  V1WrappedCreateTradeInOutput,
  V1WrappedGetBrandsOutput,
  V1WrappedGetCategoriesOutput,
  V1WrappedGetFaqsOutput,
  V1WrappedGetModelQuestionsOutput,
  V1WrappedGetModelsOutput,
  V1WrappedGetSeriesOutput,
  V1WrappedGetTradeInItemDataOutput,
  V1WrappedGetTradeInOrderDataOutput,
  V1WrappedGetTradeInOrderItemDataOutput,
  V1WrappedValidateSellerInfoOutput,
} from '~partner/dtos'

const CACHE_TIME = '3 hours'

type ValidateSellerInfoError = {
  field: string
  message: string
}

const validateNameMaxLength = (firstName: string, lastName: string): boolean => {
  return firstName.trim().length + lastName.trim().length <= 35
}

const validateDob = (dob: Date): boolean => {
  const today = new Date()
  const birthDate = new Date(dob)
  const age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    return age - 1 >= 18
  }
  return age >= 18
}

const validateHouseNumber = (houseNumber: string, addition?: string): boolean => {
  const trimmedHouse = houseNumber.trim()
  if (!addition) {
    return trimmedHouse.length <= 8
  }
  const trimmedAddition = addition.trim()
  return trimmedHouse.length + trimmedAddition.length + 1 <= 8
}

const validateHouseNumberHasNumber = (houseNumber: string): boolean => {
  return /\d/.test(houseNumber)
}

const validatePostalCode = (postalCode: string, countryCode: string): boolean => {
  if (!postcodeValidatorExistsForCountry(countryCode)) {
    return false
  }
  return postcodeValidator(postalCode, countryCode)
}

const validatePhoneNumber = (phone: string): boolean => {
  // Remove all non-digit characters except +
  const cleanPhone = phone.replace(/[^\d+]/g, '')
  return cleanPhone.length >= 4 && /^\d{4,}$/.test(cleanPhone)
}

@Injectable()
export class V1PartnerService {
  constructor(
    private readonly saleService: SaleService,
    private readonly saleItemService: SaleItemService,
    private readonly saleItemOfferService: SaleItemOfferService,
    private readonly i18n: I18nService
  ) {}

  async getCategories({ lang }: V1GetCategoriesInput): Promise<V1WrappedGetCategoriesOutput> {
    if (!lang) {
      lang = 'nl' as const
    }
    try {
      const categories = await CategoryEntity.find({
        where: { publishedAt: Not(IsNull()) },
        order: { sortOrder: 'ASC' },
        relations: { icon: true, image: true },
        select: {
          id: true,
          [`name__${lang}`]: true,
          icon: { id: true, url: true },
          image: { id: true, url: true },
          sortOrder: true,
        },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const data = categories.map((category) => ({
        id: category.id,
        name: category[`name__${lang}`],
        icon: category.icon.url,
        image: category.image.url,
      }))

      return {
        success: true,
        data,
        statusCode: 200,
      }
    } catch (error) {
      console.error('Failed to get categories:', error)
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async getBrands({ lang, categoryId }: V1GetBrandsInput): Promise<V1WrappedGetBrandsOutput> {
    if (!lang) {
      lang = 'nl' as const
    }

    try {
      const series = await ProductSeriesEntity.find({
        where: { ...(categoryId ? { categoryId } : {}) },
        select: { id: true, brandId: true },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })
      const brandIds = uniq(series.map((s) => s.brandId))
      const brands = await BrandEntity.find({
        where: { id: In(brandIds), publishedAt: Not(IsNull()) },
        order: { sortOrder: 'ASC' },
        relations: { image: true },
        select: { id: true, [`name__${lang}`]: true, image: { id: true, url: true }, sortOrder: true },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const data = brands.map((brand) => ({
        id: brand.id,
        name: brand[`name__${lang}`],
        image: brand.image.url,
      }))

      return {
        success: true,
        data,
        statusCode: 200,
      }
    } catch (error) {
      console.error('Failed to get brands:', error)
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async getSeries({ lang, categoryId, brandId }: V1GetSeriesInput): Promise<V1WrappedGetSeriesOutput> {
    if (!lang) {
      lang = 'nl' as const
    }

    try {
      const series = await ProductSeriesEntity.find({
        where: { ...(categoryId ? { categoryId } : {}), ...(brandId ? { brandId } : {}) },
        select: { id: true, [`name__${lang}`]: true, sortOrder: true },
        order: { sortOrder: 'ASC' },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const data = series.map((series) => ({
        id: series.id,
        name: series[`name__${lang}`],
      }))

      return {
        success: true,
        data,
        statusCode: 200,
      }
    } catch (error) {
      console.error('Failed to get series:', error)
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async getModels({
    lang,
    categoryId,
    brandId,
    onlyShowEligible,
  }: V1GetModelsInput): Promise<V1WrappedGetModelsOutput> {
    if (!lang) {
      lang = 'nl' as const
    }

    try {
      const models = await ProductModelEntity.find({
        where: {
          categoryId,
          ...(brandId ? { brandId } : {}),
          publishedAt: Not(IsNull()),
          ...(onlyShowEligible ? { recycle: false } : {}),
        },
        order: { [`name__${lang}`]: 'ASC' },
        relations: { image: true, brand: true },
        select: {
          id: true,
          [`name__${lang}`]: true,
          nameIncludesBrandName: true,
          image: { id: true, url: true },
          brand: { id: true, [`name__${lang}`]: true },
        },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const data = models.map((model) => ({
        id: model.id,
        name: model[`name__${lang}`],
        additionalBrandName: model.nameIncludesBrandName ? undefined : model.brand[`name__${lang}`],
        image: model.image.url,
      }))

      return {
        success: true,
        data,
        statusCode: 200,
      }
    } catch (error) {
      console.error('Failed to get models:', error)
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async getFaqs({ lang, collection }: V1GetFaqsInput): Promise<V1WrappedGetFaqsOutput> {
    if (!lang) {
      lang = 'nl' as const
    }

    try {
      const faqs = await FaqEntity.find({
        where: {
          collections: ArrayContains([collection]),
          publishedAt: Not(IsNull()),
        },
        order: { collections: 'ASC', sortOrder: 'ASC' },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const data = faqs.map((faq) => ({
        id: faq.id,
        question: faq[`question__${lang}`],
        answer: faq[`answer__${lang}`],
      }))

      return {
        success: true,
        data,
        statusCode: 200,
      }
    } catch (error) {
      console.error('Failed to get faqs:', error)
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async getModelQuestions({ lang, modelId }: V1GetModelQuestionsInput): Promise<V1WrappedGetModelQuestionsOutput> {
    if (!lang) {
      lang = 'nl' as const
    }

    try {
      const model = await ProductModelEntity.findOne({
        where: { id: modelId },
        relations: {
          image: true,
        },
        select: {
          id: true,
          recycle: true,
          [`name__${lang}`]: true,
          image: { id: true, url: true },
          questionTypeId: true,
        },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const attributes = await ProductModelAttributeEntity.find({
        where: { modelId },
        relations: { options: true },
        select: {
          id: true,
          [`name__${lang}`]: true,
          [`question__${lang}`]: true,
          [`desc__${lang}`]: true,
          options: {
            id: true,
            [`name__${lang}`]: true,
          },
          sortOrder: true,
        },
        order: { sortOrder: 'ASC' },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const attributeCombinations = await ProductModelAttributeCombinationEntity.find({
        where: { modelId, variantId: Not(IsNull()) },
        relations: { choices: true },
        select: {
          id: true,
          variantId: true,
          choices: {
            attributeId: true,
            optionId: true,
          },
        },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const conditionQuestions = await QuestionTypeConditionEntity.find({
        where: { questionTypeId: model.questionTypeId },
        relations: { options: true },
        select: {
          id: true,
          [`name__${lang}`]: true,
          [`question__${lang}`]: true,
          [`desc__${lang}`]: true,
          options: {
            id: true,
            [`name__${lang}`]: true,
            [`desc__${lang}`]: true,
            percentOfChoice: true,
            sortOrder: true,
          },
          sortOrder: true,
        },
        order: { sortOrder: 'ASC', options: { sortOrder: 'ASC' } },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const problemQuestions = await QuestionTypeProblemEntity.find({
        where: { questionTypeId: model.questionTypeId },
        select: {
          id: true,
          [`name__${lang}`]: true,
          [`desc__${lang}`]: true,
          sortOrder: true,
        },
        order: { sortOrder: 'ASC' },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const problemImageTexts = await QuestionTypeImageTextEntity.find({
        where: { questionTypeId: model.questionTypeId },
        relations: { image: true },
        select: {
          id: true,
          [`name__${lang}`]: true,
          image: { id: true, url: true },
        },
        order: { sortOrder: 'ASC' },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })

      const data = {
        id: model.id,
        name: model[`name__${lang}`],
        image: model.image.url,
        isEligibleForTradeIn: !model.recycle,
        attributeQuestions: attributes.map((attribute) => ({
          id: attribute.id,
          name: attribute[`name__${lang}`],
          question: attribute[`question__${lang}`],
          description: attribute[`desc__${lang}`],
          options: attribute.options.map((option) => ({
            id: option.id,
            name: option[`name__${lang}`],
          })),
        })),
        attributeCombinations: attributeCombinations.map((combination) => ({
          id: combination.id,
          variantId: combination.variantId,
          choices: combination.choices.map((choice) => ({
            attributeId: choice.attributeId,
            optionId: choice.optionId,
          })),
        })),
        conditionQuestions: conditionQuestions.map((condition) => ({
          id: condition.id,
          name: condition[`name__${lang}`],
          question: condition[`question__${lang}`],
          description: condition[`desc__${lang}`],
          options: condition.options.map((option) => ({
            id: option.id,
            name: option[`name__${lang}`],
            description: option[`desc__${lang}`],
            percentOfChoice: option.percentOfChoice,
          })),
        })),
        problemQuestions: problemQuestions.map((problem) => ({
          id: problem.id,
          name: problem[`name__${lang}`],
          description: problem[`desc__${lang}`],
        })),
        problemImageTexts: problemImageTexts.map((problemImageText) => ({
          id: problemImageText.id,
          name: problemImageText[`name__${lang}`],
          image: problemImageText.image.url,
        })),
      }

      return {
        success: true,
        data,
        statusCode: 200,
      }
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          type: 'partner-v1:getModelQuestions',
        },
        extra: {
          modelId,
        },
      })
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async getTradeInItemData({
    lang,
    channelId,
    isProductFunctional,
    modelId,
    attributes,
    conditions,
    problems,
    partnerId,
  }: V1GetTradeInItemDataInput & {
    partnerId: string
    channelId: string
  }): Promise<V1WrappedGetTradeInItemDataOutput> {
    if (!partnerId) {
      throw new BadRequestException('Partner ID is required')
    }

    if (!lang) {
      lang = 'nl' as const
    }

    const partner = await PartnerEntity.findOne({
      where: { id: partnerId },
      select: { supportedPlanTypes: true },
      cache: envConfig.isProd ? ms(CACHE_TIME) : false,
    })

    try {
      const { recycleThreshold } = await PriceThresholdEntity.findOne({
        where: { channelId },
        cache: envConfig.isProd ? ms('2 hours') : false,
      })

      const attributeCombinations = await ProductModelAttributeCombinationEntity.find({
        where: { modelId },
        relations: { choices: { option: true } },
        select: { id: true, choices: { id: true, option: { id: true, [`name__${lang}`]: true } }, variantId: true },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })
      const attributeCombination = attributeCombinations.find((combination) => {
        return (
          attributes.length === combination.choices.length &&
          combination.choices.every((choice) =>
            attributes.find(
              (attribute) => attribute.attributeId === choice.attributeId && attribute.optionId === choice.option.id
            )
          )
        )
      })
      if (!attributeCombination) {
        return {
          success: false,
          message: 'Attribute combination not found',
          statusCode: 400,
        }
      }
      if (!attributeCombination.variantId) {
        return {
          success: false,
          message: 'Variant not found',
          statusCode: 400,
        }
      }
      const { variantId } = attributeCombination
      const model = await ProductModelEntity.findOne({
        where: { id: modelId },
        relations: { image: true },
        select: {
          id: true,
          categoryId: true,
          [`name__${lang}`]: true,
          image: { id: true, url: true },
        },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })
      const category = await CategoryEntity.findOne({
        where: { id: model.categoryId },
        select: { id: true, savedCo2: true, savedEwaste: true },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      })
      const sellerPaymentPeriod = await SellerPaymentPeriodEntity.findOne({
        where: { channelId },
        cache: envConfig.isProd ? ms('2 hours') : false,
      })
      const data: V1GetTradeInItemDataOutput = {
        name: model[`name__${lang}`],
        image: model.image.url,
        variantId,
        isProductFunctional,
        attributes: attributeCombination.choices.map((choice) => choice.option[`name__${lang}`]),
        answers: [],
        paymentPlans: [],
        isEligibleForTradeIn: false,
        ecoSavings: {
          savedCo2: category.savedCo2,
          savedEwaste: category.savedEwaste,
        },
      }
      if (isProductFunctional) {
        const combination = await (
          await ProductVariantConditionCombinationEntity.find({
            where: { variantId, prices: { channelId } },
            relations: { prices: true, choices: true },
            select: {
              id: true,
              prices: true,
              choices: {
                conditionId: true,
                optionId: true,
              },
            },
            cache: envConfig.isProd ? ms(CACHE_TIME) : false,
          })
        ).find((combination) => {
          return (
            combination.choices.length === conditions.length &&
            conditions.every(({ conditionId, optionId }) => {
              return combination.choices.some(
                (choice) => choice.conditionId === conditionId && choice.optionId === optionId
              )
            })
          )
        })

        if (!combination) {
          Sentry.captureException(new Error('No combination found for the given conditions'), {
            tags: {
              type: 'partner-v1:getModelQuestions',
            },
            extra: {
              modelId,
              conditions,
              channelId,
            },
          })
          return {
            success: false,
            message: 'No combination found for the given conditions',
            statusCode: 400,
          }
        }

        data.conditionCombinationId = combination.id

        const conditionOptions = await QuestionTypeConditionOptionEntity.find({
          where: { id: In(conditions.map(({ optionId }) => optionId)) },
          relations: { condition: true },
          select: {
            id: true,
            [`name__${lang}`]: true,
            condition: {
              id: true,
              [`name__${lang}`]: true,
            },
          },
          cache: envConfig.isProd ? ms(CACHE_TIME) : false,
        })

        conditionOptions.forEach((option) =>
          data.answers.push(`${option.condition[`name__${lang}`]}: ${option[`name__${lang}`]}`)
        )

        const priceItem = combination.prices?.[0]
        if (!priceItem) {
          Sentry.captureException(new Error('No price found for the given conditions'), {
            tags: {
              type: 'partner-v1:getModelQuestions',
            },
            extra: {
              modelId,
              conditions,
              channelId,
            },
          })
          return {
            success: false,
            message: 'No price found for the given conditions',
            statusCode: 400,
          }
        }

        if (priceItem.c2bPrice > recycleThreshold && partner.supportedPlanTypes.includes(SaleItemPlanType.C2B)) {
          data.paymentPlans.push({
            plan: SaleItemPlanType.C2B,
            currency: priceItem.currencyId,
            price: priceItem.c2bPrice,
            minPaymentTime: sellerPaymentPeriod.c2bFrom,
            maxPaymentTime: sellerPaymentPeriod.c2bTo,
            paymentTimeUnit: sellerPaymentPeriod.c2bUnit,
          })
        }

        if (priceItem.c2cPrice > recycleThreshold && partner.supportedPlanTypes.includes(SaleItemPlanType.C2C)) {
          data.paymentPlans.push({
            plan: SaleItemPlanType.C2C,
            paymentTimeUnit: sellerPaymentPeriod.c2cUnit,
            currency: priceItem.currencyId,
            price: priceItem.c2cPrice,
            minPaymentTime: sellerPaymentPeriod.c2cFrom,
            maxPaymentTime: sellerPaymentPeriod.c2cTo,
          })
        }

        data.isEligibleForTradeIn = data.paymentPlans.some((plan) => plan.price > 0)

        return {
          success: true,
          data,
          statusCode: 200,
        }
      } else {
        data.problemIds = problems
        const basePriceItem = await ProductVariantBasePriceEntity.findOne({ where: { variantId, channelId } })
        if (!basePriceItem) {
          Sentry.captureException(new Error('No base price found for the given variant'), {
            tags: {
              type: 'partner-v1:getModelQuestions',
            },
            extra: {
              variantId,
              channelId,
            },
          })
          return {
            success: false,
            message: 'No base price found for the given variant',
            statusCode: 400,
          }
        }
        const variantProblems = await ProductVariantProblemEntity.find({
          where: { problemId: In(problems), variantId, prices: { channelId } },
          relations: { prices: true },
        })
        if (variantProblems.length !== problems.length) {
          Sentry.captureException(new Error('No price found for the given problems'), {
            tags: {
              type: 'partner-v1:getModelQuestions',
            },
            extra: {
              variantId,
              problems,
              channelId,
            },
          })
          return {
            success: false,
            message: 'No price found for the given problems',
            statusCode: 400,
          }
        }

        data.answers.push(
          ...(
            await QuestionTypeProblemEntity.find({
              where: { id: In(problems) },
              select: { id: true, [`name__${lang}`]: true },
              cache: envConfig.isProd ? ms(CACHE_TIME) : false,
            })
          ).map((problem) => this.i18n.t('sale.problem', { lang }) + ': ' + problem[`name__${lang}`])
        )

        let price = basePriceItem.basePrice
        const currencyId = basePriceItem.currencyId

        for (const variantProblem of variantProblems) {
          price = currency(price).subtract(variantProblem.prices[0].priceReduction).value
        }
        if (price > recycleThreshold) {
          data.paymentPlans.push({
            plan: SaleItemPlanType.C2B,
            currency: currencyId,
            price,
            minPaymentTime: sellerPaymentPeriod.c2bFrom,
            maxPaymentTime: sellerPaymentPeriod.c2bTo,
            paymentTimeUnit: sellerPaymentPeriod.c2bUnit,
          })
        }
        return {
          success: true,
          data,
          statusCode: 200,
        }
      }
    } catch (error) {
      console.error(error)
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async createTradeIn(
    data: V1CreateTradeInInput & { partnerId: string; channelId: string; ip?: string }
  ): Promise<V1WrappedCreateTradeInOutput> {
    if (!data.partnerId) {
      throw new BadRequestException('Partner ID is required')
    }

    if (!data.lang) {
      data.lang = 'nl' as const
    }
    try {
      let bankAccount = cloneDeep(data.bankAccount) as { holderName: string; accountNumber: string } | undefined
      const partner = await PartnerEntity.findOne({
        where: { id: data.partnerId },
        select: { id: true, manageCustomerEmails: true, supportedPaymentTypes: true, supportedPlanTypes: true },
        cache: envConfig.isProd ? ms('1 hour') : false,
      })

      if (!partner && data.partnerId) {
        return {
          success: false,
          message: 'Partner not found',
          statusCode: 400,
        }
      }

      if (partner) {
        if (!partner.supportedPaymentTypes?.includes(data.paymentType)) {
          return {
            success: false,
            message: 'Partner does not support this payment type',
            statusCode: 400,
          }
        }
        if (!data.items.every((item) => partner.supportedPlanTypes?.includes(item.plan))) {
          return {
            success: false,
            message: 'Some items have unsupported plan types',
            statusCode: 400,
          }
        }
      }

      if (data.paymentType === SalePaymentType.BANK_TRANSFER) {
        if (!bankAccount?.accountNumber) {
          return {
            success: false,
            message: 'No bank account number provided',
            statusCode: 400,
          }
        }
        if (!bankAccount?.holderName) {
          bankAccount.holderName = `${data.shippingAddress.firstName} ${data.shippingAddress.lastName}`
        }
      } else {
        bankAccount = undefined
      }

      const models = await ProductVariantEntity.find({
        where: { id: In(data.items.map((item) => item.variantId)) },
        select: { id: true, modelId: true },
        cache: envConfig.isProd ? ms(CACHE_TIME) : false,
      }).then((variants) =>
        variants.reduce((acc, variant) => ({ ...acc, [variant.id]: variant.modelId }), {} as Record<string, string>)
      )

      // TODO: this is very hacky. We should save conditionCombinationId directly
      const conditionMap = (
        await ProductVariantConditionCombinationChoiceEntity.find({
          where: { combinationId: In(data.items.map(({ conditionCombinationId }) => conditionCombinationId)) },
          select: { combinationId: true, conditionId: true, optionId: true },
          cache: envConfig.isProd ? ms(CACHE_TIME) : false,
        })
      ).reduce(
        (acc, item) => {
          const { combinationId, ...rest } = item
          if (!acc[combinationId]) {
            acc[combinationId] = []
          }
          acc[combinationId].push(rest)
          return acc
        },
        {} as Record<string, { conditionId: string; optionId: string }[]>
      )

      const createdAt = new Date()
      const input: CreateSaleInput = {
        channelId: data.channelId,
        ...pick(data, ['email', 'dateOfBirth']),
        bankAccount,
        anonymousId: uuid(),
        shippingLabel: SaleShippingLabelType.EMAIL,
        paymentType: data.paymentType,
        address: { ...data.shippingAddress },
        language: data.lang,
        from: UserFrom.PARTNER,
        items: data.items.map((item) => ({
          id: uuid(),
          plan: item.plan as SaleItemPlanType,
          createdAt,
          ...{
            variantId: item.variantId,
            modelId: models[item.variantId],
            price: item.price,
            type: item.isProductFunctional
              ? ProductModelSellerQuestionType.CONDITION
              : ProductModelSellerQuestionType.PROBLEM,
            conditions: conditionMap[item.conditionCombinationId],
            problems: item.problemIds,
          },
        })),
      }
      const { success, error, saleId, saleNumber, userId } = await this.saleService.createSale({
        input,
        partnerId: data.partnerId,
        partnerPlatform: data.partnerPlatform || PartnerPlatform.EMBEDDED,
      })
      if (success) {
        const data = {
          tradeInId: saleId,
          userId,
          tradeInOrderNumber: saleNumber,
          version: 1,
        }
        return {
          success: true,
          data,
          statusCode: 200,
        }
      } else {
        return {
          success: false,
          message: error,
          statusCode: 400,
        }
      }
    } catch (error) {
      console.error('Failed to create trade-in:', error)
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async getTradeInShippingLabel(saleId: string, partnerId: string, format: ParcelDocumentFormat) {
    if (!saleId || !partnerId) {
      throw new BadRequestException('Sale ID and partner ID are required')
    }

    const sale = await SaleEntity.findOne({
      where: { id: saleId, partnerId },
      relations: { address: true },
      select: { id: true, parcelId: true, address: { id: true, countryId: true } },
    })
    if (!sale || !sale.parcelId) {
      throw new NotFoundException('Shipping label not found')
    }
    try {
      return sendcloudGetShippingLabel(sale.parcelId, format)
    } catch (error) {
      throw new NotFoundException(`Failed to get shipping label: ${error}`)
    }
  }

  async getTradeInPaperlessCode(saleId: string, partnerId: string, format: ParcelDocumentFormat) {
    const sale = await SaleEntity.findOne({
      where: { id: saleId, partnerId },
      relations: { address: true },
      select: { id: true, parcelId: true, address: { id: true, countryId: true } },
    })
    if (!sale || !sale.parcelId) {
      throw new NotFoundException('Paperless code not found')
    }
    try {
      return sendcloudGetPaperlessCode(sale.parcelId, format)
    } catch (error) {
      throw new NotFoundException(`Failed to get paperless code: ${error}`)
    }
  }

  private async convertSaleItemsData(saleItems: SaleItemEntity[], lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]) {
    const attributeMap: Record<string, string[]> = {}
    const conditionMap: Record<string, string> = {}
    const problemMap: Record<string, string> = {}
    const channelId = saleItems[0].channelId
    const attributeCombinations = await ProductModelAttributeCombinationEntity.find({
      where: {
        variantId: In(uniq(saleItems.map((item) => item.productVariantId))),
      },
      relations: { choices: { option: true } },
      select: { id: true, variantId: true, choices: { id: true, option: { id: true, [`name__${lang}`]: true } } },
    })
    attributeCombinations.forEach((combination) => {
      attributeMap[combination.variantId] = combination.choices.map((choice) => choice.option[`name__${lang}`])
    })
    const conditionOptions = await QuestionTypeConditionOptionEntity.find({
      where: {
        id: In(uniq(saleItems.flatMap((item) => item.answers?.CONDITION ?? []).map(({ optionId }) => optionId))),
      },
      relations: { condition: true },
      select: { id: true, [`name__${lang}`]: true, condition: { id: true, [`name__${lang}`]: true } },
    })
    conditionOptions.forEach((option) => {
      conditionMap[option.id] = `${option.condition[`name__${lang}`]}: ${option[`name__${lang}`]}`
    })
    const problemOptions = await QuestionTypeProblemEntity.find({
      where: { id: In(uniq(saleItems.flatMap((item) => item.answers?.PROBLEM ?? []))) },
      select: { id: true, [`name__${lang}`]: true },
    })
    problemOptions.forEach((option) => {
      problemMap[option.id] = this.i18n.t('sale.problem', { lang }) + ': ' + option[`name__${lang}`]
    })
    const sellerPaymentPeriod = await SellerPaymentPeriodEntity.findOne({
      where: { channelId },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })

    return pMap(saleItems, async (item) => {
      const isProductFunctional = item.answers.type === ProductModelSellerQuestionType.CONDITION
      const offerGradingNote = await this.saleItemService.getCustomerNotes(item)
      return {
        tradeInItemVersion: item.version,
        tradeInId: item.saleId,
        tradeInItemId: item.id,
        status: item.status,
        offerId: item.offerId,
        offerStatus: item.offerStatus,
        offerAuthToken: item.offer?.authToken,
        returnShipment: item.returnShipment
          ? {
              trackingNumber: item.returnShipment.trackingNumber,
              trackingUrl: item.returnShipment.trackingUrl,
            }
          : undefined,
        offerGradingNote,
        offerExpiresAt: item.offer?.expiresAt,
        variantId: item.productVariantId,
        isProductFunctional,
        name: item.productModel[`name__${lang}`],
        image: item.productModel.image.url,
        attributes: attributeMap[item.productVariantId],
        answers: isProductFunctional
          ? (item.answers?.CONDITION ?? []).map(({ optionId }) => conditionMap[optionId])
          : (item.answers?.PROBLEM ?? []).map((problemId) => problemMap[problemId]),
        paymentPlan: {
          plan: item.type,
          currency: item.currencyId,
          price: item.price,
          minPaymentTime: sellerPaymentPeriod.c2bFrom,
          maxPaymentTime: sellerPaymentPeriod.c2bTo,
          paymentTimeUnit: sellerPaymentPeriod.c2bUnit,
        },
      }
    })
  }

  async getTradeInOrderItemData(
    tradeInItemId: string,
    partnerId: string,
    version?: number
  ): Promise<V1WrappedGetTradeInOrderItemDataOutput> {
    if (!tradeInItemId || !partnerId) {
      throw new BadRequestException('Trade-in item ID and partner ID are required')
    }

    const saleItem = await SaleItemEntity.findOne({
      where: { id: tradeInItemId, sale: { partnerId } },
      relations: { sale: true, productModel: { image: true }, offer: true },
    })
    if (!saleItem || !saleItem?.sale?.partnerId) {
      throw new NotFoundException('Trade-in item not found')
    }

    const lang = saleItem.sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]
    try {
      // Check if data has changed since last request
      if (version && version === saleItem.version) {
        return {
          success: true,
          hasUpdates: false,
          statusCode: 304,
          data: null,
        }
      }
      const data = (await this.convertSaleItemsData([saleItem], lang))[0]
      return {
        success: true,
        hasUpdates: true,
        data,
        statusCode: 200,
      }
    } catch (error) {
      console.error('Failed to get trade-in order item data:', error)
      return {
        success: false,
        hasUpdates: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async getTradeInOrderData(
    tradeInId: string,
    partnerId: string,
    returnTradeInItems = false,
    version?: number
  ): Promise<V1WrappedGetTradeInOrderDataOutput> {
    if (!tradeInId || !partnerId) {
      throw new BadRequestException('Trade-in ID and partner ID are required')
    }

    const sale = await SaleEntity.findOne({
      where: { id: tradeInId, partnerId },
      relations: {
        saleItems: {
          productModel: { image: true },
          offer: true,
        },
        shipment: true,
      },
    })

    if (!sale) {
      throw new NotFoundException('Trade-in not found')
    }

    try {
      // Check if data has changed since last request
      if (version && version === sale.version) {
        return {
          success: true,
          hasUpdates: false,
          statusCode: 304,
          data: null,
        }
      }

      const lang = sale.languageId as (typeof LOCALE_ENABLED_LANGUAGES)[number]

      const data: V1TradeInOrderDataOutput = {
        tradeInVersion: sale.version,
        tradeInId: sale.id,
        userId: sale.userId,
        tradeInOrderNumber: sale.saleNumber,
        status: sale.status,
        isShippingLabelPaperless: sale.shipment.isPaperless,
        trackingNumber: sale.shipment.trackingNumber,
        trackingUrl: sale.shipment.trackingUrl,
        ...(returnTradeInItems ? { items: await this.convertSaleItemsData(sale.saleItems, lang) } : {}),
      }

      return {
        success: true,
        hasUpdates: true,
        data,
        statusCode: 200,
      }
    } catch (error) {
      console.error('Failed to get trade-in order data:', error)
      return {
        success: false,
        hasUpdates: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async cancelTradeIn(tradeInId: string, partnerId: string): Promise<V1WrappedCancelTradeInOutput> {
    if (!tradeInId || !partnerId) {
      throw new BadRequestException('Trade-in ID and partner ID are required')
    }
    const sale = await SaleEntity.findOne({ where: { id: tradeInId, partnerId } })
    if (!sale) {
      throw new NotFoundException('Trade-in not found')
    }
    try {
      if (sale.status === SaleStatus.SUBMITTED) {
        sale.status = SaleStatus.CANCELLED
        await sale.save()
        return {
          success: true,
          statusCode: 200,
        }
      } else {
        return {
          success: false,
          message: `Trade-in is not in SUBMITTED status, current status: ${sale.status}`,
          statusCode: 400,
        }
      }
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          type: 'partner-v1:cancelTradeIn',
        },
        extra: {
          tradeInId,
          partnerId,
        },
        contexts: {
          error: {
            message: 'Failed to cancel trade-in',
          },
        },
      })
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }

  async confirmTradeInItemOffer({
    tradeInItemOfferId,
    partnerId,
    confirmationType,
    authToken,
  }: V1ConfirmTradeInItemOfferInput & {
    partnerId: string
    tradeInItemOfferId: string
  }): Promise<V1WrappedConfirmTradeInItemOfferOutput> {
    if (!tradeInItemOfferId || !partnerId) {
      throw new BadRequestException('Trade-in item ID and partner ID are required')
    }
    const saleItemOffer = await SaleItemOfferEntity.findOne({
      where: { id: tradeInItemOfferId },
      relations: { saleItem: true },
    })

    let success: boolean
    let message: string
    let data: { errorReason: SaleItemOfferErrorType | null } = { errorReason: null }

    switch (true) {
      case !saleItemOffer || !saleItemOffer.saleItem || saleItemOffer.saleItem.partnerId !== partnerId:
        success = false
        data = { errorReason: SaleItemOfferErrorType.NOT_FOUND }
        message = 'Offer not found'
        break

      case saleItemOffer.saleItem.offerId !== saleItemOffer.id ||
        saleItemOffer.expiresAt < new Date() ||
        saleItemOffer.saleItem.status !== SaleItemStatus.PENDING_OFFER:
        success = false
        data = { errorReason: SaleItemOfferErrorType.EXPIRED }
        message = 'Offer expired'
        break

      case !authToken || saleItemOffer.authToken !== authToken:
        success = false
        data = { errorReason: SaleItemOfferErrorType.NOT_AUTHORIZED }
        message = 'Offer not authorized'
        break

      case saleItemOffer.saleItem.offerStatus === SaleItemOfferStatus.ACCEPTED:
        success = false
        data = { errorReason: SaleItemOfferErrorType.ALREADY_ACCEPTED }
        message = 'Offer already accepted'
        break

      case saleItemOffer.saleItem.offerStatus === SaleItemOfferStatus.DECLINED_RECYCLE ||
        saleItemOffer.saleItem.offerStatus === SaleItemOfferStatus.DECLINED_RETURN:
        success = false
        data = { errorReason: SaleItemOfferErrorType.ALREADY_REJECTED }
        message = 'Offer already rejected'
        break

      default:
        success = true
        data = { errorReason: null }
        message = 'Offer confirmed'
        await this.saleItemOfferService.confirm(saleItemOffer.saleItemId, confirmationType)
        break
    }
    return {
      success,
      message,
      data,
      statusCode: 200,
    }
  }

  async validateSellerInfo(
    data: V1ValidateSellerInfoInput & { channelId: string },
    ip: string
  ): Promise<V1WrappedValidateSellerInfoOutput> {
    try {
      const errors: ValidateSellerInfoError[] = []
      const {
        firstName,
        lastName,
        dateOfBirth,
        email,
        phoneAreaCode,
        phoneNumber,
        country,
        postalCode,
        houseNumber,
        addition,
        street,
        city,
        holderName,
        accountNumber,
      } = data

      // Validate shipping address
      if (!firstName || firstName.length > 30) {
        errors.push({ field: 'firstName', message: 'First name is required and must not exceed 30 characters' })
      }

      if (!lastName || lastName.length > 30) {
        errors.push({ field: 'lastName', message: 'Last name is required and must not exceed 30 characters' })
      }

      if (firstName && lastName && !validateNameMaxLength(firstName, lastName)) {
        errors.push({ field: 'name', message: 'Combined first and last name must not exceed 35 characters' })
      }

      if (!dateOfBirth || !validateDob(new Date(dateOfBirth))) {
        errors.push({ field: 'dateOfBirth', message: 'Must be at least 18 years old' })
      }

      if (!email || !isEmail(email)) {
        errors.push({ field: 'email', message: 'Valid email is required' })
      }

      if (!street || street.length < 3) {
        errors.push({ field: 'street', message: 'Street is required with minimum 3 characters' })
      }

      if (!houseNumber || !validateHouseNumber(houseNumber, addition)) {
        errors.push({ field: 'houseNumber', message: 'Valid house number is required' })
      }

      if (!validateHouseNumberHasNumber(houseNumber)) {
        errors.push({ field: 'houseNumber', message: 'House number must contain at least one number' })
      }

      if (addition && addition.length > 6) {
        errors.push({ field: 'addition', message: 'Addition must not exceed 6 characters' })
      }

      if (!country || !isISO31661Alpha2(country)) {
        errors.push({ field: 'country', message: 'Valid country selection is required' })
      }

      if (!postalCode || !validatePostalCode(postalCode, country)) {
        errors.push({ field: 'postalCode', message: 'Valid postal code is required' })
      }

      if (!city) {
        errors.push({ field: 'city', message: 'City is required' })
      }

      if (phoneAreaCode && !/^\+\d+$/.test(phoneAreaCode)) {
        errors.push({ field: 'phoneAreaCode', message: 'Phone area code is invalid' })
      }

      if (phoneNumber && !validatePhoneNumber(phoneNumber)) {
        errors.push({ field: 'phoneNumber', message: 'Phone number is invalid' })
      }

      // Validate bank account if provided
      if (accountNumber) {
        if (!holderName) {
          errors.push({ field: 'holderName', message: 'Bank account holder name is required' })
        }

        if (!accountNumber || !/^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/.test(accountNumber)) {
          errors.push({ field: 'accountNumber', message: 'Valid IBAN is required' })
        }
      }

      if (errors.length > 0) {
        return {
          success: true,
          data: {
            success: false,
            field: errors[0].field,
            errorMessage: errors[0].message,
          },
          statusCode: 400,
        }
      }

      // If all validations pass, proceed with existing validation logic
      const result = await this.saleService.validateSellerInfo(data, ip)
      return {
        success: true,
        data: result,
        statusCode: 200,
      }
    } catch (error) {
      console.error('Failed to validate seller info:', error)
      return {
        success: false,
        message: 'Internal server error. Please try again later.',
        statusCode: 500,
      }
    }
  }
}
