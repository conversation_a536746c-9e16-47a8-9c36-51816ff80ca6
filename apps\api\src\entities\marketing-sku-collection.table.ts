import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  ImageEntity,
  type IMarketingBanner,
  type IMarketingTag,
  type IProductSku,
  type ISeoContent,
  MarketingBannerEntity,
  MarketingTagEntity,
  ProductSkuEntity,
} from '~/entities'

@ObjectType('MarketingSkuCollection')
@Entity('marketing_sku_collection')
export class MarketingSkuCollectionEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column({ unique: true })
  slug__en: string

  @Field()
  @Column({ unique: true })
  slug__nl: string

  @Field()
  @Column({ unique: true })
  slug__de: string

  @Field()
  @Column({ unique: true })
  slug__pl: string

  @OneToMany(() => MarketingBannerEntity, (banner) => banner.collection, { nullable: true, cascade: true })
  banners: Relation<MarketingBannerEntity>[]

  @OneToMany(() => MarketingTagEntity, (tag) => tag.collection, { nullable: true, cascade: true })
  tags: Relation<MarketingTagEntity>[]

  @ManyToMany(() => ProductSkuEntity, (sku) => sku.marketingSkuCollections, { nullable: false, onDelete: 'CASCADE' })
  productSkus: ProductSkuEntity[]

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingSkuCollectionHeaderBgImageDesktop__en, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn({ name: 'header_bg_image_desktop_id__en' })
  headerBgImageDesktop__en: Relation<ImageEntity>

  @Column()
  @RelationId((collection: MarketingSkuCollectionEntity) => collection.headerBgImageDesktop__en)
  headerBgImageDesktopId__en: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingSkuCollectionHeaderBgImageMobile__en, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn({ name: 'header_bg_image_mobile_id__en' })
  headerBgImageMobile__en: Relation<ImageEntity>

  @Column()
  @RelationId((collection: MarketingSkuCollectionEntity) => collection.headerBgImageMobile__en)
  headerBgImageMobileId__en: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingSkuCollectionHeaderBgImageDesktop__nl, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn({ name: 'header_bg_image_desktop_id__nl' })
  headerBgImageDesktop__nl: Relation<ImageEntity>

  @Column()
  @RelationId((collection: MarketingSkuCollectionEntity) => collection.headerBgImageDesktop__nl)
  headerBgImageDesktopId__nl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingSkuCollectionHeaderBgImageMobile__nl, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn({ name: 'header_bg_image_mobile_id__nl' })
  headerBgImageMobile__nl: Relation<ImageEntity>

  @Column()
  @RelationId((collection: MarketingSkuCollectionEntity) => collection.headerBgImageMobile__nl)
  headerBgImageMobileId__nl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingSkuCollectionHeaderBgImageDesktop__de, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn({ name: 'header_bg_image_desktop_id__de' })
  headerBgImageDesktop__de: Relation<ImageEntity>

  @Column()
  @RelationId((collection: MarketingSkuCollectionEntity) => collection.headerBgImageDesktop__de)
  headerBgImageDesktopId__de: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingSkuCollectionHeaderBgImageMobile__de, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn({ name: 'header_bg_image_mobile_id__de' })
  headerBgImageMobile__de: Relation<ImageEntity>

  @Column()
  @RelationId((collection: MarketingSkuCollectionEntity) => collection.headerBgImageMobile__de)
  headerBgImageMobileId__de: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingSkuCollectionHeaderBgImageDesktop__pl, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn({ name: 'header_bg_image_desktop_id__pl' })
  headerBgImageDesktop__pl: Relation<ImageEntity>

  @Column()
  @RelationId((collection: MarketingSkuCollectionEntity) => collection.headerBgImageDesktop__pl)
  headerBgImageDesktopId__pl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.marketingSkuCollectionHeaderBgImageMobile__pl, {
    nullable: false,
    cascade: true,
  })
  @JoinColumn({ name: 'header_bg_image_mobile_id__pl' })
  headerBgImageMobile__pl: Relation<ImageEntity>

  @Column()
  @RelationId((collection: MarketingSkuCollectionEntity) => collection.headerBgImageMobile__pl)
  headerBgImageMobileId__pl: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IMarketingSkuCollection = Omit<MarketingSkuCollectionEntity, keyof BaseEntity> & {
  banners: IMarketingBanner[]
  tags: IMarketingTag[]
  productSkus: IProductSku[]
  seoContent?: ISeoContent
}
