import { Injectable } from '@nestjs/common'
import * as pMap from 'p-map'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageUsedInType } from '~/constants'
import { MarketingBannerEntity } from '~/entities'
import { entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'
import { entityFixPublishedAt } from '~/utils/entity-subscriber'

const imageProperties = [
  'imageDesktop__en',
  'imageMobile__en',
  'imageDesktop__nl',
  'imageMobile__nl',
  'imageDesktop__de',
  'imageMobile__de',
] as const

@Injectable()
@EventSubscriber()
export class MarketingBannerSubscriber implements EntitySubscriberInterface<MarketingBannerEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return MarketingBannerEntity
  }

  async beforeInsert(event: InsertEvent<MarketingBannerEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async afterInsert(event: InsertEvent<MarketingBannerEntity>) {
    const { entity, manager } = event

    // process images
    await pMap(imageProperties, async (property) => {
      return entityImageAutoProcess({
        image: entity[property],
        entityId: entity.id,
        imageUsedIn: ImageUsedInType.MARKETING_BANNER,
        manager,
      })
    })
  }

  async beforeUpdate(event: UpdateEvent<MarketingBannerEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }

  async afterUpdate(event: UpdateEvent<MarketingBannerEntity>) {
    const { entity, manager } = event

    // process images
    await pMap(imageProperties, async (property) => {
      return entityImageAutoProcess({
        image: entity[property],
        entityId: entity.id,
        imageUsedIn: ImageUsedInType.MARKETING_BANNER,
        manager,
      })
    })
  }

  async afterRemove(event: RemoveEvent<MarketingBannerEntity>) {
    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.MARKETING_BANNER,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
