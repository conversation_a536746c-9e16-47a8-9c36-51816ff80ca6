import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PartnerEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudPartnerService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: PartnerEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      channel: {},
      bankAccount: {},
    },
  },
})
@Controller('admin/partners')
export class CrudPartnersController implements CrudController<PartnerEntity> {
  constructor(public service: CrudPartnerService) {}
}
