import { forwardRef, Inject } from '@nestjs/common'

import { EmailType } from '~/constants'
import { OrderService, SaleService } from '~/services'

export class EmailService {
  constructor(
    @Inject(forwardRef(() => SaleService)) private readonly saleService: SaleService,
    @Inject(forwardRef(() => OrderService)) private readonly orderService: OrderService
  ) {}

  async resendEmail({ type, id }: { type: EmailType.SELLER_NEW_SALE | EmailType.BUYER_NEW_ORDER; id: string }) {
    switch (type) {
      case EmailType.SELLER_NEW_SALE:
        return this.saleService.sendSellerSaleEmail(id)
      case EmailType.BUYER_NEW_ORDER:
        return this.orderService.sendBuyerOrderEmail(id)
    }
  }
}
