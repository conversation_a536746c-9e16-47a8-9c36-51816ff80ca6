import { Injectable } from '@nestjs/common'
import { pick } from 'lodash'
import ms from 'ms'
import { IsNull, Not } from 'typeorm'

import { envConfig } from '~/configs'
import { LOCALE_ENABLED_LANGUAGES, SeoContentType } from '~/constants'
import { CategoryEntity, ProductModelEntity } from '~/entities'

@Injectable()
export class SeoContentService {
  async getSeoContent({ type, slug, lang }: { type: SeoContentType; slug: string; lang: string }) {
    switch (type) {
      case SeoContentType.CATEGORY: {
        let category = await CategoryEntity.findOne({
          where: { [`slug__${lang}`]: slug, publishedAt: Not(IsNull()) },
          relations: { seoContent: true },
          cache: envConfig.isProd ? ms('1 hours') : false,
        })

        // Check other languages, make sure language switching has no issue
        if (!category) {
          category = await CategoryEntity.findOne({
            where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({ [`slug__${l}`]: slug })),
            relations: { seoContent: true },
          })
        }
        return {
          ...pick(category, ['name__en', 'name__nl', 'name__de', 'name__pl']),
          ...pick(category?.seoContent ?? {}, [
            'header__en',
            'header__nl',
            'header__de',
            'header__pl',
            'footer__en',
            'footer__nl',
            'footer__de',
            'footer__pl',
          ]),
        }
      }
      case SeoContentType.MODEL: {
        let model = await ProductModelEntity.findOne({
          where: { [`slug__${lang}`]: slug, publishedAt: Not(IsNull()) },
          relations: { seoContent: true },
          cache: envConfig.isProd ? ms('1 hours') : false,
        })

        // Check other languages, make sure language switching has no issue
        if (!model) {
          model = await ProductModelEntity.findOne({
            where: LOCALE_ENABLED_LANGUAGES.filter((l) => l !== lang).map((l) => ({ [`slug__${l}`]: slug })),
            relations: { seoContent: true },
          })
        }
        return {
          ...pick(model, ['name__en', 'name__nl', 'name__de', 'name__pl']),
          ...pick(model?.seoContent ?? {}, [
            'header__en',
            'header__nl',
            'header__de',
            'header__pl',
            'footer__en',
            'footer__nl',
            'footer__de',
            'footer__pl',
          ]),
        }
      }
    }
  }
}
