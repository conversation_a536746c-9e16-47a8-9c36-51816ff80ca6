import { Args, Query, Resolver } from '@nestjs/graphql'

import { GetSeoContentInput, GetSeoContentOutput } from '~/dtos'
import { SeoContentService } from '~/services'

@Resolver()
export class SeoContentResolver {
  constructor(private readonly service: SeoContentService) {}

  @Query(() => GetSeoContentOutput, { nullable: true })
  async getSeoContent(@Args() { type, slug, lang }: GetSeoContentInput) {
    return this.service.getSeoContent({ type, slug, lang })
  }
}
