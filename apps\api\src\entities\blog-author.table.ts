import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { BlogPostEntity, type IBlogPost, ImageEntity } from '~/entities'

@ObjectType('BlogAuthor')
@Entity('blog_author')
export class BlogAuthorEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  name: string

  @Field({ description: 'The title of the author in English, e.g. "CEO"' })
  @Column()
  title__en: string

  @Field({ description: 'The title of the author in Dutch, e.g. "CEO"' })
  @Column()
  title__nl: string

  @Field({ description: 'The title of the author in German, e.g. "CEO"' })
  @Column()
  title__de: string

  @Field({ description: 'The title of the author in Polish, e.g. "CEO"' })
  @Column()
  title__pl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.blogAuthorAvatar, { nullable: false, cascade: true })
  @JoinColumn()
  avatar: Relation<ImageEntity>

  @Column()
  @RelationId((blogAuthor: BlogAuthorEntity) => blogAuthor.avatar)
  avatarId: string

  @Field(() => [BlogPostEntity])
  @OneToMany(() => BlogPostEntity, (post) => post.author)
  posts: Relation<BlogPostEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IBlogAuthor = Omit<BlogAuthorEntity, keyof BaseEntity> & {
  posts: IBlogPost[]
}
