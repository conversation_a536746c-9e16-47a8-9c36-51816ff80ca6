import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { SaleStatus, ShipmentTrackingStatusCode } from '~/constants'
import { ShipmentEntity } from '~/entities'

// TODO: delete this file when the two fields on order and sale are not used
@Injectable()
@EventSubscriber()
export class ShipmentSubscriber implements EntitySubscriberInterface<ShipmentEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ShipmentEntity
  }

  async afterInsert(event: InsertEvent<ShipmentEntity>) {
    const { entity, manager } = event

    // TODO: remove this legacy Prime compatibility code
    if (entity?.id) {
      const shipment = await manager.findOne(ShipmentEntity, {
        where: { id: entity.id },
        relations: { order: true, sale: true },
      })
      if (shipment?.order) {
        shipment.order.trackingNumber = shipment.trackingNumber
        shipment.order.parcelId = shipment.parcelId
        await manager.save(shipment.order)
      }
      if (shipment?.sale) {
        shipment.sale.trackingNumber = shipment.trackingNumber
        shipment.sale.parcelId = shipment.parcelId
        await manager.save(shipment.sale)
      }
    }
  }

  async afterUpdate(event: UpdateEvent<ShipmentEntity>) {
    const { entity, manager } = event

    if (
      [
        ShipmentTrackingStatusCode.EN_ROUTE_TO_SORTING_CENTER,
        ShipmentTrackingStatusCode.AT_SORTING_CENTRE,
        ShipmentTrackingStatusCode.PARCEL_EN_ROUTE,
      ].includes(entity?.statusCode)
    ) {
      const shipment = await manager.findOne(ShipmentEntity, {
        where: { id: entity.id },
        relations: { sale: true },
        select: { id: true, sale: { id: true, status: true } },
      })

      if (shipment?.sale) {
        shipment.sale.status = SaleStatus.IN_TRANSIT
        await manager.save(shipment.sale)
      }
    }
  }
}
