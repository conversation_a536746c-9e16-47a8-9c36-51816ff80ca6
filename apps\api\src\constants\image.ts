export enum ImageUsedInType {
  BLOG_POST = 'BLOG_POST',
  BLOG_AUTHOR = 'BLOG_AUTHOR',
  BRAND = 'BRAND',
  CATEGORY = 'CATEGORY',
  ACCESSORY_PRODUCT = 'ACCESSORY_PRODUCT',
  PRODUCT_MODEL = 'PRODUCT_MODEL',
  PRODUCT_SKU = 'PRODUCT_SKU',
  PRODUCT_SKU_ORIGINAL_ACCESSORY = 'PRODUCT_SKU_ORIGINAL_ACCESSORY',
  SELLER_GUIDANCE_QUESTION = 'SELLER_GUIDANCE_QUESTION',
  QUESTION_TYPE_PROBLEM_IMAGE_TEXT = 'QUESTION_TYPE_PROBLEM_IMAGE_TEXT',
  MARKETING_TAG = 'MARKETING_TAG',
  MARKETING_BANNER = 'MARKETING_BANNER',
  MARKETING_SKU_COLLECTION = 'MARKETING_SKU_COLLECTION',
}

export const ImageBasePathMapMap = {
  [ImageUsedInType.BLOG_POST]: 'blog-post' as const,
  [ImageUsedInType.BLOG_AUTHOR]: 'blog-author' as const,
  [ImageUsedInType.BRAND]: 'brand' as const,
  [ImageUsedInType.CATEGORY]: 'category' as const,
  [ImageUsedInType.ACCESSORY_PRODUCT]: 'accessory' as const,
  [ImageUsedInType.PRODUCT_MODEL]: 'model' as const,
  [ImageUsedInType.PRODUCT_SKU]: 'sku' as const,
  [ImageUsedInType.PRODUCT_SKU_ORIGINAL_ACCESSORY]: 'original-accessory' as const,
  [ImageUsedInType.SELLER_GUIDANCE_QUESTION]: 'guidance-question' as const,
  [ImageUsedInType.QUESTION_TYPE_PROBLEM_IMAGE_TEXT]: 'problem-image-text' as const,
  [ImageUsedInType.MARKETING_TAG]: 'marketing-tag' as const,
  [ImageUsedInType.MARKETING_BANNER]: 'marketing-banner' as const,
  [ImageUsedInType.MARKETING_SKU_COLLECTION]: 'marketing-sku' as const,
}

export enum ImageProcessNameType {
  TRANSLATE = 'TRANSLATE',
  COPY = 'COPY',
  HIDE = 'HIDE',
}
