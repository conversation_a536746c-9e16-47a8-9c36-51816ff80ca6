import { extname } from 'node:path'

import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import * as Sentry from '@sentry/nestjs'
import { type Express } from 'express'
import { Repository } from 'typeorm'
import { v4 as uuid } from 'uuid'

import { FileEntity } from '~/entities'
import { s3GetUrl, s3UploadFile } from '~/utils'
export class FileService extends TypeOrmCrudService<FileEntity> {
  constructor(@InjectRepository(FileEntity) repo: Repository<FileEntity>) {
    super(repo)
  }

  async uploadFile({ file, originalFilename }: { file: Express.Multer.File; originalFilename: string }) {
    const id = uuid()
    const path = `temp/${id}${extname(originalFilename)}`
    const url = s3GetUrl(path)
    await s3UploadFile(path, file.buffer)
    Sentry.captureMessage('Error uploading file', {
      tags: {
        type: 'file:uploadFile',
      },
      extra: {
        file,
      },
    })

    return FileEntity.save(
      FileEntity.create({
        id,
        path,
        url,
        size: file.size,
        mimeType: file.mimetype,
        originalFilename,
        isTemp: true,
      })
    )
  }
}
