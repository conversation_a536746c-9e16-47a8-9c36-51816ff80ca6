import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { type ISaleItemStripePayment, type IUser, SaleItemStripePaymentEntity, UserEntity } from '~/entities'

@ObjectType('BankAccount')
// @Unique(['accountNumber', 'userId'])
@Entity('bank_account')
export class BankAccountEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column()
  holderName: string

  @Field()
  @Column()
  accountNumber: string

  @Column({ nullable: true })
  stripeBankAccount?: string

  @ManyToOne(() => UserEntity, (user) => user.bankAccounts, { nullable: false, onDelete: 'CASCADE' })
  user: Relation<UserEntity>

  @Column()
  @RelationId((bankAccount: BankAccountEntity) => bankAccount.user)
  userId: string

  @OneToMany(() => SaleItemStripePaymentEntity, (saleItemPayment) => saleItemPayment.toBankAccount, { nullable: true })
  payments: Relation<SaleItemStripePaymentEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IBankAccount = Omit<BankAccountEntity, keyof BaseEntity> & {
  user: IUser
  payments: ISaleItemStripePayment[]
}
