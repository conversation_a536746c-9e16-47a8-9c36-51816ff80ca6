// Shipping functionalities type definition
export type IShippingProductMethodFunctionalities = {
  /** Integer indicating minimum age requirement for recipient (e.g., for alcohol products) */
  age_check?: number
  /** Whether the shipment is business-to-business */
  b2b: boolean
  /** Whether the shipment is business-to-consumer */
  b2c: boolean
  /** Whether the shipment fits in a standard box */
  boxable: boolean
  /** Whether the shipment is oversized/bulky and doesn't fit in standard boxes */
  bulky_goods: boolean
  /** Billing type based on country-to-country or zone-to-zone shipping */
  carrier_billing_type?: 'country' | 'zonal'
  /** Whether payment is collected upon delivery */
  cash_on_delivery?: boolean
  /** Whether shipment can contain hazardous materials */
  dangerous_goods: boolean
  /** Number of delivery attempts before return/discard */
  delivery_attempts?: number
  /** Time of day deadline for delivery (e.g., "before 12:00") */
  delivery_before?: string
  /** Expected delivery timeframe */
  delivery_deadline?: 'best_effort' | 'nextday' | 'sameday' | 'within_24h' | 'within_48h' | 'within_72h'
  /** Whether shipping requires direct carrier contract vs Sendcloud rates */
  direct_contract_only: boolean
  /** Whether shipping uses eco-friendly delivery methods */
  eco_delivery: boolean
  /** Whether shipment uses Easy Return Solution system */
  ers: boolean
  /** Method of initial shipment collection */
  first_mile?: 'dropoff' | 'fulfilment' | 'pickup' | 'pickup_dropoff'
  /** Whether receiver can choose delivery time/location before delivery */
  flex_delivery: boolean
  /** Physical format of the shipment */
  form_factor?: 'letter' | 'long' | 'mailbox' | 'pallet' | 'parcel'
  /** Whether shipment can contain fragile items */
  fragile_goods: boolean
  /** Whether shipment can contain perishable goods */
  fresh_goods: boolean
  /** Whether label includes customs information */
  harmonized_label: boolean
  /** Whether recipient must show ID for delivery */
  id_check: boolean
  /** International shipping terms for customs/duties responsibility */
  incoterm?: 'dap' | 'ddp' | 'ddu' | 'dap_dp' | 'ddp_dp'
  /** Insurance value for the shipment */
  insurance?: number
  /** Whether returns can use QR/number instead of label */
  labelless: boolean
  /** Final delivery method */
  last_mile?: 'home_delivery' | 'mailbox' | 'pobox' | 'service_point'
  /** Whether label requires manual attachment (Deutsche Post specific) */
  manually: boolean
  /** Whether multiple package shipments are supported */
  multicollo: boolean
  /** Whether delivery to neighbors is allowed */
  neighbor_delivery: boolean
  /** Whether package can be processed on conveyor systems */
  non_conveyable: boolean
  /** Whether uses personalized delivery (Deutsche Post specific) */
  personalized_delivery: boolean
  /** Whether carrier considers this premium service */
  premium: boolean
  /** Shipping speed/priority level */
  priority?: 'economical' | 'express' | 'priority' | 'standard'
  /** Whether Proof of Delivery is provided to sender */
  registered_delivery: boolean
  /** Whether this is a return shipment */
  returns: boolean
  /** PostNL international pricing zone */
  segment?: 'a' | 'b' | 'c' | 'd'
  /** Geographic scope of delivery service */
  service_area?: 'domestic' | 'domestic_remote' | 'international'
  /** Whether signature is required upon delivery */
  signature: boolean
  /** Size classification of the shipment */
  size: 'xs' | 's' | 'm' | 'l' | 'xl'
  /** Whether shipments are pre-sorted for carrier */
  sorted: boolean
  /** Whether carrier may apply additional charges based on weight */
  surcharge: boolean
  /** Whether shipment has tracking capability */
  tracked: boolean
  /** Whether shipment can contain tires */
  tyres: boolean
  /** Weekend delivery options */
  weekend_delivery?: 'saturday' | 'sunday' | 'weekends'
}
