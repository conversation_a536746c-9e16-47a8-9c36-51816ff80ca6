import { BadRequestException, Injectable } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import * as currency from 'currency.js'
import { isNil, isNumber } from 'lodash'
import ms from 'ms'
import { In } from 'typeorm'

import { envConfig } from '~/configs'
import { GetProductModelSellerPricesItemOutput } from '~/dtos'
import {
  PriceThresholdEntity,
  ProductVariantConditionCombinationEntity,
  ProductVariantEntity,
  SellerPaymentPeriodEntity,
} from '~/entities'
import { ProductModelService } from '~/services'

@Injectable()
export class ProductVariantService {
  constructor(private readonly modelService: ProductModelService) {}

  // TODO: fix throws
  async findPricesForConditions({
    modelId,
    variantId,
    channelId,
    conditions,
  }: {
    modelId: string
    variantId: string
    channelId: string
    conditions: { conditionId: string; optionId: string }[]
  }) {
    const sellerPaymentPeriod = await SellerPaymentPeriodEntity.findOne({
      where: { channelId },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })
    const result = {
      recycle: false,
      c2c: {
        price: 0,
        disabled: false as boolean,
        paymentPeriodFrom: sellerPaymentPeriod?.c2cFrom,
        paymentPeriodTo: sellerPaymentPeriod?.c2cTo,
        paymentPeriodUnit: sellerPaymentPeriod?.c2cUnit,
      } satisfies GetProductModelSellerPricesItemOutput,
      c2b: {
        price: 0,
        disabled: false as boolean,
        paymentPeriodFrom: sellerPaymentPeriod?.c2bFrom,
        paymentPeriodTo: sellerPaymentPeriod?.c2bTo,
        paymentPeriodUnit: sellerPaymentPeriod?.c2bUnit,
      } satisfies GetProductModelSellerPricesItemOutput,
      saved: {
        co2: 0,
        ewaste: 0,
      },
    }
    const modelMisc = await this.modelService.findModelMisc(modelId)
    Object.assign(result.saved, modelMisc.saved)
    result.recycle = modelMisc.recycle
    if (modelMisc.recycle) {
      return result
    }

    const { recycleThreshold } = await PriceThresholdEntity.findOne({
      where: { channelId },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })

    const combination = await (
      await ProductVariantConditionCombinationEntity.find({
        where: { variantId, prices: { channelId } },
        relations: { prices: true, choices: true },
        select: {
          id: true,
          prices: {
            c2cPrice: true,
            c2bPrice: true,
          },
          choices: {
            conditionId: true,
            optionId: true,
          },
        },
        cache: envConfig.isProd ? ms('2 hours') : false,
      })
    ).find((combination) => {
      return (
        combination.choices.length === conditions.length &&
        conditions.every(({ conditionId, optionId }) => {
          return combination.choices.some(
            (choice) => choice.conditionId === conditionId && choice.optionId === optionId
          )
        })
      )
    })
    if (!combination) {
      Sentry.captureException(new Error('No combination found for the given conditions'), {
        tags: {
          type: 'product-variant:findPricesForConditions',
        },
        extra: {
          modelId,
          variantId,
          channelId,
          conditions,
        },
      })
      throw new BadRequestException('No combination found for the given conditions')
    }
    const price = combination.prices?.[0]
    if (!price) {
      Sentry.captureException(new Error('No price found for the given conditions'), {
        tags: {
          type: 'product-variant:findPricesForConditions',
        },
        extra: {
          modelId,
          variantId,
          channelId,
          conditions,
        },
      })
      throw new BadRequestException('No price found for the given conditions')
    }

    // TODO: remove disabled
    result.c2c.price = price.c2cPrice
    result.c2b.price = price.c2bPrice
    if (result.c2c.price <= recycleThreshold) {
      result.c2c.disabled = true
    }
    if (result.c2b.price <= recycleThreshold) {
      result.c2b.disabled = true
    }
    result.recycle = result.c2c.disabled && result.c2b.disabled
    return result
  }

  // TODO: fix throws
  async findPricesForProblems({
    modelId,
    variantId,
    channelId,
    problems,
  }: {
    modelId: string
    variantId: string
    channelId: string
    problems: string[]
  }) {
    const sellerPaymentPeriod = await SellerPaymentPeriodEntity.findOne({
      where: { channelId },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })
    const result = {
      recycle: false,
      c2c: {
        price: 0,
        disabled: true as boolean,
        paymentPeriodFrom: sellerPaymentPeriod?.c2cFrom,
        paymentPeriodTo: sellerPaymentPeriod?.c2cTo,
        paymentPeriodUnit: sellerPaymentPeriod?.c2cUnit,
      } satisfies GetProductModelSellerPricesItemOutput,
      c2b: {
        price: 0,
        disabled: false as boolean,
        paymentPeriodFrom: sellerPaymentPeriod?.c2bFrom,
        paymentPeriodTo: sellerPaymentPeriod?.c2bTo,
        paymentPeriodUnit: sellerPaymentPeriod?.c2bUnit,
      } satisfies GetProductModelSellerPricesItemOutput,
      saved: {
        co2: 0,
        ewaste: 0,
      },
    }
    const modelMisc = await this.modelService.findModelMisc(modelId)
    Object.assign(result.saved, modelMisc.saved)
    result.recycle = modelMisc.recycle
    if (modelMisc.recycle) {
      result.recycle = true
      result.c2b.disabled = true
      return result
    }

    const { recycleThreshold } = await PriceThresholdEntity.findOne({
      where: { channelId },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })

    const variant = await ProductVariantEntity.findOne({
      where: {
        id: variantId,
        basePrices: { channelId },
        problems: { prices: { channelId }, problemId: In(problems) },
      },
      relations: { basePrices: true, problems: { prices: true } },
      select: {
        id: true,
        basePrices: {
          id: true,
          basePrice: true,
        },
        problems: {
          id: true,
          prices: true,
        },
      },
      cache: envConfig.isProd ? ms('2 hours') : false,
    })

    if (!variant) {
      throw new BadRequestException(`No variant found for the given variant ID: ${variantId}`)
    }
    result.c2b.price = variant.basePrices?.[0]?.basePrice
    if (isNil(result.c2b.price)) {
      throw new BadRequestException(`No price found for the given variant ID: ${variantId}`)
    }
    for (const problem of variant.problems) {
      if (!isNumber(problem.prices?.[0]?.priceReduction) || problem.prices[0].priceReduction < 0) {
        throw new BadRequestException(
          `No price reduction found for the given problem ID ${problem.id}, or it has a negative value`
        )
      }
      result.c2b.price = currency(result.c2b.price).subtract(problem.prices?.[0]?.priceReduction).value
    }
    if (result.c2b.price > recycleThreshold) {
      result.c2b.disabled = false
      result.recycle = false
    } else {
      result.c2b.disabled = true
      result.recycle = true
    }
    return result
  }
}
