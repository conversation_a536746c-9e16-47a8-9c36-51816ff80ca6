import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { ProductVariantEntity } from '~/entities'

export class CrudProductVariantService extends TypeOrmCrudService<ProductVariantEntity> {
  constructor(@InjectRepository(ProductVariantEntity) repo: Repository<ProductVariantEntity>) {
    super(repo)
  }
}
