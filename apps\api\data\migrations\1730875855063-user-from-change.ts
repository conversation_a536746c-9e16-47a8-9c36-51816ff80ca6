import { MigrationInterface, QueryRunner } from 'typeorm'

export class UserFromChange1730875855063 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update user table
    await queryRunner.query(`
      UPDATE "user"
      SET "from" = 'PARTNER'
      WHERE "from" = 'PARTNER_API'
    `)

    // Update order table
    await queryRunner.query(`
      UPDATE "order"
      SET "from" = 'PARTNER'
      WHERE "from" = 'PARTNER_API'
    `)

    // Update pre_order table
    await queryRunner.query(`
      UPDATE "pre_order"
      SET "from" = 'PARTNER'
      WHERE "from" = 'PARTNER_API'
    `)

    // Update sale table
    await queryRunner.query(`
      UPDATE "sale"
      SET "from" = 'PARTNER'
      WHERE "from" = 'PARTNER_API'
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert user table
    await queryRunner.query(`
      UPDATE "user"
      SET "from" = 'PARTNER_API'
      WHERE "from" = 'PARTNER'
    `)

    // Revert order table
    await queryRunner.query(`
      UPDATE "order"
      SET "from" = 'PARTNER_API'
      WHERE "from" = 'PARTNER'
    `)

    // Revert pre_order table
    await queryRunner.query(`
      UPDATE "pre_order"
      SET "from" = 'PARTNER_API'
      WHERE "from" = 'PARTNER'
    `)

    // Revert sale table
    await queryRunner.query(`
      UPDATE "sale"
      SET "from" = 'PARTNER_API'
      WHERE "from" = 'PARTNER'
    `)
  }
}
