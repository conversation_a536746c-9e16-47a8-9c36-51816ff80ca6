import { Button, Tooltip } from 'antd'
import copy from 'copy-to-clipboard'
import { FC, useState } from 'react'
import { PiClipboard } from 'react-icons/pi'

export type ButtonCopyProps = {
  text: string
  label: string
  size?: 'small' | 'middle' | 'large'
}

export const ButtonCopy: FC<ButtonCopyProps> = ({ text, label, size }) => {
  const [open, setOpen] = useState(false)
  return (
    <Tooltip title="Copied" placement="top" open={open}>
      <Button
        icon={<PiClipboard className="-mb-0.5 -mr-0.5" />}
        onClick={() => {
          copy(text)
          setOpen(true)
          setTimeout(() => setOpen(false), 2000)
        }}
        size={size}
      >
        {label}
      </Button>
    </Tooltip>
  )
}
