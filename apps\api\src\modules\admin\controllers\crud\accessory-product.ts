import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { AccessoryProductEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudAccessoryProductService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: AccessoryProductEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      heroImage: {},
      prices: {},
      relatedProductModels: {},
    },
  },
})
@Controller('admin/accessory-products')
export class CrudAccessoryProductController implements CrudController<AccessoryProductEntity> {
  constructor(public service: CrudAccessoryProductService) {}
}
