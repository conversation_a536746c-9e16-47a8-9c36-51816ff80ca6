{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["ESNext"], "rootDir": "./src", "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "declaration": true, "sourceMap": true, "removeComments": true, "isolatedModules": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}