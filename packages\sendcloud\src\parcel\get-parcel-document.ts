import { sendcloudClientV2 } from '../client'
import { ParcelDocumentDPI, ParcelDocumentFormat, ParcelDocumentType } from '../constants'

const DPI_VALIDATION_MAP: Record<
  ParcelDocumentFormat,
  {
    default: ParcelDocumentDPI
    valid: ParcelDocumentDPI[]
  }
> = {
  [ParcelDocumentFormat.PDF]: {
    default: ParcelDocumentDPI.DPI_72,
    valid: [ParcelDocumentDPI.DPI_72],
  },
  [ParcelDocumentFormat.ZPL]: {
    default: ParcelDocumentDPI.DPI_203,
    valid: [ParcelDocumentDPI.DPI_203, ParcelDocumentDPI.DPI_300, ParcelDocumentDPI.DPI_600],
  },
  [ParcelDocumentFormat.PNG]: {
    default: ParcelDocumentDPI.DPI_300,
    valid: [ParcelDocumentDPI.DPI_150, ParcelDocumentDPI.DPI_300],
  },
}

export type IGetParcelDocumentOptions = {
  dpi?: ParcelDocumentDPI
  format?: ParcelDocumentFormat
  raw?: boolean
}

export const getParcelDocument = async (
  parcelId: number | string,
  type: ParcelDocumentType,
  options: IGetParcelDocumentOptions = {}
): Promise<ArrayBuffer> => {
  const { format = ParcelDocumentFormat.PDF } = options
  const { dpi = DPI_VALIDATION_MAP[format].default } = options
  const { raw = false } = options

  if (!DPI_VALIDATION_MAP[format].valid.includes(dpi)) {
    throw new Error(`Invalid DPI value for ${format}. Must be one of: ${DPI_VALIDATION_MAP[format].valid.join(', ')}`)
  }

  const response = await sendcloudClientV2.get(`/parcels/${parcelId}/documents/${type}`, {
    params: {
      ...(dpi ? { dpi } : {}),
      ...(raw !== undefined ? { raw: String(raw) } : {}),
    },
    headers: {
      Accept: format,
    },
    responseType: 'arraybuffer',
  })

  return response.data
}
