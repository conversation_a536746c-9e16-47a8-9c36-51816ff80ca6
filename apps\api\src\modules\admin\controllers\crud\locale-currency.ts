import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { LocaleCurrencyEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'

import { CrudLocaleCurrencyService } from '../../services/crud/locale-currency'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: LocaleCurrencyEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
  query: {
    join: {
      channels: {},
    },
  },
})
@Controller('admin/currencies')
export class CrudLocaleCurrencyController implements CrudController<LocaleCurrencyEntity> {
  constructor(public service: CrudLocaleCurrencyService) {}
}
