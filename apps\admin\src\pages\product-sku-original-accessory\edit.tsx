import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useUpdate } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type { IProductSkuOriginalAccessory } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, InputNumber, RadioGroup, TextArea, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useState } from 'react'

import { InputImage, InputLanguageTab, InputMultiLang, InputPublish, LanguageTabChoices } from '~/components'

export const ProductSkuOriginalAccessoryEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, id, saveButtonProps, query, formLoading } = useForm<
    IProductSkuOriginalAccessory,
    HttpError,
    IProductSkuOriginalAccessory
  >({
    meta: {
      join: {
        field: 'thumbnail',
      },
    },
  })

  const record = query?.data?.data
  if (formProps.initialValues?.publishedAt) {
    formProps.initialValues.publishedAt = new Date(formProps.initialValues.publishedAt)
  }
  const { mutate } = useUpdate<IProductSkuOriginalAccessory, HttpError, Partial<IProductSkuOriginalAccessory>>()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!record?.publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
            mutate({
              resource: 'admin/original-accessories',
              id: id! as string,
              values: {
                publishedAt: value ? new Date() : null,
              },
              successNotification: (data) => {
                return {
                  type: 'success',
                  message: `ProductSkuOriginalAccessory has been ${value ? 'published' : 'unpublished'} successfully`,
                }
              },
              errorNotification: () => {
                return {
                  type: 'error',
                  message: `Failed to ${value ? 'publish' : 'unpublish'} original accessory`,
                }
              },
            })
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input label="Key" name="key" rules={['required']} disabled />
        <Watch list={['name__en', 'name__nl', 'name__de']}>
          {([name__en, name__nl, name__de]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <TextArea label="Description (English)" name="desc__en" className={clsHideEn} />
              <TextArea label="Description (Dutch)" name="desc__nl" className={clsHideNl} />
              <TextArea label="Description (German)" name="desc__de" className={clsHideDe} />
              <Input label="Highlight (English)" name="highlight__en" className={clsHideEn} />
              <Input label="Highlight (Dutch)" name="highlight__nl" className={clsHideNl} />
              <Input label="Highlight (German)" name="highlight__de" className={clsHideDe} />
              <RadioGroup
                label="Display in results"
                name="displayInResults"
                options={[
                  { label: 'Yes', value: true },
                  { label: 'No', value: false },
                ]}
                rules={['required']}
              />
              <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
              <InputImage
                label="Image"
                name="thumbnail"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className="col-span-2"
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.PRODUCT_SKU_ORIGINAL_ACCESSORY}
                processName={ImageProcessNameType.COPY}
                defaultNames={{ name__en, name__nl, name__de }}
              />
            </>
          )}
        </Watch>
        <Input noStyle type="datetime" name="publishedAt" hidden />
      </Form>
    </Edit>
  )
}
