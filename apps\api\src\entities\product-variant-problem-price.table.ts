import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  Unique,
  UpdateDateColumn,
} from 'typeorm'

import {
  ChannelEntity,
  type IChannel,
  type ILocaleCurrency,
  type IProductVariantProblem,
  LocaleCurrencyEntity,
  ProductVariantProblemEntity,
} from '~/entities'

@ObjectType('ProductVariantProblemPrice')
@Entity('product_variant_problem_price')
@Unique(['channelId', 'variantProblemId'])
export class ProductVariantProblemPriceEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  priceReduction: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Index()
  @Column()
  @RelationId((problem: ProductVariantProblemPriceEntity) => problem.channel)
  channelId: string

  @Field(() => LocaleCurrencyEntity)
  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((item: ProductVariantProblemPriceEntity) => item.currency)
  currencyId: string

  @ManyToOne(() => ProductVariantProblemEntity, { nullable: false, onDelete: 'CASCADE' })
  variantProblem: Relation<ProductVariantProblemEntity>

  @Column()
  @RelationId((problem: ProductVariantProblemPriceEntity) => problem.variantProblem)
  variantProblemId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IProductVariantProblemPrice = Omit<ProductVariantProblemPriceEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
  variantProblem: IProductVariantProblem
}
