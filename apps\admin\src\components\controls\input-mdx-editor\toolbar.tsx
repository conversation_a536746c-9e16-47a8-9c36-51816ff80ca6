import {
  applyListType$,
  BlockTypeSelect,
  BoldItalicUnderlineToggles,
  CreateLink,
  currentListType$,
  iconComponentFor$,
  InsertImage,
  Separator,
  SingleChoiceToggleGroup,
  UndoRedo,
  useCellValues,
  usePublisher,
} from '@mdxeditor/editor'
import { FC } from 'react'

export const ListsToggle = () => {
  const [currentListType, iconComponentFor] = useCellValues(currentListType$, iconComponentFor$)
  const applyListType = usePublisher(applyListType$)
  return (
    <SingleChoiceToggleGroup
      value={currentListType || ''}
      items={[
        { title: 'Bulleted list', contents: iconComponentFor('format_list_bulleted'), value: 'bullet' },
        { title: 'Numbered list', contents: iconComponentFor('format_list_numbered'), value: 'number' },
      ]}
      onChange={applyListType}
    />
  )
}

export type InputMDXToolbarProps = {
  image?: boolean
}

export const InputMDXToolbar: FC<InputMDXToolbarProps> = ({ image = false }) => {
  return (
    <>
      <UndoRedo />
      <Separator />
      <BoldItalicUnderlineToggles />
      <Separator />
      <ListsToggle />
      <Separator />
      <BlockTypeSelect />
      <Separator />
      <CreateLink />
      {image ? <InsertImage /> : null}
    </>
  )
}
