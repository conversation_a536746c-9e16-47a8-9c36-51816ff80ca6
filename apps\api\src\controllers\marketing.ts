import { Controller, Get, Param } from '@nestjs/common'

import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { MarketingService } from '~/services'

@Controller('marketing')
export class MarketingController {
  constructor(private readonly marketingService: MarketingService) {}

  @Get('feed/:lang')
  async productSkuFeed(@Param('lang') lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]) {
    return this.marketingService.productSkuFeed(lang)
  }
}
