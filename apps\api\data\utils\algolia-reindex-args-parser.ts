import * as parse from 'yargs-parser'

type AlgoliaReindexArgsType = {
  _: (string | number)[]
  // rebuild sku
  sku?: boolean
  s?: boolean
  // rebuild model
  model?: boolean
  m?: boolean
  // purge the index only
  purgeOnly?: boolean
  p?: boolean
}

type AlgoliaReindexOptionsType = {
  sku?: boolean
  model?: boolean
  purgeOnly?: boolean
}

export const algoliaReindexOptions = (() => {
  const args: AlgoliaReindexArgsType = parse(process.argv.slice(2))

  const options: AlgoliaReindexOptionsType = {
    purgeOnly: false,
    sku: false,
    model: false,
  }

  if (args.sku || args.s) {
    options.sku = true
  }

  if (args.model || args.m) {
    options.model = true
  }

  if (!args.sku && !args.s && !args.model && !args.m) {
    options.sku = true
    options.model = true
  }

  if (args.purgeOnly || args.p) {
    options.purgeOnly = true
  }

  return options
})()
