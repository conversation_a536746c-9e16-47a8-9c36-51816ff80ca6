import { IReturnParcel } from './return-parcel'

/** An object representing a Return.
 * This includes details of the original outgoing parcel, the incoming parcel and the returned items.
 */
export type IReturnObject = {
  /** The unique identifier of this Return object
   * @example 1
   */
  id: number

  /** The creation date of this Return, in ISO 8601 format
   * @example 2022-06-07T16:36:49.419457+02:00
   */
  created_at: string

  /** Identifier of the reason for this return
   * @example 1
   */
  reason: number

  /** Identifier of the original outgoing Parcel object
   * @example 1
   */
  outgoing_parcel: number

  /** Identifier of the incoming return Parcel object
   * @example 1
   */
  incoming_parcel: number

  /** Return reason message as written by the customer
   * @example 'Not good enough for me'
   */
  message: string

  /** The type of compensation the customer chose for the returned items */
  refund: IReturnObjectRefund

  /** Whether the incoming return parcel can still be cancelled */
  is_cancellable: boolean

  /** The fee associated with this return
   * @example 3.5
   */
  return_fee: number

  /** Currency of the return fee in three-letter ISO 4217 format
   * @example 'EUR'
   */
  return_fee_currency: string

  /** Cost of the label for the incoming return parcel
   * @example 6.95
   */
  label_cost: number

  /** Currency of the label cost in three-letter ISO 4217 format
   * @example 'EUR'
   */
  label_currency: string

  /** Total cost of the returned items
   * @example 48.95
   */
  items_cost: number

  /** Delivery date of the incoming return parcel
   * @example 2022-06-07T16:36:49.419457+02:00
   */
  delivered_at: string

  /**
   * The options the customer has for returning this parcel
   */
  delivery_option: 'drop_off_point' | 'drop_off_labelless' | 'in_store' | 'pickup'

  /**
   * The identifier of the in-store return address
   * @example 1
   */
  store_location?: number

  incoming_parcel_data?: IReturnParcel
  outgoing_parcel_data?: IReturnParcel

  /**
   * Object describing the status of the incoming return parcel
   */
  incoming_parcel_status?: {
    /**
     * Identifier of the Parcel Status object
     * @example 1
     */
    id: number

    /**
     * Human readable status description
     * @example 'Delivered'
     */
    message: string

    /**
     * Lookup key for the Parcel Status
     * @example 'delivered'
     */
    global_status_slug: string
  }

  /**
   * A list of images attached to a return
   */
  images?: {
    /**
     * The temporary publicly accessible URL of the image
     * @example 'https://s3-eu-central-1.amazonaws.com/returns-media/image.png'
     */
    uploaded_image: string

    /**
     * The temporary publicly accessible URL of the image thumbnail
     * @example 'https://s3-eu-central-1.amazonaws.com/returns-media/image.png'
     */
    thumbnail_image: string

    /**
     * The ID of product that this image is attached to
     * @example '123456'
     */
    product_id: string

    /**
     * A description of the uploaded image
     * @example 'My broken camera, with a crack on the lens'
     */
    description: string
  }[]

  /**
   * List of return rules applied to this Return
   */
  rule_modifications?: {
    /**
     * Name of the rule that has been applied
     */
    rule_name: string

    /**
     * Field that this rule affects
     */
    field: string

    /**
     * New value of the field based on this rule. The type depends on the field type.
     */
    value: any

    /**
     * Action taken based on this rule
     */
    action: string

    /**
     * Human-readable name for the action
     */
    friendly_name: string

    /**
     * Priority of this rule. The rule with a higher priority value takes precedence.
     * @minimum 0
     */
    priority: number

    /**
     * Item that this rule has been applied to. Will be null if the rule is applied to the whole return.
     */
    item_id: string | null
  }[]
}

/** Define the refund type separately */
export type IReturnObjectRefundType = {
  /** Identifier of the refund type */
  code: 'money' | 'credit' | 'exchange'

  /** Human readable identifier of the refund type
   * @example 'Money back'
   */
  label: string

  /** Whether this refund type requires an additional message from the customer */
  require_message: boolean
}

export type IReturnObjectRefund = {
  /** Total amount that needs to be refunded, in floating point format
   * @example '70.00'
   */
  total_refund: string

  /** Timestamp of when the refund was issued
   * @example 1654683771746
   */
  refunded_at: number

  /** Additional information about the refund as written by the user
   * @example 'Exchange for size M'
   */
  message: string

  /** Refund type details */
  refund_type: IReturnObjectRefundType
}
