import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { UserEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudUserService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: UserEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      language: {},
    },
  },
})
@Controller('admin/users')
export class CrudUserController implements CrudController<UserEntity> {
  constructor(public service: CrudUserService) {}
}
