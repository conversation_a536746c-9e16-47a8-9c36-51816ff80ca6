import { endOfMonth, endOfQuarter, startOfMonth, startOfQuarter, subMonths, subQuarters } from 'date-fns'
import { groupBy, inRange, pick } from 'lodash'

import { OrderSkuItemStatus, SUCCESSFUL_ORDER_STATUSES, UserLifecycleStage } from '~/constants'
import { OrderEntity, SaleEntity, UserEntity } from '~/entities'

export const userCalculateLifecycleStage = (items: OrderEntity[] | SaleEntity[], currentDate: Date) => {
  const currentMonthStart: Date = startOfMonth(currentDate)
  const currentMonthEnd: Date = endOfMonth(currentDate)
  const prevMonthStart: Date = subMonths(currentMonthStart, 1)
  const prevMonthEnd: Date = endOfMonth(prevMonthStart)
  let lifecycleStage
  switch (true) {
    case items.some(({ createdAt }) =>
      inRange(new Date(createdAt).getTime(), currentMonthStart.getTime(), currentMonthEnd.getTime())
    ) && !items.some(({ createdAt }) => new Date(createdAt) < currentMonthStart):
      lifecycleStage = UserLifecycleStage.NEW
      break
    case items.some(({ createdAt }) =>
      inRange(new Date(createdAt).getTime(), prevMonthStart.getTime(), prevMonthEnd.getTime())
    ) &&
      items.some(({ createdAt }) =>
        inRange(new Date(createdAt).getTime(), currentMonthStart.getTime(), currentMonthEnd.getTime())
      ):
      lifecycleStage = UserLifecycleStage.ACTIVE
      break
    case !items.some(({ createdAt }) =>
      inRange(new Date(createdAt).getTime(), currentMonthStart.getTime(), currentMonthEnd.getTime())
    ) && items.some(({ createdAt }) => new Date(createdAt) < currentMonthStart):
      lifecycleStage = UserLifecycleStage.CHURNED
      break
    case !items.some(({ createdAt }) =>
      inRange(new Date(createdAt).getTime(), prevMonthStart.getTime(), prevMonthEnd.getTime())
    ) &&
      items.some(({ createdAt }) => new Date(createdAt) < prevMonthStart) &&
      items.some(({ createdAt }) =>
        inRange(new Date(createdAt).getTime(), currentMonthStart.getTime(), currentMonthEnd.getTime())
      ):
      lifecycleStage = UserLifecycleStage.RESURRECTED
      break
    default:
      lifecycleStage = UserLifecycleStage.NON_CONVERTED
  }
  return lifecycleStage
}

export const userGetTraits = async (userId: string) => {
  // ['orders.orderItems.productSku.productVariant.productModel.category', 'sales.saleItems.productVariant.productModel.category', 'bankAccounts', 'addresses'], select: ['id', 'email', 'language', 'from', 'createdAt'] });
  const user = await UserEntity.findOne({
    where: { id: userId },
    relations: {
      orders: { orderSkuItems: { productSku: { category: true } } },
      sales: { saleItems: { productVariant: { model: { category: true } } } },
      bankAccounts: true,
      addresses: true,
    },
    order: {
      orders: { createdAt: 'ASC' },
      sales: { createdAt: 'ASC' },
    },
  })
  const now = new Date()
  const currentQuarterStart = startOfQuarter(now)
  const prevQuarterStart = subQuarters(currentQuarterStart, 1)
  const prevQuarterEnd = endOfQuarter(prevQuarterStart)

  if (!user.addresses?.[0]?.countryId || !user.addresses?.[0]?.firstName || !user.addresses?.[0]?.lastName) {
    return
  }
  const orders = user.orders
  const successfulOrders = orders.filter(({ status }) => SUCCESSFUL_ORDER_STATUSES.includes(status))
  const orderUnits = orders.flatMap((order) =>
    order.orderSkuItems
      .filter(({ status }) => status === OrderSkuItemStatus.COMPLETED)
      .map((orderSkuItem) =>
        Object.assign(pick(orderSkuItem, ['id', 'createdAt']), { productSku: orderSkuItem.productSku })
      )
  )
  const successfulOrderUnits = successfulOrders.flatMap((order) =>
    order.orderSkuItems
      .filter(({ status }) => status === OrderSkuItemStatus.COMPLETED)
      .map((orderItem) => Object.assign(pick(orderItem, ['id', 'createdAt']), { productSku: orderItem.productSku }))
  )
  const lifecycleStageBuyer = userCalculateLifecycleStage(orders, now)

  const sales = user.sales
  const successfulSales = sales.filter(({ status }) => ['received'].includes(status))
  const saleUnits = sales.flatMap((sale) =>
    sale.saleItems.map((saleItem) =>
      Object.assign(pick(saleItem, ['id', 'createdAt']), { productVariant: saleItem.productVariant })
    )
  )
  const successfulSaleUnits = successfulSales.flatMap((sale) =>
    sale.saleItems.map((saleItem) =>
      Object.assign(pick(saleItem, ['id', 'createdAt']), { productVariant: saleItem.productVariant })
    )
  )
  const currentQuarterSoldUnits = successfulSaleUnits.filter(
    ({ createdAt }) => new Date(createdAt) > currentQuarterStart
  )
  const previousQuarterSoldUnits = successfulSaleUnits.filter(
    ({ createdAt }) => new Date(createdAt) > prevQuarterStart && new Date(createdAt) < prevQuarterEnd
  )
  const lifecycleStageSeller = userCalculateLifecycleStage(sales, now)

  const groupedBuyerCategories = groupBy(
    orderUnits.map((orderSkuItem) => orderSkuItem?.productModel?.category),
    'id'
  )
  const favoriteBuyerCategory = Object.values(groupedBuyerCategories).sort((a, b) => b.length - a.length)?.[0]?.[0]
    ?.name__en

  const groupedSellerCategories = groupBy(
    saleUnits.map((saleItem) => saleItem?.productVariant?.model?.category),
    'id'
  )
  const favoriteSellerCategory = Object.values(groupedSellerCategories).sort((a, b) => b.length - a.length)?.[0]?.[0]
    ?.name__en

  const firstSaleAt = sales.length ? sales[0].createdAt : null
  const firstOrderAt = orders.length ? orders[0].createdAt : null

  return {
    id: user.id,
    email: user.email,
    createdAt: user.createdAt,
    country: user.addresses[0].countryId,
    firstName: user.addresses[0].firstName,
    lastName: user.addresses[0].lastName,
    birthday: user.dateOfBirth,
    language: user.language,
    buyer: !!orders.length,
    seller: !!sales.length,
    lifecycleStageBuyer,
    lifecycleStageSeller,
    source: user.from,
    totalOrders: orders.length,
    totalSuccessfulOrders: successfulOrders.length,
    totalUnitsOrdered: orderUnits.length,
    totalUnitsOrderedSuccessfully: successfulOrderUnits.length,
    totalSales: sales.length,
    totalSuccessfulSales: successfulSales.length,
    totalUnitsSold: saleUnits.length,
    totalUnitsSoldSuccessfully: successfulSaleUnits.length,
    currentQuarterSoldUnits: currentQuarterSoldUnits.length,
    previousQuarterSoldUnits: previousQuarterSoldUnits.length,
    favoriteBuyerCategory,
    favoriteSellerCategory,
    firstSaleAt,
    firstOrderAt,
  }
}
