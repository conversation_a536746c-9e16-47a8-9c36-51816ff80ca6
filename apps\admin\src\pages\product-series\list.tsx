import {
  DateField,
  <PERSON><PERSON><PERSON>utton,
  EditButton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useSelect,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IBrand, ICategory, IProductSeries } from '@valyuu/api/entities'
import { Input, Select, Space, Table } from 'antd'
import type { FC } from 'react'

import { PublishStatus } from '~/components'
import { filterSearchIcon, handleTableRowClick } from '~/utils'

export const ProductSeriesList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IProductSeries>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'name__en', 'slug__en', 'sortOrder', 'createdAt', 'publishedAt'],
      join: [
        {
          field: 'brand',
          select: ['id', 'name__en'],
        },
        {
          field: 'category',
          select: ['id', 'name__en'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    filters: {
      initial: [
        {
          field: 'name__en',
          operator: 'contains',
          value: '',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })
  const { selectProps: brandSelectProps } = useSelect<IBrand>({
    resource: 'admin/brands',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: categorySelectProps } = useSelect<ICategory>({
    resource: 'admin/categories',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/product-series', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="name__en"
          title="Name (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('name__en', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Name (English)" />
            </FilterDropdown>
          )}
          filterIcon={filterSearchIcon}
        />
        <Table.Column
          dataIndex="slug__en"
          title="Slug (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('slug__en')}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Slug (English)" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="categoryId"
          title="Category"
          className="cursor-pointer"
          render={(_, row: IProductSeries) => row.category.name__en}
          defaultFilteredValue={getDefaultFilter('categoryId', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} {...categorySelectProps} placeholder="Category" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="brandId"
          title="Brand"
          className="cursor-pointer"
          render={(_, row: IProductSeries) => row.brand.name__en}
          defaultFilteredValue={getDefaultFilter('brandId', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} {...brandSelectProps} placeholder="Brand" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="sortOrder"
          title="Order"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
          width="5rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<IProductSeries>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
