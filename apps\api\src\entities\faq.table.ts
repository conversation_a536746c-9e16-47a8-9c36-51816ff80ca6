import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

import { FaqCollectionType } from '~/constants'

registerEnumType(FaqCollectionType, {
  name: 'FaqCollectionType',
})

@ObjectType('Faq')
@Entity('faq')
export class FaqEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field(() => [FaqCollectionType])
  @Column({ type: 'varchar', array: true })
  @Index()
  collections: FaqCollectionType[]

  @Field()
  @Column({ nullable: false })
  question__en: string

  @Field()
  @Column({ nullable: false })
  question__nl: string

  @Field()
  @Column({ nullable: false })
  question__de: string

  @Field()
  @Column({ nullable: false })
  question__pl: string

  @Field()
  @Column({ type: 'text', nullable: false })
  answer__en: string

  @Field()
  @Column({ type: 'text', nullable: false })
  answer__nl: string

  @Field()
  @Column({ type: 'text', nullable: false })
  answer__de: string

  @Field()
  @Column({ type: 'text', nullable: false })
  answer__pl: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Field()
  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null
}

export type IFaq = Omit<FaqEntity, keyof BaseEntity>
