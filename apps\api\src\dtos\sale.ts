import { ArgsType, Field, InputType, ObjectType, PickType, registerEnumType } from '@nestjs/graphql'
import {
  ArrayNotEmpty,
  IsDateString,
  IsEmail,
  IsEmpty,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsNumberString,
  IsUUID,
  Matches,
  Max,
  Min,
  NotEquals,
  ValidateIf,
} from 'class-validator'

import {
  LOCALE_ENABLED_LANGUAGES,
  ProductModelSellerQuestionType,
  SaleItemPlanType,
  SalePaymentType,
  SaleShippingLabelType,
  UserFrom,
} from '~/constants'
import { GetProductModelSellerPricesConditionInput } from '~/dtos'

@InputType()
export class GetSaleUpdatedCartItemPriceItemInput {
  @Field()
  @IsUUID('4')
  variantId: string

  @Field()
  @IsUUID('4')
  modelId: string

  @Field(() => ProductModelSellerQuestionType)
  @IsEnum(ProductModelSellerQuestionType)
  type: ProductModelSellerQuestionType

  @Field(() => [String], {
    description: 'Selected problems of the product, only used when type === PROBLEM',
    nullable: true,
  })
  @ValidateIf((o) => o.type === ProductModelSellerQuestionType.PROBLEM)
  @IsNotEmpty({ message: 'Problems are required when type is PROBLEM' })
  @ValidateIf((o) => o.type === ProductModelSellerQuestionType.PROBLEM)
  @IsUUID('4', { each: true, message: 'Problems must be UUID' })
  problems?: string[]

  @Field(() => [GetProductModelSellerPricesConditionInput], {
    description: 'Selected conditions of the product, only used when type === CONDITION',
    nullable: true,
  })
  @ValidateIf((o) => o.type === ProductModelSellerQuestionType.CONDITION)
  @IsNotEmpty({ message: 'Conditions are required when type is CONDITION' })
  conditions?: GetProductModelSellerPricesConditionInput[]

  @Field()
  @IsUUID('4')
  cartItemId: string

  @Field()
  @IsNumber()
  oldPrice: number

  @Field(() => SaleItemPlanType)
  plan: SaleItemPlanType
}

@ArgsType()
export class GetSaleCartItemPricesInput {
  @Field()
  channelId: string

  @Field(() => [GetSaleUpdatedCartItemPriceItemInput])
  items: GetSaleUpdatedCartItemPriceItemInput[]
}

@ObjectType()
export class GetSaleCartItemPricesItemOutput {
  @Field()
  cartItemId: string

  @Field()
  hasPriceChange: boolean

  @Field()
  hasRecycled: boolean

  @Field()
  oldPrice: number

  @Field()
  newPrice: number
}

@ObjectType()
export class GetSaleCartItemPricesOutput {
  @Field()
  hasPriceChange: boolean

  @Field()
  hasRecycled: boolean

  @Field(() => [GetSaleCartItemPricesItemOutput])
  items: GetSaleCartItemPricesItemOutput[]

  @Field()
  totalAmount: number
}

@InputType()
export class SaleCartItemInput {
  @Field()
  @IsUUID('4')
  id: string

  @Field()
  @IsUUID('4')
  variantId: string

  @Field()
  @IsUUID('4')
  modelId: string

  @Field(() => ProductModelSellerQuestionType)
  @IsEnum(ProductModelSellerQuestionType)
  type: ProductModelSellerQuestionType

  @Field(() => [String], {
    description: 'Selected problems of the product, only used when type === PROBLEM',
    nullable: true,
  })
  @ValidateIf((o) => o.type === ProductModelSellerQuestionType.PROBLEM)
  @IsNotEmpty({ message: 'Problems are required when type is PROBLEM' })
  @ValidateIf((o) => o.type === ProductModelSellerQuestionType.PROBLEM)
  @IsUUID('4', { each: true, message: 'Problems must be UUID' })
  problems?: string[]

  @Field(() => [GetProductModelSellerPricesConditionInput], {
    description: 'Selected conditions of the product, only used when type === CONDITION',
    nullable: true,
  })
  @ValidateIf((o) => o.type === ProductModelSellerQuestionType.CONDITION)
  @IsNotEmpty({ message: 'Conditions are required when type is CONDITION' })
  conditions?: GetProductModelSellerPricesConditionInput[]

  @Field(() => SaleItemPlanType)
  @IsEnum(SaleItemPlanType)
  plan: SaleItemPlanType

  @Field()
  @IsNumber()
  price: number

  @Field()
  @IsDateString()
  createdAt: Date
}

@InputType()
export class CreateSaleAddressInput {
  @Field()
  @Min(1)
  @Max(30)
  firstName: string

  @Field()
  @Min(1)
  @Max(30)
  lastName: string

  @Field({ nullable: true })
  @ValidateIf((o) => !!o.phoneNumber || !!o.phoneAreaCode)
  @IsNotEmpty()
  @Matches(/^\+\d{1,4}$/, { message: 'Value must be a valid European country code' })
  phoneAreaCode?: string

  @Field({ nullable: true })
  @ValidateIf((o) => !!o.phoneNumber || !!o.phoneAreaCode)
  @IsNotEmpty()
  @IsNumberString({ no_symbols: true }, { message: 'Value must be a valid phone number' })
  @Min(4)
  @Max(15)
  phoneNumber?: string

  @Field()
  @IsNotEmpty()
  country: string

  @Field()
  @IsNotEmpty()
  postalCode: string

  @Field()
  @Max(8)
  houseNumber: string

  @Field({ nullable: true })
  @ValidateIf((o) => o.addition?.trim())
  @Max(6)
  addition?: string

  @Field()
  @Min(3)
  street: string

  @Field()
  @IsNotEmpty()
  city: string
}

@InputType()
export class CreateSaleBankAccountInput {
  @Field()
  @IsNotEmpty()
  @Min(1)
  holderName: string

  @Field()
  @IsNotEmpty()
  @Matches(
    /^(AD|AL|AT|BA|BE|BG|BY|CH|CY|CZ|DE|DK|EE|ES|FI|FO|FR|GB|GE|GI|GR|HR|HU|IE|IS|IT|LI|LT|LU|LV|MC|MD|ME|MK|MT|NL|NO|PL|PT|RO|RS|RU|SE|SI|SK|SM|TR|UA|VA)/i
  )
  @Min(10)
  accountNumber: string
}

registerEnumType(SalePaymentType, { name: 'SalePaymentType' })

@ObjectType()
export class SaleCartItemOutput extends PickType(SaleCartItemInput, ['id', 'price']) {}

@ArgsType()
export class CreateSaleInput {
  @Field()
  channelId: string

  @Field(() => [SaleCartItemInput])
  @IsNotEmpty({ each: true })
  @ArrayNotEmpty()
  items: SaleCartItemInput[]

  @Field(() => SalePaymentType)
  @IsEnum(SalePaymentType)
  paymentType: SalePaymentType

  @Field()
  @IsEmail()
  email: string

  @Field(() => SaleShippingLabelType)
  @IsEnum(SaleShippingLabelType)
  shippingLabel: SaleShippingLabelType

  @Field()
  @IsNotEmpty()
  address: CreateSaleAddressInput

  @Field({ nullable: true })
  @ValidateIf((o) => o.paymentType === SalePaymentType.BANK_TRANSFER)
  @IsNotEmpty()
  @ValidateIf((o) => o.paymentType === SalePaymentType.DONATION)
  @IsEmpty()
  bankAccount?: CreateSaleBankAccountInput

  @Field({ nullable: true })
  @ValidateIf((o) => o.paymentType === SalePaymentType.BANK_TRANSFER)
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'Value must be a valid date of birth' })
  dateOfBirth?: string

  @Field()
  @IsIn(LOCALE_ENABLED_LANGUAGES)
  language: string

  @Field(() => UserFrom, {
    description:
      "Where the user comes from, default to UserFrom.VALYUU, it's only going to be changed when the user is from an affiliated link, like abnambro.valyuu.com",
  })
  @IsEnum(UserFrom)
  from: UserFrom

  @Field()
  @IsNotEmpty()
  anonymousId: string
}

@ObjectType()
export class CreateSaleOutput {
  @Field()
  success: boolean

  @Field({ nullable: true })
  error?: string

  @Field({ nullable: true })
  createdAt: Date

  @Field({ nullable: true })
  saleId: string

  @Field({ nullable: true })
  saleNumber: string

  userId: string
}

@ObjectType()
export class ValidateSellerInfoOutput {
  @Field()
  success: boolean

  @Field({ nullable: true })
  field?: string

  @Field({ nullable: true })
  errorMessage?: string
}

@ArgsType()
export class ValidateSellerInfoInput {
  @Field()
  channelId: string

  @Field()
  email: string

  @Field()
  firstName: string

  @Field()
  lastName: string

  @Field({ nullable: true })
  phoneAreaCode?: string

  @Field({ nullable: true })
  phoneNumber?: string

  @Field()
  country: string

  @Field()
  postalCode: string

  @Field()
  houseNumber: string

  @Field({ nullable: true })
  addition?: string

  @Field()
  street: string

  @Field()
  city: string

  @Field({ nullable: true })
  dateOfBirth?: string

  @Field()
  holderName: string

  @Field()
  accountNumber: string
}
