import { IsEmail } from 'class-validator'
import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

import { AdminRoleType, AdminUserProviderType } from '~admin/constants'

@Entity('admin_user')
export class AdminUserEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @IsEmail()
  @Column({ unique: true })
  email: string

  @Column()
  name: string

  @Column({ nullable: true })
  picture: string

  @Column({ default: false })
  enabled: boolean

  @Column({ type: 'varchar', array: true, nullable: false, default: [] })
  roles: AdminRoleType[]

  @Column({ type: 'varchar' })
  provider: AdminUserProviderType

  @Column({ type: 'jsonb', default: {} })
  settings: Record<string, any>

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt: Date

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IAdminUser = Omit<AdminUserEntity, keyof BaseEntity> & {
  roles: AdminRoleType[]
}
