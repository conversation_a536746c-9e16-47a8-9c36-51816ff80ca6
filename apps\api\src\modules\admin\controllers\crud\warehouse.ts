import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { WarehouseEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudWareHouseService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: WarehouseEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      country: {},
      shippingToCountries: {},
      receivingFromCountries: {},
    },
  },
})
@Controller('admin/warehouses')
export class CrudWarehouseController implements CrudController<WarehouseEntity> {
  constructor(public service: CrudWareHouseService) {}
}
