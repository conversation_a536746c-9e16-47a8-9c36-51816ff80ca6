import {
  DateF<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>on,
  <PERSON>Button,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { TestimonialRole } from '@valyuu/api/constants'
import type { ITestimonial } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { Select } from 'antx'
import { capitalize } from 'lodash'
import { FC } from 'react'

import { PublishStatus } from '~/components'
import { formatSelectOptionsFromEnum, handleTableRowClick } from '~/utils'

export const TestimonialList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<ITestimonial>({
    syncWithLocation: true,
    meta: {
      fields: ['name', 'role', 'date', 'sortOrder', 'createdAt', 'publishedAt'],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/testimonials', id!)))}
      >
        <Table.Column dataIndex="name" title="Customer name" className="cursor-pointer" />
        <Table.Column
          dataIndex="role"
          title="Role"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('role', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select
                style={{ minWidth: 200 }}
                options={formatSelectOptionsFromEnum(TestimonialRole)}
                placeholder="Role"
              />
            </FilterDropdown>
          )}
          render={(value) => capitalize(value)}
        />
        <Table.Column
          dataIndex="date"
          title="Date of review"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          className="cursor-pointer"
          defaultSortOrder={getDefaultSortOrder('date', sorters)}
          sorter
          width="9rem"
        />
        <Table.Column
          dataIndex="sortOrder"
          title="Order"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
          width="5rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<ITestimonial>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
