import { Args, Query, Resolver } from '@nestjs/graphql'

import { ChannelEntity } from '~/entities'
import { ChannelService } from '~/services'

@Resolver()
export class ChannelResolver {
  constructor(private readonly service: ChannelService) {}

  @Query(() => ChannelEntity)
  async getChannelByCountry(@Args('countryCode') countryCode: string) {
    return this.service.findOneByCountry(countryCode)
  }
}
