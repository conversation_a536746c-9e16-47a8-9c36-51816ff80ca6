import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { BadRequestException, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { cancelParcel } from '@valyuu/sendcloud'
import { Repository } from 'typeorm'

import { ShipmentType } from '~/constants'
import { OrderEntity } from '~/entities'
import { OrderService, ShipmentService } from '~/services'

export class CrudOrderService extends TypeOrmCrudService<OrderEntity> {
  constructor(
    @InjectRepository(OrderEntity) repo: Repository<OrderEntity>,
    private readonly orderService: OrderService,
    private readonly shipmentService: ShipmentService
  ) {
    super(repo)
  }

  async createShipment(id: string, override = false) {
    const order = await OrderEntity.findOne({
      where: { id },
      relations: { user: true, shippingAddress: true, billingAddress: true, shipment: true },
    })
    if (!order) {
      throw new NotFoundException('Order not found')
    }

    if (!order.user || !order.shippingAddress) {
      throw new BadRequestException('Order must have user and address information')
    }

    if (!order.orderNumber) {
      throw new BadRequestException('Order must have a order number')
    }

    if (!order.channelId) {
      throw new BadRequestException('Order must have channel information')
    }

    if (override && order.shipment?.parcelId) {
      await cancelParcel(order.shipment.parcelId).catch(console.error)
    }

    const shipment = await this.shipmentService.create({
      type: ShipmentType.ORDER,
      orderNumber: order.orderNumber,
      customerEmail: order.user.email,
      customerAddress: order.shippingAddress,
      isReturn: false,
      channelId: order.channelId,
      orderId: order.id,
    })
    if (shipment) {
      await this.orderService.sendBuyerOrderEmail(order.id)
    }
  }
}
