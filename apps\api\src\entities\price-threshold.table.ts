import { Field, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { ChannelEntity, type IChannel, type ILocaleCurrency, LocaleCurrencyEntity } from '~/entities'

@ObjectType('PriceThreshold')
@Entity('price_threshold')
export class PriceThresholdEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @OneToOne(() => ChannelEntity, { nullable: false, onDelete: 'CASCADE' })
  @JoinColumn()
  channel: Relation<ChannelEntity>

  @Field()
  @Column()
  @RelationId((threshold: PriceThresholdEntity) => threshold.channel)
  channelId: string

  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Field()
  @Column()
  @RelationId((threshold: PriceThresholdEntity) => threshold.currency)
  currencyId: string

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
    comment: 'Price change threshold value for trade-ins',
    default: 10,
  })
  priceChangeThreshold: number

  @Field()
  @Column({
    type: 'decimal',
    unsigned: true,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
    comment: 'Recycle threshold value for trade-ins',
    default: 10,
  })
  recycleThreshold: number

  @UpdateDateColumn()
  updatedAt: Date
}

export type IPriceThreshold = Omit<PriceThresholdEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
}
