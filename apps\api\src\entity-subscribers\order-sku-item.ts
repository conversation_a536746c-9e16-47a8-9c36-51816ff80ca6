import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { OrderSkuItemStatus } from '~/constants'
import { OrderSkuItemEntity } from '~/entities'

@Injectable()
@EventSubscriber()
export class OrderSkuItemSubscriber implements EntitySubscriberInterface<OrderSkuItemEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return OrderSkuItemEntity
  }

  async afterUpdate(event: UpdateEvent<OrderSkuItemEntity>) {
    const { entity, databaseEntity, manager } = event

    if (!entity?.id || !databaseEntity?.id) {
      return
    }

    if (entity.status === OrderSkuItemStatus.CANCELLED && databaseEntity.status !== OrderSkuItemStatus.CANCELLED) {
      const orderSkuItem = await manager.findOne(OrderSkuItemEntity, {
        where: { id: entity.id },
        relations: { productSku: { orderSkuItems: true } },
        select: {
          id: true,
          productSku: {
            id: true,
            orderSkuItems: {
              id: true,
              status: true,
            },
          },
        },
      })
      if (orderSkuItem?.productSku?.orderSkuItems?.every((item) => item.status === OrderSkuItemStatus.CANCELLED)) {
        orderSkuItem.productSku.sold = false
        await manager.save(orderSkuItem.productSku)
      }
    }
  }
}
