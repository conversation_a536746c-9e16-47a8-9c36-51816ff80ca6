import { Field, ID, Int, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { FileProcessNameType, FileUsedInType, ImageUsedInType } from '~/constants'
import { type IProductSku, ProductSkuEntity } from '~/entities'

@ObjectType('File', { description: 'Amazon S3 file' })
@Entity('file')
export class FileEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column()
  url: string

  @Field({ description: 'File path from AWS S3' })
  @Column()
  path: string

  @Field({ nullable: true, description: 'File name in English when being downloaded' })
  @Column({ nullable: true })
  downloadFilename__en?: string | null

  @Field({ nullable: true, description: 'File name in Dutch when being downloaded' })
  @Column({ nullable: true })
  downloadFilename__nl?: string | null

  @Field({ nullable: true, description: 'File name in German when being downloaded' })
  @Column({ nullable: true })
  downloadFilename__de?: string | null

  @Field({ nullable: true, description: 'File name in Polish when being downloaded' })
  @Column({ nullable: true })
  downloadFilename__pl?: string | null

  @Column({ nullable: true })
  originalFilename?: string | null

  @Field(() => Int, { description: 'Size of the file in bytes' })
  @Column({ unsigned: true })
  size: number

  @Field({ description: "File's mime type" })
  @Column()
  mimeType: string

  @Index()
  @Column({ type: 'varchar', array: true, nullable: true })
  usedIn?: FileUsedInType[] | null

  @Column({ default: true })
  isTemp: boolean

  @OneToOne(() => ProductSkuEntity, (productSku) => productSku.testReport, { nullable: true, onDelete: 'CASCADE' })
  productSkuTestReport?: Relation<ProductSkuEntity>

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  processName?: FileProcessNameType | null = null
}

export type IFile = Omit<FileEntity, keyof BaseEntity> &
  Pick<FileEntity, 'processName'> & {
    productSkuTestReport?: IProductSku
  }
