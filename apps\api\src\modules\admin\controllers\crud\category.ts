import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { CategoryEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudCategoryService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: CategoryEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      icon: {},
      image: {},
      productSeries: {},
      productModels: {},
      productVariants: {},
      warrantyRules: {},
      seoContent: {},
      defaultOriginalAccessories: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/categories')
export class CrudCategoryController implements CrudController<CategoryEntity> {
  constructor(public service: CrudCategoryService) {}
}
