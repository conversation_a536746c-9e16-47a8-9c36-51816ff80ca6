import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChannelPaymentPeriod1733295328307 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create table if not exists
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS "seller_payment_period" (
        "type" varchar NOT NULL,
        "channel_id" varchar,
        "c2b_from" smallint,
        "c2b_to" smallint NOT NULL,
        "c2b_unit" varchar NOT NULL,
        "c2c_from" smallint,
        "c2c_to" smallint NOT NULL,
        "c2c_unit" varchar NOT NULL,
        "created_at" timestamp NOT NULL DEFAULT now(),
        "updated_at" timestamp NOT NULL DEFAULT now(),
        "partner_id" uuid,
        "donation_from" smallint,
        "donation_to" smallint NOT NULL,
        "donation_unit" varchar NOT NULL,
        "id" uuid NOT NULL DEFAULT uuid_generate_v4()
      )
    `)

    // Insert initial data
    await queryRunner.query(`
      INSERT INTO seller_payment_period (
        type, channel_id, c2b_from, c2b_to, c2b_unit,
        c2c_from, c2c_to, c2c_unit,
        donation_from, donation_to, donation_unit
      )
      SELECT 
        'default', id, 3, 5, 'DAYS',
        NULL, 14, 'DAYS',
        NULL, 14, 'DAYS'
      FROM channel
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
