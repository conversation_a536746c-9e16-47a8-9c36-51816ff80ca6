import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ChannelEntity } from '~/entities'
import { entityFixPublishedAt } from '~/utils/entity-subscriber'

@Injectable()
@EventSubscriber()
export class ChannelSubscriber implements EntitySubscriberInterface<ChannelEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ChannelEntity
  }

  async beforeInsert(event: InsertEvent<ChannelEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async beforeUpdate(event: UpdateEvent<ChannelEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }
}
