import { MigrationInterface, QueryRunner } from 'typeorm'

export class WarehouseName1732656748113 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const hasNameColumn = await queryRunner.hasColumn('warehouse', 'name')
    if (hasNameColumn) {
      await queryRunner.query(`ALTER TABLE "warehouse" RENAME COLUMN "name" TO "company_name"`)
    }

    const hasContactNameColumn = await queryRunner.hasColumn('warehouse', 'contact_name')
    if (!hasContactNameColumn) {
      await queryRunner.query(`ALTER TABLE "warehouse" ADD COLUMN "contact_name" varchar`)
      await queryRunner.query(`UPDATE "warehouse" SET "contact_name" = 'Valyuu'`)
      await queryRunner.query(`ALTER TABLE "warehouse" ALTER COLUMN "contact_name" SET NOT NULL`)
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
