// TODO: to be deleted
import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { type IQuestionTypeCondition, QuestionTypeConditionEntity } from '~/entities'

@ObjectType('QuestionTypeConditionOption')
@Entity('question_type_condition_option')
export class QuestionTypeConditionOptionEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  key: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__en: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__nl: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__de: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__pl: string

  @Field({ nullable: true })
  @Column({ unsigned: true, nullable: true })
  percentOfChoice: number

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @ManyToOne(() => QuestionTypeConditionEntity, (condition) => condition.options, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  condition: Relation<QuestionTypeConditionEntity>

  @Column()
  @RelationId((option: QuestionTypeConditionOptionEntity) => option.condition)
  conditionId: string

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IQuestionTypeConditionOption = Omit<QuestionTypeConditionOptionEntity, keyof BaseEntity> & {
  condition: IQuestionTypeCondition
}
