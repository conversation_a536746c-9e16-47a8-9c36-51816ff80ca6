import { plainToClass } from 'class-transformer'
import { pick } from 'lodash'
import * as pMap from 'p-map'
import type { ConditionalExcept } from 'type-fest'
import { type EntityManager, IsNull, Not } from 'typeorm'

import { ProductSkuIndex } from '~/algolia-indices'
import { envConfig } from '~/configs'
import { ChannelEntity, ProductModelEntity, ProductSkuEntity, ProductSkuPriceEntity } from '~/entities'
import { AlgoliaIndex } from '~/utils'

export type ProductModelIndexPriceFrom = Record<
  string,
  {
    skuId: string
    price: number
    originalPrice: number
    brandNewPrice: number
    isDeal: boolean
  }
>

export type ProductModelIndexCrudArgs = {
  id: string
  manager?: EntityManager
}

export type ProductModelIndexRebuildArgs = {
  manager?: EntityManager
}

export type ProductModelIndexCountArgs = {
  modelId: string
  manager?: EntityManager
}

export class ProductModelIndex extends AlgoliaIndex {
  static indexName = envConfig.ALGOLIA_INDEX_PREFIX + 'product_model'
  static entity = ProductModelEntity

  objectID: string

  name__en: string

  name__nl: string

  name__de: string

  nameIncludesBrandName: boolean

  slug__en: string

  slug__nl: string

  slug__de: string

  brandId: string

  brandName__en: string

  brandName__nl: string

  brandName__de: string

  brandSlug__en: string

  brandSlug__nl: string

  brandSlug__de: string

  brandSortOrder: number

  categoryId: string

  categoryName__en: string

  categoryName__nl: string

  categoryName__de: string

  categorySlug__en: string

  categorySlug__nl: string

  categorySlug__de: string

  categorySortOrder: number

  seriesId: string

  seriesName__en: string

  seriesName__nl: string

  seriesName__de: string

  seriesSlug__en: string

  seriesSlug__nl: string

  seriesSlug__de: string

  seriesSortOrder: number

  imagePublicId: string

  imageUrl: string

  releaseDate: string

  releaseDateTimestamp: number

  recycle: boolean

  // sales count within 3 months
  recentSalesCount: number = 0

  // orders count within 3 months
  recentOrdersCount: number = 0

  skuCount: number = 0

  skuPricesFrom: ProductModelIndexPriceFrom = {}

  // TODO: think about it
  isBestseller: boolean = false

  isMvp: boolean = false

  static async getObject(objectID: string): Promise<ProductModelIndex> {
    return super.getObject(objectID)
  }

  static async getObjects(objectIDs: string[]): Promise<ProductModelIndex[]> {
    return super.getObjects(objectIDs)
  }

  static async partialUpdateObject(object: Partial<ProductModelIndex['_model']>) {
    return super.partialUpdateObject(object)
  }

  static async partialUpdateObjects(objects: Partial<ProductModelIndex['_model']>[]) {
    return super.partialUpdateObjects(objects)
  }

  static async getSkuCount({
    modelId,
    manager = ProductModelEntity.getRepository().manager,
  }: ProductModelIndexCountArgs) {
    return manager.count(ProductSkuEntity, { where: { modelId, publishedAt: Not(IsNull()), sold: false } })
  }
  static async getSkuPriceFrom({
    modelId,
    manager = ProductModelEntity.getRepository().manager,
  }: ProductModelIndexCountArgs): Promise<ProductModelIndexPriceFrom> {
    const priceFrom: ProductModelIndexPriceFrom = {}
    const channels = await manager.find(ChannelEntity, {
      where: { publishedAt: Not(IsNull()), disableBuyer: false },
      select: ['id'],
    })
    await pMap(channels, async ({ id: channelId }) => {
      const price = await manager.findOne(ProductSkuPriceEntity, {
        where: { channelId, sku: { modelId, publishedAt: Not(IsNull()), sold: false } },
        order: { price: 'ASC' },
        relations: { sku: true },
        select: {
          id: true,
          channelId: true,
          price: true,
          originalPrice: true,
          brandNewPrice: true,
          isDeal: true,
          skuId: true,
          sku: { id: true, modelId: true },
        },
      })
      if (price) {
        priceFrom[channelId] = pick(price, ['skuId', 'price', 'originalPrice', 'brandNewPrice', 'isDeal'])
      }
    })
    return priceFrom
  }

  static async fromEntity({
    id,
    manager = ProductModelEntity.getRepository().manager,
  }: ProductModelIndexCrudArgs): Promise<Partial<ProductModelIndex>> {
    const model = await manager.findOneOrFail(ProductModelEntity, {
      where: { id },
      relations: {
        brand: true,
        category: true,
        series: true,
        image: true,
        metrics: true,
      },
      select: {
        id: true,
        name__en: true,
        name__nl: true,
        name__de: true,
        nameIncludesBrandName: true,
        slug__en: true,
        slug__nl: true,
        slug__de: true,
        recycle: true,
        metrics: {
          id: true,
          recentOrdersCount: true,
          recentSalesCount: true,
          isMvp: true,
          isBestseller: true,
        },
        brand: {
          id: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          sortOrder: true,
        },
        category: {
          id: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          sortOrder: true,
        },
        series: {
          id: true,
          name__en: true,
          name__nl: true,
          name__de: true,
          slug__en: true,
          slug__nl: true,
          slug__de: true,
          sortOrder: true,
        },
        image: {
          publicId: true,
          url: true,
        },
        releaseDate: true,
      },
    })

    const skuCount = await this.getSkuCount({ modelId: id, manager })
    const skuPricesFrom = await this.getSkuPriceFrom({ modelId: id, manager })

    return plainToClass(ProductModelIndex, {
      ...pick(model, [
        'name__en',
        'name__nl',
        'name__de',
        'nameIncludesBrandName',
        'slug__en',
        'slug__nl',
        'slug__de',
        'recycle',
      ]),
      objectID: model.id,
      brandId: model.brand.id,
      brandName__en: model.brand.name__en,
      brandName__nl: model.brand.name__nl,
      brandName__de: model.brand.name__de,
      brandSlug__en: model.brand.slug__en,
      brandSlug__nl: model.brand.slug__nl,
      brandSlug__de: model.brand.slug__de,
      brandSortOrder: model.brand.sortOrder,
      categoryId: model.category.id,
      categoryName__en: model.category.name__en,
      categoryName__nl: model.category.name__nl,
      categoryName__de: model.category.name__de,
      categorySlug__en: model.category.slug__en,
      categorySlug__nl: model.category.slug__nl,
      categorySlug__de: model.category.slug__de,
      categorySortOrder: model.category.sortOrder,
      seriesId: model.series.id,
      seriesName__en: model.series.name__en,
      seriesName__nl: model.series.name__nl,
      seriesName__de: model.series.name__de,
      seriesSlug__en: model.series.slug__en,
      seriesSlug__nl: model.series.slug__nl,
      seriesSlug__de: model.series.slug__de,
      seriesSortOrder: model.series.sortOrder,
      imagePublicId: model.image.publicId,
      imageUrl: model.image.url,
      isBestseller: model.metrics.isBestseller,
      isMvp: model.metrics.isMvp,
      releaseDate: model.releaseDate,
      releaseDateTimestamp: new Date(model.releaseDate).getTime(),
      skuCount,
      skuPricesFrom,
      recentSalesCount: model.metrics.recentSalesCount,
      recentOrdersCount: model.metrics.recentOrdersCount,
    })
  }

  static async addIndex({ id, manager = ProductModelEntity.getRepository().manager }: ProductModelIndexCrudArgs) {
    const index = await ProductModelIndex.fromEntity({ id, manager })
    await index.save()
    return index
  }
  static async updateIndex({ id, manager = ProductModelEntity.getRepository().manager }: ProductModelIndexCrudArgs) {
    const indexFromEntity = await ProductModelIndex.fromEntity({
      id,
      manager,
    })
    await indexFromEntity.save()
  }
  static async deleteIndex({ id, manager = ProductModelEntity.getRepository().manager }: ProductModelIndexCrudArgs) {
    await ProductModelIndex.deleteObject(id)
    const skus = await manager.find(ProductSkuEntity, { where: { modelId: id }, select: ['id'] })
    await ProductSkuIndex.deleteObjects(skus.map((sku: ProductSkuEntity) => sku.id))
  }
  // TODO: update statistic fields
  static async rebuild({ manager = ProductModelEntity.getRepository().manager }: ProductModelIndexRebuildArgs = {}) {
    await this.init()
    await this.clearObjects()
    const skus = await manager.find(ProductModelEntity, {
      where: { publishedAt: Not(IsNull()) },
      select: { id: true },
    })
    await pMap(
      skus,
      async ({ id }) => {
        await ProductModelIndex.addIndex({ id, manager })
      },
      { concurrency: 10 }
    )
  }
}

export type IProductModelIndex = Omit<ConditionalExcept<ProductModelIndex, Function>, keyof AlgoliaIndex>
