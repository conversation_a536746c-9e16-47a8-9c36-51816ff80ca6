import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IQuestionType,
  type IQuestionTypeConditionOption,
  QuestionTypeConditionOptionEntity,
  QuestionTypeEntity,
} from '~/entities'

@ObjectType('QuestionTypeCondition')
@Entity('question_type_condition')
export class QuestionTypeConditionEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  // TODO: start removing
  @Field()
  @Column()
  key: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column()
  question__en: string

  @Field()
  @Column()
  question__nl: string

  @Field()
  @Column()
  question__de: string

  @Field()
  @Column()
  question__pl: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__en?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__nl?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__de?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__pl?: string

  @Field(() => [QuestionTypeConditionOptionEntity])
  @OneToMany(() => QuestionTypeConditionOptionEntity, (option) => option.condition, {
    nullable: false,
    cascade: true,
  })
  options: Relation<QuestionTypeConditionOptionEntity>[]
  // TODO: end removing

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @ManyToOne(() => QuestionTypeEntity, (questionType) => questionType.conditions, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  questionType: Relation<QuestionTypeEntity>

  @Column()
  @RelationId((condition: QuestionTypeConditionEntity) => condition.questionType)
  questionTypeId: string

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IQuestionTypeCondition = Omit<QuestionTypeConditionEntity, keyof BaseEntity> & {
  options: IQuestionTypeConditionOption[]
  questionType: IQuestionType
}
