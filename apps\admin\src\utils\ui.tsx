import { FilterDropdown } from '@refinedev/antd'
import type { FilterDropdownProps, FilterValue } from 'antd/lib/table/interface'
import type { MouseEvent } from 'react'
import { AiOutlineSearch } from 'react-icons/ai'

// Bypassing the actions column of the table
export const handleTableRowClick = (onClick: () => void) => ({
  onClick: ({ target }: MouseEvent) => {
    let node: HTMLElement | null = target as HTMLElement
    let level = 0

    while (node && level <= 6) {
      if (node.nodeName === 'TD' && node.nextSibling) {
        onClick()
        break
      }
      node = node.parentElement
      level++
    }
  },
})

type GetFilterDropdownPropsArgs = {
  icon?: 'default' | 'search'
  input: JSX.Element
  defaultFilteredValue?: FilterValue | null
  filterDropdownProps?: Partial<FilterDropdownProps>
}

export const getFilterDropdownProps = ({
  icon,
  input,
  defaultFilteredValue,
  filterDropdownProps,
}: GetFilterDropdownPropsArgs) => {
  let filterIcon: JSX.Element | ((filtered: boolean) => JSX.Element) | undefined
  if (icon === 'search') {
    filterIcon = (filtered) => <AiOutlineSearch style={{ color: filtered ? '#1677ff' : undefined }} />
  }
  return {
    filterIcon,
    filterDropdown: (props: FilterDropdownProps) => (
      <FilterDropdown {...props} {...(filterDropdownProps ?? null)}>
        {input}
      </FilterDropdown>
    ),
    defaultFilteredValue,
  } as const
}

export const filterSearchIcon = (filtered: boolean) => (
  <AiOutlineSearch className={filtered ? 'text-[#1677ff]' : undefined} />
)
