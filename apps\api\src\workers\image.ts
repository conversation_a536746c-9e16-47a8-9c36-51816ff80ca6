import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq'
import { Job } from 'bullmq'

export type ImageCleanupJobData = {
  imageId: string
  publicId: string
}

@Processor('image')
export class ImageProcessor extends WorkerHost {
  async process(job: Job<ImageCleanupJobData>) {}

  // TODO: delete temporary files
  @OnWorkerEvent('completed')
  onCompleted() {
    // do some stuff
  }
}
