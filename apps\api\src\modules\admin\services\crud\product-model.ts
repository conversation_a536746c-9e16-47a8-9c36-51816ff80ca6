import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { ProductModelEntity } from '~/entities'

export class CrudProductModelService extends TypeOrmCrudService<ProductModelEntity> {
  constructor(@InjectRepository(ProductModelEntity) repo: Repository<ProductModelEntity>) {
    super(repo)
  }

  // TODO: handle updateOne to remove deleted attributes
}
