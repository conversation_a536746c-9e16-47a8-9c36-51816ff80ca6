export enum ErrorType {
  MEILISEARCH__TRANSFORM = 'MEILISEARCH__TRANSFORM',
  SALE_ITEM_PAYMENT__TRANSFER = 'SALE_ITEM_PAYMENT__TRANSFER',
  STRIPE_WEBHOOK__SIGNATURE_VERIFICATION = 'STRIPE_WEBHOOK__SIGNATURE_VERIFICATION',
  STRIPE_WEBHOOK__META_DATA = 'STRIPE_WEBHOOK__META_DATA',
  ORDER__CALCULATE_PRICE = 'ORDER__CALCULATE_PRICE',
  ORDER__CREATE = 'ORDER__CREATE',
  SENDCLOUD__CREATE_PARCEL = 'SENDCLOUD__CREATE_PARCEL',
  SENDCLOUD__DEFAULT_ADDRESS = 'SENDCLOUD__DEFAULT_ADDRESS',
  SENDMAIL__SEND = 'SENDMAIL__SEND',
  PRE_ORDER__CREATE = 'PRE_ORDER__CREATE',
  SALE__CREATE = 'SALE__CREATE',
  SALE__FRONTEND_CONFIRM = 'SALE__FRONTEND_CONFIRM',
  SALE_ITEM_PAYMENT__CREATE = 'SALE_ITEM_PAYMENT__CREATE',
  SALE_ITEM_PAYMENT__PAYOUT = 'SALE_ITEM_PAYMENT__PAYOUT',
  MEMBERSHIP_REWARD__INCORRECT_ITEMS_COUNT = 'MEMBERSHIP_REWARD__INCORRECT_ITEMS_COUNT',
  MEMBERSHIP_REWARD__SCHEME_NOT_FOUND = 'MEMBERSHIP_REWARD__SCHEME_NOT_FOUND',
}

export enum ErrorSeverityType {
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
}
