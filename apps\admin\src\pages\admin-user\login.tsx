import { useGoogleLogin } from '@react-oauth/google'
import { useApiUrl, useLogin } from '@refinedev/core'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { <PERSON>ton, Card, Layout } from 'antd'
import { FC } from 'react'
import { FcGoogle } from 'react-icons/fc'

import Logo from '~/assets/images/logo-icon.svg?react'
import type { AdminUserFrontendType } from '~/interfaces'

export const AdminUserLogin: FC = () => {
  const apiUrl = useApiUrl()
  const { mutate: login } = useLogin()
  const loginError = () => login({})

  const googleLogin = useGoogleLogin({
    onSuccess: async ({ access_token: accessToken }) => {
      try {
        const { data } = await axios.post<AdminUserFrontendType>(
          'admin/auth/google',
          { accessToken },
          { baseURL: apiUrl }
        )
        if (!data?.id || !data?.email || !data?.token) {
          loginError()
        }
        const user: AdminUserFrontendType = data
        login(user)
      } catch (error) {
        loginError()
      }
    },
    onError: () => loginError(),
  })

  return (
    <Layout className="flex h-screen w-screen items-center justify-center">
      <Logo className="mb-3 h-20 rounded-md bg-[#1677FF]" />
      <Card className="mx-auto mt-5 w-1/4 py-10">
        <h3 className="mb-1 whitespace-pre-wrap break-words text-center  text-2xl leading-8 text-gray-600">
          Log in to your account
        </h3>
        <p className="text-md mb-8 text-center text-gray-500">Please use your Google account to login</p>
        <Button
          type="primary"
          className="mx-auto flex items-center justify-center"
          onClick={() => googleLogin()}
          icon={<FcGoogle size={18} className="rounded-sm bg-white" />}
        >
          Login with Google
        </Button>
      </Card>
    </Layout>
  )
}
