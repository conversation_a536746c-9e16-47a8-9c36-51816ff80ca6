import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { CustomerReviewEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudCustomerReviewService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: CustomerReviewEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
})
@Controller('admin/customer-review')
export class CrudCustomerReviewController implements CrudController<CustomerReviewEntity> {
  constructor(public service: CrudCustomerReviewService) {}
}
