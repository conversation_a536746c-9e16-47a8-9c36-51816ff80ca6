# Database
DATABASE_URL="postgresql://user:password@localhost:5432/shipment_db"

# Environment
NODE_ENV="development"

# AWS Lambda (for deployment)
AWS_REGION="eu-central-1"
AWS_LAMBDA_FUNCTION_NAME="shipment-webhook"

# Optional: SendCloud webhook authentication (if needed)
SENDCLOUD_WEBHOOK_SECRET="your_webhook_secret"

# Optional: Sentry error tracking (if needed)
SENTRY_DSN=""

# Server (for local development)
PORT=3000

# JWT Configuration
JWT_SECRET_KEY="your-secret-key-here"

# Refine Server Configuration
REFINE_SERVER_URL="https://your-refine-server.com" 