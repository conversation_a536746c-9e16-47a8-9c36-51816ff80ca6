{
  "tailwindCSS.classAttributes": ["class", "className"],
  "tailwindCSS.experimental.classRegex": [
    "cva\\(([^)]*)\\)",
    "clsx\\(([^)]*)\\)",
    "cn\\(([^)]*)\\)",
    "cx\\(([^)]*)\\)",
  ],
  "eslint.workingDirectories": [
    {
      "pattern": "apps/*/"
    },
    {
      "pattern": "packages/*/"
    }
  ],
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "always",
    "source.fixAll.stylelint": "always",
    "source.addMissingImports": "always",
  },
  "cSpell.words": ["cloudinary", "deepl", "Strapi", "typeorm", "Valyuu"],
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.next": true,
    "**/.turbo": true
  }
}
