import { Injectable } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import type { EntitySubscriberInterface, RemoveEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { FileEntity } from '~/entities'
import { s3DeleteFile } from '~/utils'

@Injectable()
@EventSubscriber()
export class FileSubscriber implements EntitySubscriberInterface<FileEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return FileEntity
  }

  async afterRemove(event: RemoveEvent<FileEntity>) {
    const { databaseEntity } = event

    if (databaseEntity?.path) {
      try {
        await s3DeleteFile(event.databaseEntity.path)
      } catch {
        Sentry.captureException(new Error('Failed to delete file from S3'), {
          tags: {
            type: 'file:delete-from-s3',
          },
          extra: {
            path: event.databaseEntity.path,
          },
          level: 'warning',
        })
      }
    }
  }
}
