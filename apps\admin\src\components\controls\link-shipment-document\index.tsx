import { useApiUrl, useNotification } from '@refinedev/core'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Image, Popover } from 'antd'
import { useState } from 'react'
import { AiOutlineDownload, AiOutlineEye } from 'react-icons/ai'

type LinkShipmentDocumentProps = {
  shipmentId: string
  trackingNumber: string
}

export const LinkShipmentDocument = ({ shipmentId, trackingNumber }: LinkShipmentDocumentProps) => {
  const [previewImage, setPreviewImage] = useState('')
  const apiUrl = useApiUrl()
  const { open } = useNotification()

  const handlePreview = async () => {
    try {
      const { data: base64String } = await axios.get(`${apiUrl}/admin/shipments/${shipmentId}/preview-document`)
      setPreviewImage(base64String)
    } catch (error) {
      open?.({
        type: 'error',
        message: 'Error',
        description: 'Failed to load document preview',
      })
      console.error('Failed to load preview:', error)
    }
  }

  const handleDownload = async () => {
    try {
      const response = await axios.get(`${apiUrl}/admin/shipments/${shipmentId}/download-document`, {
        responseType: 'blob',
      })

      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `shipment-${trackingNumber}-label.pdf`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
    } catch {
      open?.({
        type: 'error',
        message: 'Error',
        description: 'Failed to download document',
      })
    }
  }

  return (
    <div className="flex gap-4">
      <Popover
        content={
          previewImage ? (
            <Image src={previewImage} alt="Shipment Document" width={200} />
          ) : (
            <span>Loading preview...</span>
          )
        }
        trigger="hover"
        onOpenChange={(visible) => {
          if (visible && !previewImage) {
            handlePreview()
          }
        }}
      >
        <a className="flex items-center gap-1 text-blue-500 hover:text-blue-700">
          <AiOutlineEye /> Preview
        </a>
      </Popover>
      <a onClick={handleDownload} className="flex cursor-pointer items-center gap-1 text-blue-500 hover:text-blue-700">
        <AiOutlineDownload /> Download
      </a>
    </div>
  )
}
