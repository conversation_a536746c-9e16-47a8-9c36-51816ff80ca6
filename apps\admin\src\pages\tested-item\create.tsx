import { Create, useForm, useSelect } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { ITestedItem, ITestedItemList } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber, Watch } from 'antx'
import cx from 'clsx'
import { FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import {
  InputLanguageTab,
  InputMultiEntitySelect,
  InputMultiLang,
  InputPublish,
  LanguageTabChoices,
} from '~/components'

export const TestedItemCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps } = useForm<ITestedItem, HttpError, ITestedItem>()
  const id = useMemo(() => uuid(), [])

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const { selectProps: listSelectProps } = useSelect<ITestedItemList>({
    resource: 'admin/tested-item-lists',
    optionLabel: 'internalName',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Watch list={['name__en', 'name__nl', 'name__de']}>
          {([name__en, name__nl, name__de]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
            </>
          )}
        </Watch>
        <InputMultiEntitySelect label="List" name="lists" {...(listSelectProps as any)} />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
      </Form>
    </Create>
  )
}
