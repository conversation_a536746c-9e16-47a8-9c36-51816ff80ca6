import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { type IQuestionType, QuestionTypeEntity } from '~/entities'

@ObjectType('QuestionTypeProblem')
@Entity('question_type_problem')
export class QuestionTypeProblemEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryColumn()
  id: string

  @Field()
  @Column()
  key: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__en?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__nl?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__de?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  desc__pl?: string

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @ManyToOne(() => QuestionTypeEntity, (questionType) => questionType.conditions, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  questionType: Relation<QuestionTypeEntity>

  @Column()
  @RelationId((option: QuestionTypeProblemEntity) => option.questionType)
  questionTypeId: string

  @CreateDateColumn()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IQuestionTypeProblem = Omit<QuestionTypeProblemEntity, keyof BaseEntity> & {
  questionType: IQuestionType
}
