import { CrudRequest } from '@dataui/crud'
import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { InjectRepository } from '@nestjs/typeorm'
import { plainToClass } from 'class-transformer'
import * as pMap from 'p-map'
import { DeepPartial, Repository } from 'typeorm'

import { ProductSkuEntity } from '~/entities'

export class CrudProductSkuService extends TypeOrmCrudService<ProductSkuEntity> {
  constructor(@InjectRepository(ProductSkuEntity) repo: Repository<ProductSkuEntity>) {
    super(repo)
  }

  // for removing deleted images, testReport etc.
  public async updateOne(req: CrudRequest, dto: DeepPartial<ProductSkuEntity>): Promise<ProductSkuEntity> {
    const { allowParamsOverride, returnShallow } = req.options.routes.updateOneBase
    const paramsFilters = this.getParamFilters(req.parsed)
    // disable cache while updating
    req.options.query.cache = false
    let updated: ProductSkuEntity

    // for publish/unpublish from refine
    const isPublishUnpublish = Object.hasOwnProperty.call(dto, 'publishedAt') && Object.keys(dto).length === 1
    await ProductSkuEntity.getRepository().manager.transaction(async (manager) => {
      if (!isPublishUnpublish) {
        const existingProductSku = await manager.findOneOrFail(ProductSkuEntity, {
          where: paramsFilters,
          relations: {
            images: true,
            testReport: true,
          },
        })
        const oldImageIds: string[] = existingProductSku.images.flatMap((image) => (image.id ? [image.id] : []))
        const newImageIds: string[] = dto.images?.flatMap((image) => (image.id ? [image.id] : [])) ?? []

        const oldImageIdsToDelete = oldImageIds.filter((id) => !newImageIds.includes(id))
        if (oldImageIdsToDelete.length) {
          await pMap(
            oldImageIdsToDelete,
            async (id: string) => {
              const image = existingProductSku.images.find((image) => image.id === id)
              await manager.remove(image)
            },
            { concurrency: 5 }
          )
        }

        // if (existingProductSku.testReport?.id && dto.testReport?.id !== existingProductSku.testReport?.id) {
        //   if (existingProductSku.testReport?.id) {
        //     const testReport = existingProductSku.testReport
        //     existingProductSku.testReport = null
        //     existingProductSku.testReportId = null
        //     await manager.save(existingProductSku)
        //     await manager.remove(testReport)
        //   }
        // }
      }

      updated = await manager.save(
        ProductSkuEntity,
        plainToClass(
          this.entityType,
          { ...dto, ...(allowParamsOverride ? {} : paramsFilters), ...req.parsed.authPersist },
          req.parsed.classTransformOptions
        ) as unknown as DeepPartial<ProductSkuEntity>
      )
    })

    if (returnShallow) {
      return updated
    } else {
      req.parsed.paramsFilter.forEach((filter) => {
        filter.value = updated[filter.field as keyof ProductSkuEntity]
      })

      return this.getOneOrFail(req)
    }
  }
}
