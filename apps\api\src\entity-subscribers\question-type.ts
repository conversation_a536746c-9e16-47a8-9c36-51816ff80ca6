import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { QuestionTypeEntity } from '~/entities'
import { entityFixPublishedAt } from '~/utils/entity-subscriber'

@Injectable()
@EventSubscriber()
export class QuestionTypeSubscriber implements EntitySubscriberInterface<QuestionTypeEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return QuestionTypeEntity
  }

  async beforeInsert(event: InsertEvent<QuestionTypeEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async beforeUpdate(event: UpdateEvent<QuestionTypeEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }
}
