import { Create, useForm } from '@refinedev/antd'
import type { HttpError, IResourceComponentsProps } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type { IProductSkuOriginalAccessory } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber, RadioGroup, TextArea, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputImage, InputLanguageTab, InputMultiLang, InputPublish, LanguageTabChoices } from '~/components'

export const ProductSkuOriginalAccessoryCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<
    IProductSkuOriginalAccessory,
    HttpError,
    IProductSkuOriginalAccessory
  >()
  const id = useMemo(() => uuid(), [])
  const publishedAt = Form.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Input label="Key" name="key" rules={['required']} />
        <Watch list={['name__en', 'name__nl', 'name__de']}>
          {([name__en, name__nl, name__de]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <TextArea label="Description (English)" name="desc__en" className={clsHideEn} />
              <TextArea label="Description (Dutch)" name="desc__nl" className={clsHideNl} />
              <TextArea label="Description (German)" name="desc__de" className={clsHideDe} />
              <Input label="Highlight (English)" name="highlight__en" className={clsHideEn} />
              <Input label="Highlight (Dutch)" name="highlight__nl" className={clsHideNl} />
              <Input label="Highlight (German)" name="highlight__de" className={clsHideDe} />
              <RadioGroup
                label="Display in results"
                name="displayInResults"
                options={[
                  { label: 'Yes', value: true },
                  { label: 'No', value: false },
                ]}
                rules={['required']}
              />
              <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
              <InputImage
                label="Image"
                name="thumbnail"
                limit={1}
                rules={[{ required: true, message: 'Please upload an image' }]}
                className="col-span-2"
                plugins={['ImageEditor']}
                entityId={id! as string}
                entityType={ImageUsedInType.PRODUCT_SKU_ORIGINAL_ACCESSORY}
                processName={ImageProcessNameType.COPY}
                defaultNames={{ name__en, name__nl, name__de }}
              />
            </>
          )}
        </Watch>
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
