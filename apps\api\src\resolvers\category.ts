import { Info, Query, Resolver } from '@nestjs/graphql'
import { GraphQLResolveInfo } from 'graphql'
import { GraphRelationBuilder } from 'typeorm-relations-graphql'

import { GetCategoriesOutput } from '~/dtos'
import { CategoryEntity } from '~/entities'
import { CategoryService } from '~/services'

@Resolver()
export class CategoryResolver {
  constructor(
    private readonly relationBuilder: GraphRelationBuilder,
    private readonly service: CategoryService
  ) {}

  @Query(() => [GetCategoriesOutput])
  async getCategories(@Info() info: GraphQLResolveInfo) {
    const relations = this.relationBuilder.buildForQuery(CategoryEntity, info).toFindOptionsRelations()

    return this.service.findAll(relations)
  }
}
