import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { OFFER_EXPIRATION_DAYS } from '~/constants'
import { ISaleItem, SaleItemEntity } from '~/entities'

@Entity('sale_item_offer')
export class SaleItemOfferEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ default: () => "encode(gen_random_bytes(32), 'hex')" })
  authToken: string

  @ManyToOne(() => SaleItemEntity, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  saleItem: Relation<SaleItemEntity>

  @Column()
  @RelationId((offer: SaleItemOfferEntity) => offer.saleItem)
  saleItemId: string

  @Column({ type: 'smallint' })
  version: number

  @Column({ type: 'timestamp' })
  expiresAt?: Date

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ISaleItemOffer = Omit<SaleItemEntity, keyof BaseEntity> & {
  saleItem: ISaleItem
}
