import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { LocaleCountryEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudLocaleCountryService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: LocaleCountryEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
  query: {
    join: {
      channels: {},
      languages: {},
    },
  },
})
@Controller('admin/countries')
export class CrudLocaleCountryController implements CrudController<LocaleCountryEntity> {
  constructor(public service: CrudLocaleCountryService) {}
}
