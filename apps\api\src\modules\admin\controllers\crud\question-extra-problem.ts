import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { QuestionExtraProblemEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudQuestionExtraProblemService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: QuestionExtraProblemEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      questionType: {},
    },
  },
})
@Controller('admin/question-extra-problems')
export class CrudQuestionExtraProblemController implements CrudController<QuestionExtraProblemEntity> {
  constructor(public service: CrudQuestionExtraProblemService) {}
}
