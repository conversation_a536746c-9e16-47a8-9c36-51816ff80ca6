import {
  BaseEntity,
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  Relation,
  RelationId,
} from 'typeorm'

import { ChangeHistoryChangedByType, SaleItemOfferStatus, SaleItemPlanType, SaleItemStatus } from '~/constants'
import { AdminUserEntity, PartnerEntity, SaleItemEntity } from '~/entities'
import { SaleItemAnswersType } from '~/interfaces'

@Entity('sale_item_history')
export class SaleItemHistoryEntity extends BaseEntity {
  @PrimaryColumn({ type: 'smallint' })
  version: number

  @ManyToOne(() => SaleItemEntity, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  saleItem: Relation<SaleItemEntity>

  @PrimaryColumn()
  @RelationId((history: SaleItemHistoryEntity) => history.saleItem)
  saleItemId: string

  @Column({
    type: 'enum',
    enum: ChangeHistoryChangedByType,
    nullable: false,
  })
  changedByType: ChangeHistoryChangedByType

  @Column('uuid', { nullable: true })
  changedById?: string

  @ManyToOne(() => AdminUserEntity, { createForeignKeyConstraints: false, nullable: true })
  @JoinColumn({
    name: 'changed_by_id',
    referencedColumnName: 'id',
  })
  changedByAdminUser?: Relation<AdminUserEntity>

  @ManyToOne(() => PartnerEntity, { createForeignKeyConstraints: false, nullable: true })
  @JoinColumn({
    name: 'changed_by_id',
    referencedColumnName: 'id',
  })
  changedByPartner?: Relation<PartnerEntity>

  get changedBy(): Relation<AdminUserEntity | PartnerEntity> | undefined {
    return this.changedByType === ChangeHistoryChangedByType.ADMIN ? this.changedByAdminUser : this.changedByPartner
  }

  // SaleItemEntity fields
  @Column({ type: 'jsonb', nullable: true })
  answers: SaleItemAnswersType

  @Column({ type: 'text', nullable: true })
  customerNote?: string

  @Column({ type: 'varchar' })
  type: SaleItemPlanType

  @Column({ type: 'varchar' })
  status: SaleItemStatus

  @Column({ type: 'varchar' })
  offerStatus: SaleItemOfferStatus

  @Column({ type: 'timestamp', nullable: true })
  offerAcceptedAt: Date

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
  })
  price: number

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    nullable: true,
  })
  initialPrice: number

  @Column({ nullable: true })
  returnAddressId?: string

  @Column({ nullable: true })
  returnShipmentId?: string

  @Column({ type: 'boolean' })
  isTested: boolean

  @Column()
  productVariantId: string

  @Column()
  productModelId: string

  @Column({ nullable: true })
  offerId?: string

  @Column({ type: 'timestamp', nullable: true })
  receivedAt?: Date

  @CreateDateColumn()
  @Index()
  createdAt: Date
}

export type ISaleItemHistory = Omit<SaleItemHistoryEntity, keyof BaseEntity>
