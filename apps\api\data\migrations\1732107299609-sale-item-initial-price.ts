import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleItemInitialPrice1732107299609 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if column exists before adding it
    const hasColumn = await queryRunner.hasColumn('sale_item', 'initial_price')

    if (!hasColumn) {
      await queryRunner.query(`
        ALTER TABLE sale_item 
        ADD COLUMN initial_price numeric(10,2) NULL
      `)

      // Copy price values to initial_price
      await queryRunner.query(`
        UPDATE sale_item 
        SET initial_price = price
      `)
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
