import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { OrganizationBankAccountType } from '@valyuu/api/constants'
import type { IOrganizationBankAccount } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, Select } from 'antx'
import type { FC } from 'react'

import { LabelWithDetails } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const OrganizationBankAccountEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, query, form, id, saveButtonProps, formLoading } = useForm<
    IOrganizationBankAccount,
    HttpError,
    IOrganizationBankAccount
  >({
    meta: {
      join: [
        {
          field: 'partner',
          select: ['id', 'name'],
        },
        {
          field: 'charity',
          select: ['id', 'name__en'],
        },
      ],
    },
  })

  const record = query?.data?.data

  const type = Form.useWatch('type', form)

  const { editUrl } = useNavigation()

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Select
          label="Type"
          name="type"
          rules={['required']}
          options={formatSelectOptionsFromEnum(OrganizationBankAccountType)}
          disabled
        />
        <Input label="Holder name" name="holderName" rules={['required']} />
        <Input label="Account number" name="accountNumber" rules={['required']} />
        {type === OrganizationBankAccountType.CHARITY && (
          <>
            <Input noStyle type="hidden" name="partnerId" value="" />
            <Select
              label="Charity"
              name={['charity', 'name__en']}
              options={[{ label: record?.charity?.name__en, value: record?.charity?.id }]}
              disabled
            />
          </>
        )}
        {type === OrganizationBankAccountType.PARTNER && (
          <>
            <Input noStyle type="hidden" name="charityId" value="" />
            <Select
              label={
                <LabelWithDetails
                  link={record?.partner?.id ? editUrl('admin/partners', record.partner.id) : undefined}
                  label={'Partner'}
                />
              }
              name={['partner', 'name']}
              options={[{ label: record?.partner?.name, value: record?.partner?.id }]}
              disabled
            />
          </>
        )}
      </Form>
    </Edit>
  )
}
