import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { FaqEntity } from '~/entities'
import { entityFixPublishedAt } from '~/utils/entity-subscriber'

@Injectable()
@EventSubscriber()
export class FaqSubscriber implements EntitySubscriberInterface<FaqEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return FaqEntity
  }

  async beforeInsert(event: InsertEvent<FaqEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async beforeUpdate(event: UpdateEvent<FaqEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }
}
