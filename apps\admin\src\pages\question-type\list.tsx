import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Button,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { BaseRecord, getDefaultFilter, IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { IQuestionType } from '@valyuu/api/entities'
import { Input, Space, Table } from 'antd'
import { FC } from 'react'

import { PublishStatus } from '~/components'
import { handleTableRowClick } from '~/utils'

export const QuestionTypeList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<IQuestionType>({
    syncWithLocation: true,
    meta: {
      fields: ['internalName', 'updatedAt', 'publishedAt'],
    },
    sorters: {
      initial: [
        {
          field: 'updatedAt',
          order: 'desc',
        },
      ],
    },
    filters: {
      initial: [
        {
          field: 'internalName',
          operator: 'contains',
          value: '',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/question-types', id!)))}
      >
        <Table.Column
          dataIndex="internalName"
          title="Internal name"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('internalName', filters, 'contains')}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Name" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="updatedAt"
          title="Updated at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('updatedAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column
          title="Actions"
          dataIndex="actions"
          render={(_, record: BaseRecord) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
