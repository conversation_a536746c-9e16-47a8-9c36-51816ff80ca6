import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { MarketingSkuCollectionEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudMarketingSkuCollectionService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: MarketingSkuCollectionEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      banners: {},
      tags: {},
      productSkus: {},
      headerBgImageDesktop__en: {},
      headerBgImageMobile__en: {},
      headerBgImageDesktop__nl: {},
      headerBgImageMobile__nl: {},
      headerBgImageDesktop__de: {},
      headerBgImageMobile__de: {},
    },
  },
})
@Controller('admin/marketing-sku-collections')
export class CrudMarketingSkuCollectionController implements CrudController<MarketingSkuCollectionEntity> {
  constructor(public service: CrudMarketingSkuCollectionService) {}
}
