import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, UseGuards } from '@nestjs/common'

import { LicensePlateDeviceEntity, LicensePlateStatusEntity, LicensePlateUnmatchedEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudLicensePlateService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: LicensePlateDeviceEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      product: {},
      purchase: {},
      sale: {},
      status: {},
    },
  },
})
@Controller('admin/licenseplate')
export class CrudLicensePlateController implements CrudController<LicensePlateDeviceEntity> {
  constructor(public service: CrudLicensePlateService) {}

  @Get('guid/:guid')
  async getByGuid(@Param('guid') guid: string) {
    return this.service.getByGuid(guid)
  }

  @Get('imei/:imei')
  async getByImei(@Param('imei') imei: string) {
    return this.service.getByImei(imei)
  }

  @Get('serial/:serial')
  async getBySerial(@Param('serial') serial: string) {
    return this.service.getBySerial(serial)
  }

  @Post('')
  async create(@Body() body: LicensePlateDeviceEntity) {
    return this.service.create(body)
  }

  @Put('')
  async update(@Body() body: LicensePlateDeviceEntity) {
    return this.service.update(body)
  }

  @Post('bulk')
  async createMultiple(@Body() body: LicensePlateDeviceEntity[]) {
    return this.service.createMultiple(body)
  }

  @Post('unmatched')
  async createUnmatched(@Body() body: LicensePlateUnmatchedEntity) {
    return this.service.createUnmatched(body)
  }

  @Put('bulk')
  async updateMultiple(@Body() body: LicensePlateDeviceEntity[]) {
    return this.service.updateMultiple(body)
  }

  @Put('imei/:imei')
  async updateByImei(@Param('imei') imei: string, @Body() body: LicensePlateDeviceEntity) {
    return this.service.updateByImei(body, imei)
  }

  @Put('serial/:serial')
  async updateBySerial(@Param('serial') serial: string, @Body() body: LicensePlateDeviceEntity) {
    return this.service.updateBySerial(body, serial)
  }

  @Delete('guid/:guid')
  async deleteByGuid(@Param('guid') guid: string) {
    return this.service.deleteByGuid(guid)
  }

  @Post('status/guid/:guid')
  async addStatusByGuid(@Param('guid') guid: string, @Body() body: LicensePlateStatusEntity) {
    return this.service.addStatusByGuid(guid, body)
  }

  @Post('status/imei/:imei')
  async addStatusByImei(@Param('imei') imei: string, @Body() body: LicensePlateStatusEntity) {
    return this.service.addStatusByImei(imei, body)
  }

  @Post('status/serial/:serial')
  async addStatusBySerial(@Param('serial') serial: string, @Body() body: LicensePlateStatusEntity) {
    return this.service.addStatusBySerial(serial, body)
  }
}
