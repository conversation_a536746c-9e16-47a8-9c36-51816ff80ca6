import { Show } from '@refinedev/antd'
import {
  type IResourceComponentsProps,
  Link,
  useApiUrl,
  useCustom,
  useCustomMutation,
  useNavigation,
  useNotification,
} from '@refinedev/core'
import { PaymentListProblemItemReason, PaymentListStatus, PaymentListType } from '@valyuu/api/constants'
import { IPaymentListBulkItem, IPaymentListIndividualItem } from '@valyuu/api/entities'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Button, Col, Form, Modal, Popconfirm, Row, Table, Tag, Typography } from 'antd'
import { Input, Select, TextArea } from 'antx'
import { type FC, useMemo, useState } from 'react'
import { AiOutlineClose, AiOutlineDown, AiOutlineLeft, AiOutlineUp, AiOutlineWarning } from 'react-icons/ai'
import { FiDownload } from 'react-icons/fi'
import { useParams } from 'react-router-dom'

import { formatDate, formatMoney, formatSelectOptionsFromEnum } from '~/utils'

const RemoveButton = ({
  record,
  onRemove,
  size = 'middle',
}: {
  record: IPaymentListIndividualItem
  onRemove: (record: IPaymentListIndividualItem | IPaymentListBulkItem) => void
  size?: 'small' | 'middle' | 'large'
}) => {
  return (
    <Button danger size={size} icon={<AiOutlineClose size={14} />} onClick={() => onRemove(record)}>
      Remove
    </Button>
  )
}

export const PaymentListShow: FC<IResourceComponentsProps> = () => {
  const apiUrl = useApiUrl()
  const { id } = useParams()
  const { open } = useNotification()

  // Use custom mutation for removing items from payment list
  const { mutate: removeItemMutate, isLoading: isRemoving } = useCustomMutation()

  // Use specific API endpoints for approve and markAsPaid
  const { mutate: approveMutate, isLoading: isApproving } = useCustomMutation()
  const { mutate: markAsPaidMutate, isLoading: isMarkingAsPaid } = useCustomMutation()

  // State for download loading
  const [isDownloading, setIsDownloading] = useState(false)

  // State for modal
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [selectedItem, setSelectedItem] = useState<IPaymentListIndividualItem | IPaymentListBulkItem | null>(null)

  // Form instance for the removal form
  const [form] = Form.useForm()

  // State for mark as paid modal
  const [isMarkAsPaidModalVisible, setIsMarkAsPaidModalVisible] = useState(false)
  const [markAsPaidForm] = Form.useForm()

  const { data, isLoading, refetch } = useCustom({
    url: `${apiUrl}/admin/payment-lists/${id}/details`,
    method: 'get',
  })

  // Function to refresh data after successful action
  const refreshData = () => {
    refetch()
  }

  const paymentList = data?.data
  const type = paymentList?.type
  const individualItems = paymentList?.individualItems ?? ([] as IPaymentListIndividualItem[])
  const bulkItems = paymentList?.bulkItems ?? ([] as IPaymentListBulkItem[])
  const status = paymentList?.status

  // Check if there are any high-value items (amount > 1000)
  const hasHighValueItems = useMemo(() => {
    const allItems = [...individualItems, ...bulkItems]
    return allItems.some((item) => Number(item.amount) >= 1000)
  }, [individualItems, bulkItems])

  console.log(paymentList)
  const { goBack } = useNavigation()

  // Keep track of expanded rows by their ID
  const [expandedRows, setExpandedRows] = useState<Record<string, { saleNumbers: boolean; stockIds: boolean }>>({})

  const toggleExpand = (rowId: string, field: 'saleNumbers' | 'stockIds') => {
    setExpandedRows((prev) => ({
      ...prev,
      [rowId]: {
        ...prev[rowId],
        [field]: !prev[rowId]?.[field],
      },
    }))
  }

  // Handler for remove button click
  const handleRemoveClick = (record: IPaymentListIndividualItem | IPaymentListBulkItem) => {
    setSelectedItem(record)
    form.resetFields()
    setIsModalVisible(true)
  }

  // Handler for remove confirmation
  const handleRemoveConfirm = () => {
    form.validateFields().then((values) => {
      // Use the proper API endpoint for removing payment list items
      removeItemMutate(
        {
          url: `${apiUrl}/admin/payment-lists/remove-item`,
          method: 'post',
          values: {
            type: type,
            itemId: selectedItem?.id,
            reason: values.reason,
            note: values.note,
          },
        },
        {
          onSuccess: () => {
            setIsModalVisible(false)
            setSelectedItem(null)
            form.resetFields()
            open?.({
              type: 'success',
              message: 'Successfully removed the item',
              description: 'The item has been removed successfully',
            })
            refreshData()
          },
          onError: (error) => {
            open?.({
              type: 'error',
              message: 'Error',
              description: 'Something went wrong when removing the item',
            })
            console.error('Error removing item:', error)
          },
        }
      )
    })
  }

  const handleApprove = () => {
    approveMutate(
      {
        url: `${apiUrl}/admin/payment-lists/${id}/approve`,
        method: 'post',
        values: {},
      },
      {
        onSuccess: () => {
          open?.({
            type: 'success',
            message: 'Successfully approved',
            description: 'The payment list has been approved',
          })
          refreshData()
        },
        onError: (error) => {
          open?.({
            type: 'error',
            message: 'Error',
            description: 'Something went wrong when approving the payment list',
          })
          console.error('Error approving payment list:', error)
        },
      }
    )
  }

  const handleMarkAsPaid = () => {
    markAsPaidForm.validateFields().then((values) => {
      markAsPaidMutate(
        {
          url: `${apiUrl}/admin/payment-lists/${id}/mark-as-paid`,
          method: 'post',
          values: {
            referenceNumber: values.referenceNumber,
          },
        },
        {
          onSuccess: () => {
            setIsMarkAsPaidModalVisible(false)
            markAsPaidForm.resetFields()
            open?.({
              type: 'success',
              message: 'Successfully marked as paid',
              description: 'The payment list has been marked as paid',
            })
            refreshData()
          },
          onError: (error) => {
            open?.({
              type: 'error',
              message: 'Error',
              description: 'Something went wrong when marking the payment list as paid',
            })
            console.error('Error marking payment list as paid:', error)
          },
        }
      )
    })
  }

  const handleDownload = async () => {
    setIsDownloading(true)
    try {
      const { data, headers } = await axios.get(`${apiUrl}/admin/payment-lists/${id}/download-excel`, {
        responseType: 'blob',
      })

      // Parse Content-Disposition header to extract filename
      const contentDisposition = headers['content-disposition']
      let filename = `payment-list-${id}.xlsx`

      if (contentDisposition) {
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
        const matches = filenameRegex.exec(contentDisposition)
        if (matches != null && matches[1]) {
          filename = matches[1].replace(/['"]/g, '')
        }
      }

      // Create a download link and trigger it
      const href = window.URL.createObjectURL(new Blob([data]))
      const link = document.createElement('a')
      link.href = href
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      open?.({
        type: 'success',
        message: 'Download successful',
      })
    } catch (error) {
      console.error('Download failed:', error)
      open?.({
        type: 'error',
        message: 'Download failed',
        description: 'There was an error downloading the payment list',
      })
    } finally {
      setIsDownloading(false)
    }
  }

  // Get the sale number and stock ID for display in modal
  const getSaleNumberAndStockId = (item: IPaymentListIndividualItem | IPaymentListBulkItem) => {
    if (type === PaymentListType.D2C && 'saleItem' in item) {
      return {
        saleNumber: item.saleItem.sale?.saleNumber,
        stockId: item.saleItem?.stockId,
      }
    } else if (type === PaymentListType.BULK && 'saleItems' in item && item.saleItems && item.saleItems.length > 0) {
      return {
        saleNumber: item.saleItems.map((si) => si.sale?.saleNumber).join(', '),
        stockId: item.saleItems.map((si) => si.stockId).join(', '),
      }
    }
    return { saleNumber: '', stockId: '' }
  }

  // Add status color and label mapping like in list.tsx
  const statusColorMap = {
    [PaymentListStatus.APPROVED]: 'processing',
    [PaymentListStatus.PENDING_REVIEW]: 'warning',
    [PaymentListStatus.PAID]: 'success',
  }

  const statusLabels = {
    [PaymentListStatus.APPROVED]: 'Approved',
    [PaymentListStatus.PENDING_REVIEW]: 'Pending Review',
    [PaymentListStatus.PAID]: 'Paid',
  }

  // Update the renderStatusBadge function to use Tag instead of Badge
  const renderStatusBadge = (status?: PaymentListStatus) => {
    if (!status) return null

    const warning = status === PaymentListStatus.PENDING_REVIEW && hasHighValueItems
    return (
      <Tag
        color={warning ? 'error' : statusColorMap[status]}
        className="flex items-center gap-1 px-3 py-1 text-sm leading-6"
        icon={warning ? <AiOutlineWarning className="text-red-500" size={18} /> : undefined}
      >
        {statusLabels[status]}
      </Tag>
    )
  }

  const renderDetailItem = (label: string, value: React.ReactNode) => (
    <div className="mb-4">
      <Typography.Text type="secondary" style={{ display: 'block' }}>
        {label}
      </Typography.Text>
      <Typography.Text strong>{value}</Typography.Text>
    </div>
  )

  // Add this component to render expandable links with row-specific state
  const ExpandableLinks = ({
    rowId,
    field,
    items,
    renderLink,
  }: {
    rowId: string
    field: 'saleNumbers' | 'stockIds'
    items: any[]
    renderLink: (item: any) => React.ReactNode
  }) => {
    if (!items || items.length === 0) return <span>-</span>

    const isExpanded = expandedRows[rowId]?.[field] || false

    return (
      <div>
        <Button
          type="link"
          onClick={() => toggleExpand(rowId, field)}
          icon={isExpanded ? <AiOutlineUp /> : <AiOutlineDown />}
          style={{ padding: 0 }}
        >
          {items.length} {items.length === 1 ? 'item' : 'items'}
        </Button>

        {isExpanded && (
          <div style={{ marginTop: 8 }}>
            {items.map((item, index) => (
              <div key={index} style={{ marginBottom: 4 }}>
                {renderLink(item)}
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  // Function to render amount with warning for high values
  const renderAmount = (amount: number) => {
    const isHighValue = Number(amount) >= 1000

    return (
      <div className={`flex items-center gap-1 ${isHighValue ? 'rounded p-1' : ''}`}>
        {formatMoney(amount)}
        {isHighValue && <AiOutlineWarning className="text-red-500" />}
      </div>
    )
  }

  // Item details to display in the modal
  const itemDetails = selectedItem
    ? [
        { label: 'Beneficiary Name', value: selectedItem.beneficiaryName },
        { label: 'IBAN', value: selectedItem.iban },
        { label: 'Amount', value: formatMoney(selectedItem.amount) },
        { label: 'Due Date', value: formatDate(selectedItem.dueDate) },
        { label: 'Sale Number', value: getSaleNumberAndStockId(selectedItem).saleNumber },
        { label: 'Stock ID', value: getSaleNumberAndStockId(selectedItem).stockId },
      ]
    : []

  // Define the reason options with human readable labels
  const reasonOptions = formatSelectOptionsFromEnum(PaymentListProblemItemReason)

  const paymentTypeMap = {
    [PaymentListType.D2C]: 'Direct to customer',
    [PaymentListType.BULK]: 'Bulk',
  } as const

  // Get the payment list details for display in Mark as Paid modal
  const getPaymentListDetails = () => [
    { label: 'List ID', value: id },
    { label: 'Type', value: type ? paymentTypeMap[type as keyof typeof paymentTypeMap] : '' },
    { label: 'Amount', value: formatMoney(paymentList?.amount) },
    { label: 'Record', value: `${individualItems.length + bulkItems.length} payments` },
    { label: 'Due Date', value: formatDate(paymentList?.dueDate) },
  ]

  return (
    <div>
      <Button icon={<AiOutlineLeft />} onClick={goBack} type="text" style={{ marginBottom: 16, padding: 0 }}>
        <span style={{ marginLeft: 8 }}>View Payment List</span>
      </Button>

      <Show
        isLoading={isLoading}
        headerProps={{
          title: (
            <div>
              <Typography.Text type="secondary">List ID</Typography.Text>
              <Typography.Title level={4} style={{ margin: 0 }}>
                {id}
              </Typography.Title>
            </div>
          ),
          extra: (
            <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
              {renderStatusBadge(paymentList?.status)}
              {status === PaymentListStatus.PENDING_REVIEW && (
                <Popconfirm
                  title="Approve Payment List"
                  description="Are you sure you want to approve this payment list?"
                  onConfirm={handleApprove}
                  okText="Yes"
                  cancelText="No"
                  okButtonProps={{ loading: isApproving }}
                >
                  <Button type="primary">Approve</Button>
                </Popconfirm>
              )}
              {status === PaymentListStatus.APPROVED && (
                <Button icon={<FiDownload />} onClick={handleDownload} loading={isDownloading} disabled={isDownloading}>
                  Download
                </Button>
              )}
              {status === PaymentListStatus.APPROVED && (
                <Button type="primary" onClick={() => setIsMarkAsPaidModalVisible(true)}>
                  Mark as Paid
                </Button>
              )}
            </div>
          ),
        }}
        headerButtons={[]}
      >
        <Row gutter={24} className="mb-6">
          <Col span={6}>
            {renderDetailItem('Type', type ? paymentTypeMap[type as keyof typeof paymentTypeMap] : '')}
          </Col>
          <Col span={6}>{renderDetailItem('Total amount', formatMoney(paymentList?.amount))}</Col>
          <Col span={6}>{renderDetailItem('Record', `${individualItems.length + bulkItems.length} payments`)}</Col>
          <Col span={6}>{renderDetailItem('Due date', formatDate(paymentList?.dueDate))}</Col>
          {paymentList?.referenceNumber && (
            <Col span={6}>{renderDetailItem('Reference number', paymentList?.referenceNumber)}</Col>
          )}
          {paymentList?.paidAt && <Col span={6}>{renderDetailItem('Paid at', formatDate(paymentList?.paidAt))}</Col>}
        </Row>
        {type === PaymentListType.D2C && (
          <Table<IPaymentListIndividualItem>
            dataSource={individualItems}
            pagination={false}
            rowKey="id"
            rowClassName={(record) => (Number(record.amount) >= 1000 ? 'bg-yellow-50' : '')}
          >
            <Table.Column
              dataIndex="id"
              title="ID"
              className="max-w-10"
              render={(value) => <span title={value}>{value}</span>}
              width="5rem"
              ellipsis={true}
            />
            <Table.Column title="Beneficiary Name" dataIndex="beneficiaryName" />
            <Table.Column title="IBAN" dataIndex="iban" />
            <Table.Column title="Amount" dataIndex="amount" render={(value) => renderAmount(value)} />
            <Table.Column title="Due Date" dataIndex="dueDate" render={(value) => formatDate(value)} />
            <Table.Column
              title="Sale Number"
              dataIndex="saleItem"
              render={(value) => (
                <Link to={`/sales/edit/${value.sale.id}`} target="_blank">
                  {value.sale.saleNumber}
                </Link>
              )}
            />
            <Table.Column
              title="Stock ID"
              dataIndex="saleItem"
              render={(value) => (
                <Link to={`/sale-items/edit/${value.id}`} target="_blank">
                  {value.stockId}
                </Link>
              )}
            />
            {paymentList?.status !== PaymentListStatus.PAID && (
              <Table.Column
                title="Action"
                key="action"
                render={(_, record) => (
                  <RemoveButton
                    record={record as IPaymentListIndividualItem}
                    size="small"
                    onRemove={handleRemoveClick}
                  />
                )}
              />
            )}
          </Table>
        )}
        {type === PaymentListType.BULK && (
          <Table<IPaymentListBulkItem>
            dataSource={bulkItems}
            pagination={false}
            rowKey="id"
            rowClassName={(record) => (Number(record.amount) >= 1000 ? 'bg-yellow-50' : '')}
          >
            <Table.Column
              dataIndex="id"
              title="ID"
              className="max-w-10"
              render={(value) => <span title={value}>{value}</span>}
              width="5rem"
              ellipsis={true}
            />
            <Table.Column title="Beneficiary Name" dataIndex="beneficiaryName" />
            <Table.Column title="IBAN" dataIndex="iban" />
            <Table.Column title="Amount" dataIndex="amount" render={(value) => renderAmount(value)} />
            <Table.Column title="Due Date" dataIndex="dueDate" render={(value) => formatDate(value)} />
            <Table.Column
              title="Sale Numbers"
              dataIndex="saleItems"
              render={(saleItems, record) => (
                <ExpandableLinks
                  rowId={record.id}
                  field="saleNumbers"
                  items={saleItems || []}
                  renderLink={(item) => (
                    <Link to={`/sales/edit/${item.sale.id}`} target="_blank">
                      {item.sale.saleNumber}
                    </Link>
                  )}
                />
              )}
            />
            <Table.Column
              title="Stock IDs"
              dataIndex="saleItems"
              render={(saleItems, record) => (
                <ExpandableLinks
                  rowId={record.id}
                  field="stockIds"
                  items={saleItems || []}
                  renderLink={(item) => (
                    <Link to={`/sale-items/edit/${item.id}`} target="_blank">
                      {item.stockId}
                    </Link>
                  )}
                />
              )}
            />
          </Table>
        )}
      </Show>

      {/* Single modal for removal confirmation with Ant Design Form */}
      <Modal
        title="Reason of Removal"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          form.resetFields()
        }}
        onOk={handleRemoveConfirm}
        okText="Save"
        cancelText="Cancel"
        okButtonProps={{
          loading: isRemoving,
        }}
      >
        <Form form={form} layout="vertical" className="!grid-cols-1">
          {/* Item details */}
          <div className="mb-6">
            {itemDetails.map((detail, index) => (
              <div key={index} className="mb-2 flex">
                <Typography.Text type="secondary" style={{ width: 140 }}>
                  {detail.label}
                </Typography.Text>
                <Typography.Text>{detail.value}</Typography.Text>
              </div>
            ))}
          </div>

          {/* Reason selection */}
          <Select
            name="reason"
            label="Select a reason"
            rules={['required']}
            placeholder="Select the most suitable reason"
            style={{ width: '100%' }}
            options={reasonOptions}
            labelCol={{ span: 24 }}
          />

          {/* Note field */}
          <TextArea name="note" label="Note" placeholder="Enter additional notes" rows={4} labelCol={{ span: 24 }} />
        </Form>
      </Modal>

      {/* Mark as Paid modal */}
      <Modal
        title="Mark as Paid"
        open={isMarkAsPaidModalVisible}
        onCancel={() => {
          setIsMarkAsPaidModalVisible(false)
          markAsPaidForm.resetFields()
        }}
        onOk={handleMarkAsPaid}
        okText="Save"
        cancelText="Cancel"
        okButtonProps={{
          loading: isMarkingAsPaid,
          disabled: isMarkingAsPaid,
        }}
        maskClosable={false}
      >
        <div className="mb-6">
          {getPaymentListDetails().map((detail, index) => (
            <div key={index} className="mb-2 flex">
              <Typography.Text type="secondary" style={{ width: 140 }}>
                {detail.label}
              </Typography.Text>
              <Typography.Text>{detail.value}</Typography.Text>
            </div>
          ))}
        </div>

        <Form form={markAsPaidForm} layout="vertical" className="!grid-cols-1">
          <Input
            name="referenceNumber"
            label="Reference Number"
            rules={[
              { required: true, message: 'Reference number is required' },
              {
                pattern: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\s\d{6}$/,
                message: 'Reference number format is invalid',
              },
            ]}
            placeholder="YYYY-MM-DDThh:mm:ss XXXXXX"
            labelCol={{ span: 24 }}
          />
          <Typography.Text type="secondary" className="mt-1">
            Format: YYYY-MM-DDThh:mm:ss XXXXXX
          </Typography.Text>
        </Form>
      </Modal>
    </div>
  )
}
