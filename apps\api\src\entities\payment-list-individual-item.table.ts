import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import {
  type IPartner,
  type IPaymentList,
  type ISaleItem,
  PartnerEntity,
  PaymentListEntity,
  SaleItemEntity,
} from '~/entities'

@Entity('payment_list_individual_item')
export class PaymentListIndividualItemEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column()
  dueDate: Date

  @Column()
  beneficiaryName: string

  @Column()
  iban: string

  @Column()
  isDonation: boolean

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  amount: number

  @ManyToOne(() => PaymentListEntity, (paymentList) => paymentList.individualItems, {
    onDelete: 'CASCADE',
  })
  paymentList: Relation<PaymentListEntity>

  @Column()
  @RelationId((paymentListIndividualItem: PaymentListIndividualItemEntity) => paymentListIndividualItem.paymentList)
  paymentListId: string

  // Used for generating reports
  @ManyToOne(() => PartnerEntity, { nullable: true })
  partner: Relation<PartnerEntity>

  @Column({ nullable: true })
  @RelationId((paymentListIndividualItem: PaymentListIndividualItemEntity) => paymentListIndividualItem.partner)
  partnerId?: string

  @OneToOne(() => SaleItemEntity, (saleItem) => saleItem.paymentListIndividualItem, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  saleItem: Relation<SaleItemEntity>

  @Column()
  @RelationId((paymentListIndividualItem: PaymentListIndividualItemEntity) => paymentListIndividualItem.saleItem)
  saleItemId: string

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IPaymentListIndividualItem = Omit<PaymentListIndividualItemEntity, keyof BaseEntity> & {
  saleItem: ISaleItem
  paymentList: IPaymentList
  partner?: IPartner
}
