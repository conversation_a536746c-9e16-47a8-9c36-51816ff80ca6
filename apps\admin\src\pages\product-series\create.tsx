import { Create, useForm, useSelect } from '@refinedev/antd'
import type { IResourceComponentsProps } from '@refinedev/core'
import type { IBrand, ICategory, IProductSeries, ITestedItemList } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber, Select, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputLanguageTab, InputMultiLang, InputPublish, InputSlug, LanguageTabChoices } from '~/components'

export const ProductSeriesCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps } = useForm<IProductSeries>()
  const { selectProps: brandSelectProps } = useSelect<IBrand>({
    resource: 'admin/brands',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: categorySelectProps } = useSelect<ICategory>({
    resource: 'admin/categories',
    optionLabel: 'name__en',
    optionValue: 'id',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { selectProps: testedItemListSelectProps } = useSelect<ITestedItemList>({
    resource: 'admin/tested-item-lists',
    optionLabel: 'internalName',
    optionValue: 'id',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const id = useMemo(() => uuid(), [])
  const publishedAt = Form.useWatch('publishedAt', form)

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Create
      saveButtonProps={saveButtonProps}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Select {...(brandSelectProps as any)} label="Brand" name="brandId" rules={['required']} />
        <Select {...(categorySelectProps as any)} label="Category" name="categoryId" rules={['required']} />
        <Watch list={['name__en', 'name__nl', 'name__de']}>
          {([name__en, name__nl, name__de]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
                autoFollow
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
                autoFollow
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
                autoFollow
              />
            </>
          )}
        </Watch>
        <Select
          {...(testedItemListSelectProps as any)}
          label="Tested items list"
          name="testComponentListId"
          rules={['required']}
        />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} initialValue={10} />
        <Input noStyle type="datetime" name="publishedAt" hidden initialValue={null} />
      </Form>
    </Create>
  )
}
