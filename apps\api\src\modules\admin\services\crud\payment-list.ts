import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { BadRequestException, NotFoundException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import * as Sentry from '@sentry/nestjs'
import * as currency from 'currency.js'
import { addDays, endOfDay, format, isWeekend, startOfDay } from 'date-fns'
import { Response } from 'express'
import { groupBy } from 'lodash'
import * as pMap from 'p-map'
import { join } from 'path'
import { Between, In, LessThanOrEqual, Repository } from 'typeorm'

import {
  PaymentListProblemItemReason,
  PaymentListStatus,
  PaymentListType,
  SaleItemStatus,
  SalePaymentType,
} from '~/constants'
import {
  BankAccountEntity,
  PaymentListBulkItemEntity,
  PaymentListEntity,
  PaymentListIndividualItemEntity,
  PaymentListProblemItemEntity,
  SaleEntity,
  SaleItemEntity,
} from '~/entities'
import { PaymentListUpcomingItem } from '~/interfaces'
import {
  googleApiDeleteFile,
  googleApiGetSpreadsheetStream,
  googleApiUploadExcelToGoogleSheets,
  googleApiWriteValues,
} from '~/utils/googleapi'

const paymentListExcelTemplate = join(__dirname, '../../../../files/payment-list/ing-payment-template.xlsx')

export class CrudPaymentListService extends TypeOrmCrudService<PaymentListEntity> {
  constructor(@InjectRepository(PaymentListEntity) repo: Repository<PaymentListEntity>) {
    super(repo)
  }

  async upcoming(orderByField?: string, order?: 'ASC' | 'DESC') {
    let date = startOfDay(new Date())
    let i = 0
    const upcomingItems: PaymentListUpcomingItem[] = []
    while (i < 4) {
      date = addDays(date, 1)
      if (isWeekend(date)) {
        continue
      }
      i++
      const endOfDate = endOfDay(date)
      const saleItems = await SaleItemEntity.find({
        where: {
          payoutDueDate: i === 1 ? LessThanOrEqual(endOfDate) : Between(date, endOfDate),
          status: SaleItemStatus.PENDING_PAYMENT,
        },
        relations: { sale: true },
        select: {
          id: true,
          price: true,
          partnerId: true,
          sale: {
            id: true,
            paymentType: true,
          },
        },
      })
      const saleItemsByPaymentType = groupBy(saleItems, 'sale.paymentType')
      if (saleItemsByPaymentType[SalePaymentType.BANK_TRANSFER]?.length) {
        upcomingItems.push({
          date,
          type: PaymentListType.D2C,
          amount: saleItemsByPaymentType[SalePaymentType.BANK_TRANSFER].reduce(
            (acc, item) => currency(acc).add(item.price).value,
            0
          ),
          itemsCount: saleItemsByPaymentType[SalePaymentType.BANK_TRANSFER].length,
        })
      }
      if (saleItemsByPaymentType[SalePaymentType.BULK_SETTLEMENT]?.length) {
        upcomingItems.push({
          date,
          type: PaymentListType.BULK,
          amount: saleItemsByPaymentType[SalePaymentType.BULK_SETTLEMENT].reduce(
            (acc, item) => currency(acc).add(item.price).value,
            0
          ),
          itemsCount: Object.keys(groupBy(saleItemsByPaymentType[SalePaymentType.BULK_SETTLEMENT], 'partnerId')).length,
        })
      }
    }
    if (orderByField === 'date') {
      upcomingItems.sort((a, b) => (order === 'ASC' ? 1 : -1) * (a.date.getTime() - b.date.getTime()))
    } else if (orderByField === 'amount') {
      upcomingItems.sort((a, b) => (order === 'ASC' ? 1 : -1) * (a.amount - b.amount))
    } else if (orderByField === 'itemsCount') {
      upcomingItems.sort((a, b) => (order === 'ASC' ? 1 : -1) * (a.itemsCount - b.itemsCount))
    }
    return upcomingItems
  }

  async details(id: string) {
    const paymentList = await this.repo.findOne({
      where: { id },
      relations: { individualItems: { saleItem: { sale: true } }, bulkItems: { saleItems: { sale: true } } },
      select: {
        id: true,
        type: true,
        dueDate: true,
        amount: true,
        status: true,
        referenceNumber: true,
        individualItems: {
          id: true,
          beneficiaryName: true,
          iban: true,
          amount: true,
          dueDate: true,
          saleItem: {
            id: true,
            sale: {
              id: true,
              saleNumber: true,
            },
            stockId: true,
          },
        },
        bulkItems: {
          id: true,
          beneficiaryName: true,
          iban: true,
          amount: true,
          dueDate: true,
          saleItems: {
            id: true,
            sale: {
              id: true,
              saleNumber: true,
            },
            stockId: true,
          },
        },
      },
    })
    return paymentList
  }

  async removeItem({
    type,
    itemId,
    reason,
    note,
  }: {
    type: PaymentListType
    itemId: string // PaymentListIndividualItem ID for D2C or SaleItem ID for BULK
    reason: PaymentListProblemItemReason
    note?: string
  }) {
    if (type === PaymentListType.D2C) {
      const paymentListItem = await PaymentListIndividualItemEntity.findOne({
        where: { id: itemId },
        relations: { saleItem: true },
      })
      if (!paymentListItem) {
        throw new NotFoundException('Payment list item not found')
      }

      this.repo.manager.transaction(async (manager) => {
        await manager.delete(PaymentListIndividualItemEntity, { id: itemId })
        await manager.save(
          PaymentListProblemItemEntity,
          manager.create(PaymentListProblemItemEntity, {
            paymentListType: type,
            saleItemId: paymentListItem.saleItem.id,
            reason,
            note,
            dueDate: paymentListItem.dueDate,
            beneficiaryName: paymentListItem.beneficiaryName,
            iban: paymentListItem.iban,
            amount: paymentListItem.amount,
            isDonation: paymentListItem.isDonation,
          })
        )
        paymentListItem.saleItem.paymentListIndividualItemId = null
        paymentListItem.saleItem.status = SaleItemStatus.PAYMENT_FAILED
        await manager.save(SaleItemEntity, paymentListItem.saleItem)
        // Update payment list amount and items count
        const paymentList = await manager.findOne(PaymentListEntity, {
          where: { id: paymentListItem.paymentListId },
          relations: {
            individualItems: true,
          },
        })
        if (!paymentList) {
          throw new NotFoundException('Payment list not found')
        }
        paymentList.amount = paymentList.individualItems.reduce((acc, item) => currency(acc).add(item.amount).value, 0)
        paymentList.itemsCount = paymentList.individualItems.length
        paymentList.status = PaymentListStatus.PENDING_REVIEW
        if (paymentList.itemsCount) {
          await manager.save(PaymentListEntity, paymentList)
        } else {
          await manager.delete(PaymentListEntity, { id: paymentList.id })
        }
      })
    } else if (type === PaymentListType.BULK) {
      const saleItem = await SaleItemEntity.findOne({
        where: { id: itemId },
      })
      if (!saleItem) {
        throw new NotFoundException('Sale item not found')
      }
      if (!saleItem.paymentListBulkItemId) {
        throw new NotFoundException('Payment list bulk item not found')
      }

      this.repo.manager.transaction(async (manager) => {
        saleItem.paymentListBulkItemId = null
        saleItem.status = SaleItemStatus.PAYMENT_FAILED
        await manager.save(SaleItemEntity, saleItem)
        const paymentListItem = await manager.findOne(PaymentListBulkItemEntity, {
          where: { id: saleItem.paymentListBulkItemId },
          relations: { saleItems: true },
        })
        // Update payment list item amount and items count
        paymentListItem.amount = paymentListItem.saleItems.reduce((acc, item) => currency(acc).add(item.price).value, 0)
        paymentListItem.itemsCount = paymentListItem.saleItems.length
        if (paymentListItem.itemsCount) {
          await manager.save(PaymentListBulkItemEntity, paymentListItem)
        } else {
          await manager.delete(PaymentListBulkItemEntity, { id: paymentListItem.id })
        }

        const paymentList = await manager.findOne(PaymentListEntity, {
          where: { id: paymentListItem.paymentListId },
          relations: {
            bulkItems: true,
          },
        })
        paymentList.amount = paymentList.bulkItems.reduce((acc, item) => currency(acc).add(item.amount).value, 0)
        paymentList.itemsCount = paymentList.bulkItems.length
        paymentList.status = PaymentListStatus.PENDING_REVIEW
        if (paymentList.itemsCount) {
          await manager.save(PaymentListEntity, paymentList)
        } else {
          await manager.delete(PaymentListEntity, { id: paymentList.id })
        }
      })
    }
  }

  async resolveItem(id: string) {
    const problemItem = await PaymentListProblemItemEntity.findOne({
      where: { id },
      relations: { saleItem: true },
    })
    if (!problemItem) {
      throw new NotFoundException('Payment list problem item not found')
    }
    this.repo.manager.transaction(async (manager) => {
      problemItem.isResolved = true
      await manager.save(PaymentListProblemItemEntity, problemItem)
      problemItem.saleItem.status = SaleItemStatus.PENDING_PAYMENT
      await manager.save(SaleItemEntity, problemItem.saleItem)
    })
  }

  async updateItemAmount(id: string) {
    const problemItem = await PaymentListProblemItemEntity.findOne({
      where: { id },
      relations: { saleItem: true },
    })
    if (!problemItem) {
      throw new NotFoundException('Payment list problem item not found')
    }
    problemItem.amount = problemItem.saleItem.price
    await problemItem.save()
  }

  async updateBankInfo({ id, beneficiaryName, iban }: { id: string; beneficiaryName: string; iban: string }) {
    const paymentProblemItem = await PaymentListProblemItemEntity.findOne({
      where: { id },
      relations: { saleItem: { sale: true } },
      select: {
        id: true,
        paymentListType: true,
        beneficiaryName: true,
        iban: true,
        saleItem: {
          id: true,
          sale: {
            id: true,
            bankAccountId: true,
            userId: true,
          },
        },
      },
    })
    if (!paymentProblemItem) {
      throw new NotFoundException('Payment list item not found')
    }
    if (paymentProblemItem.paymentListType === PaymentListType.BULK) {
      throw new BadRequestException("Bulk payment list items's bank info cannot be updated")
    }
    if (!paymentProblemItem.saleItem.sale) {
      throw new BadRequestException('Sale item has no bank account')
    }

    await this.repo.manager.transaction(async (manager) => {
      paymentProblemItem.beneficiaryName = beneficiaryName
      paymentProblemItem.iban = iban
      await manager.save(PaymentListProblemItemEntity, paymentProblemItem)
      let bankAccount = await manager.findOne(BankAccountEntity, {
        where: { id: paymentProblemItem.saleItem.sale.bankAccountId },
      })
      if (!bankAccount) {
        bankAccount = manager.create(BankAccountEntity, {
          holderName: beneficiaryName,
          accountNumber: iban,
          userId: paymentProblemItem.saleItem.sale.userId,
        })
      }
      bankAccount.holderName = beneficiaryName
      bankAccount.accountNumber = iban
      await manager.save(BankAccountEntity, bankAccount)
      paymentProblemItem.saleItem.sale.bankAccountId = bankAccount.id
      await manager.save(SaleEntity, paymentProblemItem.saleItem.sale)
      // Update other individual payment listitem's bank account with the same sale
      const otherPaymentListItems = await manager.find(PaymentListIndividualItemEntity, {
        where: {
          saleItem: { sale: { id: paymentProblemItem.saleItem.sale.id } },
          paymentList: {
            status: In([PaymentListStatus.APPROVED, PaymentListStatus.PENDING_REVIEW]),
          },
        },
        relations: { saleItem: { sale: true }, paymentList: true },
      })
      for (const item of otherPaymentListItems) {
        if (!item.paymentList.isUpdated) {
          item.paymentList.isUpdated = true
          item.paymentList.status = PaymentListStatus.PENDING_REVIEW
          await manager.save(PaymentListEntity, item.paymentList)
        }
        item.beneficiaryName = beneficiaryName
        item.iban = iban
        await manager.save(PaymentListIndividualItemEntity, item)
      }
    })
  }

  async checkIsUpdated(id: string) {
    const paymentList = await this.repo.findOne({
      where: { id },
    })
    return paymentList.isUpdated
  }

  async downloadExcel(id: string, res: Response) {
    const paymentList = await this.repo.findOne({
      where: { id },
    })

    if (paymentList.isUpdated) {
      throw new BadRequestException('Payment list is updated')
    }

    const values: [string, string, number, string, string][] = []
    if (!paymentList) {
      throw new NotFoundException('Payment list not found')
    }
    if (paymentList.type === PaymentListType.D2C) {
      const individualItems = await PaymentListIndividualItemEntity.find({
        where: { paymentListId: paymentList.id },
        relations: { saleItem: { sale: true } },
        select: {
          beneficiaryName: true,
          iban: true,
          amount: true,
          saleItem: { sale: { saleNumber: true }, stockId: true },
        },
      })
      individualItems.forEach((item) => {
        values.push([
          item.beneficiaryName,
          item.iban,
          item.amount,
          item.saleItem.sale.saleNumber,
          item.saleItem.stockId,
        ])
      })
    } else if (paymentList.type === PaymentListType.BULK) {
      const bulkItems = await PaymentListBulkItemEntity.find({
        where: { paymentListId: paymentList.id },
        relations: { partner: true },
        select: {
          id: true,
          beneficiaryName: true,
          iban: true,
          amount: true,
          partner: { name: true },
        },
      })
      bulkItems.forEach((item) => {
        values.push([
          item.beneficiaryName,
          item.iban,
          item.amount,
          item.partner.name + ' - ' + format(paymentList.dueDate, 'dd-MM-yyyy'),
          item.id,
        ])
      })
    }

    if (!values.length) {
      throw new NotFoundException('Payment list is empty')
    }

    const spreadsheetId = await googleApiUploadExcelToGoogleSheets(paymentListExcelTemplate)
    // Write to C4 a date in the format of dd-MM-yyyy
    await googleApiWriteValues(spreadsheetId, 'C4', [[format(new Date(), 'dd-MM-yyyy')]])
    await googleApiWriteValues(spreadsheetId, `B12:F${11 + values.length}`, values)

    const { stream } = await googleApiGetSpreadsheetStream(spreadsheetId)

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    // Setup cleanup handlers
    stream.on('end', async () => {
      try {
        await googleApiDeleteFile(spreadsheetId)
        if (paymentList.isUpdated) {
          paymentList.isUpdated = false
          await this.repo.save(paymentList)
        }
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'payment-list:download-excel',
          },
          level: 'warning',
        })
      }
    })

    stream.on('error', async (error) => {
      Sentry.captureException(error, {
        tags: {
          type: 'payment-list:download-excel-stream',
        },
        level: 'error',
      })

      try {
        await googleApiDeleteFile(spreadsheetId)
      } catch (deleteError) {
        Sentry.captureException(deleteError, {
          tags: {
            type: 'payment-list:download-excel-cleanup',
          },
          level: 'warning',
        })
      }
    })

    // Pipe the stream to the response
    stream.pipe(res)
  }

  async approve(id: string) {
    const paymentList = await this.repo.findOne({
      where: { id },
      relations: { individualItems: true, bulkItems: true },
    })
    if (!paymentList) {
      throw new NotFoundException('Payment list not found')
    }
    if (paymentList.status !== PaymentListStatus.PENDING_REVIEW) {
      throw new BadRequestException('Payment list is not pending review')
    }
    paymentList.status = PaymentListStatus.APPROVED
    await this.repo.save(paymentList)
  }

  async markAsPaid(id: string, referenceNumber: string) {
    const existingPaymentListWithSameReferenceNumber = await PaymentListEntity.findOne({
      where: { referenceNumber },
    })
    if (existingPaymentListWithSameReferenceNumber) {
      throw new BadRequestException('Payment list with same reference number already exists')
    }
    const paymentList = await PaymentListEntity.findOne({
      where: { id },
      relations: { individualItems: { saleItem: true }, bulkItems: { saleItems: true } },
    })
    if (!paymentList) {
      throw new NotFoundException('Payment list not found')
    }
    if (paymentList.status !== PaymentListStatus.APPROVED) {
      throw new BadRequestException('Payment list is not approved')
    }
    await this.repo.manager.transaction(async (manager) => {
      paymentList.status = PaymentListStatus.PAID
      paymentList.paidAt = new Date()
      paymentList.referenceNumber = referenceNumber
      await manager.save(PaymentListEntity, paymentList)
      await pMap(
        paymentList.individualItems,
        async (item) => {
          item.saleItem.status = SaleItemStatus.PAID
          await manager.save(SaleItemEntity, item.saleItem)
        },
        { concurrency: 10 }
      )
      await pMap(
        paymentList.bulkItems.flatMap((item) => item.saleItems),
        async (item) => {
          item.status = SaleItemStatus.PAID
          await manager.save(SaleItemEntity, item)
        },
        { concurrency: 10 }
      )
    })
  }
}
