import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ImageUsedInType } from '~/constants'
import { CategoryEntity } from '~/entities'
import { entityImageAutoProcess, entityImageDeleteByPrefix } from '~/utils'
import { entityFixPublishedAt } from '~/utils/entity-subscriber'

@Injectable()
@EventSubscriber()
export class CategorySubscriber implements EntitySubscriberInterface<CategoryEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return CategoryEntity
  }

  async beforeInsert(event: InsertEvent<CategoryEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async afterInsert(event: InsertEvent<CategoryEntity>) {
    const { entity, manager } = event

    // process image
    await entityImageAutoProcess({
      image: entity.image,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.CATEGORY,
      manager,
    })
    await entityImageAutoProcess({
      image: entity.icon,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.CATEGORY,
      manager,
    })

    entityFixPublishedAt({ event, type: 'insert' })
  }

  async beforeUpdate(event: UpdateEvent<CategoryEntity>) {
    entityFixPublishedAt({ event, type: 'update' })
  }

  async afterUpdate(event: UpdateEvent<CategoryEntity>) {
    const { entity, manager } = event

    await entityImageAutoProcess({
      image: entity.image ?? { id: entity.imageId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.CATEGORY,
      manager,
    })
    await entityImageAutoProcess({
      image: entity.icon ?? { id: entity.iconId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.CATEGORY,
      manager,
    })
  }

  async afterRemove(event: RemoveEvent<CategoryEntity>) {
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.CATEGORY,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
