import { Edit, useForm, useSelect } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { ITestedItem, ITestedItemList } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { InputNumber, Watch } from 'antx'
import cx from 'clsx'
import { FC, useState } from 'react'

import { InputLanguageTab, InputMultiEntitySelect, InputMultiLang, LanguageTabChoices } from '~/components'

export const TestedItemEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, formLoading } = useForm<ITestedItem, HttpError, ITestedItem>({
    meta: {
      join: {
        field: 'lists',
      },
    },
  })

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const { selectProps: listSelectProps } = useSelect<ITestedItemList>({
    resource: 'admin/tested-item-lists',
    optionLabel: 'internalName',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
            </>
          )}
        </Watch>
        <InputMultiEntitySelect label="List" name="lists" {...(listSelectProps as any)} />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
      </Form>
    </Edit>
  )
}
