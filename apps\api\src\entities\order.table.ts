import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { OrderStatus, UserFrom } from '~/constants'
import {
  AddressEntity,
  ChannelEntity,
  type IAddress,
  type IChannel,
  type ILocaleCurrency,
  type ILocaleLanguage,
  type IOrderAccessoryProductItem,
  type IOrderExtraPayment,
  type IOrderSkuItem,
  type IShipment,
  type IUser,
  LocaleCurrencyEntity,
  LocaleLanguageEntity,
  OrderAccessoryProductItemEntity,
  OrderExtraPaymentEntity,
  OrderSkuItemEntity,
  PreOrderEntity,
  SaleEntity,
  ShipmentEntity,
  UserEntity,
} from '~/entities'

registerEnumType(OrderStatus, { name: 'OrderStatusType' })

@ObjectType('Order')
@Entity('order')
export class OrderEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column({ unique: true })
  orderNumber: string

  @Field()
  @Column({
    type: 'decimal',
    nullable: false,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Index()
  total: number

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((order: OrderEntity) => order.channel)
  channelId: string

  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((order: OrderEntity) => order.currency)
  currencyId: string

  @Field()
  @Column()
  stripeCharge: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  trackingNumber?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  parcelId?: string

  @OneToOne(() => ShipmentEntity, { nullable: true })
  @JoinColumn()
  shipment?: Relation<ShipmentEntity>

  @Column({ nullable: true })
  @RelationId((sale: SaleEntity) => sale.shipment)
  shipmentId?: string

  @Field(() => OrderStatus)
  @Index()
  @Column({ type: 'varchar' })
  status: OrderStatus

  @OneToOne(() => PreOrderEntity, (preOrder) => preOrder.order, {
    nullable: true,
  })
  @JoinColumn()
  preOrder?: Relation<PreOrderEntity>

  @Column({ nullable: true })
  @RelationId((order: OrderEntity) => order.preOrder)
  preOrderId: string

  @OneToMany(() => OrderSkuItemEntity, (orderSkuItem) => orderSkuItem.order)
  orderSkuItems: Relation<OrderSkuItemEntity>[]

  @OneToMany(() => OrderAccessoryProductItemEntity, (orderAccessoryProductItem) => orderAccessoryProductItem.order)
  orderAccessoryItems: Relation<OrderAccessoryProductItemEntity>[]

  @ManyToOne(() => AddressEntity, { nullable: false })
  shippingAddress: Relation<AddressEntity>

  @Column()
  @RelationId((order: OrderEntity) => order.shippingAddress)
  shippingAddressId: string

  @Field()
  @Column()
  toPickupPoint: boolean

  @ManyToOne(() => AddressEntity, { nullable: false })
  billingAddress: Relation<AddressEntity>

  @Column()
  @RelationId((order: OrderEntity) => order.billingAddress)
  billingAddressId: string

  @ManyToOne(() => UserEntity, (user) => user.orders, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  user: Relation<UserEntity>

  @Column()
  @RelationId((order: OrderEntity) => order.user)
  userId: string

  @ManyToOne(() => LocaleLanguageEntity, { nullable: false })
  language: Relation<LocaleLanguageEntity>

  @Column()
  @RelationId((order: OrderEntity) => order.language)
  languageId: string

  @Field(() => UserFrom)
  @Column({ default: 'VALYUU' })
  from: string

  @OneToMany(() => OrderExtraPaymentEntity, (extraPayment) => extraPayment.order)
  extraPayments: Relation<OrderExtraPaymentEntity>[]

  @Column({ nullable: true })
  note?: string

  @Field()
  @Column()
  isReturningCustomer: boolean

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IOrder = Omit<OrderEntity, keyof BaseEntity> & {
  currency: ILocaleCurrency
  shipment?: IShipment
  channel: IChannel
  orderSkuItems: IOrderSkuItem[]
  accessoryProductItems: IOrderAccessoryProductItem[]
  extraPayments: IOrderExtraPayment[]
  shippingAddress: IAddress
  billingAddress: IAddress
  user: IUser
  language: ILocaleLanguage
}
