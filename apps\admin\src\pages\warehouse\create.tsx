import { Create, useForm, useSelect } from '@refinedev/antd'
import { HttpError, IResourceComponentsProps } from '@refinedev/core'
import type { ILocaleCountry, IWarehouse } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, InputNumber, Select } from 'antx'
import { FC, useMemo } from 'react'
import { v4 as uuid } from 'uuid'

import { InputMultiEntitySelect } from '~/components'

export const WarehouseCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps } = useForm<IWarehouse, HttpError, IWarehouse>()
  const id = useMemo(() => uuid(), [])

  const { selectProps: countrySelectProps } = useSelect<ILocaleCountry>({
    resource: 'admin/countries',
    optionLabel: 'name__en',
    meta: {
      fields: ['id', 'name__en'],
    },
    pagination: {
      mode: 'off',
    },
  })

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Input noStyle hidden name="id" initialValue={id} />
        <Select label="Country" name="countryId" rules={['required']} {...(countrySelectProps as any)} />
        <Input label="Company name" name="companyName" rules={['required']} />
        <Input label="Contact name" name="contactName" />
        <Input label="Email" name="email" rules={['required', 'email']} />
        <Input label="City" name="city" rules={['required']} />
        <Input label="Postal code" name="postalCode" rules={['required']} />
        <Input
          label="Phone"
          name="phone"
          rules={[
            'required',
            { pattern: /^\+\d+ \d+/, message: 'Phone number must be in the format of "+[country_code] [pure_number]"' },
          ]}
        />
        <Input label="Street" name="street" rules={['required']} />
        <Input label="House number" name="houseNumber" rules={['required']} />
        <InputMultiEntitySelect
          label="Shipping to countries"
          name="shippingToCountries"
          {...(countrySelectProps as any)}
        />
        <InputMultiEntitySelect
          label="Receiving from countries"
          name="receivingFromCountries"
          {...(countrySelectProps as any)}
        />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
      </Form>
    </Create>
  )
}
