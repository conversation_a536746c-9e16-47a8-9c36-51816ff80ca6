import { MigrationInterface, QueryRunner } from 'typeorm'

export class OrderLanguage1741736829131 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if column exists first
    const hasColumn = await queryRunner.hasColumn('order', 'language_id')

    if (!hasColumn) {
      // Add nullable column first
      await queryRunner.query(`
        ALTER TABLE "order"
        ADD COLUMN "language_id" varchar COLLATE "pg_catalog"."default"
      `)
    }

    // Update language_id from related user's language_id
    await queryRunner.query(`
      UPDATE "order" o
      SET language_id = u.language_id
      FROM "user" u
      WHERE o.user_id = u.id
    `)

    // Set NOT NULL constraint
    await queryRunner.query(`
      ALTER TABLE "order"
      ALTER COLUMN "language_id" SET NOT NULL
    `)

    // Add foreign key constraint if not exists
    await queryRunner.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (
          SELECT 1 
          FROM information_schema.table_constraints 
          WHERE constraint_name = 'fk_order_language'
        ) THEN
          ALTER TABLE "order"
          ADD CONSTRAINT "fk_order_language"
          FOREIGN KEY ("language_id")
          REFERENCES "locale_language"("id");
        END IF;
      END $$;
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
