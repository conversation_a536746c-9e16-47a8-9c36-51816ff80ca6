import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleLanguage1731334499416 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const hasColumn = await queryRunner.hasColumn('sale', 'language_id')

    if (!hasColumn) {
      await queryRunner.query(`
        ALTER TABLE "sale" 
        ADD COLUMN "language_id" varchar NULL
      `)
    }
    await queryRunner.query(`
        UPDATE "sale"
        SET language_id = (
          SELECT language_id 
          FROM "user" 
          WHERE "user".id = "sale".user_id
        )
      `)

    await queryRunner.query(`
        ALTER TABLE "sale" 
        ALTER COLUMN "language_id" SET NOT NULL
      `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "sale" DROP COLUMN "language_id"`)
  }
}
