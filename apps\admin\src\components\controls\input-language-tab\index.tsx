import { LOCALE_ENABLED_LANGUAGES, LOCALE_LANGUAGE_NAMES } from '@valyuu/api/constants'
import { Form, Segmented } from 'antd'
import { FC } from 'react'

export type LanguageTabChoices = (typeof LOCALE_ENABLED_LANGUAGES)[number] | ''

export type LanguageTabProps = {
  value: LanguageTabChoices
  onChange?: (value: LanguageTabChoices) => void
}

export const InputLanguageTab: FC<LanguageTabProps> = ({ value, onChange }) => {
  return (
    <Form.Item label="Language filter" className="col-span-2">
      <Segmented
        value={value}
        options={[
          { label: 'All', value: '' },
          ...LOCALE_ENABLED_LANGUAGES.map((value) => ({
            label: LOCALE_LANGUAGE_NAMES[value],
            value,
          })),
        ]}
        onChange={(value) => {
          onChange?.(value as LanguageTabChoices)
        }}
      />
    </Form.Item>
  )
}
