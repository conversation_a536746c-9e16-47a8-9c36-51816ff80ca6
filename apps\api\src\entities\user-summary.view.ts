import { BaseEntity, ViewColumn, ViewEntity } from 'typeorm'

import { SUCCESSFUL_ORDER_STATUSES, SUCCESSFUL_SALE_STATUSES } from '~/constants'

const successfulOrderFilter = `o.status IN ('${SUCCESSFUL_ORDER_STATUSES.join("', '")}')`
const successfulSaleFilter = `s.status IN ('${SUCCESSFUL_SALE_STATUSES.join("', '")}')`
const currentQuarterSaleFilter = `DATE_TRUNC('quarter', s.created_at) = DATE_TRUNC('quarter', CURRENT_DATE)`
const lastQuarterSaleFilter = `DATE_TRUNC('quarter', s.created_at) = DATE_TRUNC('quarter', CURRENT_DATE - INTERVAL '3 months')`
const currentQuarterOrderFilter = `DATE_TRUNC('quarter', o.created_at) = DATE_TRUNC('quarter', CURRENT_DATE)`
const lastQuarterOrderFilter = `DATE_TRUNC('quarter', o.created_at) = DATE_TRUNC('quarter', CURRENT_DATE - INTERVAL '3 months')`

@ViewEntity({
  name: 'user_summary',
  expression: `
      SELECT
        u.id AS user_id,
        a.first_name,
        a.last_name,
        a.first_name || ' ' || a.last_name AS name,
        u.email AS email,
        a.country_id AS country,
        a.city AS city,
        u.date_of_birth,
        u.language_id AS language,
        u.from AS source,
        COUNT(DISTINCT o.id) AS total_orders,
        COUNT(DISTINCT s.id) AS total_sales,
        COUNT(DISTINCT CASE WHEN ${successfulOrderFilter} THEN o.id ELSE NULL END) AS total_successful_orders,
        COUNT(DISTINCT CASE WHEN ${successfulSaleFilter} THEN s.id ELSE NULL END) AS total_successful_sales,
        COUNT(DISTINCT si.id) AS total_sale_items,
        COUNT(DISTINCT osi.id) AS total_order_sku_items,
        COUNT(DISTINCT oapi.id) AS total_order_accessory_items,
        COUNT(DISTINCT CASE WHEN ${successfulOrderFilter} THEN osi.id ELSE NULL END) AS total_successful_order_sku_items,
        COUNT(DISTINCT CASE WHEN ${successfulOrderFilter} THEN oapi.id ELSE NULL END) AS total_successful_order_accessory_items,
        COUNT(DISTINCT CASE WHEN ${successfulSaleFilter} THEN si.id ELSE NULL END) AS total_successful_sale_items,
        COUNT(DISTINCT CASE WHEN ${currentQuarterSaleFilter} THEN si.id ELSE NULL END) AS current_quarter_sale_items,
        COUNT(DISTINCT CASE WHEN ${lastQuarterSaleFilter} THEN si.id ELSE NULL END) AS last_quarter_sale_items,
        COUNT(DISTINCT CASE WHEN ${currentQuarterOrderFilter} THEN osi.id ELSE NULL END) AS current_quarter_order_sku_items,
        COUNT(DISTINCT CASE WHEN ${lastQuarterOrderFilter} THEN osi.id ELSE NULL END) AS last_quarter_order_sku_items,
        MIN(s.created_at) AS first_sale,
        MIN(o.created_at) AS first_order,
        COALESCE(BOOL_OR(o.is_returning_customer), FALSE) OR COALESCE(BOOL_OR(s.is_returning_customer), FALSE) AS returning_customer
    FROM
        "user" u
      LEFT JOIN
        "order" o ON u.id = o.user_id
      LEFT JOIN
        "order_sku_item" osi ON o.id = osi.order_id
      LEFT JOIN
        "order_accessory_product_item" oapi ON o.id = oapi.order_id
      LEFT JOIN
        "sale" s ON u.id = s.user_id
      LEFT JOIN
        "sale_item" si ON s.id = si.sale_id
      LEFT JOIN
        "address" a ON u.id = a.user_id
      GROUP BY
        u.id, u.email, a.first_name, a.last_name, a.country_id, a.city
    `,
})
export class UserSummaryEntity extends BaseEntity {
  @ViewColumn()
  userId: number

  @ViewColumn()
  firstName: string

  @ViewColumn()
  lastName: string

  @ViewColumn()
  name: string

  @ViewColumn()
  email: string

  @ViewColumn()
  country: string

  @ViewColumn()
  city: string

  @ViewColumn()
  dateOfBirth: Date

  @ViewColumn()
  language: string

  @ViewColumn()
  source: string

  @ViewColumn()
  totalOrders: number

  @ViewColumn()
  totalSuccessfulOrders: number

  @ViewColumn()
  totalOrderSkuItems: number

  @ViewColumn()
  totalSuccessfulOrderSkuItems: number

  @ViewColumn()
  totalOrderAccessoryItems: number

  @ViewColumn()
  totalSuccessfulOrderAccessoryItems: number

  @ViewColumn()
  totalSales: number

  @ViewColumn()
  totalSuccessfulSales: number

  @ViewColumn()
  totalSaleItems: number

  @ViewColumn()
  totalSuccessfulSaleItems: number

  @ViewColumn()
  currentQuarterSaleItems: number

  @ViewColumn()
  lastQuarterSaleItems: number

  @ViewColumn()
  currentQuarterOrderItems: number

  @ViewColumn()
  lastQuarterOrderItems: number

  @ViewColumn()
  returningCustomer: boolean
}
