import { TypeOrmCrudService } from '@dataui/crud-typeorm'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'

import { ProductSeriesEntity } from '~/entities'

@Injectable()
export class CrudProductSeriesService extends TypeOrmCrudService<ProductSeriesEntity> {
  constructor(@InjectRepository(ProductSeriesEntity) repo: Repository<ProductSeriesEntity>) {
    super(repo)
  }
}
