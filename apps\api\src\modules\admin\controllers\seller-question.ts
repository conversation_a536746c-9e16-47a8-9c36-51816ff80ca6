import { Controller, Get, Query, UseGuards } from '@nestjs/common'

import { LOCALE_ENABLED_LANGUAGES } from '~/constants'
import { AdminJwtAuthGuard } from '~admin/guards'
import { SellerQuestionsService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Controller('admin/seller-questions')
export class SellerQuestionController {
  constructor(private readonly service: SellerQuestionsService) {}

  @Get('condition')
  async getConditionQuestions(
    @Query('variant-id') variantId: string,
    @Query('channel-id') channelId: string,
    @Query('lang') lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
  ) {
    return this.service.getConditionQuestions({
      variantId,
      channelId,
      lang,
    })
  }

  @Get('problem')
  async getProblemQuestions(
    @Query('variant-id') variantId: string,
    @Query('channel-id') channelId: string,
    @Query('lang') lang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
  ) {
    return this.service.getProblemQuestions({
      variantId,
      channelId,
      lang,
    })
  }
}
