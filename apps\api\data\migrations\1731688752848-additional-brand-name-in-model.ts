import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddAdditionalBrandNameInModel1731688752848 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE product_model
      ADD COLUMN IF NOT EXISTS name_includes_brand_name BOOLEAN DEFAULT TRUE NOT NULL
    `)

    await queryRunner.query(`
      UPDATE product_model
      SET name_includes_brand_name = TRUE
    `)

    await queryRunner.query(`
      UPDATE product_model pm
      SET name_includes_brand_name = false
      FROM brand b
      WHERE pm.brand_id = b.id
        AND pm.name__en NOT ILIKE '%placeholder%'
        AND pm.name__en NOT ILIKE '%' || b.name__en || '%'
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
