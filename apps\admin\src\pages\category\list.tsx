import { limitFit } from '@cloudinary/url-gen/actions/resize'
import {
  <PERSON><PERSON>ield,
  DeleteButton,
  EditButton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { ICategory } from '@valyuu/api/entities'
import { Input, Space, Table } from 'antd'
import type { FC } from 'react'

import { PublishStatus } from '~/components'
import { cloudinary, handleTableRowClick } from '~/utils'

export const CategoryList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<ICategory>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'name__en', 'slug__en', 'sortOrder', 'createdAt', 'publishedAt'],
      join: [
        {
          field: 'image',
          select: ['publicId'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    filters: {
      initial: [
        {
          field: 'name__en',
          operator: 'contains',
          value: '',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/categories', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="image"
          title="Image"
          className="cursor-pointer"
          align="center"
          render={(value) => (
            <img
              src={cloudinary.image(value.publicId).resize(limitFit(30, 30)).toURL()}
              className="h-auto max-h-[30px] max-w-[30px]"
              alt=""
            />
          )}
          width="7rem"
        />
        <Table.Column
          dataIndex="name__en"
          title="Name (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('name__en', filters, 'contains')}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Name (English)" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="slug__en"
          title="Slug (English)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('slug__en', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 200 }} placeholder="Slug (English)" />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="sortOrder"
          title="Order"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
          width="5rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column
          dataIndex="publishedAt"
          title="Status"
          render={(value) => <PublishStatus value={value} />}
          defaultSortOrder={getDefaultSortOrder('publishedAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<ICategory>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
