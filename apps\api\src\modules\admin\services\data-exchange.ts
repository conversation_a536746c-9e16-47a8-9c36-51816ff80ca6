import { InjectRedis } from '@liaoliaots/nestjs-redis'
import { BadRequestException } from '@nestjs/common'
import * as currency from 'currency.js'
import { format } from 'date-fns'
import * as ExcelJS from 'exceljs'
import type { Express } from 'express'
import { Response } from 'express'
import Redis from 'ioredis'
import { isEqual } from 'lodash'
import * as pMap from 'p-map'
import { BaseEntity } from 'typeorm'

import { PLACEHOLDER_UUID, ProductModelSellerQuestionType, SaleItemPlanType } from '~/constants'
import {
  ChannelEntity,
  OrderAccessoryProductItemEntity,
  OrderSkuItemEntity,
  ProductVariantEntity,
  QuestionTypeConditionEntity,
  QuestionTypeEntity,
  QuestionTypeProblemEntity,
} from '~/entities'

import { DATA_EXCHANGE_JOB_REDIS_KEY, REDIS_JOB_EXPIRATION_IN_SECONDS } from '../constants'
import { DataExchangeJob } from '../interfaces'

const markets = [
  { id: SaleItemPlanType.C2B, key: 'MB', name__en: `[${SaleItemPlanType.C2B}] Fast pay` },
  { id: SaleItemPlanType.C2C, key: 'MC', name__en: `[${SaleItemPlanType.C2C}] Best value` },
]

type Option = {
  conditionId: string
  conditionKey: string
  conditionName__en: string
  id: string
  key: string
  name__en: string
}

// Function to generate all possible combinations using flatMap
const generateCombinations = (conditions: QuestionTypeConditionEntity[]): Option[][] => {
  conditions.sort((a, b) => a.sortOrder - b.sortOrder)
  conditions.forEach((condition) => {
    condition.options.sort((a, b) => a.sortOrder - b.sortOrder)
  })
  // A closure function that will be used to combine options
  const combine = (acc: Option[][], condition: QuestionTypeConditionEntity): Option[][] => {
    if (acc.length === 0) {
      return condition.options.map((option) => [
        {
          conditionId: condition.id,
          conditionKey: condition.key,
          conditionName__en: condition.name__en,
          id: option.id,
          key: option.key,
          name__en: option.name__en,
        },
      ])
    }

    return acc.flatMap((combination) =>
      condition.options.map((option) => [
        ...combination,
        {
          conditionId: condition.id,
          conditionKey: condition.key,
          conditionName__en: condition.name__en,
          id: option.id,
          key: option.key,
          name__en: option.name__en,
        },
      ])
    )
  }

  // Reduce the conditions array using the combine function to get all possible combinations
  return conditions.reduce(combine, [])
}

export class DataExchangeService {
  constructor(@InjectRedis() private readonly redis: Redis) {}

  async variantPriceExport(channelId: string, res: Response, fileFormat: 'xlsx' | 'json') {
    const isXlsx = fileFormat === 'xlsx'
    const isJson = fileFormat === 'json'

    if (!channelId) {
      throw new BadRequestException('Channel is required')
    } else if (!(await ChannelEntity.findOneBy({ id: channelId, disableSeller: false }))) {
      throw new BadRequestException('Channel not found')
    }

    const existingJob = await this.redis.get(DATA_EXCHANGE_JOB_REDIS_KEY)
    if (existingJob) {
      if (JSON.parse(existingJob).finished === false) {
        throw new BadRequestException('Another job is already running')
      }
    }

    const job: DataExchangeJob = {
      type: 'VARIANT_PRICE_EXPORT',
      total: null,
      processed: null,
      channel: channelId,
      started: new Date(),
      finished: false,
      failed: false,
    }

    await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', REDIS_JOB_EXPIRATION_IN_SECONDS)

    try {
      const xlsxOutput = isXlsx
        ? new ExcelJS.stream.xlsx.WorkbookWriter({
            stream: res,
            useStyles: true,
            useSharedStrings: true,
          })
        : null

      const jsonOutput: any = isJson ? {} : null

      // Use QueryBuilder for better performance and selective loading
      const questionTypesQuery = QuestionTypeEntity.createQueryBuilder('qt')
        .select(['qt.id', 'qt.internalName'])
        .where('qt.id != :placeholderUuid', { placeholderUuid: PLACEHOLDER_UUID })
        .getMany()

      // Count query optimization
      const totalQuery = ProductVariantEntity.createQueryBuilder('pv').where('pv.questionTypeId IS NOT NULL').getCount()

      // Run queries in parallel
      const [questionTypes, total] = await Promise.all([questionTypesQuery, totalQuery])

      job.total = total
      job.processed = 0
      await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', REDIS_JOB_EXPIRATION_IN_SECONDS)

      for (const questionType of questionTypes) {
        const worksheet = isXlsx ? xlsxOutput.addWorksheet(questionType.internalName) : null

        const subJson = isJson ? (jsonOutput[questionType.internalName] = {} as any) : null

        // Optimize conditions query with single JOIN
        const conditions = await QuestionTypeConditionEntity.createQueryBuilder('condition')
          .leftJoinAndSelect('condition.options', 'options')
          .where('condition.questionTypeId = :typeId', { typeId: questionType.id })
          .select([
            'condition.id',
            'condition.key',
            'condition.name__en',
            'condition.name__pl',
            'condition.sortOrder',
            'options.id',
            'options.key',
            'options.name__en',
            'options.name__pl',
            'options.sortOrder',
          ])
          .orderBy('condition.sortOrder', 'ASC')
          .addOrderBy('options.sortOrder', 'ASC')
          .getMany()

        const problems = await QuestionTypeProblemEntity.find({
          where: { questionTypeId: questionType.id },
          select: {
            id: true,
            key: true,
            name__en: true,
            name__pl: true,
          },
          order: {
            sortOrder: 'ASC',
          },
        })
        const conditionChoiceGroups = generateCombinations(conditions)
        const conditionChoiceCombinations = conditionChoiceGroups.map((choices) =>
          choices.reduce(
            (prev, curr) => Object.assign(prev, { [curr.conditionId]: curr.id }),
            {} as Record<Option['conditionId'], Option['id']>
          )
        )
        const idRow = [
          questionType.id,
          null,
          'Machine readable row, please do not modify or delete',
          ...markets.flatMap((market) =>
            conditionChoiceCombinations.map((choices) =>
              JSON.stringify({
                market: market.id,
                choices,
              })
            )
          ),
          null,
          ...problems.map((problem) => problem.id),
        ]
        if (isXlsx) Object.assign(worksheet.addRow(idRow), { hidden: true })
        if (isJson) {
          subJson.structure = [
            { type: 'id' },
            { type: 'legacy_id' },
            { type: 'name' },
            ...markets.flatMap((market) =>
              conditionChoiceCombinations.map((choices) => ({
                type: 'condition',
                market: market.id,
                choices,
              }))
            ),
            { type: 'base_price' },
            ...problems.map((problem) => ({ type: 'problem', id: problem.id })),
          ]
          subJson.rows = []
        }
        const titleRow = [
          'ID',
          'Legacy ID',
          'Name',
          ...markets.flatMap((market) =>
            conditionChoiceGroups.map(
              (choices) => market.key + choices.reduce((prev, curr) => prev + '-' + curr.conditionKey + curr.key, '')
            )
          ),
          'RP',
          ...problems.map((problem) => 'MA-' + problem.key),
        ]
        if (isXlsx) worksheet.addRow(titleRow)
        const descriptionRow = [
          null,
          'ID in 1.0',
          'Description',
          ...markets.flatMap((market) =>
            conditionChoiceGroups.map(
              (choices) =>
                `Market: ${market.name__en}\r\n${choices.map(({ conditionName__en, conditionKey, name__en, key }) => `[${conditionKey}] ${conditionName__en}: [${key}] ${name__en}`).join('\r\n')}`
            )
          ),
          'Base price',
          ...problems.map(({ name__en }) => name__en),
        ]

        if (isXlsx) {
          worksheet.addRow(descriptionRow)
          worksheet.getColumn(3).width = 30
        }

        // Optimize variants query with selective loading and JOIN
        const variants = await ProductVariantEntity.createQueryBuilder('variant')
          .leftJoinAndSelect('variant.basePrices', 'basePrices', 'basePrices.channelId = :channelId')
          .leftJoinAndSelect('variant.conditionCombinations', 'combinations')
          .leftJoinAndSelect('combinations.prices', 'prices', 'prices.channelId = :channelId')
          .leftJoinAndSelect('combinations.choices', 'choices')
          .leftJoinAndSelect('choices.option', 'option')
          .leftJoinAndSelect('variant.problems', 'problems')
          .leftJoinAndSelect('problems.prices', 'problemPrices', 'problemPrices.channelId = :channelId')
          .where('variant.questionTypeId = :typeId', { typeId: questionType.id })
          .andWhere('basePrices.channelId = :channelId')
          .orderBy('variant.createdAt', 'ASC')
          .setParameter('channelId', channelId)
          .select([
            'variant.id',
            'variant.legacyId',
            'variant.name__en',
            'variant.name__pl',
            'variant.createdAt',
            'basePrices.id',
            'basePrices.basePrice',
            'combinations.id',
            'choices.conditionId',
            'choices.optionId',
            'prices.id',
            'prices.c2bPrice',
            'prices.c2cPrice',
            'problems.id',
            'problems.problemId',
            'problemPrices.priceReduction',
          ])
          .getMany()

        for (const variant of variants) {
          const variantConditionPrices = conditionChoiceCombinations.map(
            (choices) =>
              variant.conditionCombinations.find((combination) =>
                isEqual(
                  combination.choices.reduce(
                    (prev, curr) => Object.assign(prev, { [curr.conditionId]: curr.optionId }),
                    {} as Record<Option['conditionId'], Option['id']>
                  ),
                  choices
                )
              )?.prices[0] // can be empty?
          )

          const row = [
            variant.id,
            variant.legacyId ?? null,
            variant.name__en,
            ...markets.flatMap((market) =>
              variantConditionPrices.map((price) => {
                switch (market.id) {
                  case SaleItemPlanType.C2B:
                    return price?.c2bPrice ?? 0
                  case SaleItemPlanType.C2C:
                    return price?.c2cPrice ?? 0
                  default:
                    return 0
                }
              })
            ),
            variant.basePrices[0]?.basePrice ?? 0,
            ...problems.map(
              (problem) =>
                variant.problems.find(({ problemId }) => problemId === problem.id)?.prices[0]?.priceReduction ?? false
            ),
          ]
          if (isXlsx) worksheet.addRow(row)
          if (isJson) {
            subJson.rows.push(row)
          }

          job.processed++
          await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', 10)
        }
      }
      if (isXlsx) await xlsxOutput.commit()
      if (isJson) {
        res.write(JSON.stringify(jsonOutput))
      }
    } catch (error) {
      job.failed = true
      throw error
    } finally {
      job.finished = true
      await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', 10)
    }
  }

  async variantPriceImport(channelId: string, file: Express.Multer.File) {
    if (!channelId) {
      throw new BadRequestException('Channel is required')
    } else if (!(await ChannelEntity.findOneBy({ id: channelId, disableSeller: false }))) {
      throw new BadRequestException('Channel not found')
    }

    const existingJob = await this.redis.get(DATA_EXCHANGE_JOB_REDIS_KEY)
    if (existingJob) {
      if (JSON.parse(existingJob).finished === false) {
        throw new BadRequestException('Another job is already running')
      }
    }

    const job: DataExchangeJob = {
      type: 'VARIANT_PRICE_IMPORT',
      total: null,
      processed: null,
      channel: channelId,
      started: new Date(),
      filename: file.filename,
      finished: false,
      failed: false,
    }

    await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', REDIS_JOB_EXPIRATION_IN_SECONDS)

    try {
      const workbook = new ExcelJS.Workbook()
      await workbook.xlsx.load(file.buffer)

      let total = 0
      workbook.eachSheet((worksheet) => {
        worksheet.eachRow(() => {
          total++
        })
        total -= 3 // for header rows
      })

      job.total = total
      job.processed = 0
      await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', REDIS_JOB_EXPIRATION_IN_SECONDS)

      for (const worksheet of workbook.worksheets) {
        const idRow: (
          | {
              type: 'CONDITION'
              market: SaleItemPlanType
              choices: Record<string, string>
            }
          | { type: 'PROBLEM'; id: string }
          | { type: 'BASE_PRICE'; isBasePrice: true }
          | { type: 'NULL' }
        )[] = []
        for (let line = 1; line <= worksheet.rowCount; line++) {
          if (line === 1) {
            let type = ProductModelSellerQuestionType.CONDITION
            worksheet.getRow(line).eachCell({ includeEmpty: true }, (cell, colNumber) => {
              if (colNumber < 4) {
                idRow.push({ type: 'NULL' })
                return
              }
              // RP row
              if (!String(cell)) {
                idRow.push({ type: 'BASE_PRICE', isBasePrice: true })
                type = ProductModelSellerQuestionType.PROBLEM
                return
              }
              if (type === ProductModelSellerQuestionType.CONDITION) {
                // TODO: change this to choices
                const { market, choices } = JSON.parse(String(cell))
                idRow.push({ type, market, choices })
              } else {
                idRow.push({ type, id: String(cell) })
              }
            })
            continue
          }
          if (line < 4) {
            continue
          }
          const row = worksheet.getRow(line)
          const variantId = String(row.getCell(1).value)
          if (!variantId || variantId === 'null') {
            continue
          }
          const variant = await ProductVariantEntity.findOneOrFail({
            where: {
              id: variantId,
              basePrices: { channelId },
              conditionCombinations: { prices: { channelId } },
              problems: { prices: { channelId } },
            },
            relations: {
              basePrices: true,
              conditionCombinations: { choices: true, prices: true },
              problems: { prices: true },
            },
            select: {
              id: true,
              basePrices: {
                id: true,
                basePrice: true,
              },
              conditionCombinations: {
                id: true,
                prices: {
                  id: true,
                  c2bPrice: true,
                  c2cPrice: true,
                },
                choices: {
                  conditionId: true,
                  optionId: true,
                },
              },
              problems: {
                id: true,
                problemId: true,
                prices: {
                  id: true,
                },
              },
            },
          })

          switch (true) {
            case !variant.basePrices?.[0]:
              throw new BadRequestException(`Variant base price not found, worksheet: ${worksheet.name}, line: ${line}`)
            case !variant.conditionCombinations ||
              !variant.conditionCombinations.every((combination) => combination.prices?.[0]):
              throw new BadRequestException(
                `Variant condition prices not found, worksheet: ${worksheet.name}, line: ${line}`
              )
            case !variant.problems || !variant.problems.every((problem) => problem.prices[0]):
              throw new BadRequestException(
                `Variant problem prices not found, worksheet: ${worksheet.name}, line: ${line}`
              )
          }

          for (let col = 4; col <= idRow.length; col++) {
            const cell = row.getCell(col)
            const typeInfo = idRow[col - 1]
            switch (typeInfo.type) {
              case ProductModelSellerQuestionType.CONDITION: {
                const combination = variant.conditionCombinations.find((combination) =>
                  isEqual(
                    combination.choices.reduce(
                      (prev, curr) => Object.assign(prev, { [curr.conditionId]: curr.optionId }),
                      {} as Record<string, string>
                    ),
                    typeInfo.choices
                  )
                )
                if (!combination || !combination.prices?.[0]) {
                  throw new BadRequestException(`Combination not found, worksheet: ${worksheet.name}, line: ${line}`)
                }
                switch (typeInfo.market) {
                  case SaleItemPlanType.C2B: {
                    const c2cPrice = Number(cell.value) || 0
                    combination.prices[0].c2bPrice = c2cPrice
                    break
                  }
                  case SaleItemPlanType.C2C:
                    combination.prices[0].c2cPrice = Number(cell.value) || 0
                    break
                }
                break
              }
              case ProductModelSellerQuestionType.PROBLEM: {
                const problem = variant.problems.find(({ problemId }) => problemId === typeInfo.id)
                if (!problem || !problem.prices?.[0]) {
                  throw new BadRequestException(`Problem not found, worksheet: ${worksheet.name}, line: ${line}`)
                }
                problem.prices[0].priceReduction = Number(cell.value)
                break
              }
              case 'BASE_PRICE':
                variant.basePrices[0].basePrice = Number(cell.value)
                break
            }
          }
          await pMap(
            [
              variant.basePrices[0],
              ...variant.conditionCombinations.map((combination) => combination.prices[0]),
              ...variant.problems.map((problem) => problem.prices[0]),
            ],
            async (entity: BaseEntity) => {
              await entity.save()
            },
            { concurrency: 10 }
          )
          job.processed++
        }
      }
      await ProductVariantEntity.getRepository().manager.connection.queryResultCache.clear()
    } catch (error) {
      job.failed = true
      console.error(error)
      throw error
    } finally {
      job.finished = true
      await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', 10)
    }
  }

  async orderExport(res: Response) {
    const existingJob = await this.redis.get(DATA_EXCHANGE_JOB_REDIS_KEY)
    if (existingJob) {
      if (JSON.parse(existingJob).finished === false) {
        throw new BadRequestException('Another job is already running')
      }
    }

    const job: DataExchangeJob = {
      type: 'ORDER_EXPORT',
      total: null,
      processed: null,
      started: new Date(),
      finished: false,
      failed: false,
    }

    await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', REDIS_JOB_EXPIRATION_IN_SECONDS)

    try {
      const workbook = new ExcelJS.stream.xlsx.WorkbookWriter({
        stream: res,
        useStyles: true,
        useSharedStrings: true,
      })

      const orderItems = await OrderSkuItemEntity.find({
        where: {},
        relations: {
          order: {
            user: true,
            shippingAddress: true,
          },
          productSku: {
            variant: true,
            saleItem: {
              sale: {
                user: true,
              },
            },
          },
        },
        select: {
          id: true,
          createdAt: true,
          unitPrice: true,
          warrantyPrice: true,
          order: {
            id: true,
            currencyId: true,
            orderNumber: true,
            createdAt: true,
            stripeCharge: true,
            shippingAddress: {
              id: true,
              firstName: true,
              lastName: true,
              countryId: true,
            },
          },
          productSku: {
            id: true,
            serialNumber: true,
            stockId: true,
            grading: true,
            source: true,
            displayColor__en: true,
            createdAt: true,
            btw: true,
            variant: {
              id: true,
              name__en: true,
              name__pl: true,
            },
            saleItem: {
              id: true,
              price: true,
              sale: {
                id: true,
                user: {
                  id: true,
                },
              },
            },
          },
        },
        order: { createdAt: 'ASC' },
      })

      const accessoryItems = await OrderAccessoryProductItemEntity.find({
        where: {},
        relations: {
          order: {
            shippingAddress: true,
            user: true,
          },
          accessoryProduct: true,
        },
      })

      job.total = orderItems.length + accessoryItems.length
      job.processed = 0
      await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', REDIS_JOB_EXPIRATION_IN_SECONDS)

      const skuWorksheet = workbook.addWorksheet('Order items')
      const titleRow = [
        'Order ID',
        'Order item ID',
        'SKU name',
        'Stock ID',
        'Color',
        'Grade',
        'Chanel',
        'Date of order sent',
        'Buyer name',
        'Serial number',
        'Order number',
        '',
        '',
        'Currency',
        'Warranty price',
        'Total device price',
        'VAT of the device',
        'Non-taxed device price',
        'Total order item price',
        'Is C2C',
        'Sold date',
        'Publish date',
        'Seller received',
        'Buyer ID',
        'Seller ID',
        'Buyer country',
        'Stripe charge ID',
      ]
      skuWorksheet.addRow(titleRow)
      skuWorksheet.getColumn(3).width = 30
      skuWorksheet.getColumn(9).width = 20
      skuWorksheet.getColumn(10).width = 20
      skuWorksheet.getColumn(11).width = 15
      skuWorksheet.getColumn(21).width = 20
      skuWorksheet.getColumn(22).width = 20

      for (const orderItem of orderItems) {
        if (!orderItem?.productSku?.variant || !orderItem?.order?.shippingAddress) {
          return
        }
        const { productSku, order } = orderItem
        const { variant } = productSku
        const { shippingAddress } = order
        const name = shippingAddress.firstName.trim() + ' ' + shippingAddress.lastName
        const unitPrice = orderItem.unitPrice
        const warrantyPrice = orderItem.warrantyPrice
        const vatPercentage = { NONE: 0, '21%': 21 }[productSku.btw] ?? 0
        const priceWithoutVat = currency(unitPrice).divide(1 + vatPercentage / 100).value
        const vat = unitPrice - priceWithoutVat
        const total = currency(unitPrice).add(warrantyPrice).value
        const isC2C = productSku.source === 'C2C'
        const dateSold = format(new Date(order.createdAt), 'd MMMM yyyy')
        const datePublished = format(new Date(productSku.createdAt), 'd MMMM yyyy')
        const price = productSku?.saleItem?.price
        const buyerID = order.user.id
        const sellerID = productSku?.saleItem?.sale?.user?.id
        const row = [
          orderItem.order.id,
          orderItem.id,
          variant.name__en,
          productSku.stockId,
          productSku.displayColor__en,
          productSku.grading,
          'Prioont',
          null,
          name,
          productSku.serialNumber,
          order.orderNumber,
          '',
          '',
          order.currencyId,
          warrantyPrice,
          unitPrice,
          vat,
          priceWithoutVat,
          total,
          isC2C,
          dateSold,
          datePublished,
          isC2C ? price : null,
          buyerID,
          sellerID,
          shippingAddress.countryId,
          order.stripeCharge,
        ]
        skuWorksheet.addRow(row)

        job.processed++
        await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', 10)
      }

      const accessoryWorksheet = workbook.addWorksheet('Accessory items')
      const accessoryTitleRow = [
        'Order ID',
        'Order accessory ID',
        'Accessory name',
        'Currency',
        'Accessory unit price',
        'Quantity',
        'Total order accessory price',
        'Total order accessory price without VAT',
        'Buyer ID',
        'Buyer country',
        'Sold date',
      ]

      accessoryWorksheet.getColumn(3).width = 30
      accessoryWorksheet.addRow(accessoryTitleRow)

      for (const accessoryItem of accessoryItems) {
        const { order, accessoryProduct } = accessoryItem
        const { shippingAddress } = order
        const unitPrice = accessoryItem.unitPrice
        const quantity = accessoryItem.quantity
        const total = currency(unitPrice).multiply(quantity).value
        const vatPercentage = 21
        const priceWithoutVat = currency(unitPrice).divide(1 + vatPercentage / 100).value
        const row = [
          order.id,
          accessoryItem.id,
          accessoryProduct.name__en,
          accessoryItem.currencyId,
          unitPrice,
          quantity,
          total,
          priceWithoutVat,
          order.user.id,
          shippingAddress.countryId,
          format(new Date(order.createdAt), 'd MMMM yyyy'),
        ]
        accessoryWorksheet.addRow(row)

        job.processed++
        await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', 10)
      }

      await workbook.commit()
    } catch (error) {
      job.failed = true
      throw error
    } finally {
      job.finished = true
      await this.redis.set(DATA_EXCHANGE_JOB_REDIS_KEY, JSON.stringify(job), 'EX', 10)
    }
  }
}
