import './index.css'

import { useApiUrl } from '@refinedev/core'
import { LOCALE_ENABLED_LANGUAGES } from '@valyuu/api/constants'
import { axiosInstance as axios } from '@valyuu/refine-crud-adapter'
import { Form, Input, Popconfirm } from 'antd'
import { create } from 'antx'
import { useState } from 'react'
import { BsTranslate } from 'react-icons/bs'
import { GoDuplicate } from 'react-icons/go'

export type IInputMultiLangProps = {
  value?: string
  onChange?: (value: string) => void
  sources: Partial<Record<(typeof LOCALE_ENABLED_LANGUAGES)[number] | 'auto', string>>
  targetLang: (typeof LOCALE_ENABLED_LANGUAGES)[number]
  fillType: 'translate' | 'duplicate'
  element?: 'input' | 'textarea'
  rows?: number
  disabled?: boolean
}

export const InputMultiLang = create(
  ({ value, onChange, sources, targetLang, fillType, element = 'input', rows, disabled }: IInputMultiLangProps) => {
    const [isTranslating, setIsTranslating] = useState(false)

    const apiUrl = useApiUrl()

    const hasSource = Object.entries(sources).some(([lang, text]) => lang !== targetLang && text && text.trim())

    const handleTranslateClick = async () => {
      if (disabled) {
        return
      }
      const [source, text] =
        Object.entries(sources).filter(([lang, text]) => lang !== targetLang && text && text.trim())[0] ?? []
      if (source && text && !isTranslating) {
        const { data } = await axios.post(`${apiUrl}/admin/deepl/translate`, {
          text,
          target: targetLang,
          source,
        })

        try {
          setIsTranslating(true)
          if (data) {
            onChange?.(data)
          }
        } finally {
          setIsTranslating(false)
        }
      }
    }

    const handleDuplicateClick = () => {
      const [, text] =
        Object.entries(sources).filter(([lang, text]) => lang !== targetLang && text && text.trim())[0] ?? []
      if (text) {
        onChange?.(text)
      }
    }

    const addon = hasSource
      ? {
          translate: value?.trim() ? (
            <Popconfirm
              key="translate"
              okText="Translate"
              cancelText="Cancel"
              okType="primary"
              title="Override the current content?"
              onConfirm={handleTranslateClick}
              className="addon"
              disabled={disabled || isTranslating}
            >
              <span title="Translate from other versions">
                <BsTranslate />
              </span>
            </Popconfirm>
          ) : (
            <span title="Translate from other versions" onClick={handleTranslateClick} className="addon">
              <BsTranslate />
            </span>
          ),
          duplicate: value?.trim() ? (
            <Popconfirm
              key="duplicate"
              okText="Duplicate"
              cancelText="Cancel"
              okType="primary"
              title="Override the current content?"
              onConfirm={handleDuplicateClick}
              className="addon"
              disabled={disabled}
            >
              <span title="Duplicate from other versions">
                <GoDuplicate />
              </span>
            </Popconfirm>
          ) : (
            <span title="Duplicate from other versions" onClick={handleDuplicateClick} className="addon">
              <GoDuplicate />
            </span>
          ),
        }[fillType]
      : null

    return element === 'input' ? (
      <Input
        lang={targetLang}
        value={value}
        onChange={({ target: { value } }) => onChange?.(value)}
        className="input-multi-lang-wrapper"
        addonAfter={addon}
        disabled={disabled}
      />
    ) : (
      <Form.Item className="textarea-multi-lang-wrapper">
        <Input.TextArea
          lang={targetLang}
          value={value}
          onChange={({ target: { value } }) => onChange?.(value)}
          className="w-full"
          disabled={disabled}
          rows={rows}
        />
        {addon}
      </Form.Item>
    )
  }
)
