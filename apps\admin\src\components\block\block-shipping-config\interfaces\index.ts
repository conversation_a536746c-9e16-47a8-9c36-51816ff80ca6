import type { Connection, Node } from '@xyflow/react'

export type ExtendedNode = Node & {
  data: Node['data'] &
    (
      | {
          nodeType: 'warehouse'
          hasBuyerCountries: boolean
          hasSellerCountries: boolean
          warehouseId: string
          countryId: string
        }
      | {
          nodeType: 'customer'
          countryId: string
        }
    )
}

export type ExtendedConnection = Connection & {
  data: {
    warehouseId: string
    customerCountryId: string
    fromCountryId: string
    toCountryId: string
    isReturn: boolean
  }
}
