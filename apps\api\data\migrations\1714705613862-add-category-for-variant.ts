import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddCategoryForVariant1714705613862 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE product_variant pv SET category_id = pm.category_id FROM product_model pm WHERE pv.model_id = pm.id;`
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
