import { sendcloudClientV2 } from '../client'

export type ICancelParcelResponse = {
  status: string
  message: string
}

/**
 * Cancels a parcel in SendCloud by its ID
 * @param {number | string} id - The ID of the parcel to cancel
 * @returns {Promise<ICancelParcelResponse>} Response containing status and message
 */
export const cancelParcel = async (id: number | string): Promise<ICancelParcelResponse> => {
  const { data } = await sendcloudClientV2.post<ICancelParcelResponse>(`/parcels/${id}/cancel`)
  return data
}
