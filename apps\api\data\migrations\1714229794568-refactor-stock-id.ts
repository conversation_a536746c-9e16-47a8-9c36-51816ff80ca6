import { MigrationInterface, QueryRunner } from 'typeorm'

export class RefactorStockId1714229794568 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('CREATE SEQUENCE IF NOT EXISTS stock_id_seq')
    await queryRunner.query(`DO $$
BEGIN
  PERFORM SETVAL('stock_id_seq', (SELECT 110000 + MAX(id) FROM stock_number));
EXCEPTION WHEN undefined_table THEN
  PERFORM SETVAL('stock_id_seq', 110000);
END
$$;
`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP SEQUENCE stock_id_seq')
  }
}
