import '@uppy/core/dist/style.min.css'
import '@uppy/dashboard/dist/style.min.css'
import '@uppy/webcam/dist/style.min.css'
import './index.css'

import { useGetIdentity } from '@refinedev/core'
import Uppy from '@uppy/core'
import { Dashboard } from '@uppy/react'
import XHR from '@uppy/xhr-upload'
import { FileProcessNameType, type FileUsedInType } from '@valyuu/api/constants'
import type { IFile } from '@valyuu/api/entities'
import { Button, Card, Descriptions, Drawer, Flex, Form, Modal, Popconfirm, Popover, Space, Typography } from 'antd'
import { create, Input } from 'antx'
import { cloneDeep, isEqual, pick } from 'lodash'
import { useEffect, useMemo, useRef, useState } from 'react'
import { AiOutlineDelete, AiOutlineEdit, AiOutlineUpload } from 'react-icons/ai'
import { BsFileEarmark } from 'react-icons/bs'
import { useImmer } from 'use-immer'

import { AdminUserFrontendType } from '~/interfaces'

export type InputFileProps<T = IFile | IFile[]> = {
  entityType: FileUsedInType
  entityId: string
  limit?: number
  value?: T
  onChange?: (value: T) => void
  plugins?: 'GoogleDrive'[]
  defaultNames?: {
    downloadFilename__en?: string
    downloadFilename__nl?: string
    downloadFilename__de?: string
    downloadFilename__pl?: string
  }
  processName?: FileProcessNameType
  allowedFileTypes?: string[]
}

export const InputFile = create(
  ({
    entityType,
    entityId,
    limit = Infinity,
    value = [],
    onChange,
    plugins = [],
    defaultNames,
    processName = undefined,
    allowedFileTypes = undefined,
  }: InputFileProps) => {
    const [uploadOpen, setUploadOpen] = useState(false)
    const [editingFile, setEditingFile] = useState<IFile | null>(null)
    const normalizedValue = value ? cloneDeep(Array.isArray(value) ? value : ([value as IFile] as IFile[])) : []
    const [fileList, setFileList] = useImmer<IFile[]>(normalizedValue)
    const fileListLastValue = useRef(fileList)
    const { data: user } = useGetIdentity<AdminUserFrontendType>() ?? {}

    const uppy = useMemo(() => {
      const uppy = new Uppy({
        restrictions: {
          allowedFileTypes,
          maxNumberOfFiles: Math.max(limit - fileList.length, 0),
        },
        debug: import.meta.env.DEV,
        meta: {
          entityId,
          entityType,
        },
      })

      uppy.use(XHR, {
        endpoint: import.meta.env.VITE_API_URL + '/admin/files/upload',
        formData: true,
        fieldName: 'file',
        onBeforeRequest: (request) => {
          if (user?.token) {
            request.setRequestHeader('Authorization', `Bearer ${user.token}`)
          }
        },
        onAfterResponse: (responseText, response) => {
          if (response !== 200) {
            throw new Error('Upload failed')
          }
        },
      })

      return uppy
    }, [limit, entityId, entityType, user?.token])

    useEffect(() => {
      uppy.setOptions({
        restrictions: {
          allowedFileTypes,
          maxNumberOfFiles: Math.max(limit - fileList.length, 0),
        },
        debug: import.meta.env.DEV,
        meta: {
          entityId,
          entityType,
        },
      })
    }, [fileList.length, limit, entityId, entityType, uppy])

    useEffect(() => {
      if (onChange && !isEqual(fileList, fileListLastValue.current)) {
        if (limit === 1) {
          onChange(fileList[0] ?? undefined)
        } else {
          onChange(fileList)
        }
        fileListLastValue.current = fileList
      }
    }, [fileList, limit, onChange])

    const [form] = Form.useForm<{
      downloadFilename__en?: string | null
      downloadFilename__nl?: string | null
      downloadFilename__de?: string | null
      downloadFilename__pl?: string | null
    }>()

    const fileNameFieldsAllHaveDefaultValue = !!(
      defaultNames?.downloadFilename__en &&
      defaultNames?.downloadFilename__nl &&
      defaultNames?.downloadFilename__de &&
      defaultNames?.downloadFilename__pl
    )
    return (
      <>
        <Button
          disabled={fileList.length >= limit}
          title={fileList.length >= limit ? 'Maximum number of files reached' : undefined}
          icon={<AiOutlineUpload />}
          onClick={() => setUploadOpen(true)}
        >
          Upload
        </Button>
        <Flex className="mt-4" wrap="wrap" gap="large">
          {fileList.map((file, index) => (
            <Card
              key={file.id}
              className="file-preview-card w-[322px]"
              cover={
                <div className="!flex h-[15.125rem] items-center justify-center">
                  <BsFileEarmark size={48} />
                </div>
              }
              actions={[
                <div
                  className="size-full"
                  onClick={() => {
                    setEditingFile(file)
                  }}
                  title="Edit file info"
                >
                  <AiOutlineEdit key="edit" />
                </div>,
                // <div
                //   className="size-full"
                //   onClick={() => {
                //     setEditingImage(file)
                //   }}
                //   title="Replace file"
                // >
                //   <AiOutlineCloudUpload key="replace" className="size-4" />
                // </div>,
                <Popconfirm
                  title="Delete the task"
                  description="Are you sure to delete this task?"
                  onConfirm={() =>
                    setFileList((draft) => {
                      draft.splice(index, 1)
                    })
                  }
                  okText="Yes"
                  cancelText="No"
                >
                  <div className="size-full" title="Delete file">
                    <AiOutlineDelete />
                  </div>
                </Popconfirm>,
              ]}
              bordered
            >
              <Card.Meta
                description={
                  <Popover
                    content={
                      <Descriptions bordered column={1}>
                        <Descriptions.Item label="File ID" className="max-w-[200px]">
                          <Typography.Paragraph copyable ellipsis className="!mb-0 w-full">
                            {file.id}
                          </Typography.Paragraph>
                        </Descriptions.Item>
                        <Descriptions.Item label="URL" className="max-w-[200px]">
                          <Typography.Link copyable ellipsis className="!mb-0 w-full" href={file.url} target="_blank">
                            {file.url}
                          </Typography.Link>
                        </Descriptions.Item>
                        <Descriptions.Item label="MIME type">
                          <Typography.Text>{file.mimeType}</Typography.Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="Name (EN)">
                          <Typography.Paragraph className="!mb-0">
                            <Typography.Text type={file.downloadFilename__en ? undefined : 'danger'}>
                              {file.downloadFilename__en || 'Not set'}
                            </Typography.Text>
                          </Typography.Paragraph>
                        </Descriptions.Item>
                        <Descriptions.Item label="Name (NL)">
                          <Typography.Paragraph className="!mb-0">
                            <Typography.Text type={file.downloadFilename__nl ? undefined : 'danger'}>
                              {file.downloadFilename__nl || 'Not set'}
                            </Typography.Text>
                          </Typography.Paragraph>
                        </Descriptions.Item>
                        <Descriptions.Item label="Name (DE)">
                          <Typography.Paragraph className="!mb-0">
                            <Typography.Text type={file.downloadFilename__de ? undefined : 'danger'}>
                              {file.downloadFilename__de || 'Not set'}
                            </Typography.Text>
                          </Typography.Paragraph>
                        </Descriptions.Item>
                        <Descriptions.Item label="Name (PL)">
                          <Typography.Paragraph className="!mb-0">
                            <Typography.Text type={file.downloadFilename__pl ? undefined : 'danger'}>
                              {file.downloadFilename__pl || 'Not set'}
                            </Typography.Text>
                          </Typography.Paragraph>
                        </Descriptions.Item>
                      </Descriptions>
                    }
                  >
                    <div className="size-full cursor-help">
                      <Typography.Text
                        type={
                          file.downloadFilename__en &&
                          file.downloadFilename__nl &&
                          file.downloadFilename__de &&
                          file.downloadFilename__pl
                            ? 'success'
                            : 'danger'
                        }
                        className="w-full"
                      >
                        <span>
                          {file.downloadFilename__en &&
                          file.downloadFilename__nl &&
                          file.downloadFilename__de &&
                          file.downloadFilename__pl
                            ? 'All fields filled'
                            : 'Some fields are empty'}
                        </span>
                        <span className="ml-2 text-gray-400">(hover for details)</span>
                      </Typography.Text>
                    </div>
                  </Popover>
                }
              />
            </Card>
          ))}
        </Flex>
        <Modal
          open={uploadOpen}
          title="Upload"
          onCancel={() => {
            setUploadOpen(false)
            uppy.cancelAll()
          }}
          width="80vh"
          centered
          maskClosable={false}
          footer={null}
        >
          <Dashboard
            uppy={uppy}
            plugins={plugins}
            width="100%"
            height="80vh"
            singleFileFullScreen={false}
            proudlyDisplayPoweredByUppy={false}
            doneButtonHandler={() => {
              setFileList((draft) => {
                draft.push(
                  ...uppy
                    .getFiles()
                    .filter((file) => file?.progress?.uploadComplete && file?.response?.body?.id)
                    .slice(0, limit - fileList.length)
                    .map(
                      (file) =>
                        ({
                          ...file.response?.body,
                          downloadFilename__en: defaultNames?.downloadFilename__en,
                          downloadFilename__nl: defaultNames?.downloadFilename__nl,
                          downloadFilename__de: defaultNames?.downloadFilename__de,
                          downloadFilename__pl: defaultNames?.downloadFilename__pl,
                        }) as IFile
                    )
                )
              })
              uppy.cancelAll()
              setUploadOpen(false)
            }}
          />
        </Modal>
        <Drawer
          title="Edit file info"
          open={!!editingFile}
          onClose={() => setEditingFile(null)}
          afterOpenChange={() => {
            form.setFieldsValue(
              pick(editingFile, [
                'downloadFilename__en',
                'downloadFilename__nl',
                'downloadFilename__de',
                'downloadFilename__pl',
              ])
            )
          }}
          footer={null}
        >
          <Form
            form={form}
            component={false}
            layout="horizontal"
            initialValues={{
              downloadFilename__en: editingFile?.downloadFilename__en,
              downloadFilename__nl: editingFile?.downloadFilename__nl,
              downloadFilename__de: editingFile?.downloadFilename__de,
              downloadFilename__pl: editingFile?.downloadFilename__pl,
            }}
          >
            <Form.Item
              noStyle={!processName && !fileNameFieldsAllHaveDefaultValue}
              extra={
                processName === FileProcessNameType.HIDE || !processName
                  ? undefined
                  : fileNameFieldsAllHaveDefaultValue
                    ? 'Please note: If any fields are left blank, they will automatically be filled with the default values shown beneath the input fields.'
                    : `Please note: If you enter a name in only one language and leave the other fields blank, they will automatically be ${
                        {
                          [FileProcessNameType.COPY]: 'copied',
                          [FileProcessNameType.TRANSLATE]: 'translated',
                        }[processName]
                      } into the corresponding languages.`
              }
            >
              <Input
                label="Download filename (EN)"
                name="downloadFilename__en"
                autoComplete="off"
                extra={
                  defaultNames?.downloadFilename__en ? `Default to ${defaultNames.downloadFilename__en}` : undefined
                }
                hidden={processName === FileProcessNameType.HIDE}
              />
              <Input
                label="Download filename (NL)"
                name="downloadFilename__nl"
                autoComplete="off"
                extra={
                  defaultNames?.downloadFilename__nl ? `Default to ${defaultNames.downloadFilename__nl}` : undefined
                }
                hidden={processName === FileProcessNameType.HIDE}
              />
              <Input
                label="Download filename (DE)"
                name="downloadFilename__de"
                autoComplete="off"
                extra={
                  defaultNames?.downloadFilename__de ? `Default to ${defaultNames.downloadFilename__de}` : undefined
                }
                hidden={processName === FileProcessNameType.HIDE}
              />
              <Input
                label="Download filename (PL)"
                name="downloadFilename__pl"
                autoComplete="off"
                extra={
                  defaultNames?.downloadFilename__pl ? `Default to ${defaultNames.downloadFilename__pl}` : undefined
                }
                hidden={processName === FileProcessNameType.HIDE}
              />
            </Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                onClick={() => {
                  const formValues = form.getFieldsValue()
                  let { downloadFilename__en, downloadFilename__nl, downloadFilename__de } = formValues
                  let downloadFilename__pl = formValues.downloadFilename__pl
                  if (!downloadFilename__en?.trim()) {
                    downloadFilename__en = defaultNames?.downloadFilename__en || null
                  }
                  if (!downloadFilename__nl?.trim()) {
                    downloadFilename__nl = defaultNames?.downloadFilename__nl || null
                  }
                  if (!downloadFilename__de?.trim()) {
                    downloadFilename__de = defaultNames?.downloadFilename__de || null
                  }
                  if (!downloadFilename__pl?.trim()) {
                    downloadFilename__pl = defaultNames?.downloadFilename__pl || null
                  }
                  setFileList((draft) => {
                    const index = draft.findIndex((file) => file.id === editingFile?.id)
                    if (index !== -1) {
                      draft[index] = {
                        ...draft[index],
                        downloadFilename__en,
                        downloadFilename__nl,
                        downloadFilename__de,
                        downloadFilename__pl,
                        processName,
                      }
                    }
                  })
                  setEditingFile(null)
                }}
              >
                Submit
              </Button>
              <Button
                htmlType="button"
                onClick={() => form.resetFields()}
                hidden={processName === FileProcessNameType.HIDE}
              >
                Reset
              </Button>
            </Space>
          </Form>
        </Drawer>
      </>
    )
  }
)
