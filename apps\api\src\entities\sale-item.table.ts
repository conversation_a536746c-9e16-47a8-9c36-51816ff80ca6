import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import { Min } from 'class-validator'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { SaleItemOfferStatus, SaleItemPlanType, SaleItemStatus } from '~/constants'
import {
  AddressEntity,
  ChannelEntity,
  type IAddress,
  type IChannel,
  type ILocaleCurrency,
  type IPartner,
  type IPaymentListBulkItem,
  type IPaymentListIndividualItem,
  type IPaymentListProblemItem,
  type IProductModel,
  type IProductSku,
  type IProductVariant,
  type ISale,
  type ISaleItemHistory,
  type ISaleItemOffer,
  type ISaleItemStripePayment,
  type IShipment,
  type IStock,
  type IUser,
  LocaleCurrencyEntity,
  PartnerEntity,
  PaymentListBulkItemEntity,
  PaymentListIndividualItemEntity,
  PaymentListProblemItemEntity,
  ProductModelEntity,
  ProductSkuEntity,
  ProductVariantEntity,
  SaleEntity,
  SaleItemHistoryEntity,
  SaleItemOfferEntity,
  SaleItemStripePaymentEntity,
  ShipmentEntity,
  StockEntity,
  UserEntity,
} from '~/entities'
import { SaleItemAnswersType } from '~/interfaces'

registerEnumType(SaleItemPlanType, { name: 'SaleItemPlanType' })

@ObjectType('SaleItem')
@Entity('sale_item')
export class SaleItemEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'smallint', default: 1 })
  version: number

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Column({ type: 'boolean', default: false })
  isLegacy: boolean

  // TODO: Remove this
  @Column({ type: 'jsonb', nullable: true })
  answers: SaleItemAnswersType

  @Column({ type: 'text', nullable: true })
  customerNote?: string

  // TODO: this is for the future
  // @Column()
  // isProductFunctional: boolean

  // @Column({ type: 'uuid', array: true, default: () => 'ARRAY[]::uuid[]' })
  // conditions: string[]

  // @Column({ type: 'uuid', array: true, default: () => 'ARRAY[]::uuid[]' })
  // problems: string[]
  // end of TODO

  @Index()
  @Field(() => SaleItemPlanType)
  @Column({ type: 'varchar' })
  type: SaleItemPlanType

  @Index()
  @Field(() => SaleItemStatus)
  @Column({ type: 'varchar', default: SaleItemStatus.SUBMITTED })
  status: SaleItemStatus

  @Index()
  @Column({ type: 'timestamp', nullable: true })
  payoutDueDate?: Date

  @Index()
  @Column({ type: 'varchar', default: SaleItemOfferStatus.INITIAL })
  offerStatus: SaleItemOfferStatus

  @Column({ type: 'timestamp' })
  offerAcceptedAt: Date

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  @Min(0)
  price: number

  @Column({
    type: 'decimal',
    unsigned: true,
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
    nullable: true,
  })
  @Min(0)
  initialPrice: number

  @ManyToOne(() => AddressEntity, { nullable: true })
  returnAddress?: Relation<AddressEntity>

  @Column({ nullable: true })
  @RelationId((saleItem: SaleItemEntity) => saleItem.returnAddress)
  returnAddressId?: string

  @OneToOne(() => ShipmentEntity, (shipment) => shipment.saleItem, { nullable: true })
  @JoinColumn()
  returnShipment?: Relation<ShipmentEntity>

  @Column({ nullable: true })
  @RelationId((saleItem: SaleItemEntity) => saleItem.returnShipment)
  returnShipmentId?: string

  @Index()
  @Column({ type: 'boolean', default: false })
  isTested: boolean

  @ManyToOne(() => ChannelEntity, { nullable: false })
  channel: Relation<ChannelEntity>

  @Column()
  @RelationId((saleItem: SaleItemEntity) => saleItem.channel)
  channelId: string

  @ManyToOne(() => LocaleCurrencyEntity, { nullable: false })
  currency: Relation<LocaleCurrencyEntity>

  @Column()
  @RelationId((saleItem: SaleItemEntity) => saleItem.currency)
  currencyId: string

  @Column({ nullable: true })
  trelloId?: string

  @Column({ nullable: true })
  note?: string

  @ManyToOne(() => ProductVariantEntity, (variant) => variant.saleItems, { nullable: false })
  productVariant: Relation<ProductVariantEntity>

  @Column()
  @RelationId((saleItem: SaleItemEntity) => saleItem.productVariant)
  productVariantId: string

  @ManyToOne(() => ProductModelEntity, (model) => model.saleItems, { nullable: false })
  productModel: Relation<ProductModelEntity>

  @Column()
  @RelationId((saleItem: SaleItemEntity) => saleItem.productModel)
  productModelId: string

  @ManyToOne(() => SaleEntity, (sale) => sale.saleItems, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  sale: Relation<SaleEntity>

  @Column()
  @RelationId((saleItem: SaleItemEntity) => saleItem.sale)
  saleId: string

  @OneToOne(() => ProductSkuEntity, (product) => product.saleItem, {
    nullable: true,
  })
  productSku?: Relation<ProductSkuEntity>

  // TODO: Pending removal
  @OneToMany(() => SaleItemStripePaymentEntity, (saleItemPayment) => saleItemPayment.saleItem, { nullable: true })
  stripePayments?: Relation<SaleItemStripePaymentEntity>[]

  @OneToOne(() => StockEntity, (stock) => stock.saleItem, { nullable: false })
  @JoinColumn()
  stock: Relation<StockEntity>

  @Column()
  @RelationId((saleItem: SaleItemEntity) => saleItem.stock)
  stockId: string

  @Index()
  @Column({ nullable: true })
  imei?: string

  @ManyToOne(() => UserEntity, (user) => user.orders, { nullable: false })
  user: Relation<UserEntity>

  @Column()
  @RelationId((saleItem: SaleItemEntity) => saleItem.user)
  userId: string

  @ManyToOne(() => PartnerEntity, { nullable: true })
  partner?: Relation<PartnerEntity>

  @Column({ nullable: true })
  @RelationId((saleItem: SaleItemEntity) => saleItem.partner)
  partnerId?: string | null

  @OneToOne(() => SaleItemOfferEntity, (offer) => offer.saleItem, {
    nullable: false,
    cascade: true,
  })
  offers: Relation<SaleItemOfferEntity>[]

  @OneToOne(() => SaleItemOfferEntity, {
    nullable: true,
    cascade: true,
  })
  @JoinColumn()
  offer?: Relation<SaleItemOfferEntity>

  @Column({ nullable: true })
  @RelationId((saleItem: SaleItemEntity) => saleItem.offer)
  offerId?: string

  @OneToMany(() => SaleItemHistoryEntity, (history) => history.saleItem)
  histories: Relation<SaleItemHistoryEntity>[]

  @OneToOne(() => PaymentListIndividualItemEntity, (paymentListIndividualItem) => paymentListIndividualItem.saleItem, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  paymentListIndividualItem?: Relation<PaymentListIndividualItemEntity>

  @Column({ nullable: true })
  @RelationId((saleItem: SaleItemEntity) => saleItem.paymentListIndividualItem)
  paymentListIndividualItemId?: string

  @ManyToOne(() => PaymentListBulkItemEntity, (paymentListBulkItem) => paymentListBulkItem.saleItems, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  paymentListBulkItem?: Relation<PaymentListBulkItemEntity>

  @Column({ nullable: true })
  @RelationId((saleItem: SaleItemEntity) => saleItem.paymentListBulkItem)
  paymentListBulkItemId?: string

  @OneToMany(() => PaymentListProblemItemEntity, (paymentListProblemItem) => paymentListProblemItem.saleItem)
  paymentListProblemItems?: Relation<PaymentListProblemItemEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ISaleItemAnswersType = SaleItemAnswersType

export type ISaleItem = Omit<SaleItemEntity, keyof BaseEntity> & {
  channel: IChannel
  currency: ILocaleCurrency
  productVariant: IProductVariant
  productModel: IProductModel
  sale: ISale
  productSku?: IProductSku
  stripePayments?: ISaleItemStripePayment[]
  stock?: IStock
  returnAddress?: IAddress
  returnShipment?: IShipment
  user: IUser
  partner?: IPartner
  offers: ISaleItemOffer[]
  offer: ISaleItemOffer
  histories: ISaleItemHistory[]
  paymentListIndividualItem?: IPaymentListIndividualItem
  paymentListBulkItem?: IPaymentListBulkItem
  paymentListProblemItems?: IPaymentListProblemItem[]
}
