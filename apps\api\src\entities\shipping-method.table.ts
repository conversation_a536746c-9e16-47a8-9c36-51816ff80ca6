import { CarrierCode } from '@valyuu/sendcloud'
import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryColumn, UpdateDateColumn } from 'typeorm'

@Entity('shipping_method')
export class ShippingMethodEntity extends BaseEntity {
  @PrimaryColumn()
  id: string

  @Index()
  @Column({ type: 'varchar' })
  carrier: CarrierCode

  @Column()
  shippingProductName: string

  @Index()
  @Column()
  shippingProductCode: string

  @Column()
  name: string

  @Column()
  minWeightG: number

  @Column()
  maxWeightG: number

  @Index()
  @Column()
  isPaperless: boolean

  @Column()
  isReturn: boolean

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IShippingMethod = Omit<ShippingMethodEntity, keyof BaseEntity>
