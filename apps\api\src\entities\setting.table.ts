import { BaseEntity, Column, CreateDateColumn, Entity, Index, PrimaryColumn, UpdateDateColumn } from 'typeorm'

@Entity('setting')
export class SettingEntity extends BaseEntity {
  @PrimaryColumn()
  id: string

  @Column({ nullable: false })
  name: string

  @Column({ type: 'jsonb', nullable: false })
  value: any

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ISetting = Omit<SettingEntity, keyof BaseEntity>
