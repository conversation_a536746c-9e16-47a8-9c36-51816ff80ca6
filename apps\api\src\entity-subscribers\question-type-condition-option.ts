import { Injectable } from '@nestjs/common'
import * as pMap from 'p-map'
import type { EntitySubscriberInterface, InsertEvent, RemoveEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import {
  ProductVariantConditionCombinationChoiceEntity,
  ProductVariantConditionCombinationPriceEntity,
  QuestionTypeConditionOptionEntity,
} from '~/entities'
import { productVariantGenerateConditionCombinations } from '~/utils'

@Injectable()
@EventSubscriber()
export class QuestionTypeConditionOptionSubscriber
  implements EntitySubscriberInterface<QuestionTypeConditionOptionEntity>
{
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return QuestionTypeConditionOptionEntity
  }

  async afterInsert(event: InsertEvent<QuestionTypeConditionOptionEntity>) {
    const { entity, manager } = event

    if (entity.id) {
      const questionTypeOption = await manager.findOneOrFail(QuestionTypeConditionOptionEntity, {
        where: { id: entity.id },
        relations: { condition: { questionType: { productVariants: true } } },
        select: { id: true, condition: { id: true, questionType: { id: true, productVariants: { id: true } } } },
      })
      await pMap(questionTypeOption?.condition?.questionType?.productVariants ?? [], (variant) =>
        productVariantGenerateConditionCombinations(variant.id, manager)
      )
    }
  }

  async afterRemove(event: RemoveEvent<QuestionTypeConditionOptionEntity>) {
    const { entity, manager } = event
    if (entity.id) {
      const choices = await manager.find(ProductVariantConditionCombinationChoiceEntity, {
        where: { optionId: entity.id },
        relations: { combination: true },
      })
      await pMap(
        choices,
        async ({ combination }) => {
          if (combination) {
            await manager.remove(combination)
            const prices = await manager.find(ProductVariantConditionCombinationPriceEntity, {
              where: { conditionCombinationId: combination.id },
            })
            await pMap(prices, async (price) => {
              await manager.remove(price)
            })
          }
        },
        { concurrency: 5 }
      )
    }
  }
}
