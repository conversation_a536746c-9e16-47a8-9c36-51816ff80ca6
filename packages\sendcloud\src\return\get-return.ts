import { sendcloudClientV2 } from '../client/v2'
import { IReturnObject } from '../interfaces'

/**
 * Fetches a return by its ID from the SendCloud API
 * @param returnId - The ID of the return to fetch
 * @returns Promise containing the return object
 */
export const getReturn = async (returnId: number | string): Promise<IReturnObject> => {
  const { data } = await sendcloudClientV2.get<{ return: IReturnObject }>(`/returns/${returnId}`)
  return data.return
}
