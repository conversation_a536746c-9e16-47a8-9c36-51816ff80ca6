import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CrudFilter } from '@refinedev/core'
import { Request<PERSON>ueryBuilder, SCondition } from '@dataui/crud-request'
import { mapOperator } from './map-operator'

export const generateSearchFilter = (filters: CrudFilters): SCondition => {
  return createSearchQuery({
    operator: 'and',
    value: filters,
  })
}

export const createSearchQuery = (filter: CrudFilter): SCondition => {
  if (filter.operator !== 'and' && filter.operator !== 'or' && 'field' in filter) {
    // query
    return {
      [filter.field]: {
        [mapOperator(filter.operator)]: filter.value,
      },
    }
  }

  const { operator } = filter

  return {
    [mapOperator(operator)]: filter.value.map((filter) => createSearchQuery(filter)),
  }
}

export const handleFilter = (query: RequestQueryBuilder, filters?: CrudFilters) => {
  if (filters) {
    query.search(generateSearchFilter(filters))
  }
  return query
}
