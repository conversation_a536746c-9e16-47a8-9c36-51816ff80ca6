import { DateF<PERSON>, EditButton, FilterDropdown, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { getDefaultFilter, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { ICharity } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import { Input } from 'antx'
import type { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const CharityList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<ICharity>({
    syncWithLocation: true,
    meta: {
      fields: ['id', 'name__en', 'name__nl', 'name__de', 'name__pl', 'createdAt'],
      join: [
        {
          field: 'bankAccount',
          select: ['id', 'holderName', 'accountNumber'],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/charities', id!)))}
      >
        <Table.Column
          dataIndex="id"
          title="ID"
          className="max-w-10 cursor-pointer"
          render={(value) => <span title={value}>{value}</span>}
          width="5rem"
          ellipsis={true}
        />
        <Table.Column
          dataIndex="name__en"
          title="Name (EN)"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('name__en', filters)}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Input style={{ minWidth: 280 }} placeholder="Name (EN)" normalize={(value) => value.trim()} />
            </FilterDropdown>
          )}
          width="15rem"
        />
        <Table.Column dataIndex={['bankAccount', 'holderName']} title="Bank account" className="cursor-pointer" />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<ICharity>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
