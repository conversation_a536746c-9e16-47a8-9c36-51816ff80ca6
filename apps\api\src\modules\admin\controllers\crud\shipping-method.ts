import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, Get, Query, UseGuards } from '@nestjs/common'
import { CarrierCode } from '@valyuu/sendcloud'

import { ShippingMethodEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudShippingMethodService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: ShippingMethodEntity,
  },
  params: { id: { field: 'id', type: 'number', primary: true } },
})
@Controller('admin/shipping-methods')
export class CrudShippingMethodController implements CrudController<ShippingMethodEntity> {
  constructor(public service: CrudShippingMethodService) {}

  @Get('find')
  findBetweenCountries(
    @Query('from_country') fromCountry: string,
    @Query('to_country') toCountry: string,
    @Query('is_return') isReturn: string,
    @Query('is_production') isProduction?: string,
    @Query('carrier') carrier?: string,
    @Query('paperless') paperless?: string
  ) {
    return this.service.findBetweenCountries({
      fromCountry,
      toCountry,
      isReturn: isReturn?.toLocaleLowerCase() === 'true',
      isProduction: isProduction === undefined ? true : !(isProduction?.toLocaleLowerCase() !== 'true'),
      carrier: carrier as CarrierCode,
      paperless: paperless ? !(paperless.toLowerCase() !== 'true') : undefined,
    })
  }

  @Get('price')
  getPrice(
    @Query('from_country') fromCountry: string,
    @Query('to_country') toCountry: string,
    @Query('method_id') methodId: string,
    @Query('weight_kg') weightKg?: string
  ) {
    return this.service.getPrice({ fromCountry, toCountry, methodId, weightKg: weightKg ? parseFloat(weightKg) : 1 })
  }
}
