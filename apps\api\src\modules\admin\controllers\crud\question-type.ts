import { <PERSON><PERSON>, Crud<PERSON>ontroller } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { PLACEHOLDER_UUID } from '~/constants'
import { QuestionTypeEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudQuestionTypeService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: QuestionTypeEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      conditions: {},
      'conditions.options': {},
      problems: {},
      productModels: {},
    },
    filter: {
      id: {
        $ne: PLACEHOLDER_UUID,
      },
    },
  },
})
@Controller('admin/question-types')
export class CrudQuestionTypeController implements CrudController<QuestionTypeEntity> {
  constructor(public service: CrudQuestionTypeService) {}
}
