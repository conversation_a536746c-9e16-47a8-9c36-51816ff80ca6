{"include": ["src", "types"], "compilerOptions": {"rootDir": "./src", "baseUrl": ".", "module": "ESNext", "lib": ["DOM", "ESNext", "DOM.Iterable"], "importHelpers": true, "declaration": true, "sourceMap": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "Node", "esModuleInterop": true, "noEmit": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true}}