import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { BlogTagColor } from '~/constants'

import { BlogPostEntity, type IBlogPost } from './blog-post.table'

registerEnumType(BlogTagColor, { name: 'BlogTagColor' })

@ObjectType('BlogTag')
@Entity('blog_tag')
export class BlogTagEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column({ unique: true })
  slug__en: string

  @Field()
  @Column({ unique: true })
  slug__nl: string

  @Field()
  @Column({ unique: true })
  slug__de: string

  @Field()
  @Column({ unique: true })
  slug__pl: string

  @Field(() => BlogTagColor, { description: 'The color of the tag' })
  @Column({ type: 'varchar' })
  color: BlogTagColor

  @Field(() => [BlogPostEntity])
  @OneToMany(() => BlogPostEntity, (post) => post.tag)
  posts: Relation<BlogPostEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IBlogTag = Omit<BlogTagEntity, keyof BaseEntity> & {
  posts: IBlogPost[]
}
