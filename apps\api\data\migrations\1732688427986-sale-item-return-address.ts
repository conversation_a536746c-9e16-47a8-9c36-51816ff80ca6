import { MigrationInterface, QueryRunner } from 'typeorm'

export class SaleItemReturnAddress1732688427986 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const hasReturnAddressColumn = await queryRunner.hasColumn('sale_item', 'return_address_id')
    if (!hasReturnAddressColumn) {
      // Changed from varchar to uuid type
      await queryRunner.query(`ALTER TABLE "sale_item" ADD COLUMN "return_address_id" uuid`)
    }
    // Update return_address_id from related sale table
    await queryRunner.query(`
      UPDATE sale_item 
      SET return_address_id = sale.address_id
      FROM sale 
      WHERE sale_item.sale_id = sale.id
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
