import { Field, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm'

import { ILicensePlateProduct, LicensePlateProductEntity } from './licenseplate-product.table'
import { ILicensePlatePurchase, LicensePlatePurchaseEntity } from './licenseplate-purchase.table'
import { ILicensePlateSale, LicensePlateSaleEntity } from './licenseplate-sale.table'
import { ILicensePlateStatus, LicensePlateStatusEntity } from './licenseplate-status.table'

@ObjectType('LicensePlateDevice')
@Entity('licenseplate_device')
export class LicensePlateDeviceEntity extends BaseEntity {
  @Field()
  @PrimaryColumn()
  guid: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  imei?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  serial?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  grade_inbound?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  grade_outbound?: string

  @Field()
  @CreateDateColumn()
  created_at: Date

  @Field()
  @UpdateDateColumn()
  updated_at: Date

  @Field({ nullable: true })
  @Column({ nullable: true })
  updated_by?: string

  @Field({ nullable: true })
  @Column({ nullable: true })
  is_returned?: boolean

  @OneToOne(() => LicensePlateProductEntity, (product) => product.device, { cascade: true })
  product: LicensePlateProductEntity | null

  @OneToOne(() => LicensePlateSaleEntity, (sale) => sale.device, { cascade: true })
  sale: LicensePlateSaleEntity | null

  @OneToOne(() => LicensePlatePurchaseEntity, (purchase) => purchase.device, { cascade: true })
  purchase: LicensePlatePurchaseEntity | null

  @OneToMany(() => LicensePlateStatusEntity, (status) => status.device, { cascade: true })
  status: LicensePlateStatusEntity[]
}

export type ILicensePlateDevice = Omit<LicensePlateDeviceEntity, keyof BaseEntity> & {
  product: ILicensePlateProduct | null
  sale: ILicensePlateSale | null
  purchase: ILicensePlatePurchase | null
  status: ILicensePlateStatus[]
}
