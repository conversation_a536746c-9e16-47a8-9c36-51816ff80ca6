import '~/configs'

import { setTimeout } from 'node:timers/promises'

import ms from 'ms'
import { DataSource } from 'typeorm'

import { ProductModelIndex, ProductSkuIndex } from '~/algolia-indices'
import { typeOrmConfig } from '~/configs'

import { algoliaReindexOptions } from '../utils'

console.info('Rebuilding algolia index, using parameters:')
Object.entries(algoliaReindexOptions).forEach(([key, value]) => {
  console.info(`- ${key}:`, value)
})

new DataSource({
  ...typeOrmConfig,
  dropSchema: false,
  logging: false,
})
  .initialize()
  .then(async () => {
    if (algoliaReindexOptions.model) {
      console.time(`Finished ${algoliaReindexOptions.purgeOnly ? 'purging' : 'rebuilding'} algolia index for model`)
      if (algoliaReindexOptions.purgeOnly) {
        await ProductModelIndex.clearObjects()
      } else {
        await ProductModelIndex.rebuild()
      }
      console.timeEnd(`Finished ${algoliaReindexOptions.purgeOnly ? 'purging' : 'rebuilding'} algolia index for model`)
      if (algoliaReindexOptions.sku) {
        console.info('Waiting for 1 minute before rebuilding sku index, because of the delay in algolia indexing')
        await setTimeout(ms('1 minute'))
      }
    }
    if (algoliaReindexOptions.sku) {
      console.time(`Finished ${algoliaReindexOptions.purgeOnly ? 'purging' : 'rebuilding'} algolia index for sku`)
      if (algoliaReindexOptions.purgeOnly) {
        await ProductSkuIndex.clearObjects()
      } else {
        await ProductSkuIndex.rebuild()
      }
      console.timeEnd(`Finished ${algoliaReindexOptions.purgeOnly ? 'purging' : 'rebuilding'} algolia index for sku`)
    }
    process.exit(0)
  })
