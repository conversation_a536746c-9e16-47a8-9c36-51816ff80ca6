import { Readable } from 'node:stream'

import {
  CopyObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3'
import { extension } from 'mime-types'

import { envConfig } from '~/configs'
import { FileBasePathMapMap, FileUsedInType } from '~/constants'

const client = new S3Client({
  region: envConfig.S3_REGION,
  credentials: { accessKeyId: envConfig.S3_ACCESS_KEY_ID, secretAccessKey: envConfig.S3_SECRET_ACCESS_KEY },
})

export const s3GetUrl = (filePath: string) => `${envConfig.S3_DOMAIN_URL}/${filePath}`

export const s3UploadFile = async (filePath: string, fileContent: Buffer | Readable) => {
  const params = {
    Bucket: envConfig.S3_BUCKET_NAME,
    Key: filePath,
    Body: fileContent,
  }
  return client.send(new PutObjectCommand(params))
}

export const s3DeleteFile = async (filePath: string) => {
  const params = {
    Bucket: envConfig.S3_BUCKET_NAME,
    Key: filePath,
  }
  return client.send(new DeleteObjectCommand(params))
}

export const s3DeleteFilesByPrefix = async (prefix: string) => {
  const params = {
    Bucket: envConfig.S3_BUCKET_NAME,
    Prefix: prefix,
  }

  const data = await client.send(new ListObjectsV2Command(params))
  if (data?.Contents?.length) {
    await client.send(
      new DeleteObjectsCommand({
        Bucket: envConfig.S3_BUCKET_NAME,
        Delete: { Objects: data.Contents.map((item) => ({ Key: item.Key })) },
      })
    )
  }
  return data
}

export const s3FileExists = async (filePath: string) => {
  const params = {
    Bucket: envConfig.S3_BUCKET_NAME,
    Key: filePath,
  }

  try {
    await client.send(new HeadObjectCommand(params))
    return true
  } catch (err) {
    if ((err as Error).name === 'NotFound') {
      return false
    }
    throw err
  }
}

export const s3ListFiles = async (directory?: string) => {
  const params = {
    Bucket: envConfig.S3_BUCKET_NAME,
    ...(directory && { Prefix: directory }),
  }
  const data = await client.send(new ListObjectsV2Command(params))
  return data.Contents
}

export const s3RenameFile = async (oldFilePath: string, newFilePath: string, overwrite = false) => {
  const oldParams = {
    Bucket: envConfig.S3_BUCKET_NAME,
    Key: oldFilePath,
  }

  const newParams = {
    Bucket: envConfig.S3_BUCKET_NAME,
    Key: newFilePath,
    CopySource: `${envConfig.S3_BUCKET_NAME}/${oldFilePath}`,
    ...(overwrite && ({ MetadataDirective: 'REPLACE' } as const)),
  }

  await client.send(new CopyObjectCommand(newParams))
  await client.send(new DeleteObjectCommand(oldParams))
}

export const s3GetEntityFolderPath = ({ fileUsedIn, entityId }: { fileUsedIn: FileUsedInType; entityId: string }) => {
  return `${FileBasePathMapMap[fileUsedIn]}/${entityId}/`
}

export const s3GetEntityFilePath = ({
  fileUsedIn,
  entityId,
  fileId,
  mimeType,
}: {
  fileUsedIn: FileUsedInType
  entityId: string
  fileId: string
  mimeType: string
}) => {
  const ext = extension(mimeType)
  return `${s3GetEntityFolderPath({ fileUsedIn, entityId })}${fileId + (ext ? `.${ext}` : '')}`
}

export const s3GetFileInfo = async (filePath: string) => {
  const params = {
    Bucket: envConfig.S3_BUCKET_NAME,
    Key: filePath,
  }
  return client.send(new HeadObjectCommand(params))
}
