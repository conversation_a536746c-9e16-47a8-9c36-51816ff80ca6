import { Field, ID, ObjectType } from '@nestjs/graphql'
import { subMonths } from 'date-fns'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  MoreThan,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { RECENT_ORDERS_SALES_LOOK_BACK_MONTHS } from '~/constants'
import {
  AccessoryProductEntity,
  BrandEntity,
  CategoryEntity,
  type IAccessoryProduct,
  type IBrand,
  type ICategory,
  type IImage,
  ImageEntity,
  type IOrderSkuItem,
  type IProductModelAttribute,
  type IProductModelAttributeCombination,
  type IProductModelMetrics,
  type IProductSeries,
  type IProductVariant,
  type IQuestionType,
  type ISaleItem,
  type ISeoContent,
  OrderSkuItemEntity,
  ProductModelAttributeCombinationEntity,
  ProductModelAttributeEntity,
  ProductModelMetricsEntity,
  ProductSeriesEntity,
  ProductSkuEntity,
  ProductVariantEntity,
  QuestionTypeEntity,
  SaleItemEntity,
  SeoContentEntity,
} from '~/entities'

@ObjectType('ProductModel')
@Entity('product_model')
export class ProductModelEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Index()
  @Column({ nullable: true })
  legacyId?: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Field()
  @Column({ default: true })
  nameIncludesBrandName: boolean

  @Field()
  @Column({ unique: true })
  slug__en: string

  @Field()
  @Column({ unique: true })
  slug__nl: string

  @Field()
  @Column({ unique: true })
  slug__de: string

  @Field()
  @Column({ unique: true })
  slug__pl: string

  @Field(() => ImageEntity)
  @OneToOne(() => ImageEntity, (image) => image.productModelImage, { nullable: false, cascade: true })
  @JoinColumn()
  image: Relation<ImageEntity>

  @Field()
  @Column({ default: false })
  recycle: boolean

  @Field()
  @Column({ type: 'date' })
  releaseDate: Date

  @ManyToOne(() => BrandEntity, (brand) => brand.productModels, {
    nullable: false,
  })
  brand: Relation<BrandEntity>

  @Column()
  @RelationId((productModel: ProductModelEntity) => productModel.brand)
  brandId: string

  @ManyToOne(() => CategoryEntity, (category) => category.productModels, {
    nullable: false,
  })
  category: Relation<CategoryEntity>

  @Column()
  @RelationId((productModel: ProductModelEntity) => productModel.category)
  categoryId: string

  @ManyToOne(() => ProductSeriesEntity, (productSeries) => productSeries.models, {
    nullable: false,
  })
  series: Relation<ProductSeriesEntity>

  @Column()
  @RelationId((productModel: ProductModelEntity) => productModel.series)
  seriesId: string

  @Field(() => [ProductModelAttributeEntity])
  @OneToMany(() => ProductModelAttributeEntity, (attribute) => attribute.model, {
    nullable: false,
    cascade: true,
  })
  attributes: Relation<ProductModelAttributeEntity>[]

  @OneToMany(() => ProductVariantEntity, (productVariant) => productVariant.model, {
    nullable: false,
  })
  variants: Relation<ProductVariantEntity>[]

  @OneToMany(() => ProductModelAttributeCombinationEntity, (combination) => combination.model, {
    nullable: false,
    cascade: true,
  })
  attributeCombinations: Relation<ProductModelAttributeCombinationEntity>[]

  @OneToMany(() => ProductSkuEntity, (sku) => sku.model, {
    nullable: false,
  })
  skus: Relation<ProductSkuEntity>[]

  @ManyToMany(() => AccessoryProductEntity, (accessoryProduct) => accessoryProduct.relatedProductModels, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinTable({ name: 'product_model_accessory_products' })
  accessoryProducts: Relation<AccessoryProductEntity>[]

  @ManyToOne(() => QuestionTypeEntity, (questionType) => questionType.productModels, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  questionType: Relation<QuestionTypeEntity>

  @Column()
  @RelationId((productModel: ProductModelEntity) => productModel.questionType)
  questionTypeId: string

  @OneToOne(() => ProductModelMetricsEntity, (metrics) => metrics.model, { nullable: false, cascade: true })
  @JoinColumn()
  metrics: Relation<ProductModelMetricsEntity>

  @Column()
  @RelationId((productModel: ProductModelEntity) => productModel.metrics)
  metricsId: string

  @Field(() => SeoContentEntity, { nullable: true })
  @OneToOne(() => SeoContentEntity, { nullable: true, cascade: true })
  @JoinColumn()
  seoContent?: Relation<SeoContentEntity>

  @Column({ nullable: true })
  @RelationId((collection: ProductModelEntity) => collection.seoContent)
  seoContentId?: string

  @OneToMany(() => SaleItemEntity, (saleItem) => saleItem.productModel, { nullable: false })
  saleItems: Relation<SaleItemEntity>[]

  @OneToMany(() => OrderSkuItemEntity, (orderSKuItem) => orderSKuItem.productModel, { nullable: false })
  orderItems: Relation<OrderSkuItemEntity>[]

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  @Column({ nullable: true, default: null })
  @Index()
  publishedAt?: Date | null

  static async getRecentSalesCount(modelId: string) {
    return SaleItemEntity.count({
      where: {
        productModelId: modelId,
        createdAt: MoreThan(subMonths(new Date(), RECENT_ORDERS_SALES_LOOK_BACK_MONTHS)),
      },
    })
  }

  static async getRecentOrdersCount(modelId: string) {
    return OrderSkuItemEntity.count({
      where: {
        productModelId: modelId,
        createdAt: MoreThan(subMonths(new Date(), RECENT_ORDERS_SALES_LOOK_BACK_MONTHS)),
      },
    })
  }
}

export type IProductModel = Omit<ProductModelEntity, keyof BaseEntity> & {
  accessoryProducts: IAccessoryProduct[]
  attributes: IProductModelAttribute[]
  variants: IProductVariant[]
  image: IImage
  brand: IBrand
  category: ICategory
  series: IProductSeries
  questionType: IQuestionType
  attributeCombinations: IProductModelAttributeCombination
  saleItems: ISaleItem[]
  orderItems: IOrderSkuItem[]
  seoContent?: ISeoContent
  metrics: IProductModelMetrics
}
