import { Injectable } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import type { EntitySubscriberInterface, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ProductModelIndex, ProductSkuIndex } from '~/algolia-indices'
import { ProductModelMetricsEntity, ProductSkuEntity } from '~/entities'

@Injectable()
@EventSubscriber()
export class ProductModelMetricsSubscriber implements EntitySubscriberInterface<ProductModelMetricsEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ProductModelMetricsEntity
  }

  async afterUpdate(event: UpdateEvent<ProductModelMetricsEntity>) {
    const { entity } = event

    if (entity?.id) {
      setImmediate(async () => {
        try {
          const metrics = await ProductModelMetricsEntity.findOne({ where: { id: entity.id } })
          const skus = await ProductSkuEntity.find({ where: { modelId: metrics.id }, select: { id: true } })
          const isMvpUpdate = metrics.isMvp === undefined ? {} : { isMvp: metrics.isMvp }
          const isBestsellerUpdate = metrics.isBestseller === undefined ? {} : { isBestseller: metrics.isBestseller }
          const recentOrdersCount =
            metrics.recentOrdersCount === undefined ? {} : { recentOrdersCount: metrics.recentOrdersCount }
          const recentSalesCount =
            metrics.recentSalesCount === undefined ? {} : { recentSalesCount: metrics.recentSalesCount }

          const modelIndexUpdate: Partial<ProductModelIndex['_model']> = {
            objectID: metrics.id,
            ...isMvpUpdate,
            ...isBestsellerUpdate,
            ...recentOrdersCount,
            ...recentSalesCount,
          }

          const skuIndexUpdates: Partial<ProductSkuIndex['_model']>[] = skus.map((sku) => ({
            objectID: sku.id,
            ...isMvpUpdate,
            ...isBestsellerUpdate,
            ...recentOrdersCount,
          }))
          if (!process.env.ALGOLIA_DISABLE_INDEXING) {
            await ProductModelIndex.partialUpdateObject(modelIndexUpdate)
            await ProductSkuIndex.partialUpdateObjects(skuIndexUpdates)
          }
        } catch (error) {
          Sentry.captureException(error, {
            tags: {
              type: 'product-model-metrics:update',
            },
            extra: {
              metricsId: entity.id,
            },
            level: 'warning',
          })
        }
      })
    } else {
      Sentry.captureException(new Error('ProductModelMetricsSubscriber.afterUpdate: metrics.id is missing'), {
        tags: {
          type: 'product-model-metrics:update',
        },
        extra: {
          metrics: entity,
        },
        level: 'warning',
      })
    }
  }
}
