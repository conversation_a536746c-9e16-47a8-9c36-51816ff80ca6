export type DataExchangeVariantImportDataType = {
  total: number | null
  processed: number | null
  channel: string
  started: Date | null
  filename: string
  finished: boolean
  failed: boolean
}

export type DataExchangeVariantExportDataType = {
  total: number | null
  processed: number | null
  channel: string
  started: Date | null
  finished: boolean
  failed: boolean
}

export type DataExchangeOrderExportDataType = {
  total: number | null
  processed: number | null
  started: Date | null
  finished: boolean
  failed: boolean
}

export type DataExchangeJob =
  | ({
      type: 'VARIANT_PRICE_IMPORT'
    } & DataExchangeVariantImportDataType)
  | ({
      type: 'VARIANT_PRICE_EXPORT'
    } & DataExchangeVariantExportDataType)
  | ({
      type: 'ORDER_EXPORT'
    } & DataExchangeOrderExportDataType)
  | ({
      type: 'USER_EXPORT'
    } & DataExchangeOrderExportDataType)
