import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  RelationId,
  UpdateDateColumn,
} from 'typeorm'

import { OrganizationBankAccountType } from '~/constants'
import { CharityEntity, type ICharity, type IPartner, PartnerEntity } from '~/entities'

@ObjectType('OrganizationBankAccount')
@Entity('organization_bank_account')
export class OrganizationBankAccountEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field(() => OrganizationBankAccountType)
  @Column({ type: 'varchar' })
  type: OrganizationBankAccountType

  @Field()
  @Column()
  holderName: string

  @Field()
  @Column()
  accountNumber: string

  @OneToOne(() => PartnerEntity, (partner) => partner.bankAccount, { nullable: true, onDelete: 'CASCADE' })
  partner?: Relation<PartnerEntity>

  @OneToOne(() => CharityEntity, (charity) => charity.bankAccount, { nullable: true, onDelete: 'CASCADE' })
  charity?: Relation<CharityEntity>

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type IOrganizationBankAccount = Omit<OrganizationBankAccountEntity, keyof BaseEntity> & {
  partner?: IPartner
  charity?: ICharity
}
