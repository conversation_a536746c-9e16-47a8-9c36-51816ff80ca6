import '@xyflow/react/dist/style.css'

import type { ILocaleCountry, IShippingConfig, IShippingMethod, IWarehouse } from '@valyuu/api/entities'
import {
  Connection,
  Controls,
  Edge,
  Node,
  ReactFlow,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from '@xyflow/react'
import clsx from 'clsx'
import { isEqual, omit, pick } from 'lodash'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useImmer } from 'use-immer'

import { ExtendedConnection, ExtendedNode } from '~/components'

import { GroupNodeWithLabel, WarehouseNode } from './components'
import { ShippingMethodModal } from './components'
import { createFlowEdges, createFlowNodes } from './factory'

export type BlockShippingConfigProps = {
  buyerCountries: ILocaleCountry[]
  sellerCountries: ILocaleCountry[]
  warehouses: IWarehouse[]
  value?: IShippingConfig[]
  buyerEnabled?: boolean
  sellerEnabled?: boolean
  onChange?: (value: Partial<IShippingConfig>[]) => void
  className?: string
}
import { useDeepCompareEffect } from 'use-deep-compare'

export type { ExtendedConnection, ExtendedNode } from './interfaces'

export const BlockShippingConfig = ({
  buyerCountries,
  sellerCountries,
  warehouses,
  value: originalShippingConfigs,
  onChange,
  className,
}: BlockShippingConfigProps) => {
  const [shippingConfigs, setShippingConfigs] = useImmer<Partial<IShippingConfig>[]>(originalShippingConfigs ?? [])
  const [nodes, setNodes, handleNodesChange] = useNodesState<Node>([])
  const [edges, setEdges, handleEdgesChange] = useEdgesState<Edge>([])

  const updatingRef = useRef<boolean>(false)

  useEffect(() => {
    if (originalShippingConfigs && !updatingRef.current) {
      setShippingConfigs(originalShippingConfigs)
    }
  }, [originalShippingConfigs, setShippingConfigs])

  const unfinishedNodes = useMemo(
    () =>
      nodes.filter((node) => {
        if (node.type !== 'input' && node.type !== 'output') {
          return false
        }
        if (!edges.some((edge) => edge.source === node.id || edge.target === node.id)) {
          node.style = {
            border: '1px solid #ef4444',
          }
          return true
        }
        if (node.style?.border) {
          delete node.style.border
        }
        return false
      }),
    [edges, nodes]
  )

  useEffect(() => {
    if (!isEqual(shippingConfigs, originalShippingConfigs) && !unfinishedNodes.length) {
      updatingRef.current = true
      onChange?.(shippingConfigs)
      setTimeout(() => {
        updatingRef.current = false
      })
    }
  }, [shippingConfigs, originalShippingConfigs, onChange, unfinishedNodes.length])

  const parseEdgeData = useCallback(
    ({ source, target }: { source: string; target: string }) => {
      const sourceNode = nodes.find(({ id }) => id === source)
      const targetNode = nodes.find(({ id }) => id === target)
      const isReturn = sourceNode?.data.nodeType === 'customer'
      const customerNode = isReturn ? sourceNode : targetNode
      const warehouseNode = isReturn ? targetNode : sourceNode

      const customerCountryId = (customerNode as ExtendedNode).data.countryId
      const warehouseCountryId = (warehouseNode as ExtendedNode).data.countryId
      const warehouseId = (warehouseNode as ExtendedNode).data.warehouseId as string

      const fromCountryId = isReturn ? customerCountryId : warehouseCountryId
      const toCountryId = isReturn ? warehouseCountryId : customerCountryId

      if (!customerCountryId || !warehouseCountryId || !warehouseId) {
        throw new Error('Invalid nodes data')
      }

      return {
        isReturn,
        customerCountryId,
        warehouseCountryId,
        warehouseId,
        fromCountryId,
        toCountryId,
      }
    },
    [nodes]
  )

  const isValidData = useMemo(
    () =>
      (buyerCountries.length || sellerCountries.length) &&
      warehouses.length &&
      warehouses.some((warehouse) => warehouse.receivingFromCountries.length > 0) &&
      warehouses.some((warehouse) => warehouse.shippingToCountries.length > 0),
    [buyerCountries, sellerCountries, warehouses]
  )

  const [modalCurrentConnection, setModalCurrentConnection] = useState<ExtendedConnection>()
  const modalDefaultSelectedMethodKeys = useMemo(
    () =>
      shippingConfigs
        .filter(
          (config) =>
            config.customerCountryId === modalCurrentConnection?.data.customerCountryId &&
            config.warehouseId === modalCurrentConnection?.data.warehouseId &&
            config.isReturn === modalCurrentConnection?.data.isReturn
        )
        .sort((a, b) => (a?.sortOrder ?? 0) - (b?.sortOrder ?? 0))
        .map((config) => config.shippingMethodId?.toString() ?? config.shippingMethod?.id.toString()),
    [shippingConfigs, modalCurrentConnection]
  )

  const handleConnect = (connection: Connection) => {
    const { isReturn, customerCountryId, warehouseId, fromCountryId, toCountryId } = parseEdgeData(connection)
    setModalCurrentConnection({
      ...connection,
      data: { fromCountryId, toCountryId, warehouseId, customerCountryId, isReturn },
    })
  }

  const handleModalOk = (shippingMethods: Omit<IShippingMethod, 'createdAt' | 'updatedAt'>[]) => {
    const connectionData = modalCurrentConnection!.data
    const existingConfigs = shippingConfigs.filter(
      (config) =>
        config.warehouseId === connectionData.warehouseId &&
        config.customerCountryId === connectionData.customerCountryId &&
        config.isReturn === connectionData.isReturn
    )
    const existingConfigsMapped = existingConfigs.map((config) => ({
      ...pick(config, ['warehouseId', 'customerCountryId', 'isReturn', 'sortOrder']),
      shippingMethodId: config.shippingMethodId ?? config.shippingMethod?.id,
    }))

    const newConfigsMapped = shippingMethods.map((method, index) => ({
      warehouseId: connectionData.warehouseId,
      customerCountryId: connectionData.customerCountryId,
      isReturn: connectionData.isReturn,
      shippingMethodId: method.id,
      sortOrder: index,
    }))

    if (!isEqual(existingConfigsMapped, newConfigsMapped)) {
      setShippingConfigs((draft) => {
        existingConfigs.forEach((config) => {
          const index = draft.findIndex((draftConfig) => isEqual(draftConfig, config))
          if (index !== -1) {
            draft.splice(index, 1)
          }
        })
        const addedConfigs = newConfigsMapped.map((config) => ({
          ...omit(config, 'shippingMethodId'),
          shippingMethod: shippingMethods.find((method) => String(method.id) === String(config.shippingMethodId)),
        }))
        draft.push(...addedConfigs)
      })
    }

    setModalCurrentConnection(undefined)
  }

  const handleModalCancel = () => {
    setModalCurrentConnection(undefined)
  }

  useDeepCompareEffect(() => {
    setNodes(createFlowNodes(buyerCountries, sellerCountries, warehouses))
    setEdges(createFlowEdges(shippingConfigs, warehouses))
  }, [buyerCountries, sellerCountries, warehouses, shippingConfigs])

  if (!isValidData) {
    return <div>No countries or warehouses supported for this channel. Please add them first.</div>
  }

  return (
    <>
      <div className={clsx('mt-4 h-[700px] min-h-[700px] w-full', className)}>
        <ReactFlowProvider>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onConnect={handleConnect}
            onNodesChange={handleNodesChange}
            onEdgesChange={handleEdgesChange}
            fitView
            maxZoom={1.3}
            minZoom={0.7}
            nodeTypes={{
              groupWithLabel: GroupNodeWithLabel,
              warehouse: WarehouseNode,
            }}
            isValidConnection={(connection) => {
              const { fromCountryId, toCountryId, customerCountryId, warehouseCountryId } = parseEdgeData(connection)
              return (
                (customerCountryId === fromCountryId && warehouseCountryId === toCountryId) ||
                (customerCountryId === toCountryId && warehouseCountryId === fromCountryId)
              )
            }}
            onEdgeClick={async (_event, edge) => {
              handleConnect(edge as Connection)
            }}
          >
            <Controls />
          </ReactFlow>
        </ReactFlowProvider>
        {modalCurrentConnection ? (
          <ShippingMethodModal
            currentConnection={modalCurrentConnection}
            defaultSelectedMethodKeys={modalDefaultSelectedMethodKeys}
            onOk={handleModalOk}
            onCancel={handleModalCancel}
          />
        ) : null}
      </div>
    </>
  )
}
