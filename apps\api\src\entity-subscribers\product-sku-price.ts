import { Injectable } from '@nestjs/common'
import type { EntitySubscriberInterface, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber } from 'typeorm'

import { ProductSkuIndex } from '~/algolia-indices'
import { ProductSkuPriceEntity } from '~/entities'

@Injectable()
@EventSubscriber()
export class ProductSkuPriceSubscriber implements EntitySubscriberInterface<ProductSkuPriceEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ProductSkuPriceEntity
  }

  async afterUpdate(event: UpdateEvent<ProductSkuPriceEntity>) {
    const { databaseEntity, manager } = event

    if (databaseEntity?.skuId) {
      await ProductSkuIndex.updateIndex({ id: databaseEntity.skuId, manager })
    }
  }
}
