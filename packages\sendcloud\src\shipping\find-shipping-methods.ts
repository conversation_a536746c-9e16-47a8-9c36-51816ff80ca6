import { sendcloudClientV2 } from '../client'
import { IShippingMethod } from '../interfaces'

export type IFindShippingMethodsParams = {
  /** Postal code of the sender. Required if the carrier is zonal (max 12 characters) */
  from_postal_code?: string
  /** If true, returns shipping methods which can be used for making a return shipment */
  is_return?: boolean
  /** The ID of the sender address. Use 'all' to retrieve all available shipping methods. Required if carrier is zonal */
  sender_address?: string | 'all'
  /** The ID of the service point for which you would like to know the available shipping methods */
  service_point_id?: number
  /** Country ISO 2 code for the recipient country. Required if carrier is zonal */
  to_country?: string
  /** Postal code of the recipient. Required if carrier is zonal (max 12 characters) */
  to_postal_code?: string
}

/**
 * Find available shipping methods based on provided parameters
 * @param params - Optional shipping method query parameters
 * @returns Promise with array of shipping methods
 */
export const findShippingMethods = async (params: IFindShippingMethodsParams = {}): Promise<IShippingMethod[]> => {
  const { data } = await sendcloudClientV2.get('/shipping-methods', {
    params,
  })

  return data.shipping_methods
}
