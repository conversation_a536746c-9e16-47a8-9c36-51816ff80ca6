import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { OrderSkuItemEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudOrderSkuItemService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: OrderSkuItemEntity,
  },
  params: { id: { field: 'id', type: 'uuid', primary: true } },
  query: {
    join: {
      channel: {},
      currency: {},
      warranty: {},
      order: {},
      productSku: {},
      productModel: {},
    },
  },
})
@Controller('admin/order-sku-items')
export class CrudOrderSkuItemController implements CrudController<OrderSkuItemEntity> {
  constructor(public service: CrudOrderSkuItemService) {}
}
