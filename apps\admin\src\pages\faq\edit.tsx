import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useUpdate } from '@refinedev/core'
import { FaqCollectionType } from '@valyuu/api/constants'
import type { IFaq } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, InputNumber, Select, TextArea, Watch } from 'antx'
import cx from 'clsx'
import dayjs from 'dayjs'
import { type FC, useState } from 'react'

import { InputLanguageTab, InputMultiLang, InputPublish, LanguageTabChoices } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const FaqEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, form, id, saveButtonProps, query, formLoading } = useForm<IFaq, HttpError, IFaq>()

  const record = query?.data?.data
  if (formProps.initialValues?.publishedAt) {
    formProps.initialValues.publishedAt = new Date(formProps.initialValues.publishedAt)
  }
  if (formProps.initialValues?.date) {
    formProps.initialValues.date = dayjs(new Date(formProps.initialValues.date))
  }
  const { mutate } = useUpdate<IFaq, HttpError, Partial<IFaq>>()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
      footerButtons={({ defaultButtons }) => (
        <InputPublish
          isPublished={!!record?.publishedAt}
          onChange={(value) => {
            if (value) {
              form.setFieldValue('publishedAt', new Date())
            } else {
              form.setFieldValue('publishedAt', null)
            }
            mutate({
              resource: 'admin/faqs',
              id: id! as string,
              values: {
                publishedAt: value ? new Date() : null,
              },
              successNotification: (data) => {
                return {
                  type: 'success',
                  message: `Faq has been ${value ? 'published' : 'unpublished'} successfully`,
                }
              },
              errorNotification: () => {
                return {
                  type: 'error',
                  message: `Failed to ${value ? 'publish' : 'unpublish'} testimonial`,
                }
              },
            })
          }}
        >
          {defaultButtons}
        </InputPublish>
      )}
    >
      <Form {...formProps} layout="vertical">
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Select
          label="Collections"
          name="collections"
          mode="multiple"
          style={{ minWidth: 200 }}
          options={formatSelectOptionsFromEnum(FaqCollectionType)}
          placeholder="Collections"
          rules={['required']}
        />
        <Watch
          list={[
            ['collections', 'question__en'],
            ['collections', 'question__nl'],
            ['collections', 'question__de'],
            ['collections', 'question__pl'],
          ]}
        >
          {([question__en, question__nl, question__de, question__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                targetLang="en"
                label="Question (English)"
                name="question__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                targetLang="nl"
                label="Question (Dutch)"
                name="question__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                targetLang="de"
                label="Question (German)"
                name="question__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputMultiLang
                sources={{ en: question__en, nl: question__nl, de: question__de, pl: question__pl }}
                targetLang="pl"
                label="Question (Polish)"
                name="question__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
            </>
          )}
        </Watch>
        <TextArea label="Description (English)" name="answer__en" rows={3} className={clsHideEn} />
        <TextArea label="Description (Dutch)" name="answer__nl" rows={3} className={clsHideNl} />
        <TextArea label="Description (German)" name="answer__de" rows={3} className={clsHideDe} />
        <TextArea label="Description (Polish)" name="answer__pl" rows={3} className={clsHidePl} />
        <InputNumber label="Sort order" precision={0} name="sortOrder" rules={['required']} />
        <Input noStyle type="datetime" name="publishedAt" hidden />
      </Form>
    </Edit>
  )
}
