import { Create, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps } from '@refinedev/core'
import { OrganizationBankAccountType } from '@valyuu/api/constants'
import type { IOrganizationBankAccount } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, Select } from 'antx'
import type { FC } from 'react'

import { formatSelectOptionsFromEnum } from '~/utils'

export const OrganizationBankAccountCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, form, saveButtonProps, formLoading } = useForm<
    IOrganizationBankAccount,
    HttpError,
    IOrganizationBankAccount
  >()

  return (
    <Create isLoading={formLoading} saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Select
          label="Type"
          name="type"
          rules={['required']}
          options={formatSelectOptionsFromEnum(OrganizationBankAccountType)}
        />
        <Input label="Beneficiary name" name="holderName" rules={['required']} />
        <Input label="IBAN number" name="accountNumber" rules={['required']} />
      </Form>
    </Create>
  )
}
