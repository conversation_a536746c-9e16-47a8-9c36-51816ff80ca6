## 📚 Table of Contents

- [Quick Start](#-quick-start)
- [Deployment](#-deployment)
- [Core Features](#️-core-features)
- [Project Structure](#-project-structure)
- [Database Entities](#-database-entities)
- [Entity Relationship Diagrams](#-entity-relationship-diagrams)
- [Technology Stack](#-technology-stack)
- [GraphQL Resolvers](#-graphql-resolvers)
- [Database Migrations](#-database-migrations)
- [Available Scripts](#-available-scripts)
- [API Documentation](#-api-documentation)
- [Contributing](#-contributing)
- [Code Style](#-code-style)
- [Support](#-support)

## 🔗 Project URLs

- **Production API:** https://api.valyuu.com
- **Staging API:** https://api-staging.valyuu.tech
- **Git Repository:** https://github.com/Valyuu/valyuu

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL
- Redis
- AWS S3 bucket
- Cloudinary account
- SendGrid account
- Algolia account
- Sentry account
- <PERSON>e account

### Installation

1. Install dependencies:
```bash
pnpm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Update the .env file with your credentials
```

3. Start development server:
```bash
pnpm dev
```

## 🚀 Deployment

### Local Development Environment
1. Clone the repository

2. Install and start PostgreSQL

3. Install and start Redis

4. Install dependencies:
   ```bash
   pnpm install
   ```

5. Set up environment variables:
   ```bash
   cp .env.example .env
   # Update the .env file with your credentials
   ```

6. Set up local database:
   - Create a new database
   - Import database dump from staging server

7. Start development server:
   ```bash
   pnpm dev
   ```

> 💡 **Database Connection**
> Make sure your .env file has the correct database connection settings:
> ```
> DATABASE_URL=postgresql://postgres:postgres@localhost:5432/valyuu_refine
> REDIS_HOST=127.0.0.1
> REDIS_PORT=6379
> ```

### Staging Server
- Automated deployment through GitHub Actions
- Triggered on commits to the `staging` branch
- Deployment process:
- 1. Install pnpm modules (if any)
  2. Run database migrations `pnpm migration:run`
  3. Build and run the application
  4. Restart `pm2`
- Access: https://api-staging.valyuu.tech

### Production Server
- Manual deployment process
- Steps:
  1. Install pnpm modules (if any)
  2. Run database migrations `pnpm migration:run`
  3. Build and run the application
  4. Restart `pm2`
  5. Handle any edge cases manually
- Access: https://api.valyuu.com

> ⚠️ **Important Notes**
> - Always test changes in staging before deploying to production
> - Keep database dumps up to date
> - Monitor logs for any issues on Sentry
> - Follow the deployment checklist for production releases

## 🛠️ Core Features

### Product Management
- **Product Models & Variants**
  - Complex product model system with attributes and combinations
  - SKU management with price variations
  - Product testing and quality assessment
  - Warranty management per category
  - Accessory product handling

- **Inventory & Stock**
  - Multi-warehouse support
  - Stock tracking and management
  - Pre-order system
  - Tested item tracking

### Sales & Orders
- **Order Processing**
  - Multi-currency support
  - Complex pricing system with thresholds
  - Order status tracking
  - Accessory product bundling
  - Extra payment handling

- **Sales Management**
  - Sale item offers
  - Sale change logging
  - Sale summaries and analytics
  - Marketing collections and banners

### Partner Integration
- **Partner System**
  - Partner API authentication
  - Webhook management with retry system
  - Bank account management
  - Payment period tracking
  - API logging and monitoring

### Shipping & Logistics
- **Shipment Management**
  - Multiple shipping methods
  - Shipping configuration
  - Shipment tracking
  - Order fulfillment

### Marketing & Content
- **Content Management**
  - Blog system with authors and tags
  - FAQ management
  - Customer reviews
  - Testimonials
  - SEO content optimization
  - Marketing tags and collections

### Localization
- **Multi-language Support**
  - Language management
  - Currency handling
  - Country-specific settings
  - Locale-based content

### Financial Management
- **Payment Processing**
  - Stripe integration
  - Bank payout system
  - Payment period tracking
  - Extra payment handling
  - Financial logging

## 📁 Project Structure

```
src/
├── algolia-indices/   # Search configurations and indices
├── configs/           # App configurations and environment setup
├── constants/         # App-wide constants and enums
├── controllers/       # REST endpoints and request handlers
├── decorators/        # Custom decorators for metadata and validation
├── dtos/              # Data transfer objects and validation schemas
├── emails/            # Email templates and email service
├── entities/          # Database models and TypeORM entities
├── entity-subscribers/ # Database event handlers and hooks
├── files/             # File handling and storage services
├── guards/            # Authentication and authorization guards
├── i18n/              # Internationalization and translations
├── interfaces/        # TypeScript interfaces and types
├── modules/           # Feature modules
│   ├── admin/         # Admin panel functionality
│   └── partner/       # Partner integration system
├── resolvers/         # GraphQL resolvers
├── services/          # Business logic and data processing
├── utils/             # Helper functions and utilities
└── workers/           # Background jobs and queue processors
```

### `algolia-indices/`
Configuration for Algolia search indices.
- Index definition and settings
- Field mappings
- Search rules and filters
- Ranking and relevance configuration

### `configs/`
Application configuration and setup.
- Environment variables validation
- Database connection settings
- Redis configuration
- External service credentials
- JWT and authentication setup

### `constants/`
Application-wide constants and enumerations.
- Status enums
- Default values
- Configuration constants
- Feature flags
- Type definitions

### `controllers/`
REST API endpoints using NestJS controllers.
- Route definitions
- Request validation
- Response serialization
- API versioning
- Resource management

### `decorators/`
Custom decorators for metadata and validation.
- Authentication decorators
- Role-based access control
- Request metadata
- Parameter validation
- Utility decorators

### `dtos/`
Data Transfer Objects for validation and serialization.
- Input validation schemas
- Response serialization objects
- GraphQL input types
- Query parameter validation
- Nested validation objects

### `emails/`
Email templates and email service.
- Template rendering with Handlebars
- MJML responsive email templates
- Email localization
- Template variables
- Email sending service

### `entities/`
Database models and TypeORM entities.
- Table definitions
- Column types and constraints
- Entity relationships
- Indexes and unique constraints
- Database triggers

### `entity-subscribers/`
Database event handlers and hooks.
- Before/after insert hooks
- Update listeners
- Soft delete handling
- Cascading operations
- Data transformation

### `files/`
File handling and storage services.
- File upload/download
- S3 integration
- Cloudinary integration
- File validation
- Image processing

### `guards/`
Authentication and authorization guards.
- JWT authentication
- Role-based access
- API key validation
- Rate limiting
- Permission checking

### `i18n/`
Internationalization and translations.
- Translation files
- Language detection
- Message formatting
- Pluralization rules
- Date and number formatting

### `interfaces/`
TypeScript interfaces and types.
- Common interfaces
- API response types
- Service interfaces
- Configuration interfaces
- Utility types

### `modules/`
Feature modules for different parts of the application.
- Admin module
- Partner integration
- Authentication
- Payments
- Shipping
- Products

### `resolvers/`
GraphQL resolvers for the API.
- Query resolvers
- Mutation resolvers
- Subscription resolvers
- Field resolvers
- Data loading optimization

### `services/`
Business logic and data processing.
- Data access and manipulation
- Business rules implementation
- External service integration
- Caching strategies
- Transaction management

### `utils/`
Helper functions and utilities.
- String manipulation
- Date handling
- Currency formatting
- Object transformation
- Security utilities

### `workers/`
Background jobs and queue processors.
- Email sending
- Webhook delivery
- Report generation
- Data synchronization
- Scheduled tasks


## 📊 Database Entities

The system is built around the following core entities that represent the data model. Each entity corresponds to a database table managed through TypeORM.

Entities are located in the `apps/api/src/entities` directory, with most entities following the pattern of `entity-name.table.ts` for their file naming.

### User & Authentication
- **AdminUser** - System administrators with privileged access
- **User** - End customers who buy and sell products
- **UserSummary** - Aggregated view of user activity and statistics

### Product Catalog
- **Brand** - Product manufacturers
- **Category** - Product classifications and hierarchy
- **ProductModel** - Base product specification (e.g., iPhone 13)
- **ProductModelAttribute** - Configurable aspects of a model (e.g., storage, color)
- **ProductModelAttributeOption** - Available choices for attributes
- **ProductModelAttributeCombination** - Valid combinations of attributes
- **ProductSKU** - Specific product variations with unique identifiers
- **ProductVariant** - Detailed product specifications and pricing rules
- **ProductSeries** - Product groupings across generations
- **AccessoryProduct** - Additional items that complement main products

### Sales & Transactions
- **Sale** - Customer sales of used products
- **SaleItem** - Individual items within a sale
- **SaleItemOffer** - Offers made on sale items
- **SaleChangeLog** - History of changes to sales
- **SaleItemChangeLog** - History of changes to sale items
- **SaleSummary** - Aggregated sales statistics
- **Order** - Customer purchases of products
- **OrderSummary** - Aggregated order statistics
- **OrderSKUItem** - Individual SKU items in an order
- **OrderAccessoryProductItem** - Accessories included in an order
- **OrderExtraPayment** - Additional payments related to orders
- **PreOrder** - Advance reservations for products

### Financial
- **BankAccount** - Customer payment information
- **SaleItemStripePayment** - Stripe payment records for sold items
- **OrganizationBankAccount** - Company bank accounts for different purposes
- **SellerPaymentPeriod** - Payment schedules for sellers

### Testing & Condition
- **TestedItem** - Products that have been inspected
- **TestedItemList** - Collections of tested items
- **QuestionType** - Assessment criteria categories
- **QuestionTypeCondition** - Specific condition assessment questions
- **QuestionTypeConditionOption** - Available answers for condition questions
- **QuestionTypeProblem** - Problem identification questions
- **ProductVariantProblem** - Known issues for product variants
- **ProductVariantProblemPrice** - Price adjustments based on problems
- **ProductVariantConditionCombination** - Valid condition combinations
- **ProductVariantConditionCombinationPrice** - Pricing based on condition combinations

### Marketing & Content
- **MarketingBanner** - Promotional banners for website/app
- **MarketingSKUCollection** - Curated product collections
- **MarketingTag** - Tags for product categorization
- **BlogPost** - Content marketing articles
- **BlogAuthor** - Content writers
- **BlogTag** - Article categorization
- **FAQ** - Frequently asked questions
- **Testimonial** - Customer success stories
- **CustomerReview** - Product and service reviews
- **SEOContent** - Search engine optimization content
- **FrontpageMisc** - Miscellaneous content for homepage

### Logistics
- **Warehouse** - Stock storage locations
- **Stock** - Inventory levels
- **ShippingMethod** - Available shipping options
- **ShippingConfigEntity** - Configuration for shipping services
- **ShipmentEntity** - Tracking information for deliveries
- **Address** - Customer and warehouse addresses

### Partner System
- **Partner** - B2B integration partners
- **PartnerWebhookRetry** - Failed webhook delivery tracking
- **ApiLog** - API request logging

### Miscellaneous
- **Image** - Image assets with metadata
- **File** - Stored files (documents, etc.)
- **Charity** - Charitable organizations for donations
- **Setting** - System configuration parameters
- **EmailHistory** - Record of sent emails
- **Channel** - Sales and marketing channels
- **PriceThreshold** - Price breakpoints for special handling
- **Warranty** - Product warranty information
- **CategoryWarrantyRule** - Warranty rules by product category
- **Migration** - Database migration tracking

### Localization
- **LocaleCountry** - Country information
- **LocaleCurrency** - Currency settings
- **LocaleLanguage** - Supported languages

## 📊 Entity Relationship Diagrams

Below is a simplified representation of the key entity relationships within the system:

### Product Catalog Hierarchy
```mermaid
flowchart TD
    Brand --> ProductModel
    ProductSeries --> ProductModel
    Category --> ProductModel
    
    ProductModel --> ProductModelAttribute
    ProductModel --> ProductVariant
    
    ProductModelAttribute --> ProductModelAttributeOption
    
    ProductModelAttribute --> ProductModelAttributeCombination
    ProductModelAttributeOption --> ProductModelAttributeCombination
    
    ProductModelAttributeCombination --> ProductVariant
    
    class Brand,Category,ProductSeries,ProductModel,ProductVariant,ProductSKU,ProductModelAttribute,ProductModelAttributeOption,ProductModelAttributeCombination,Stock entity;
```

#### Entity Explanations

##### User Flow
- **User**: Central entity that can both buy and sell products. Users have addresses, bank accounts, orders, and sales.
- **Address**: User addresses for shipping and billing, with country information.
- **BankAccount**: User bank accounts for receiving payments from sales.

##### Sales Flow
- **Sale**: Represents a transaction where a user sells products to Valyuu. Has a status, payment info, and references to sale items.
- **SaleItem**: Individual items being sold in a sale. References the specific product SKU and variant.
- **SaleItemOffer**: Offers made to users for their sale items, with expiration date and auth token.

##### Order Flow
- **PreOrder**: Preliminary order with payment intent, containing cart items before final order creation.
- **Order**: Represents a purchase transaction by a user. Contains order number, status, shipping info, etc.
- **OrderSKUItem**: Individual product SKU items in an order. Contains pricing, warranty, and reference to the product.
- **OrderAccessoryProductItem**: Accessory items included in an order (separate from main product SKUs).

##### Product Structure
- **ProductSKU**: Specific product instance (Stock Keeping Unit) with unique attributes, price, condition, etc.
- **ProductSkuPrice**: Price information for a SKU per channel/currency.
- **ProductSkuOriginalAccessory**: Original accessories that come with specific product SKUs.
- **ProductVariant**: Variation of a product model with specific attributes (e.g., color, storage).
- **ProductModel**: Base model of a product (e.g., iPhone 14).
- **Category**: Grouping of similar product models (e.g., Smartphones).
- **Stock**: Inventory tracking for product SKUs with warehouse location.

##### Warranty System
- **Warranty**: Defines warranty terms, period, and pricing.
- **CategoryWarrantyRule**: Rules for what warranties apply to which categories and conditions.
- **CategoryWarrantyRuleWarrantyItem**: Individual warranty items that can be applied based on category rules.

##### Shipping & Warehouse
- **Warehouse**: Physical locations where products are stored.
- **Shipment**: Tracks the shipping of products for both orders and returns.
- **ShippingMethod**: Available shipping options and carriers.
- **ShippingConfig**: Configuration for shipping methods between warehouses and countries.

### Sales & Order Flow
```mermaid
flowchart TD
    %% User Entities
    User --> Sale
    User --> Order
    User --> Address
    User --> BankAccount
    
    %% Sale Flow
    Sale --> SaleItem
    SaleItem --> ProductSKU
    SaleItem --> ProductVariant
    SaleItem --> ProductModel
    SaleItem --> SaleItemOffer
    Sale --> Shipment
    
    %% Order Flow
    Order --> OrderSKUItem
    Order --> OrderAccessoryProductItem
    PreOrder --> Order
    PreOrder --> ProductSKU
    
    %% Product Structure
    ProductSKU --> Stock
    ProductSKU --> ProductVariant
    ProductVariant --> ProductModel
    ProductModel --> Category
    ProductSKU --> ProductSkuPrice
    ProductSKU --> ProductSkuOriginalAccessory
    Category --> CategoryWarrantyRule
    CategoryWarrantyRule --> CategoryWarrantyRuleWarrantyItem
    CategoryWarrantyRuleWarrantyItem --> Warranty
    
    %% Shipping & Warehouse
    Stock --> Warehouse
    Shipment --> ShippingMethod
    Shipment --> Warehouse
    ShippingConfig --> ShippingMethod
    ShippingConfig --> Warehouse
    
    %% Addressing
    Order --> Address
    Sale --> Address
    
    class User,Sale,SaleItem,ProductSKU,Stock,Warehouse,Order,OrderSKUItem,OrderAccessoryProductItem,PreOrder,ProductVariant,ProductModel,Category,Shipment,ShippingMethod,ShippingConfig,Address,BankAccount,SaleItemOffer,ProductSkuPrice,ProductSkuOriginalAccessory,CategoryWarrantyRule,CategoryWarrantyRuleWarrantyItem,Warranty entity;
```

#### Entity Explanations

##### User
- **User**: Central entity that can both buy and sell products. Users have addresses, bank accounts, orders, and sales.

##### Sales
- **Sale**: Represents a transaction where a user sells products to Valyuu. Has a status, payment info, and references to sale items.
- **SaleItem**: Individual items being sold in a sale. References the specific product SKU and variant.
- **SaleItemOffer**: Each problem order (after receiving the trade-in product and it is being tested, the actual condition does not match the condition the user claimed to be in) will be a SaleItemOffer. Offers made to users for their sale items, with expiration date and auth token.

##### Order
- **PreOrder**: Preliminary order with payment intent, containing cart items before final order creation.
- **Order**: Represents a purchase transaction by a user. Contains order number, status, shipping info, etc.
- **OrderSKUItem**: Individual product SKU items in an order. Contains pricing, warranty, and reference to the product.
- **OrderAccessoryProductItem**: Accessory items included in an order (separate from main product SKUs).

##### Product Structure
- **ProductSKU**: Specific product instance (Stock Keeping Unit) with unique attributes, price, condition, etc. Orders are made of ProductSKU items.
- **ProductSkuPrice**: Price information for a SKU per channel/currency.
- **ProductSkuOriginalAccessory**: Original accessories that come with specific product SKUs.
- **ProductVariant**: Variation of a product model with specific attributes (e.g., color, storage). Sales are made of ProductVariant items.
- **ProductModel**: Base model of a product (e.g., iPhone 14).
- **Category**: Grouping of similar product models (e.g., Smartphones).
- **Stock**: It is currently only used to generate the StockNumber in the system, and no other usage.

##### Warranty System
- **Warranty**: Defines warranty terms, period, and pricing.
- **CategoryWarrantyRule**: Rules for what warranties apply to which categories and conditions.
- **CategoryWarrantyRuleWarrantyItem**: Individual warranty items that can be applied based on category rules.

##### Shipping & Warehouse
- **Warehouse**: Physical locations where products are stored.
- **Shipment**: Tracks the shipping of products for both orders and returns.
- **ShippingMethod**: Available shipping options and carriers.
- **ShippingConfig**: Configuration for shipping methods between warehouses and countries.

##### Address Management
- **Address**: User addresses for shipping and billing, with country information.
- **BankAccount**: User bank accounts for receiving payments from sales.

### Key Relationships
- Users create both Sales (when selling) and Orders (when buying)
- ProductSKUs are the central product entity referenced by both Sale Items and Order Items
- Warehouses store Stock which tracks inventory of ProductSKUs
- Shipments handle the logistics for both Orders (sending products) and returns
- Warranty items are linked to product categories through warranty rules

This diagram illustrates the complete flow from product categorization through sales and orders to shipping and delivery.

## 🔧 Technology Stack

### Core Framework
- **[NestJS](https://nestjs.com/)** - A progressive Node.js framework for building efficient, reliable, and scalable server-side applications.
  - Uses modern TypeScript
  - Modular architecture with dependency injection
  - Request lifecycle middleware
  - Framework-agnostic design

### Database and ORM
- **[TypeORM](https://typeorm.io/)** - ORM for TypeScript and JavaScript.
  - Entity models with decorators
  - Relations (One-to-one, One-to-many, Many-to-many)
  - Migrations system
  - ActiveRecord pattern

- **[PostgreSQL](https://www.postgresql.org/)** - Advanced open-source relational database.
  - JSONB support for complex data structures
  - Strong indexing capabilities
  - Transactions and ACID compliance

### API Development
- **[@dataui/crud](https://github.com/dataui/crud)** (formerly @nestjsx/crud) - Create CRUD controllers and services with ease.
  - Automatic CRUD operations
  - Query parsing for filtering, sorting, and pagination
  - Customizable endpoints
  - Documentation: [GitHub Wiki](https://github.com/dataui/crud/wiki)

- **[Swagger](https://swagger.io/docs/)** - API documentation.
  - Implemented with [@nestjs/swagger](https://docs.nestjs.com/openapi/introduction)
  - Interactive API explorer
  - Request/response schema validation
  - Authentication documentation

- **[GraphQL](https://graphql.org/)** - Query language for APIs.
  - Implemented with [@nestjs/graphql](https://docs.nestjs.com/graphql/quick-start)
  - Code-first approach with decorators
  - Dataloader for efficient data fetching
  - Complexity management

## 📊 GraphQL Resolvers

The following GraphQL resolvers are exclusively used for the Frontend site. These resolvers power the consumer-facing application and are not used for admin or partner interfaces.

### Product Related Resolvers

- **ProductModelResolver** (`product-model.ts`)
  - `getProductModelSellerQuestions`
    - **Parameters**: 
      - `lang`: Language code (string) - e.g., "en", "de"
      - `slug`: Product model slug (string) - e.g., "iphone-12-pro"
    - **Returns**: List of questions with answers for assessing product conditions
    - **Usage**: Used in the product selling flow to gather information about the product's condition from the seller
    - **Example call**:
      ```graphql
      query {
        getProductModelSellerQuestions(lang: "en", slug: "iphone-12-pro") {
          questions {
            id
            text
            options {
              id
              text
              value
            }
          }
        }
      }
      ```

  - `getProductModelSellerPrices`
    - **Parameters**:
      - `channelId`: Sales channel identifier (string) 
      - `modelId`: Product model ID (string)
      - `variantId`: Product variant ID (string)
      - `type`: Question type - "CONDITION" or "PROBLEM" (enum)
      - `problems`: List of problem IDs (string[]) - only when type is "PROBLEM"
      - `conditions`: List of condition values (string[]) - only when type is "CONDITION"
    - **Returns**: Pricing information for both C2C and C2B sales channels, payment periods, and environmental impact data
    - **Usage**: Used to display selling price estimates on the product selling pages
    - **Implementation note**: Dynamically calculates prices based on either condition or problems using variant service

- **ProductSkuResolver** (`product-sku.ts`)
  - `getProductSkuBySlug`
    - **Parameters**:
      - `lang`: Language code (string)
      - `slug`: Product SKU slug (string)
    - **Returns**: Detailed product SKU information including pricing, availability, and specifications
    - **Usage**: Used on product detail pages to show specific variant information
    - **Implementation note**: Uses GraphQL info parameter for optimized relationship loading

  - `getRelatedProductSkus`
    - **Parameters**:
      - `skuId`: Product SKU ID (string)
      - `limit`: Maximum number of results (number, optional)
    - **Returns**: List of related product SKUs
    - **Usage**: Used in "You might also like" sections on product pages

- **ProductIssueResolver** (`product-issue.ts`)
  - `getProductIssues`
    - **Parameters**:
      - `categoryId`: Product category ID (string)
      - `lang`: Language code (string)
    - **Returns**: List of possible product issues for the category
    - **Usage**: Used in product diagnostics and condition assessment forms
    - **Example call**:
      ```graphql
      query {
        getProductIssues(categoryId: "smartphone", lang: "en") {
          id
          name
          description
          severityLevel
        }
      }
      ```

### Sales and Orders

- **SaleResolver** (`sale.ts`)
  - `getSalesByStatus`
    - **Parameters**:
      - `status`: Sale status (enum) - "ACTIVE", "UPCOMING", "ENDED"
      - `channelId`: Sales channel ID (string)
    - **Returns**: List of sales matching the status
    - **Usage**: Used to display active promotions on homepage and category pages

- **SaleItemOfferResolver** (`sale-item-offer.ts`)
  - `getSaleItemOfferDetails`
    - **Parameters**:
      - `offerId`: Offer ID (string)
      - `authToken`: Authentication token (string)
    - **Returns**: Detailed information about a sale item offer with validation status
    - **Usage**: Used in the seller portal when customers receive offers for their items
    - **Implementation note**: Performs extensive validation to ensure the offer is valid, unexpired, and authorized

  - `confirmSaleItemOffer`
    - **Parameters**:
      - `offerId`: Offer ID (string)
      - `authToken`: Authentication token (string)
      - `confirmationType`: Type of confirmation (enum) - "ACCEPT", "DECLINE_RECYCLE", "DECLINE_RETURN"
    - **Returns**: Confirmation result with success status
    - **Usage**: Used when sellers accept or decline offers for their products
    - **Security note**: Requires valid authToken to prevent unauthorized confirmations

- **OrderResolver** (`order.ts`)
  - `getOrderCartItemPrices`
    - **Parameters**:
      - `channelId`: Sales channel ID (string)
      - `skuItems`: Array of SKUs with quantities [{skuId: string, quantity: number}]
      - `accessoryItems`: Array of accessory items with quantities (optional)
    - **Returns**: Calculated prices including subtotal, taxes, shipping, and discounts
    - **Usage**: Used in the shopping cart to calculate and display order totals
    - **Example call**:
      ```graphql
      query {
        getOrderCartItemPrices(
          channelId: "eu-de",
          skuItems: [{skuId: "iphone-12-64gb", quantity: 1}],
          accessoryItems: [{skuId: "iphone-12-case", quantity: 1}]
        ) {
          subtotal
          tax
          shipping
          discount
          total
          currency {
            code
            symbol
          }
        }
      }
      ```

  - `createPaymentIntent`
    - **Parameters**:
      - `channelId`: Sales channel ID (string)
      - `items`: Array of items to purchase with quantities and prices
      - `customerInfo`: Customer contact and shipping information
    - **Returns**: Stripe payment intent with client secret
    - **Usage**: Used in checkout flow to initialize the payment process
    - **Implementation**: Integrates with Stripe API to create a payment intent

  - `createPreOrder`
    - **Parameters**:
      - `paymentIntentId`: Stripe payment intent ID (string)
      - `customerInfo`: Customer information for the order
    - **Returns**: Pre-order confirmation with order reference
    - **Usage**: Used to create a preliminary order before payment confirmation
    - **Implementation note**: Stores order information and links it to the payment intent

  - `getOrderResult`
    - **Parameters**:
      - `paymentIntent`: Payment intent ID (string)
    - **Returns**: Order result with status and details
    - **Usage**: Used on the order confirmation page after payment
    - **Security note**: Only returns limited information for verification purposes

### Content Management

- **BlogPostResolver** (`blog.ts`)
  - `getBlogPosts`
    - **Parameters**: None required (uses GraphQL info for relations)
    - **Returns**: Array of blog posts with related content
    - **Usage**: Used on the blog listing page
    - **Implementation note**: Uses `GraphRelationBuilder` to optimize database queries based on GraphQL query fields
    - **Example call**:
      ```graphql
      query {
        getBlogPosts {
          id
          title
          slug
          summary
          publishedAt
          author {
            name
            avatar
          }
          tags {
            name
            slug
          }
        }
      }
      ```

  - `getBlogPost`
    - **Parameters**:
      - `lang`: Language code (string)
      - `slug`: Blog post slug (string)
    - **Returns**: Single blog post with all related content
    - **Usage**: Used on individual blog post pages
    - **Implementation note**: Also uses relation optimization for efficient loading

- **BrandResolver** (`brand.ts`)
  - `getBrandBySlug`
    - **Parameters**:
      - `slug`: Brand slug (string)
    - **Returns**: Brand details including logo, description, and featured products
    - **Usage**: Used on brand pages and in filters
    - **Example call**:
      ```graphql
      query {
        getBrandBySlug(slug: "apple") {
          id
          name
          description
          logo
          featuredProducts {
            id
            name
            slug
            thumbnail
          }
        }
      }
      ```

- **CategoryResolver** (`category.ts`)
  - `getCategoriesByParent`
    - **Parameters**:
      - `parentId`: Parent category ID (string, optional)
      - `lang`: Language code (string)
    - **Returns**: List of categories with hierarchy information
    - **Usage**: Used for navigation menus and category browsing
    - **Implementation note**: Recursively fetches subcategories when requested

- **FaqResolver** (`faq.ts`)
  - `getFaqs`
    - **Parameters**:
      - `lang`: Language code (string)
      - `category`: FAQ category (string, optional)
    - **Returns**: List of FAQs grouped by category
    - **Usage**: Used in help/support sections
    - **Example call**:
      ```graphql
      query {
        getFaqs(lang: "en", category: "shipping") {
          id
          question
          answer
          category
        }
      }
      ```

- **SeoContentResolver** (`seo-content.ts`)
  - `getSeoContent`
    - **Parameters**:
      - `path`: URL path (string)
      - `lang`: Language code (string)
    - **Returns**: SEO content including meta title, description, and keywords
    - **Usage**: Used across the site to dynamically set SEO metadata
    - **Implementation note**: Path-based lookup with fallbacks for missing entries

### Marketing and User Feedback

- **MarketingResolver** (`marketing.ts`)
  - `getHomePageBanners`
    - **Parameters**:
      - `lang`: Language code (string)
      - `channelId`: Sales channel ID (string)
    - **Returns**: Array of banner content for the homepage
    - **Usage**: Used to display promotional banners on the homepage
    - **Implementation note**: Filters active banners by date range and channel

  - `getCollections`
    - **Parameters**:
      - `lang`: Language code (string)
      - `channelId`: Sales channel ID (string) 
    - **Returns**: Featured collections for display
    - **Usage**: Used for curated product collections on landing pages
    - **Example call**:
      ```graphql
      query {
        getCollections(lang: "en", channelId: "eu-de") {
          id
          name
          description
          slug
          thumbnail
          products {
            id
            name
            slug
            price
          }
        }
      }
      ```

- **TestimonialResolver** (`testimonial.ts`)
  - `getTestimonials`
    - **Parameters**:
      - `lang`: Language code (string)
      - `limit`: Maximum number of testimonials (number, optional)
    - **Returns**: Array of customer testimonials
    - **Usage**: Used in trust sections throughout the site
    - **Implementation note**: Randomizes results for variety unless specific IDs are requested

- **CustomerReviewResolver** (`customer-review.ts`)
  - `getProductReviews`
    - **Parameters**:
      - `productId`: Product ID (string)
      - `page`: Page number (number, optional)
      - `limit`: Items per page (number, optional)
    - **Returns**: Paginated customer reviews with ratings and comments
    - **Usage**: Used on product detail pages to display customer feedback
    - **Example call**:
      ```graphql
      query {
        getProductReviews(productId: "iphone-12-pro", page: 1, limit: 10) {
          items {
            id
            rating
            title
            comment
            createdAt
            user {
              name
              avatar
            }
          }
          meta {
            totalItems
            itemsPerPage
            totalPages
            currentPage
          }
        }
      }
      ```

### Miscellaneous

- **ChannelResolver** (`channel.ts`)
  - `getChannels`
    - **Parameters**: None
    - **Returns**: List of available sales channels with region and currency information
    - **Usage**: Used for region/language selection and localization
    - **Implementation note**: Returns active channels only

- **FrontpageMiscResolver** (`frontpage-misc.ts`)
  - `getFrontpageMisc`
    - **Parameters**: None
    - **Returns**: Miscellaneous content for homepage including statistics and promotional text
    - **Usage**: Used for displaying environmental impact statistics and special announcements
    - **Example call**:
      ```graphql
      query {
        getFrontpageMisc {
          totalSavedCo2
          totalSavedEwaste
          promoText
          announcementText
          statsLastUpdated
        }
      }
      ```

### Implementation Patterns and Best Practices

1. **Relationship Optimization**
   - Many resolvers use `GraphRelationBuilder` to dynamically construct SQL queries based on requested GraphQL fields
   - This prevents over-fetching and improves performance by loading only necessary relationships

2. **Security Validation**
   - Resolvers that modify data (mutations) include validation logic to prevent unauthorized access
   - Token-based authentication is used for sensitive operations

3. **Error Handling**
   - Resolvers consistently return typed error responses for better frontend handling
   - Error types are enumerated for precise error messaging

4. **Performance Considerations**
   - Dataloader patterns are used for batch loading related entities
   - Caching is applied to frequently accessed, rarely changing data
   - Query complexity limitations prevent excessive resource consumption

### GraphQL Playground Usage

The GraphQL playground is available for development at http://127.0.0.1:5000/graphql. To use the playground:

1. Navigate to the URL in your browser
2. Write your query in the left panel
3. Click the "Play" button to execute
4. View results in the right panel
5. Use the "Docs" tab on the right to explore the schema

For authenticated queries in the playground, add an Authorization header in the HTTP Headers section (bottom left):

```json
{
  "Authorization": "Bearer YOUR_TOKEN_HERE"
}
```

### Authentication & Authorization
- **[Passport](https://www.passportjs.org/)** - Authentication middleware for Node.js.
  - JWT authentication strategy
  - Role-based access control (RBAC)
  - Session management

- **[nest-casl](https://github.com/sergey-telpuk/nest-casl)** - Fine-grained authorization.
  - Ability-based access control
  - Policy-based permissions
  - Integration with NestJS guards

### Payment Processing
- **[Stripe](https://stripe.com/docs/api)** - Payment infrastructure.
  - Implemented with [@golevelup/nestjs-stripe](https://github.com/golevelup/nestjs)
  - Webhook handling
  - Payment intent management
  - Subscription billing

### Background Processing
- **[BullMQ](https://docs.bullmq.io/)** - Queue system based on Redis.
  - Implemented with [@nestjs/bullmq](https://docs.nestjs.com/techniques/queues)
  - Scheduled jobs
  - Retry mechanism
  - Priority queues
  - Concurrency control

### Search
- **[Algolia](https://www.algolia.com/doc/)** - Search-as-a-service.
  - Implemented with [algoliasearch](https://www.algolia.com/doc/api-client/getting-started/install/javascript/)
  - Real-time indexing
  - Faceted search
  - Typo tolerance

### Email
- **[SendGrid](https://sendgrid.com/docs/)** - Email delivery service.
  - Implemented with [@sendgrid/mail](https://github.com/sendgrid/sendgrid-nodejs)
  - Template management
  - Email tracking
  - Batch sending

### File Storage
- **[AWS S3](https://docs.aws.amazon.com/s3/)** - Object storage service.
  - Implemented with [@aws-sdk/client-s3](https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/clients/client-s3/)
  - File upload/download
  - Access control
  - Presigned URLs

- **[Cloudinary](https://cloudinary.com/documentation)** - Cloud-based image and video management.
  - Image optimization
  - Transformations
  - CDN delivery

### Caching & State
- **[Redis](https://redis.io/docs/)** - In-memory data structure store.
  - Implemented with [@liaoliaots/nestjs-redis](https://github.com/liaoliaots/nestjs-redis)
  - Caching
  - Session storage
  - Rate limiting

### Monitoring & Error Tracking
- **[Sentry](https://docs.sentry.io/)** - Error monitoring.
  - Implemented with [@sentry/nestjs](https://docs.sentry.io/platforms/javascript/guides/nestjs/)
  - Real-time error tracking
  - Performance monitoring
  - Release tracking

### Internationalization
- **[nestjs-i18n](https://nestjs-i18n.com/)** - I18n module for NestJS.
  - Locale detection
  - Translation management
  - Pluralization
  - Message formatting

## 🔄 Database Migrations

When updating code that affects the database structure, including TypeORM entity changes, follow these steps:

1. **Create a migration script**:
   - Using pnpm script: `pnpm run migration:generate ./data/migrations/ descriptive-name`
   - Migration scripts should be placed in `data/migrations/` directory
   - Follow the naming convention: `{timestamp}-descriptive-name.ts`
   - Implement both `up` and `down` methods for forward and rollback migrations

2. **Register the migration**:
   - Add your migration to `data/migrations/index.ts`
   - Follow the existing pattern in the file

3. **Migration best practices**:
   - Always test migrations on a development database before pushing
   - Include descriptive comments in complex migrations
   - Consider data integrity when modifying existing tables
   - When performing data transformations in migrations, treat every operation as potentially destructive to production data. Always implement safeguards, validate transformed data, maintain backups before execution, and consider rollback strategies for when operations fail. Remember that once data is lost or corrupted in production, recovery may be difficult or impossible.

### Example migration:

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnToTable1234567890123 implements MigrationInterface {
  async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "table_name" 
      ADD COLUMN "new_column" VARCHAR(255)
    `)
  }

  async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "table_name" 
      DROP COLUMN "new_column"
    `)
  }
}
```

### Running migrations:
```bash
pnpm migration:run
```

### Tips for Common Migration Scenarios

#### Adding a Non-Null Field to Existing Table

When adding a non-null field to a table with existing records, you need to follow a three-step process:

```typescript
// 1. First add the column as nullable
await queryRunner.query(`
  ALTER TABLE "table_name"
  ADD COLUMN "new_column" VARCHAR(255)
`)

// 2. Update existing records with a default value
await queryRunner.query(`
  UPDATE "table_name"
  SET "new_column" = 'default_value'
  WHERE "new_column" IS NULL
`)

// 3. Then alter the column to be non-nullable
await queryRunner.query(`
  ALTER TABLE "table_name"
  ALTER COLUMN "new_column" SET NOT NULL
`)
```

This approach ensures that existing records receive default values before enforcing the non-null constraint, preventing migration failures.

## 🚀 Available Scripts

- `pnpm build` - Build the application
- `pnpm dev` - Start development server
- `pnpm test` - Run tests. Tests are not available at present
- `pnpm migration:generate` - Generate database migrations
- `pnpm migration:run` - Run database migrations
- `pnpm algolia:reindex` - Reindex Algolia search indices, source the data from database to Algolia
- `pnpm typeorm:clear-cache` - Clear TypeORM query cache stored in Redis, useful when facing stale data issues or after schema changes
- `pnpm dummify` - Replace sensitive user data with dummy data in development mode. PLEASE DO NOT USE ON PRODUCTION!
- `pnpm lint` - Run ESLint
- `pnpm format` - Format code with Prettier

## 🌐 API Documentation

The API documentation is available at:
- Main API for admin dashboard: `/swagger` (development only)
- Partner API: `/partner/swagger` (development only)
  - Local development URL: http://127.0.0.1:5000/partner/swagger
- GraphQL Playground: `/graphql` (development only)
  - Local development URL: http://127.0.0.1:5000/graphql

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 Code Style

- ESLint for code linting
- Prettier for code formatting
- TypeScript strict mode enabled
- SOLID principles
- Clean code practices
- Comprehensive documentation
- No hard-coding please!

## 🆘 Support

For support:
1. Check existing documentation
2. Review code comments
3. Consult team members
