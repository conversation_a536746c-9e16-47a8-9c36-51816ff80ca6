import { <PERSON><PERSON>, CrudController } from '@dataui/crud'
import { Controller, UseGuards } from '@nestjs/common'

import { LocaleLanguageEntity } from '~/entities'
import { AdminJwtAuthGuard } from '~admin/guards'
import { CrudLocaleLanguageService } from '~admin/services'

@UseGuards(AdminJwtAuthGuard)
@Crud({
  model: {
    type: LocaleLanguageEntity,
  },
  params: { id: { field: 'id', type: 'string', primary: true } },
  query: {
    join: {
      countries: {},
      channels: {},
    },
  },
})
@Controller('admin/languages')
export class CrudLocaleLanguageController implements CrudController<LocaleLanguageEntity> {
  constructor(public service: CrudLocaleLanguageService) {}
}
