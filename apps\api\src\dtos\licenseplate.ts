import { ArgsType, Field, Float, Int, ObjectType, registerEnumType } from '@nestjs/graphql'
import { LicensePlateGrossMarginEnum, LicensePlateStatusEnum } from '~/constants'
import { Type } from 'class-transformer'
import { IsOptional, IsString, IsUUID, IsDateString, IsEnum, ValidateNested, IsArray, IsNumber } from 'class-validator'

registerEnumType(LicensePlateStatusEnum, {
  name: 'LicensePlateStatusEnum',
})

registerEnumType(LicensePlateGrossMarginEnum, {
  name: 'LicensePlateGrossMarginEnum',
})

@ObjectType('GetLicensePlateResult')
export class GetLicensePlateResult {}

export class LicensePlateProductDto {
  @IsOptional()
  @IsUUID()
  id?: string | null

  @IsOptional()
  @IsUUID()
  guid?: string | null

  @IsOptional()
  @IsString()
  name?: string | null

  @IsOptional()
  @IsString()
  sku?: string | null

  @IsOptional()
  @IsString()
  category?: string | null

  @IsOptional()
  @IsString()
  brand?: string | null

  @IsOptional()
  @IsString()
  model?: string | null

  @IsOptional()
  @IsString()
  storage?: string | null
}

export class LicensePlatePurchaseDto {
  @IsOptional()
  @IsUUID()
  id?: string | null

  @IsOptional()
  @IsUUID()
  guid?: string | null

  @IsOptional()
  @IsDateString()
  purchase_date?: Date | string | null

  @IsOptional()
  @IsString()
  purchase_country?: string | null

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  base_purchase_price?: number | null

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  purchase_price?: number | null

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  purchase_tax_percentage?: number | null

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  purchase_tax_amount?: number | null

  @IsOptional()
  @IsEnum(LicensePlateGrossMarginEnum)
  purchase_grossmargin_type?: LicensePlateGrossMarginEnum | null

  @IsOptional()
  @IsString()
  purchase_currency?: string | null

  @IsOptional()
  @IsString()
  purchase_channel?: string | null
}

export class LicensePlateSaleDto {
  @IsOptional()
  @IsUUID()
  id?: string | null

  @IsOptional()
  @IsUUID()
  guid?: string | null

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  base_sale_price?: number | null

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  sale_price?: number | null

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  sale_tax_percentage?: number | null

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  sale_tax_amount?: number | null

  @IsOptional()
  @IsEnum(LicensePlateGrossMarginEnum)
  sale_grossmargin_type?: LicensePlateGrossMarginEnum | null

  @IsOptional()
  @IsString()
  sale_currency?: string | null

  @IsOptional()
  @IsString()
  sale_country?: string | null

  @IsOptional()
  @IsDateString()
  sale_date?: Date | string | null

  @IsOptional()
  @IsString()
  sale_channel?: string | null

  @IsOptional()
  @IsString()
  order_id?: string | null
}

export class LicensePlateStatusDto {
  @IsOptional()
  @IsUUID()
  id?: string | null

  @IsOptional()
  @IsUUID()
  guid?: string | null

  @IsOptional()
  @IsEnum(LicensePlateStatusEnum)
  status?: LicensePlateStatusEnum | null

  @IsOptional()
  @IsString()
  note?: string | null

  @IsOptional()
  @IsDateString()
  created_at?: Date | string | null

  @IsOptional()
  @IsDateString()
  updated_at?: Date | string | null
}

export class LicensePlateDeviceDto {
  @IsOptional()
  @IsUUID()
  guid?: string | null

  @IsOptional()
  @IsString()
  imei?: string | null

  @IsOptional()
  @IsString()
  serial?: string | null

  @IsOptional()
  @IsString()
  grade_inbound?: string | null

  @IsOptional()
  @IsString()
  grade_outbound?: string | null

  @IsOptional()
  @IsDateString()
  created_at?: Date | string | null

  @IsOptional()
  @IsDateString()
  updated_at?: Date | string | null

  @IsOptional()
  @IsString()
  updated_by?: string | null

  @IsOptional()
  @ValidateNested()
  @Type(() => LicensePlateProductDto)
  product?: LicensePlateProductDto | null

  @IsOptional()
  @ValidateNested()
  @Type(() => LicensePlatePurchaseDto)
  purchase?: LicensePlatePurchaseDto | null

  @IsOptional()
  @ValidateNested()
  @Type(() => LicensePlateSaleDto)
  sale?: LicensePlateSaleDto | null

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LicensePlateStatusDto)
  status?: LicensePlateStatusDto[] | null
}
