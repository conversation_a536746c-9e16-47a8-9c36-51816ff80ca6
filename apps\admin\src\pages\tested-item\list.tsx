import {
  Date<PERSON>ield,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  EditButton,
  FilterDropdown,
  getDefaultSortOrder,
  List,
  useSelect,
  useTable,
} from '@refinedev/antd'
import { getDefaultFilter, IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { ITestedItem, ITestedItemList } from '@valyuu/api/entities'
import { Select, Space, Table } from 'antd'
import { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const TestedItemList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters, filters } = useTable<ITestedItem>({
    syncWithLocation: true,
    meta: {
      fields: ['name__en', 'lists.internalName', 'sortOrder', 'createdAt', 'publishedAt'],
      join: [{ field: 'lists' }],
    },
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { selectProps: testedItemListSelectProps } = useSelect<ITestedItemList>({
    resource: 'admin/tested-item-lists',
    optionLabel: 'internalName',
    optionValue: 'id',
    meta: {
      fields: ['id', 'internalName'],
    },
    pagination: {
      mode: 'off',
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/tested-items', id!)))}
      >
        <Table.Column dataIndex="name__en" title="Name (English)" className="cursor-pointer" />
        <Table.Column
          dataIndex="lists.id"
          title="Used in lists"
          className="cursor-pointer"
          defaultFilteredValue={getDefaultFilter('lists.id', filters)}
          render={(_, record: ITestedItem) => record.lists.map((item: ITestedItemList) => item.internalName).join(', ')}
          filterDropdown={(props) => (
            <FilterDropdown {...props}>
              <Select style={{ minWidth: 200 }} placeholder="Select list" {...(testedItemListSelectProps as any)} />
            </FilterDropdown>
          )}
        />
        <Table.Column
          dataIndex="sortOrder"
          title="Order"
          defaultSortOrder={getDefaultSortOrder('sortOrder', sorters)}
          sorter
          className="cursor-pointer"
          width="5rem"
        />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-36 cursor-pointer"
          width="9rem"
        />
        <Table.Column<ITestedItem>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
              <DeleteButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
