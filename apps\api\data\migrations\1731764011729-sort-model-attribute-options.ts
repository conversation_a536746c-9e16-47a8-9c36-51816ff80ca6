import { MigrationInterface, QueryRunner } from 'typeorm'

export class SortModelAttributeOptions1731764011729 implements MigrationInterface {
  private convertToGB(value: string): number {
    // Normalize string: remove extra spaces, convert to uppercase
    const normalized = value.trim().toUpperCase()
    const match = normalized.match(/(\d+(?:\.\d+)?)\s*(GB|TB|MB)/)
    if (!match) return 0
    const [, size, unit] = match
    const numSize = parseFloat(size)
    switch (unit) {
      case 'TB':
        return numSize * 1024
      case 'MB':
        return numSize / 1024
      default:
        return numSize // GB
    }
  }

  private getCPUScore(cpu: string): number {
    const normalized = cpu.toLowerCase().trim()

    // M-series chips (highest priority)
    if (normalized.includes('m2')) return 5000
    if (normalized.includes('m1 max')) return 4000
    if (normalized.includes('m1 pro')) return 3000
    if (normalized.includes('m1')) return 2000

    // Intel chips
    const ghzMatch = normalized.match(/([\d.]+)(?:‑|\s)*ghz/)
    const coreMatch = normalized.match(/(\d+)[\s-]*core/)
    const genMatch = normalized.match(/i(\d)/)

    let score = 0
    if (ghzMatch) score += parseFloat(ghzMatch[1]) * 100
    if (coreMatch) score += parseInt(coreMatch[1]) * 200
    if (genMatch) score += parseInt(genMatch[1]) * 500

    return score
  }

  private normalizeString(str: string): string {
    return str.toLowerCase().trim().replace(/\s+/g, ' ')
  }

  private getSortValue(option: string): number {
    const normalized = this.normalizeString(option)

    // Storage capacity sorting
    if (normalized.match(/^\d+(?:\.\d+)?\s*(gb|tb|mb)$/)) {
      return this.convertToGB(normalized)
    }

    // Connectivity sorting
    const connectivityMap: Record<string, number> = {
      'wi-fi': 0,
      'wi-fi + cellular': 1,
      wifi: 0,
      'wifi + cellular': 1,
      gps: 0,
      'gps + cellular': 1,
    }
    if (normalized in connectivityMap) {
      return connectivityMap[normalized]
    }

    // Watch size sorting
    const sizeMatch = normalized.match(/^(\d+)\s*mm$/)
    if (sizeMatch) {
      return parseInt(sizeMatch[1])
    }

    // Case material sorting
    const materialMap: Record<string, number> = {
      aluminum: 0,
      aluminium: 0,
      'stainless steel': 1,
    }
    if (normalized in materialMap) {
      return materialMap[normalized]
    }

    // Controller sorting
    if (normalized.includes('no')) return 0
    if (normalized.includes('one')) return 1
    if (normalized.includes('two')) return 2

    // CPU/Chip sorting
    if (
      normalized.includes('ghz') ||
      normalized.includes('core') ||
      normalized.includes('chip') ||
      normalized.includes('m1') ||
      normalized.includes('m2')
    ) {
      return this.getCPUScore(normalized)
    }

    // Memory sorting (RAM)
    if (normalized.match(/^\d+\s*gb\s*ram$/)) {
      return this.convertToGB(normalized)
    }

    // Charging case sorting
    if (normalized.includes('charging case')) {
      if (normalized.includes('magsafe')) return 1
      if (normalized.includes('lightning')) return 0
    }

    // Default sorting - alphabetical
    return Number.MAX_SAFE_INTEGER
  }

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Get all attribute options with their attribute names for better context
    const options = await queryRunner.query(`
      SELECT 
        o.id,
        o.name__en,
        o."attribute_id",
        a.name__en as attribute_name
      FROM product_model_attribute_option o
      JOIN product_model_attribute a ON o."attribute_id" = a.id
      ORDER BY o."attribute_id", o.id
    `)

    // Group options by attributeId
    const optionsByAttribute = options.reduce((acc: any, opt: any) => {
      if (!acc[opt.attributeId]) {
        acc[opt.attributeId] = []
      }
      acc[opt.attributeId].push(opt)
      return acc
    }, {})

    // Sort options within each attribute group
    for (const attributeId in optionsByAttribute) {
      const sortedOptions = optionsByAttribute[attributeId].sort((a: any, b: any) => {
        const valueA = this.getSortValue(a.name__en)
        const valueB = this.getSortValue(b.name__en)
        if (valueA === valueB) {
          // Secondary sort by name if primary sort values are equal
          return this.normalizeString(a.name__en).localeCompare(this.normalizeString(b.name__en))
        }
        return valueA - valueB
      })

      // Update sortOrder for each option
      for (let i = 0; i < sortedOptions.length; i++) {
        await queryRunner.query(
          `
          UPDATE product_model_attribute_option
          SET "sort_order" = $1
          WHERE id = $2
        `,
          [i, sortedOptions[i].id]
        )
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
