import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import { OrganizationBankAccountType } from '@valyuu/api/constants'
import type { ICharity } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Input, Select } from 'antx'
import cx from 'clsx'
import { type FC, useState } from 'react'

import { InputLanguageTab, LabelWithDetails, LanguageTabChoices } from '~/components'

export const CharityEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, query, saveButtonProps, formLoading } = useForm<ICharity, HttpError, ICharity>({
    meta: {
      join: [
        {
          field: 'bankAccount',
          select: ['id', 'holderName', 'accountNumber'],
        },
      ],
    },
  })

  const record = query?.data?.data

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  const { editUrl } = useNavigation()

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input label="Name (EN)" name="name__en" rules={['required']} className={clsHideEn} />
        <Input label="Name (NL)" name="name__nl" rules={['required']} className={clsHideNl} />
        <Input label="Name (DE)" name="name__de" rules={['required']} className={clsHideDe} />
        <Input label="Name (PL)" name="name__pl" rules={['required']} className={clsHidePl} />
        <Input type="hidden" noStyle name={['bankAccount', 'type']} value={OrganizationBankAccountType.CHARITY} />
        <Select
          label={
            <LabelWithDetails
              link={
                record?.bankAccountId ? editUrl('admin/organization-bank-accounts', record.bankAccountId) : undefined
              }
              label={'Bank account'}
            />
          }
          name="bankAccountId"
          rules={['required']}
          options={[{ label: record?.bankAccount?.holderName, value: record?.bankAccount?.id }]}
          disabled
        />
      </Form>
    </Edit>
  )
}
