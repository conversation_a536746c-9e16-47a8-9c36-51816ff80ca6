import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps } from '@refinedev/core'
import { BlogTagColor } from '@valyuu/api/constants'
import type { IBlogTag } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Select, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useState } from 'react'

import { InputLanguageTab, InputMultiLang, InputSlug, LanguageTabChoices } from '~/components'
import { formatSelectOptionsFromEnum } from '~/utils'

export const BlogTagEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, formLoading } = useForm<IBlogTag, HttpError, IBlogTag>()

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Watch list={['name__en', 'name__nl', 'name__de', 'name__pl']}>
          {([name__en, name__nl, name__de, name__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="en"
                label="Name (English)"
                name="name__en"
                rules={['required']}
                className={clsHideEn}
                fillType="translate"
              />
              <InputSlug
                label="Slug (English)"
                name="slug__en"
                rules={['required']}
                allowEdit={false}
                sourceString={name__en}
                className={clsHideEn}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="nl"
                label="Name (Dutch)"
                name="name__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="translate"
              />
              <InputSlug
                label="Slug (Dutch)"
                name="slug__nl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__nl}
                className={clsHideNl}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="de"
                label="Name (German)"
                name="name__de"
                rules={['required']}
                className={clsHideDe}
                fillType="translate"
              />
              <InputSlug
                label="Slug (German)"
                name="slug__de"
                rules={['required']}
                allowEdit={false}
                sourceString={name__de}
                className={clsHideDe}
              />
              <InputMultiLang
                sources={{ en: name__en, nl: name__nl, de: name__de, pl: name__pl }}
                targetLang="pl"
                label="Name (Polish)"
                name="name__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="translate"
              />
              <InputSlug
                label="Slug (Polish)"
                name="slug__pl"
                rules={['required']}
                allowEdit={false}
                sourceString={name__pl}
                className={clsHidePl}
              />
            </>
          )}
        </Watch>
        <Select label="Color" name="color" rules={['required']} options={formatSelectOptionsFromEnum(BlogTagColor)} />
      </Form>
    </Edit>
  )
}
