{"name": "@valyuu/api", "version": "0.0.1", "description": "", "private": true, "license": "UNLICENSED", "exports": {"./constants": {"import": "./src/constants/index.ts", "require": "./dist/src/constants/index.js"}, "./interfaces": {"import": "./src/interfaces/index.ts", "require": "./dist/src/interfaces/index.js"}, "./entities": "./dist/src/entities/index.d.ts", "./algolia-indices": "./dist/src/algolia-indices/index.d.ts", "./admin-constants": {"import": "./src/modules/admin/constants/index.ts", "require": "./dist/src/modules/admin/constants/index.js"}, "./admin-interfaces": {"import": "./src/modules/admin/interfaces/index.ts", "require": "./dist/src/modules/admin/interfaces/index.js"}}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "debug": "nest start --debug --watch", "serve": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:run": "ts-node ./data/cli/migration-run.ts", "migration:generate": "ts-node -r tsconfig-paths/register -P tsconfig.json ./node_modules/typeorm/cli.js migration:generate -d src/configs/typeorm.ts", "algolia:reindex": "ts-node ./data/cli/algolia-reindex.ts", "dummify": "ts-node ./data/cli/dummify.ts", "typeorm:clear-cache": "ts-node ./data/cli/typeorm-clear-cache.ts"}, "dependencies": {"@algolia/client-search": "^4.22.1", "@apollo/server": "^4.11.2", "@aws-sdk/client-s3": "^3.685.0", "@cloudinary/url-gen": "^1.21.0", "@dataui/crud": "^5.3.4", "@dataui/crud-typeorm": "^5.3.4", "@golevelup/nestjs-stripe": "^0.6.5", "@liaoliaots/nestjs-redis": "^9.0.5", "@nestjs/apollo": "^12.2.1", "@nestjs/bullmq": "^10.2.2", "@nestjs/common": "^10.4.6", "@nestjs/core": "^10.4.6", "@nestjs/graphql": "^12.2.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.6", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^8.0.1", "@nestjs/terminus": "^10.2.3", "@nestjs/throttler": "^6.2.1", "@nestjs/typeorm": "^10.0.2", "@sendgrid/mail": "^8.1.4", "@sentry/nestjs": "^9.6.0", "@valyuu/sendcloud": "workspace:*", "algoliasearch": "^4.22.1", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bullmq": "^5.23.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cloudinary": "^2.5.1", "currency.js": "^2.0.4", "dataloader": "^2.2.2", "date-fns": "^4.1.0", "deepl-node": "^1.14.0", "dotenv": "^16.4.5", "envalid": "^8.0.0", "exceljs": "^4.4.0", "express": "^4.21.1", "fast-json-patch": "^3.1.1", "geoip-country": "^5.0.202411012341", "google-merchant-feed": "^0.1.0", "googleapis": "^144.0.0", "graphql": "^16.9.0", "graphql-query-complexity": "^1.0.0", "graphql-subscriptions": "^3.0.0", "graphql-type-json": "^0.3.2", "graphql-upload": "^17.0.0", "handlebars": "^4.7.8", "helmet": "^8.0.0", "html-to-text": "^9.0.5", "ioredis": "^5.4.1", "lodash": "^4.17.21", "markdown-escape": "^2.0.0", "mime-types": "^3.0.0", "mjml": "^4.15.3", "mjml-core": "^4.15.3", "ms": "^3.0.0-canary.1", "nest-casl": "^1.9.7", "nestjs-cls": "^4.4.1", "nestjs-i18n": "^10.4.9", "nestjs-otel": "^6.1.1", "nestjs-slack-bolt": "^1.3.1", "openai": "^4.70.2", "p-map": "^4.0.0", "p-retry": "^4.6.2", "passport": "^0.7.0", "passport-anonymous": "^1.0.1", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.13.1", "postcode-validator": "^3.10.2", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.1", "stripe": "^14.20.0", "transliteration": "^2.3.5", "trello.js": "^1.2.7", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0", "typeorm-relations-graphql": "^3.0.0", "uuid": "^11.0.2"}, "devDependencies": {"@faker-js/faker": "^9.2.0", "@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.6", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.21", "@types/html-to-text": "^9.0.4", "@types/jest": "29.5.14", "@types/lodash": "^4.17.13", "@types/markdown-escape": "^1.1.3", "@types/mime-types": "^2.1.4", "@types/mjml-core": "^4.15.1", "@types/ms": "^0.7.34", "@types/multer": "^1.4.12", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/yargs-parser": "^21.0.3", "async-mutex": "^0.5.0", "babel-plugin-import": "^1.13.8", "jest": "29.7.0", "supertest": "^7.0.0", "ts-jest": "29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "type-fest": "^4.26.1", "typeorm-extension": "^3.6.2", "yargs-parser": "^21.1.1"}}