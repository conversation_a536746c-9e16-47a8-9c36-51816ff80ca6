import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToMany,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { type ITestedItemList, TestedItemListEntity } from '~/entities'

@ObjectType('TestedItem')
@Entity('tested_item')
export class TestedItemEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @ManyToMany(() => TestedItemListEntity, (list) => list.items, { nullable: false, onDelete: 'CASCADE' })
  lists: Relation<TestedItemListEntity>[]

  @Column({ default: 0 })
  @Index()
  sortOrder: number

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date
}

export type ITestedItem = Omit<TestedItemEntity, keyof BaseEntity> & {
  lists: ITestedItemList[]
}
