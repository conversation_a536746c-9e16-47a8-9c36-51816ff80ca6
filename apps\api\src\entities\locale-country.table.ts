import { Field, ID, ObjectType } from '@nestjs/graphql'
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  PrimaryColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { ChannelEntity, type IChannel, type ILocaleLanguage, LocaleLanguageEntity } from '~/entities'

@ObjectType('LocaleCountry')
@Entity('locale_country')
export class LocaleCountryEntity extends BaseEntity {
  @Field(() => ID)
  @PrimaryColumn()
  id: string

  @Field()
  @Column()
  name__en: string

  @Field()
  @Column()
  name__nl: string

  @Field()
  @Column()
  name__de: string

  @Field()
  @Column()
  name__pl: string

  @Column()
  countryAreaCode: string

  @Field(() => [ChannelEntity])
  @ManyToMany(() => ChannelEntity, (channel) => channel.countries, { nullable: false })
  channels: Relation<ChannelEntity>[]

  @Field(() => [LocaleLanguageEntity])
  @ManyToMany(() => LocaleLanguageEntity, (language) => language.countries, { nullable: false })
  @JoinTable({ name: 'locale_country_languages' })
  languages: Relation<LocaleLanguageEntity>[]

  @Field()
  @CreateDateColumn()
  @Index()
  createdAt: Date

  @Field()
  @UpdateDateColumn()
  updatedAt: Date
}

export type ILocaleCountry = Omit<LocaleCountryEntity, keyof BaseEntity> & {
  channels: IChannel[]
  languages: ILocaleLanguage[]
}
