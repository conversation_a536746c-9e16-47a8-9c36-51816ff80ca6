import { Args, Mutation, Query, Resolver } from '@nestjs/graphql'

import {
  createPaymentIntentInput,
  CreatePaymentIntentOutput,
  CreatePreOrderInput,
  CreatePreOrderOutput,
  GetOrderCartItemPricesInput,
  GetOrderCartItemPricesOutput,
  GetOrderResultOutput,
} from '~/dtos'
import { OrderService, StripeService } from '~/services'

@Resolver()
export class OrderResolver {
  constructor(
    protected readonly orderService: OrderService,
    protected readonly stripeService: StripeService
  ) {}

  @Query(() => GetOrderCartItemPricesOutput)
  async getOrderCartItemPrices(@Args() { channelId, skuItems, accessoryItems }: GetOrderCartItemPricesInput) {
    return this.orderService.getOrderCartItemPrices({ channelId, skuItems, accessoryItems })
  }

  @Mutation(() => CreatePaymentIntentOutput)
  async createPaymentIntent(@Args() input: createPaymentIntentInput) {
    return this.orderService.createPaymentIntent(input)
  }

  @Mutation(() => CreatePreOrderOutput)
  async createPreOrder(@Args() input: CreatePreOrderInput): Promise<CreatePreOrderOutput> {
    return this.orderService.createPreOrder(input)
  }

  @Query(() => GetOrderResultOutput)
  async getOrderResult(@Args('paymentIntent') paymentIntent: string): Promise<GetOrderResultOutput> {
    return this.orderService.getOrderResult(paymentIntent)
  }
}
