# Valyuu Admin Dashboard

## 📚 Table of Contents

- [Introduction](#introduction)
- [Quick Start](#-quick-start)
- [Project Structure](#-project-structure)
- [Core Features](#️-core-features)
- [Technology Stack](#-technology-stack)
- [Component Library](#-component-library)
- [Data & State Management](#-data--state-management)
- [Authentication](#-authentication)
- [Available Scripts](#-available-scripts)
- [Build & Deployment](#-build--deployment)
- [Best Practices](#-best-practices)
- [Troubleshooting](#-troubleshooting)

## Introduction

The Valyuu Admin Dashboard is a comprehensive management interface for the Valyuu platform. Built with Refine, React, and Ant Design, it provides a powerful set of tools for managing products, sales, orders, users, and other aspects of the Valyuu ecosystem.

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- PNPM package manager
- Access to the Valyuu API endpoint

### Installation

1. Clone the repository
```bash
git clone https://github.com/Valyuu/valyuu.git
cd valyuu/apps/admin
```

2. Install dependencies
```bash
pnpm install
```

3. Set up environment variables
```bash
cp .env-example .env
# Update the .env file with your credentials
```

4. Start development server
```bash
pnpm dev
```

5. Access the dashboard at http://localhost:5173

## 📁 Project Structure

```
src/
├── App.tsx                # Main application component
├── components/            # Reusable UI components
│   ├── common/            # Common UI elements (buttons, cards, etc.)
│   ├── layout/            # Layout components (header, sidebar, etc.)
│   └── specialized/       # Domain-specific components
├── contexts/              # React context providers
├── hooks/                 # Custom React hooks
├── interfaces/            # TypeScript interfaces and types
├── pages/                 # Page components organized by feature
│   ├── auth/              # Authentication pages (login, forgot password)
│   ├── dashboard/         # Dashboard and analytics
│   ├── orders/            # Order management
│   ├── products/          # Product management
│   ├── sales/             # Sales management
│   └── users/             # User management
├── providers/             # Custom providers for Refine
├── routes/                # Routing configuration
├── services/              # API services and data fetching
├── utils/                 # Utility functions
└── constants.ts           # Application-wide constants
```

## 🛠️ Core Features

### Dashboard & Analytics
- Real-time sales and order statistics
- Conversion and performance metrics
- Interactive charts and data visualization
- Customizable date ranges and filters

### Product Management
- Product catalog with full CRUD operations
- Complex product model management
- Product variant creation and management
- Pricing configuration with multi-currency support
- Inventory and stock tracking
- Image upload and management

### Order & Sales Management
- Comprehensive order processing
- Order status tracking and updates
- Customer communications
- Sale item offers and negotiations
- Payment processing integration
- Shipping and delivery management

### User Management
- Customer account management
- Admin user access control
- Role-based permissions
- User activity logging

### Content Management
- Blog post creation and editing with MDX
- SEO content optimization
- Marketing assets management
- Banner and promotional content

### System Configuration
- Application settings
- Email template management
- Shipping configuration
- Tax and payment settings
- Localization and language settings

## 🔧 Technology Stack

### Core Framework
- **[React 18](https://react.dev/)** - A JavaScript library for building user interfaces
  - Functional components with hooks
  - Context API for state management
  - Concurrent rendering features

- **[TypeScript](https://www.typescriptlang.org/)** - Typed JavaScript for better developer experience
  - Strict type checking
  - Type inference
  - Interface and type definitions

- **[Vite](https://vitejs.dev/)** - Next generation frontend tooling
  - Fast hot module replacement
  - Efficient bundling
  - Plugin ecosystem

### Admin Framework
- **[Refine](https://refine.dev/)** v4 - A React-based framework for building admin applications
  - Data provider abstraction
  - Authentication provider
  - Access control
  - CRUD operations
  - Route management
  - UI integration

- **[@valyuu/refine-crud-adapter](https://github.com/Valyuu/valyuu)** - Custom adapter for connecting Refine to @dataui/crud API

### UI Framework
- **[Ant Design](https://ant.design/)** v5 - A design system with React components
  - Comprehensive UI component library
  - Theming and customization
  - Responsive design
  - Form components and validation

- **[Ant Design Pro Components](https://procomponents.ant.design/)** - Advanced UI components
  - Pro Table for advanced data tables
  - Pro Form for complex forms
  - Pro Layout for application layout

- **[TailwindCSS](https://tailwindcss.com/)** - Utility-first CSS framework
  - Responsive utility classes
  - Customizable design system
  - JIT compiler for optimized builds

### Data Management
- **[@tanstack/react-query](https://tanstack.com/query/latest)** - Powerful data fetching library
  - Data fetching, caching, and synchronization
  - Server state management
  - Error handling and retry logic

- **[Immer](https://immerjs.github.io/immer/)** - Immutable state management
  - Simplified immutable state updates
  - Integration with React state hooks
  - Performance optimizations

## 🧩 Component Library

### Layout Components
- **AppLayout** - Main application layout with navigation
- **Sidebar** - Collapsible navigation sidebar
- **Header** - Application header with user menu and notifications
- **Footer** - Application footer with meta information

### Form Components
- **FormBuilder** - Dynamic form generator from schema
- **ImageUploader** - Image upload with preview and cropping
- **RichTextEditor** - MDX-based rich text editor
- **ArrayInput** - Input for array-type data with add/remove functionality
- **JSONEditor** - Editor for JSON-structured data

### Data Display Components
- **DataTable** - Enhanced table with sorting, filtering, and pagination
- **DetailView** - Detailed record view with tabs
- **StatusBadge** - Visual indicator for status values
- **MetricCard** - Display for key metrics and statistics
- **Timeline** - Chronological display of events

### Specialized Components
- **ProductVariantMatrix** - UI for managing product variants and combinations
- **PricingTiers** - Interface for managing tiered pricing
- **OrderStatusFlow** - Visual representation of order status progression
- **PaymentSummary** - Financial summary with calculation
- **InventoryLevels** - Stock level visualization

## 📊 Data & State Management

### API Integration
- REST API communication using data providers
- Custom hooks for specific API endpoints
- Caching and invalidation strategies
- Error handling and retry logic

### State Management Patterns
- React Query for server state
- React Context for application state
- Local component state with useState/useImmer
- Form state with Ant Design Form

### Data Transformation
- Server response normalization
- Client-side data formatting
- Currency and date formatting
- Data export functionality

## 🔐 Authentication

### Authentication Flow
- JWT-based authentication
- Refresh token mechanism
- Session persistence
- Secure logout process

### Authorization
- Role-based access control
- Permission-based UI rendering
- Protected routes
- Feature flags

## 🚀 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build the application for production
- `pnpm preview` - Preview production build locally
- `pnpm refine` - Run Refine CLI commands
- `pnpm lint` - Run ESLint to check code quality
- `pnpm stories` - Start Ladle component storybook

## 🏗️ Build & Deployment

### Production Build
1. Create production build
```bash
pnpm build
```

2. The build artifacts will be stored in the `dist/` directory

### Deployment Options
- Static hosting (Netlify, Vercel, AWS S3)
- Docker containerization
- Integration with CI/CD pipelines

## 💡 Best Practices

### TypeScript Conventions
1. Prefer `type` over `interface` except for extendable definitions
2. Use `Type[]` syntax for arrays instead of `Array<Type>`
3. Avoid using `any` type, use proper typing or `unknown` when necessary
4. Document public functions and components with JSDoc comments

### Code Style
1. Use functional components with hooks
2. Prefer single quotes in code, double quotes in JSX attributes
3. Use kebab-case for file names (e.g., `data-table.tsx`)
4. Follow naming conventions:
   - PascalCase for components and types
   - camelCase for variables and functions
   - UPPER_CASE for constants
   - Prefix booleans with verbs (is, has, can)

### Performance Optimization
1. Memoize expensive calculations with `useMemo`
2. Optimize re-renders with `React.memo` and `useCallback`
3. Use virtualization for long lists
4. Implement proper loading states and error boundaries
5. Optimize API calls with batching and pagination

## 🔍 Troubleshooting

### Common Issues
1. **API Connection Issues**
   - Check API endpoint configuration in `.env`
   - Verify network connectivity and CORS settings
   - Ensure authentication tokens are valid

2. **Build Errors**
   - Clear node_modules and reinstall dependencies
   - Check for TypeScript errors
   - Verify environment variable configuration

3. **Performance Issues**
   - Check for unnecessary re-renders using React DevTools
   - Optimize expensive calculations and API calls
   - Consider implementing code splitting for large features

### Debugging Tools
- React DevTools for component inspection
- Why Did You Render for tracking re-renders
- Browser DevTools for network and performance analysis
- Sentry for error tracking and monitoring

---

For more information about the Valyuu platform and API, refer to the API documentation.
