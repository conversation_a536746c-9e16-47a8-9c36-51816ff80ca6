import { DateField, EditButton, getDefaultSortOrder, List, useTable } from '@refinedev/antd'
import { type IResourceComponentsProps, useNavigation } from '@refinedev/core'
import type { ILocaleCurrency } from '@valyuu/api/entities'
import { Space, Table } from 'antd'
import type { FC } from 'react'

import { handleTableRowClick } from '~/utils'

export const LocaleCurrencyList: FC<IResourceComponentsProps> = () => {
  const { tableProps, sorters } = useTable<ILocaleCurrency>({
    syncWithLocation: true,
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    pagination: {
      mode: 'server',
      pageSize: 20,
    },
  })

  const { editUrl, push } = useNavigation()

  return (
    <List>
      <Table
        {...tableProps}
        rowKey="id"
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        onRow={({ id }) => handleTableRowClick(() => push(editUrl('admin/currencies', id!)))}
      >
        <Table.Column dataIndex="id" title="ID" className="cursor-pointer" />
        <Table.Column dataIndex="name" title="Name" className="cursor-pointer" />
        <Table.Column dataIndex="symbol" title="Symbol" className="cursor-pointer" />
        <Table.Column
          dataIndex="createdAt"
          title="Created at"
          render={(value) => <DateField value={value} format="DD-MM-YYYY HH:mm" />}
          defaultSortOrder={getDefaultSortOrder('createdAt', sorters)}
          sorter
          className="min-w-[9rem] cursor-pointer"
          width="9rem"
        />
        <Table.Column<ILocaleCurrency>
          title="Actions"
          dataIndex="actions"
          render={(_, record) => (
            <Space>
              <EditButton size="small" recordItemId={record.id} />
            </Space>
          )}
          width="10rem"
          fixed="right"
        />
      </Table>
    </List>
  )
}
