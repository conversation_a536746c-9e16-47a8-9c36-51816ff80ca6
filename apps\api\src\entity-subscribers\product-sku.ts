import { Injectable } from '@nestjs/common'
import * as Sentry from '@sentry/nestjs'
import * as pMap from 'p-map'
import type { EntitySubscriberInterface, RemoveEvent, UpdateEvent } from 'typeorm'
import { DataSource } from 'typeorm'
import { EventSubscriber, InsertEvent } from 'typeorm'

import { ProductSkuIndex } from '~/algolia-indices'
import { FileUsedInType, ImageUsedInType, PRODUCT_SKU_MIN_SLUG_NUMBER, StockType } from '~/constants'
import {
  CategoryWarrantyRuleEntity,
  ImageEntity,
  ProductModelEntity,
  ProductSkuEntity,
  ProductVariantEntity,
  SaleItemEntity,
  StockEntity,
} from '~/entities'
import {
  entityFileAutoProcess,
  entityFileDeleteByPrefix,
  entityFixPublishedAt,
  entityImageAutoProcess,
  entityImageDeleteByPrefix,
} from '~/utils'

const generateHash = (input: string): number => {
  let hash = 0
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash |= 0 // Convert to 32bit integer
  }
  return hash
}

@Injectable()
@EventSubscriber()
export class ProductSkuSubscriber implements EntitySubscriberInterface<ProductSkuEntity> {
  constructor(private readonly dataSource?: DataSource) {
    dataSource?.subscribers.push(this)
  }

  listenTo() {
    return ProductSkuEntity
  }

  async beforeInsert(event: InsertEvent<ProductSkuEntity>) {
    const { entity, manager } = event

    // Create stock if not exists
    if (!entity.stockId && !entity.stock) {
      const type = /^EXTERNAL_/.test(String(entity.source)) ? StockType.EXTERNAL : StockType.SALE
      const stockId = await StockEntity.getNewStockId(type)
      const stock = await StockEntity.save(StockEntity.create({ id: stockId, type }))
      entity.stock = await manager.save(manager.create(StockEntity, { id: stock.id, type }))
    }

    const lockKey__en = generateHash(entity.slug__en + '_en')
    const lockKey__nl = generateHash(entity.slug__nl + '_nl')
    const lockKey__de = generateHash(entity.slug__de + '_de')

    await manager.query(`SELECT pg_advisory_xact_lock(${lockKey__en})`)
    await manager.query(`SELECT pg_advisory_xact_lock(${lockKey__nl})`)
    await manager.query(`SELECT pg_advisory_xact_lock(${lockKey__de})`)

    const { slugNumber__en } = (await manager.findOne(ProductSkuEntity, {
      where: { slug__en: entity.slug__en },
      select: { id: true, slugNumber__en: true },
      order: { slugNumber__en: 'DESC' },
    })) ?? { slugNumber__en: PRODUCT_SKU_MIN_SLUG_NUMBER - 1 }
    const { slugNumber__nl } = (await manager.findOne(ProductSkuEntity, {
      where: { slug__nl: entity.slug__nl },
      select: { id: true, slugNumber__nl: true },
      order: { slugNumber__nl: 'DESC' },
    })) ?? { slugNumber__nl: PRODUCT_SKU_MIN_SLUG_NUMBER - 1 }
    const { slugNumber__de } = (await manager.findOne(ProductSkuEntity, {
      where: { slug__de: entity.slug__de },
      select: { id: true, slugNumber__de: true },
      order: { slugNumber__de: 'DESC' },
    })) ?? { slugNumber__de: PRODUCT_SKU_MIN_SLUG_NUMBER - 1 }

    entity.slugNumber__en = String(Number(slugNumber__en) + 1)
    entity.slugNumber__nl = String(Number(slugNumber__nl) + 1)
    entity.slugNumber__de = String(Number(slugNumber__de) + 1)

    // add brand, category and model based on variant
    if (!entity.brandId || !entity.categoryId || !entity.modelId) {
      const variant = await manager.findOneOrFail(ProductVariantEntity, {
        where: { id: entity.variantId },
        relations: {
          model: true,
        },
        select: {
          id: true,
          modelId: true,
          model: {
            id: true,
            brandId: true,
            categoryId: true,
          },
        },
      })
      entity.brandId = variant.model.brandId
      entity.categoryId = variant.model.categoryId
      entity.modelId = variant.model.id
    }

    // add model metrics based on model
    if (!entity.modelMetrics) {
      const model = await manager.findOneOrFail(ProductModelEntity, {
        where: { id: entity.modelId },
        select: { metricsId: true },
      })
      entity.modelMetricsId = model.metricsId
    }

    // fix publishedAt
    entityFixPublishedAt({ event, type: 'insert' })
  }

  async afterInsert(event: InsertEvent<ProductSkuEntity>) {
    const { entity, manager } = event

    // add warranty rules
    const warrantyRuleIds = await ProductSkuEntity.getWarrantyRuleIds({ id: entity.id, manager })
    if (!warrantyRuleIds.length) {
      Sentry.captureException(new Error('No warranty rules found for product sku'), {
        extra: {
          skuId: entity.id,
        },
        level: 'warning',
      })
    }
    entity.warrantyRules = warrantyRuleIds.map((id) => ({ id })) as unknown as CategoryWarrantyRuleEntity[]

    // add seller joined year
    if (entity.saleItem) {
      const saleItem = await manager.findOne(SaleItemEntity, {
        where: { id: entity.saleItem.id },
        relations: { user: true },
        select: {
          user: {
            id: true,
            createdAt: true,
          },
        },
      })
      if (saleItem?.user?.createdAt) {
        entity.sellerJoinedYear = String(new Date(saleItem.sale.user.createdAt).getFullYear())
      }
    }

    // process images
    await entityImageAutoProcess({
      image: entity.heroImage,
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.PRODUCT_SKU,
      manager,
    })
    await pMap(entity.images, async (image: ImageEntity) => {
      await entityImageAutoProcess({ image, entityId: entity.id, imageUsedIn: ImageUsedInType.PRODUCT_SKU, manager })
    })

    // process testReport
    if (entity.testReport) {
      await entityFileAutoProcess({
        file: entity.testReport,
        entityId: entity.id,
        fileUsedIn: FileUsedInType.PRODUCT_SKU,
        manager,
      })
    }

    await manager.save(ProductSkuEntity, entity)

    // add to algolia index
    if (entity?.publishedAt && !process.env.ALGOLIA_DISABLE_INDEXING) {
      try {
        await ProductSkuIndex.addIndex({ id: entity.id, manager })
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'product-sku:add-to-algolia-index',
          },
          extra: {
            skuId: entity.id,
          },
          level: 'warning',
        })
      }
    }
  }

  async beforeUpdate(event: UpdateEvent<ProductSkuEntity>) {
    // fix publishedAt
    entityFixPublishedAt({ event, type: 'update' })
  }

  async afterUpdate(event: UpdateEvent<ProductSkuEntity>) {
    const { entity, databaseEntity, manager } = event

    if (!entity) {
      return
    }

    // change warranty rules if variantId changed
    if (entity?.variantId && databaseEntity?.variantId && entity.variantId !== databaseEntity.variantId) {
      const variant = await manager.findOneOrFail(ProductVariantEntity, {
        where: { id: entity.variantId },
        relations: {
          model: true,
        },
        select: {
          id: true,
          modelId: true,
          model: {
            id: true,
            brandId: true,
            categoryId: true,
          },
        },
      })
      entity.brandId = variant.model.brandId
      entity.categoryId = variant.model.categoryId
      entity.modelId = variant.model.id
      await manager.save(ProductSkuEntity, entity)
    }

    // TODO: update warranties

    // add/delete algolia index based on publishedAt
    if (typeof entity?.publishedAt !== 'undefined' && !process.env.ALGOLIA_DISABLE_INDEXING) {
      if (databaseEntity.publishedAt && !entity.publishedAt) {
        try {
          await ProductSkuIndex.deleteIndex({ id: entity.id, manager })
        } catch (error) {
          Sentry.captureException(error, {
            tags: {
              type: 'product-sku:delete-from-algolia-index',
            },
            extra: {
              skuId: entity.id,
            },
            level: 'warning',
          })
        }
        return
      } else if (!databaseEntity.publishedAt && entity.publishedAt) {
        try {
          await ProductSkuIndex.addIndex({ id: entity.id, manager })
        } catch (error) {
          Sentry.captureException(error, {
            tags: {
              type: 'product-sku:add-to-algolia-index',
            },
            extra: {
              skuId: entity.id,
            },
            level: 'warning',
          })
        }
        return
      }
    }

    // add/delete algolia index based on sold
    if (typeof entity?.sold !== 'undefined' && entity.sold && !process.env.ALGOLIA_DISABLE_INDEXING) {
      try {
        if (databaseEntity.sold && !entity.sold) {
          try {
            await ProductSkuIndex.addIndex({ id: entity.id, manager })
          } catch (error) {
            Sentry.captureException(error, {
              tags: {
                type: 'product-sku:add-to-algolia-index',
              },
              extra: {
                skuId: entity.id,
              },
            })
          }
          return
        } else if (!databaseEntity.sold && entity.sold) {
          try {
            await ProductSkuIndex.deleteIndex({ id: entity.id, manager })
          } catch (error) {
            Sentry.captureException(error, {
              tags: {
                type: 'product-sku:delete-from-algolia-index',
              },
              extra: {
                skuId: entity.id,
              },
            })
          }
          return
        }
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'product-sku:add-to-algolia-index',
          },
          extra: {
            skuId: entity.id,
          },
          level: 'warning',
        })
      }
    }

    // process images
    await entityImageAutoProcess({
      image: entity.heroImage ?? { id: entity.heroImageId }, // added fallback for the case that only the image id is set
      entityId: entity.id,
      imageUsedIn: ImageUsedInType.PRODUCT_SKU,
      manager,
    })
    await pMap(entity.images ?? [], async (image: ImageEntity) => {
      await entityImageAutoProcess({ image, entityId: entity.id, imageUsedIn: ImageUsedInType.PRODUCT_SKU, manager })
    })

    if (entity.testReport || entity.testReportId) {
      await entityFileAutoProcess({
        file: entity.testReport ?? { id: entity.testReportId }, // added fallback for the case that only the file id is set
        entityId: entity.id,
        fileUsedIn: FileUsedInType.PRODUCT_SKU,
        manager,
      })
    }

    // add/delete algolia index based on stock
    if (!process.env.ALGOLIA_DISABLE_INDEXING) {
      try {
        await ProductSkuIndex.updateIndex({ id: entity.id, manager })
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'product-sku:update-algolia-index',
          },
          extra: {
            skuId: entity.id,
          },
          level: 'warning',
        })
      }
    }
  }

  async afterRemove(event: RemoveEvent<ProductSkuEntity>) {
    const { databaseEntity, manager } = event

    // remove from algolia index
    if (!process.env.ALGOLIA_DISABLE_INDEXING) {
      try {
        await ProductSkuIndex.deleteIndex({ id: databaseEntity.id, manager })
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            type: 'product-sku:delete-from-algolia-index',
          },
          extra: {
            skuId: databaseEntity.id,
          },
          level: 'warning',
        })
      }
    }

    // delete images related to the entity
    entityImageDeleteByPrefix({
      imageUsedIn: ImageUsedInType.PRODUCT_SKU,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })

    entityFileDeleteByPrefix({
      fileUsedIn: FileUsedInType.PRODUCT_SKU,
      entityId: event?.databaseEntity?.id ?? event?.entity?.id ?? event?.entityId,
    })
  }
}
