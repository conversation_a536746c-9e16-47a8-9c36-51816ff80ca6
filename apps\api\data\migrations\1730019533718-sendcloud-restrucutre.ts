import { MigrationInterface, QueryRunner } from 'typeorm'

export class SendcloudRestructure1730019533718 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS "shipping_method"`)
    await queryRunner.query(`ALTER TABLE "warehouse_address" RENAME TO "warehouse"`)
    await queryRunner.query(`ALTER TABLE "warehouse" ADD COLUMN "sort_order" int4 NOT NULL DEFAULT 0`)

    // Create warehouse_receiving_from_countries table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "warehouse_receiving_from_countries";
      CREATE TABLE "warehouse_receiving_from_countries" (
        "warehouse_id" uuid NOT NULL,
        "locale_country_id" varchar NOT NULL
      )
    `)

    // Create warehouse_shipping_to_countries table
    await queryRunner.query(`
      DROP TABLE IF EXISTS "warehouse_shipping_to_countries";
      CREATE TABLE "warehouse_shipping_to_countries" (
        "warehouse_id" uuid NOT NULL,
        "locale_country_id" varchar NOT NULL
      )
    `)

    // Upsert the warehouse record
    await queryRunner.query(`
      UPDATE "warehouse" SET
        name = 'Sunbird Logistics',
        email = '<EMAIL>',
        country_id = 'DE',
        city = 'Pullheim',
        postal_code = '50259',
        phone = '+49 622982038',
        street = 'Donatusstraße',
        house_number = '102',
        updated_at = '2024-11-03 18:51:49.762914',
        sort_order = 1
      WHERE id = '26f0e73b-7bf5-4ffc-bfc9-8f0965ba6850'
    `)

    // Insert receiving countries data
    await queryRunner.query(`
      INSERT INTO "warehouse_receiving_from_countries" VALUES 
        ('75352814-8fae-4cb6-8343-648d1cfc9236', 'NL'),
        ('75352814-8fae-4cb6-8343-648d1cfc9236', 'DE'),
        ('75352814-8fae-4cb6-8343-648d1cfc9236', 'BE'),
        ('26f0e73b-7bf5-4ffc-bfc9-8f0965ba6850', 'DE')
    `)

    // Insert shipping countries data
    await queryRunner.query(`
      INSERT INTO "warehouse_shipping_to_countries" VALUES 
        ('75352814-8fae-4cb6-8343-648d1cfc9236', 'NL'),
        ('75352814-8fae-4cb6-8343-648d1cfc9236', 'DE'),
        ('75352814-8fae-4cb6-8343-648d1cfc9236', 'BE')
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
