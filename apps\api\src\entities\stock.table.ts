import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  OneToOne,
  PrimaryColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm'

import { StockType } from '~/constants'
import { IProductSku, ISaleItem, ProductSkuEntity, SaleItemEntity } from '~/entities'

@Entity('stock')
export class StockEntity extends BaseEntity {
  @PrimaryColumn()
  id: string

  @Column({ type: 'varchar' })
  type: StockType

  @OneToOne(() => ProductSkuEntity, (productSku) => productSku.stock, { nullable: true })
  productSku?: Relation<ProductSkuEntity>

  @OneToOne(() => SaleItemEntity, (saleItem) => saleItem.stock, { nullable: true })
  saleItem?: Relation<SaleItemEntity>

  @CreateDateColumn()
  @Index()
  createdAt: Date

  @UpdateDateColumn()
  updatedAt: Date

  static async getNewStockId(type: StockType) {
    const result = await StockEntity.getRepository()
      .createQueryBuilder()
      .select("NEXTVAL('stock_id_seq')", 'id')
      .getRawOne()
    return `${type === StockType.SALE ? 'S' : 'E'}${result?.id}`
  }
}

export type IStock = Omit<StockEntity, keyof BaseEntity> & {
  productSku?: IProductSku
  saleItem?: ISaleItem
}
