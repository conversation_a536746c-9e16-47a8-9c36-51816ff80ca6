import { Edit, useForm } from '@refinedev/antd'
import { type HttpError, type IResourceComponentsProps } from '@refinedev/core'
import { AdminRoleType } from '@valyuu/api/admin-constants'
import type { IAdminUser } from '@valyuu/api/entities'
import { Form, Typography } from 'antd'
import { Select } from 'antx'
import { Input, Switch } from 'antx'
import { type FC } from 'react'

import { formatSelectOptionsFromEnum } from '~/utils'

export const AdminUserEdit: FC<IResourceComponentsProps> = () => {
  const { formProps, id, saveButtonProps, formLoading } = useForm<IAdminUser, HttpError, IAdminUser>()

  return (
    <Edit
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      headerProps={{
        subTitle: (
          <>
            ID: <Typography.Text copyable>{id}</Typography.Text>
          </>
        ),
      }}
    >
      <Form {...formProps} layout="vertical">
        <Input label="Email" name="email" rules={['email']} disabled />
        <Input label="Name" name="name" rules={['required']} />
        <Input label="Picture" name="picture" type="url" />
        <Select label="Roles" name="roles" options={formatSelectOptionsFromEnum(AdminRoleType)} />
        <Switch label="Enabled" name="enabled" />
      </Form>
    </Edit>
  )
}
