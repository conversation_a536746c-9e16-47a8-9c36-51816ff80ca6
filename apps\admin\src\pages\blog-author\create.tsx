import { Create, useForm } from '@refinedev/antd'
import type { IResourceComponentsProps } from '@refinedev/core'
import { ImageProcessNameType, ImageUsedInType } from '@valyuu/api/constants'
import type { IBlogAuthor } from '@valyuu/api/entities'
import { Form } from 'antd'
import { Input, Watch } from 'antx'
import cx from 'clsx'
import { type FC, useMemo, useState } from 'react'
import { v4 as uuid } from 'uuid'

import { InputImage, InputLanguageTab, InputMultiLang, LanguageTabChoices } from '~/components'

export const BlogAuthorCreate: FC<IResourceComponentsProps> = () => {
  const { formProps, saveButtonProps } = useForm<IBlogAuthor>()

  const id = useMemo(() => uuid(), [])

  const [currentLanguage, setCurrentLanguage] = useState<LanguageTabChoices>('')
  const clsHideEn = cx({ hidden: currentLanguage && currentLanguage !== 'en' })
  const clsHideNl = cx({ hidden: currentLanguage && currentLanguage !== 'nl' })
  const clsHideDe = cx({ hidden: currentLanguage && currentLanguage !== 'de' })
  const clsHidePl = cx({ hidden: currentLanguage && currentLanguage !== 'pl' })

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form
        {...formProps}
        layout="vertical"
        onFinishFailed={(errorInfo) => {
          setCurrentLanguage('')
          formProps.onFinishFailed?.(errorInfo)
        }}
      >
        <InputLanguageTab value={currentLanguage} onChange={(value) => setCurrentLanguage(value)} />
        <Input noStyle hidden name="id" initialValue={id} />
        <Input label="Name" name="name" rules={['required']} />
        <Watch list={['title__en', 'title__nl', 'title__de', 'title__pl']}>
          {([title__en, title__nl, title__de, title__pl]) => (
            <>
              <InputMultiLang
                sources={{ en: title__en, nl: title__nl, de: title__de, pl: title__pl }}
                targetLang="en"
                label="Title (English)"
                name="title__en"
                rules={['required']}
                className={clsHideEn}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: title__en, nl: title__nl, de: title__de, pl: title__pl }}
                targetLang="nl"
                label="Title (Dutch)"
                name="title__nl"
                rules={['required']}
                className={clsHideNl}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: title__en, nl: title__nl, de: title__de, pl: title__pl }}
                targetLang="de"
                label="Title (German)"
                name="title__de"
                rules={['required']}
                className={clsHideDe}
                fillType="duplicate"
              />
              <InputMultiLang
                sources={{ en: title__en, nl: title__nl, de: title__de, pl: title__pl }}
                targetLang="pl"
                label="Title (Polish)"
                name="title__pl"
                rules={['required']}
                className={clsHidePl}
                fillType="duplicate"
              />
            </>
          )}
        </Watch>
        <Watch name="name">
          {(name) => (
            <InputImage
              label="Avatar"
              name="avatar"
              limit={1}
              rules={[{ required: true, message: 'Please upload an image' }]}
              plugins={['ImageEditor']}
              entityId={id! as string}
              entityType={ImageUsedInType.BLOG_AUTHOR}
              processName={ImageProcessNameType.TRANSLATE}
              defaultNames={{ name__en: name, name__nl: name, name__de: name, name__pl: name }}
              className="col-span-2"
            />
          )}
        </Watch>
      </Form>
    </Create>
  )
}
